version: '3.8'
# Goal of this docker compose dev is to run the services locally, with hot reload when files are changed
# you will need to copy .env-example to .env and setup and auth.toml
services:
  pdf2img:
    build:
      context: .
      dockerfile: Dockerfile.dev
    networks:
      core-services:
      caddy:
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
      - auth_toml
    command: watchgod asyncizer.pdf2jpg_worker.run_worker
    deploy:
      resources:
        limits:
          cpus: '1'
          # For complicated plans (e.g. 10MB) 2GB is not enough, better 3GB
          memory: 3000M
      placement:
        constraints: [ node.labels.generic == true ]
      replicas: 3
    volumes:
      - ./:/app
      - poetry-cache:/var/cache/pypoetry

  spacy-classifier:
    build:
      context: .
      dockerfile: Dockerfile.dev
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
      - auth_toml
    command: python classifier/spacy_worker.py
#    environment:
#      - SPACY_MODEL_DE_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_de_20220730-0032-20220731-0530.tar.gz
#      - SPACY_MODEL_EN_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_en_20220731-0825-20220731-0836.tar.gz
#      - SPACY_MODEL_FR_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_fr_20220731-0909-20220731-1347.tar.gz
#      - SPACY_MODEL_IT_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_it_20220729-2254-20220729-2315.tar.gz
    networks:
      core-services:
      caddy:

    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4500M
      replicas: 3
      placement:
        constraints: [ node.labels.generic == true ]
    volumes:
      - ./:/app
      - poetry-cache:/var/cache/pypoetry

  hyextract:
    build:
      context: .
      dockerfile: Dockerfile.dev
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
      - auth_toml
    networks:
      core-services:
      caddy:
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1'
          memory: 4750M
      placement:
        constraints: [ node.labels.generic == true ]
    volumes:
      - ./:/app
      - poetry-cache:/var/cache/pypoetry

  original_file_processor:
    build:
      context: .
      dockerfile: Dockerfile.dev
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
      - auth_toml

    command: python asyncizer/file_process_worker.py
    networks:
      core-services:
      caddy:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2048M
      replicas: 3
      placement:
        constraints: [ node.labels.generic == true ]
    volumes:
      - ./:/app
      - poetry-cache:/var/cache/pypoetry

volumes:
  poetry-cache:

secrets:
  ENV_CONF_V5:
    file: .env
  auth_toml:
    file: ./auth.toml

networks:
  core-services:
    external: true
  caddy:
    external: true

