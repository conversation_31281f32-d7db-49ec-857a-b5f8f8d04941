from abbyyplumber.converter.ValueConverter import (
    CleanNameConverter,
    MostRecentDateConverter,
    DateConverter,
    ParagraphConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrained<PERSON>rea,
    SearchElementArea,
    SearchElementLabeledField,
    create_labeled_field_vertical,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_FULLNAME
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.AllianzPensionCertificateParsers import (
    AllianzPensionCertificatePageOnePageParser,
    AllianzPensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.AsgaPensionCertificateParsers import (
    AsgaPensionCertificatePageInfoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.AxaPensionCertificateParsers import (
    AxaPensionCertificatePageOnePageParser,
    AxaPensionCertificatePageTwoPageParser,
    AxaPensionCertificateLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.BasicMatchingPageParsers import (
    IvfHartmannCertificatePageOnePageParser,
    IvfHartmannCertificatePageTwoPageParser,
    CommonPensionCertificatePageTwoFRParser,
    CpevPensionCertificatePageOneFRParser,
    CpevPensionCertificatePageTwoFRParser,
    SicpaPensionCertificatePageOneFRParser,
    AllianzPensionCertificatePageTwoFRParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.BernischePensionCertificateParsers import (
    BernischePensionCertificatePageOnePageParser,
    BernischePensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.CommonParsersDE import (
    GenericPensionCertificatePageOnePageParser,
    GenericPensionCertificatePageTwoPageParser,
    SmartPensionCertificatePageOnePageParser,
    GenericPensionCertificatePageThreePageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.HelvetiaPensionCertificateParsers import (
    HelvetiaPensionCertificatePageOnePageParser,
    HelvetiaPensionCertificatePageTwoPageParser,
    HelvetiaPensionCertificatePageInfoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.MigrosPensionCertificateParsers import (
    MigrosPensionCertificatePageOnePageParser,
    MigrosPensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.PKMobilPensionCertificateParsers import (
    PKMobilPensionCertificatePageOnePageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.PrevisPensionCertificateParsers import (
    PrevisPensionCertificatePageOnePageParser,
    PrevisPensionCertificatePageTwoPageParser,
)

from mortgageparser.documents.parser.pageparsers.pensioncertificate.RochePensionCertificateParsers import (
    RochePensionCertificatePageOnePageParser,
    RochePensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.SbbPensionCertificateParsers import (
    SbbPensionCertificatePageOnePageParser,
    SbbPensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.SpsJelmoliPensionCertificateParsers import (
    SpsJelmoliPensionCertificatePageOnePageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.SulzerPensionCertificateParsers import (
    SulzerPensionCertificateSinglePagePageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.SwissLifePensionCertificateParsers import (
    SwissLifePensionCertificatePageOnePageParser,
    SwissLifePensionCertificateV2PageOnePageParser,
    SwissLifePensionCertificateV2PageTwoPageParser,
    SwissLifePensionCertificateLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.SyngentaPensionCertificateParsers import (
    SyngentaPensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.ZGPKPensionCertificateParsers import (
    ZGPKPensionCertificatePageOnePageParser,
    ZGPKPensionCertificatePageTwoPageParser,
    ZGPKPensionCertificateLetterPageParser,
)

#
from mortgageparser.documents.parser.pageparsers.pensioncertificate.pension_certificate_parsers_i18n import (
    parsers_i18n,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.pension_certificate_util import (
    TemplatePensionCertificateFRPageTwoPageParser,
)

parsers_de = [
    SmartPensionCertificatePageOnePageParser(
        company="Agrisano Prevos",
        titles=["Versicherungsausweis per"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versichertennummer",
            text_bottom="Geburtsdatum",
            text_left="Eintritt in die Vorsorgeeinrichtung",
            converter=CleanNameConverter(),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name",
            text_bottom="Eintritt in die Vorsorgeeinrichtung",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
        use_ahv_new=False,
        document_date_label="Versicherungsausweis per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Agrisano Prevos",
            "Allgemeine Angaben",
            "Eintritt in die Vorsorgeeinrichtung",
            "garantierter Mindestzinssatz",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        # This is the same as "c/o Libera AG" except for the name.
        company="Amstein-Walthert",
        titles=["Vorsorgeausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönlich",
            text_right="Kontakt",
            text_bottom="Vorsorgeausweis per",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personalien",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
            text_right="Personal-Nummer",
            converter=DateConverter(),
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Amstein",
            "Walthert",
            "Vorsorgeausweis",
            "BVG-Altersguthaben",
            "Austrittsleistung",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CREDIT_NOTE,
        page_cat=PageCat.GENERIC_PAGE,
        company="APK",
        titles=["Abrechnung Freizügigkeitsleistung"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Abrechnung Freizügigkeitsleistung",
            x_range=PercentageRange(0.5, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Wir bestätigen den Erhalt",
            text_bottom="Versicherter Lohn",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="www.agpk.ch",
            text_bottom="Abrechnung Freizügigkeitsleistung",
        ),
        required_tokens=[
            "Aargauische Pensionskasse",
            "www.agpk.ch",
            "Wir bestätigen den Erhalt",
            "Neue Vorsorgeleistungen",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="APK",
        titles=["Simulation über den Vorbezug für Wohneigentum per"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_left="@agpk.ch", text_bottom="PK-Nr."
        ),
        document_date_label="Simulation über den Vorbezug für Wohneigentum per",
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Arbeitgeber",
            text_bottom="Versicherter Lohn",
            text_left="Geburtsdatum",
        ),
        required_tokens=[
            "Maximal möglicher Auszahlungsbetrag",
            "Auszahlungsbetrag gemäss Ihrem Bescheid",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="APK",
        titles=["Simulation Altersrücktritt per"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_left="@agpk.ch", text_bottom="PK-Nr."
        ),
        document_date_label="Simulation Altersrücktritt per",
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Grundlagen",
            text_bottom="Rentenleistungen",
            text_left="Geburtsdatum",
        ),
        required_tokens=[
            "haben wir die Altersleistungen berechnet",
            "Rentenleistungen pro",
        ],
    ),
    # Regular Certificates DE, check APK alternative (same PK)
    GenericPensionCertificatePageOnePageParser(
        "APK",
        ["Vorsorgeausweis gültig ab"],
        "Vorsorgeausweis gültig ab",
        [
            "Aargauische Pensionskasse",
            "Grundlagen",
            "Anrechenbarer Jahreslohn",
            "Altersguthaben",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="APK Austritt",
        titles=["Austrittsabrechnung per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Austrittsabrechnung per",
            x_range=PercentageRange(0.5, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Angaben für die neue Vorsorgeeinrichtung",
            text_bottom="Heiratsdatum",
            text_left="Geburtsdatum",
            text_right="AHV-Nr",
        ),
        use_ahv_new=True,
        document_date_label="Austrittsabrechnung per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Aargauische Pensionskasse",
            "Austrittsabrechnung per",
            "Ermittlung der Austrittsleistung",
            "Überweisung an",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="ASGA",
        titles=["Vorsorgeausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="www.asga.ch",
            text_bottom="Versicherte Leistungen",
            x_range=PercentageRange(0, 0.5),
        ),
        document_date_label="Versicherte Leistungen gültig ab ",
        required_tokens=[
            "ASGA Pensionskasse",
            "Vorsorgeausweis",
            "Mitglied-Nummer:",
            "Altersrente",
            "Altersguthaben",
            "Versicherte Leistungen gültig ab",
        ],
        use_ahv_new=True,
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="AHV-Nummer",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
    ),
    AsgaPensionCertificatePageInfoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="Avandis",
        product="Basis",
        titles=["Persönlicher Ausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="c/o Avandis Vorsorge AG, CH-8005",
            text_bottom="Persönlicher Ausweis",
            x_range=PercentageRange(0, 0.5),
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Persönliche Daten",
            text_left="Gültig ag",
            y_range=PercentageRange(0, 0.4),
        ),
        required_tokens=[
            "Avadis Vorsorge AG",
            "Personenkreis",
            "Gemeldeter Jahreslohn",
            "Jährliche Beiträge",
        ],
        use_ahv_new=True,
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nummer",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
    ),
    GenericPensionCertificatePageOnePageParser(
        "Avadis",
        ["Versicherungsausweis 20"],
        "",
        [
            "Avadis Vorsorge AG",
            "Basisversicherung",
            "AHV-Nummer",
            "Massgebend",
            "Versichert",  # Salär oder Lohn
            "Sparkapital",
        ],
        "Basis",
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Avandis",
        product="Ergänzung",
        titles=["Versicherungsausweis 20"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="c/o Avandis Vorsorge",
            text_bottom="Versicherungsausweis",
            x_range=PercentageRange(0, 0.5),
        ),
        se_document_date=SearchElementArea(None, None, y_range=PercentageRange(0, 0.4)),
        required_tokens=[
            "Avadis Vorsorge AG",
            "Ergänzungsversicherung",
            "AHV-Nummer",
            "Massgebend",
            "Versichert",  # Salär oder Lohn
            "Sparkapital",
        ],
    ),
    GenericPensionCertificatePageOnePageParser(
        "Avadis",
        ["Versicherungsausweis 20"],
        "Zürich, ",
        [
            "Avadis Vorsorge AG",
            "Zürich",  # Todo: add restriction here for Zusatz
            "AHV-Nummer",
            "Massgebend",
            "Versichert",  # Salär oder Lohn
            "Sparkapital",
            "Zusatz",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Avadis Basis and Zusatz Two",
        [
            "Leistungen bei vorzeitigem Rücktritt",
            "Leistungen im Rücktrittsalter 65",
            "Invaliden-Kinderrente pro Kind",
            "Zusätzliche Informationen",
            "Einlagen durch Firma",
            "WEF-Verpfändung vorhanden",
            "WEF-Bezüge",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="ASGA Austritt",
        titles=["Austrittsabrechnung für"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_bottom="Austrittsdatum", x_range=PercentageRange(0, 0.5)
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Austrittsabrechnung für",
            text_bottom="Versicherten-Nummer",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Austrittsdatum",
            text_bottom="Berechnung der Freizügigkeitsleistung",
        ),
        required_tokens=[
            "www.asga.ch",
            "Austrittsabrechnung per",
            "Berechnung der Freizügigkeitsleistung",
            "überweisen wir die Austrittsleistung",
        ],
    ),
    GenericPensionCertificatePageThreePageParser(
        "AXA Three",
        [
            "Seite 3",
            "3/3",
            "Pensionskassenausweis",
            "Gültig ab",
            "Vertrag Nr.",
            "Grundlage des Pensionskassenausweises ist das Reglement Ihrer Pensionskasse",
            "im Auftrag Ihrer Pensionskasse durch die AXA Leben AG, 8401 Winterthur",
        ],
    ),
    AxaPensionCertificateLetterPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="AXA",
        product="Zusatz",
        titles=["Pensionskassenausweis"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Ihre Personalien",
            text_bottom="Geburtsdatum",
            text_left="Name / Vorname",
            text_right="Versicherungsbeginn",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name / Vorname",
            text_bottom="Geschlecht",
            text_left="Geburtsdatum",
            text_right="Versicherungsbeginn",
        ),
        use_ahv_new=True,
        document_date_label="Gültig ab",
        required_tokens=[
            "AXA Stiftung",
            "Zusatzvorsorge",
            "Voraussichtliche Leistungen im Alter",
            "Anspruch bei Austritt",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="AXA",
        titles=["Auflösung Ihrer Freizügigkeitspolice"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Winterthur",
            text_bottom="Auflösung",
            x_range=PercentageRange(0, 0.5),
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Winterthur",
            text_bottom="Auflösung",
            x_range=PercentageRange(0, 0.5),
        ),
        required_tokens=[
            "AXA Leben AG",
            "Sie haben uns beauftragt",
            "überweisen wir zu Ihren Gunsten an",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_WITHDRAWL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="AXA",
        titles=["Abrechnung über den Vorbezug für Wohneigentum"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_bottom="Abrechnung", x_range=PercentageRange(0.05, 0.5)
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None, None, text_top="axa-winterthur.ch", text_bottom="Abrechnung"
        ),
        required_tokens=[
            "Sie haben im Rahmen der Wohneigentumsförderung",
            "Vorbezug",
            "können wir den Vorbezug auszahlen",
            "überweisen wir an",
        ],
    ),
    AxaPensionCertificatePageOnePageParser(),
    AxaPensionCertificatePageTwoPageParser(),
    AllianzPensionCertificatePageOnePageParser(),
    AllianzPensionCertificatePageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="Baloise",
        titles=["Vorsorgeausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Ihr Kontakt",
            text_bottom="Vorsorgeausweis per",
            x_range=PercentageRange(0.5, 1),
        ),
        document_date_label="Vorsorgeausweis per",
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versichertennummer",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        required_tokens=[
            "Bâloise-Sammelstiftung",
            "c/o Basler Leben AG",
            "Vorsorgeausweis",
            "Personendaten",
            "AHV-Nummer",
            "Massgebende Jahreslöhne",
            "Altersguthaben",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Baloise Two / 1",
        [
            "Voraussichtliche Leistungen bei Pensionierung",
            "Maximaler Einkaufsbetrag",
            "Altersgutschrift",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Baloise Two / 2",
        [
            "www.baloise.ch/vorsorgeausweis",
            "Austrittsleistung gem. Art. 15 FZG",
            "Altersgutschrift",
        ],
    ),
    BernischePensionCertificatePageOnePageParser(),
    BernischePensionCertificatePageTwoPageParser(),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="BLPK Austritt",
        titles=["Austrittsabrechnung per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="erstellt am",
            text_bottom="Austrittsabrechnung per",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönliche Angaben",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Austrittsabrechnung per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "www.blpk.ch",
            "Ihre Austrittsleistung per",
            "BVG-Altersguthaben",
        ],
    ),
    TemplatePageParser(
        desc="BLS Page #2",
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        doc_cat=DocumentCat.PENSION_CERTIFICATE,
        ranked_titles_all=[RankedTitle("BLS AG")],
        required_tokens=[
            "aktueller Vorsorgeplan",
            "Altersgutschriften",
            "Verwaltungskostenbeitrag",
            "Risikobeitrag",
            "BVG",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="BLVK",
        # product="Lohnanpassung",
        titles=["Anpassung der Gehaltsgrundlagen"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="Ausgestellt am", text_bottom="Ihre Ansprechperson"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nr.",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Gehaltsgrundlagen per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Bernische Lehrerversicherungskasse",
            "Anpassung der Gehaltsgrundlagen",
            "Vorsorgeleistungen im Alter 65",
            "Monatliche Ehegattenrente",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="BVK",
        titles=["Entwicklung Sparguthaben im 20"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, x_range=PercentageRange(0, 0.5), text_bottom="Entwicklung"
        ),
        use_ahv_new=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vorsorge Service",
            text_bottom="Entwicklung",
            y_range=PercentageRange(0, 0.4),
            converter=DateConverter(),
        ),
        required_tokens=[
            "BVK",
            "Vorsorge Service",
            "die Kontobewegungen des ",
            "Personalvorsorge des Kantons Zürich",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
        company="BVK",
        titles=["Berechnungen für den Wohneigentumsvorbezug"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Postfach",
            x_range=PercentageRange(0, 0.5),
            text_bottom="Sehr geehrte",
        ),
        use_ahv_new=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vorsorge Service",
            y_range=PercentageRange(0, 0.4),
            converter=DateConverter(),
        ),
        required_tokens=[
            "BVK",
            "Vorsorge Service",
            "Vorbezug",
            "Veränderung der Vorsorgesituation",
            "Personalvorsorge des Kantons Zürich",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
        company="BVK",
        titles=["Seite 2/2"],
        use_ahv_new=False,
        required_tokens=[
            "BVK",
            "Bitte kontaktieren Sie uns, falls Sie Fragen haben",
            "Freundliche Grüsse",
            "Merkblatt Vorbezug",
            "Personalvorsorge des Kantons Zürich",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="BVK",
        titles=["Berechnungen für den Wohneigentumsvorbezug"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Berechnungen für den",
            x_range=PercentageRange(0, 0.6),
        ),
        se_document_date=SearchElementConstrainedArea(
            None, None, text_top="Policen-Nr.", text_bottom="Berechnungen für den"
        ),
        required_tokens=[
            "Veränderung der Vorsorgesituation durch den Vorbezug",
            "www.bvk.ch",
            "Bei einer bestehenden Verpfändung der Leistungen",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_NON_FIRST_PAGE,
        company="BVK",
        titles=["Seite 2/2"],
        required_tokens=[
            "Freundliche Grüsse",
            "www.bvk.ch",
            "Beilagen",
            "Merkblatt Vorbezug im Rahmen der Wohneigentumsförderung",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="Carl Spaeter",
        titles=["Austrittsabrechnung per"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="Pensionskasse", text_bottom="Austrittsabrechnung"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönliche Daten",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
            text_right="Arbeitgeber",
            converter=DateConverter(),
        ),
        use_ahv_new=False,
        document_date_label="Austrittsabrechnung per",
        required_tokens=[
            "Pensionskasse der",
            "Carl Spaeter AG",
            "Austrittsabrechnung per",
            "Austrittsberechnung nach Reglement",
            "Beim Übertrag der Austrittsleistung",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="Carl Spaeter",
        titles=["Überweisungsbestätigung"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="Pensionskasse", text_bottom="Überweisungsbestätigung"
        ),
        use_ahv_new=False,
        document_date_label="rechnung per",
        required_tokens=["Pensionskasse der", "Carl Spaeter AG"],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Credit Suisse",
        titles=["Kapitalsparen - Versicherungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="credit-suisse.com/pensionskasse",
            text_bottom="Kapitalsparen - Versicherungsausweis per",
            x_range=PercentageRange(0.5, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Eintritt in die Pensionskasse",
            text_bottom="Alter/Monate",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
        use_ahv_new=True,
        document_date_label="Versicherungsausweis per",
        required_tokens=[
            "credit-suisse.com/pensionskasse",
            "Aktuelles Alterssparkapital",
            "Todesfallkapital",
        ],
    ),
    GenericPensionCertificatePageOnePageParser(
        "Credit Suisse",
        ["Rentensparen - Versicherungsausweis per"],
        "",
        [
            "PENSIONSKASSE DER CREDIT SUISSE GROUP (SCHWEIZ)",
            "credit-suisse.com/pensionskasse",
            "AHV-Nr",
            "Aktuelles Alterssparkapital",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Credit Suisse Rentensparen 2",
        [
            "Rentensparen - Versicherungsausweis",
            "Voraussichtliches Alterssparkapital",
            "Einkäufe in den letzten drei Jahren",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Credit Suisse Kapitalsparen 2",
        [
            "Kapitalsparen - Versicherungsausweis",
            "Voraussichtliches Alterssparkapital",
            "Einkäufe in den letzten drei Jahren",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="Emil Frey",
        titles=["DAS WICHTIGSTE IN KÜRZE"],
        required_tokens=[
            "Zukünftige Umwandlungssätze",
            "Pensionskasse der Emil Frey Gruppe",
            "Sollten Sie Fragen zur Pensionskasse",
        ],
    ),
    TemplatePensionCertificateFRPageTwoPageParser(
        desc="fenaco pension2 page 2 FR",
        company="fenaco",
        required_text_conditions=[
            FromStartTextCond("Caisse de pension fenaco", num_lines=5),
            FromStartTextCond("Données d'assuré", num_lines=5),
        ],
        required_tokens=["Prestations de viellesse"],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Freelance",
        titles=["Vorsorgeausweis"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Allgemeine Angaben",
            text_bottom="Versicherten-Nummer",
            x_range=PercentageRange(0.3, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nummer",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="gültig ab",
        required_tokens=[
            "Pensionskasse Freelance",
            "Vorsorgeleistungen",
            "Todesfall-Leistungen",
            "Maximale Einkaufsssumme",
            "Eingebrachte Einkaufssumme",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Futura",
        titles=["Vorsorgeausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Vorsorgeausweis per",
            x_range=PercentageRange(0, 0.5),
        ),
        document_date_label="Vorsorgeausweis per",
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name/Vorname",
            text_bottom="SV-Nummer",
            text_left="Geburtsdatum",
            text_right="Vorsorgeplan",
        ),
        use_ahv_new=True,
        required_tokens=[
            "FUTURA Vorsorge",
            "Personalien",
            "Altersguthaben am",
            "Versicherter Sparlohn",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Futura Two",
        [
            "Fortsetzung Vorsorgeausweis per",
            "Todesfalleistungen",
            "FUTURA Vorsorge",
            "www.futura.ch",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Gemeinschaftsstiftung für berufliche Vorsorge Two",
        [
            "Ausgleichskasse",
            "Freizügigkeitsleistung",
            "Gesamtbeitrag",
            "im Auftrag der Gemeinschaftsstiftung für berufliche Vorsorge im Schweizerischen Gewerbe in Bern",
            "Grundlage dieses Ausweises bildet das Reglement Ihrer Pensionskasse.",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Georg Fischer",
        titles=["Vorsorgeausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="Persönlich / Vertraulich", text_bottom="Personalien"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Zivilstand",
            text_bottom="AHV-Nummer",
            text_left="Geburtsdatum",
            text_right="Versicherungsnummer",
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Pensionskasse Georg Fischer",
            "www.georgfischer.com",
            "Vorsorgeausweis per",
            "Gesamtbeitrag Arbeitnehmer",
            "Austrittsleistung per",
        ],
    ),
    HelvetiaPensionCertificatePageOnePageParser(),
    HelvetiaPensionCertificatePageTwoPageParser(),
    HelvetiaPensionCertificatePageInfoPageParser(),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="Helvetia",
        titles=["Austrittsabrechnung per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Pensionskasse",
            text_bottom="Austrittsabrechnung",
            x_range=PercentageRange(0, 0.5),
        ),
        use_ahv_new=True,
        document_date_label="Austrittsabrechnung per",
        required_tokens=[
            "Pensionskasse der",
            "Helvetia Versicherungen",
            "BVG-Altersguthaben nach Art. 15 FZG",
            "Freizügigkeitsleistung",
            "Total Altersguthaben gemäss Reglement",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Helvetia Austritt Two",
        [
            "Austrittsleistung",
            "Die Freizügigkeitsleistung entspricht dem höchsten der Beträge",
            "Weitere Informationen",
            "Pensionskasse der",
            "Helvetia Versicherungen",
        ],
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
    ),
    IvfHartmannCertificatePageOnePageParser(),
    IvfHartmannCertificatePageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="Johnson and Johnson",
        titles=["Leistungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Persönliche Angaben",
            x_range=PercentageRange(0, 0.5),
        ),
        document_date_label="Leistungsausweis per",
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Arbeitgeber",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        required_tokens=[
            "Johnson & Johnson Pension Fund",
            "Persönliche Angaben",
            "AHV-Nummer",
            "Massgebender Jahreslohn",
            "Altersleistungen",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        # This is the same as "Amstein Waltert" except for the name. Name here is e.g. "Stiftung EAO"
        company="",  # Stifung EAO c/o Libera
        titles=["Vorsorgeausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönlich",
            text_right="Kontakt",
            text_bottom="Vorsorgeausweis per",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personalien",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
            text_right="Personal-Nummer",
            converter=DateConverter(),
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "c/o Libera AG",
            "Aeschengraben 10",
            "Vorsorgeausweis",
            "BVG-Altersguthaben",
            "Austrittsleistung",
        ],
    ),
    MigrosPensionCertificatePageOnePageParser(),
    MigrosPensionCertificatePageTwoPageParser(),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="MPK",
        titles=["Provisorische Altersleistungsberechnung per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vertraulich",
            text_left="@mpk.ch",
            text_bottom="Provisorische Altersleistungsberechnung",
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Provisorische Altersleistungsberechnung",
            text_bottom="Persönliche Angaben",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Zivilstand",
            text_bottom="AHV-Nummer",
            text_left="Geburtsdatum",
            text_right="Eintrittsdatum",
        ),
        use_ahv_new=True,
        required_tokens=["Migros-Pensionskasse", "unter folgender Annahme"],
    ),
    # Page 1 is common DE
    GenericPensionCertificatePageTwoPageParser(
        "Nest Two",
        [
            "Nest Sammelstiftung",
            "Finanzierung",
            "Entwicklung Altersguthaben",
            "maximal möglicher Einkauf",
            "www.nest-info.ch",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Noventus",
        titles=["Persönlicher Ausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönlich",
            text_bottom="Gemeldeter Jahreslohn",
            x_range=PercentageRange(0, 0.5),
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Eintritt",
            text_bottom="Persönlicher Ausweis",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nr.",
            text_bottom="Geschlecht",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        required_tokens=[
            "NOVENTUS",
            "VORSORGELÖSUNGEN",
            "Persönlicher Ausweis",
            "AHV-Nr",
            "Gemeldeter Jahreslohn",
            "Versichert",  # Salär oder Lohn
            "Altersguthaben",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Novozymes",
        titles=["Kontoauszug"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personalien",
            text_left="Vorname",
            text_bottom="Geburtsdatum",
            text_right="Versicherter Jahreslohn",
            x_range=PercentageRange(0, 0.8),
            converter=CleanNameConverter(),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vorname",
            text_bottom="Geschlecht",
            text_left="Geburtsdatum",
            text_right="Versicherter Jahreslohn",
        ),
        use_ahv_new=False,
        document_date_label="Datum: ",
        required_tokens=[
            "Personalvorsorgeeinrichtung",
            "der Novozymes Switzerland",
            "Altersguthaben",
            "Altersgutschriften",
            "WEF / Scheidung",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "PK Complan Two",
        [
            "Arbeitgeber",
            "Kontoauszug",
            "Sparen Mitglied",
            "generelle Information",
            "maximale Einkaufssumme für die ordentlichen Leistungen",
            "www.pk-complan.ch",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="PK EMK",
        titles=["Orientierung Wohneigentumsförderung"],
        required_tokens=[
            "Pensionskasse der EMK",
            "Vorbezug / Pfandverwertung",
            "Orientierung Wohneigentumsförderung für",
            "Dieses Orientierungsblatt ist keine Beschreibung",
        ],
    ),
    PKMobilPensionCertificatePageOnePageParser(),
    SmartLetterPageParser(
        desc="Vorsorgeplan B50 #1",
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PK Mobil",
        required_tokens=[
            "PK/CP MOBIL, CH-3000",
            "Vorsorgeplan",
            "Für die im aktuellen Vorsorgereglement",
            "alle in diesem Vorsorgeplan versicherten",
            "Personenkreis",
        ],
    ),
    SmartLetterPageParser(
        desc="Vorsorgeplan B50 #2",
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PK Mobil",
        required_tokens=[
            "PK/CP MOBIL",
            "CH-3000 Bern",
            "Vorsorgeplan B",
            "Stand am",
            "Mitglieder Nr.",
            "volle Deckung für Beitragsbefreiung",
            "Aufteilung des fakturierten Beitrags",
            "überobligatorisch",
        ],
        hamming_dist=4,
    ),
    SmartPensionCertificatePageOnePageParser(
        company="PKS",
        titles=["Versicherungsausweis im Beitragsprimat per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherungsausweis im Beitragsprimat per",
            text_left="Lohn und Beiträgefür Ihre Leistungen",
            text_bottom="Lohn und Beiträgefür Ihre Leistungen",
        ),
        se_document_date=SearchElementConstrainedArea(
            None, None, text_left="im Beitragsprimat per", text_bottom="AHV-ID"
        ),
        required_tokens=[
            "Pensionskasse SRG SSR",
            "Lohn und Beiträge",
            "Altersguthaben",
            "Vorhandenes Altersguthaben",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "PKE Two",  # Axpo, only page 2, page 1 is common
        [
            "Vorsorgestiftung Energie",
            "Vorsorgeausweis",
            "Altersleistung im Alter 64",
            "Bei Erwerbsunfähigkeit",
            "Im Todesfall",
            "Maximal verfügbar für Wohneigentumsförderung",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PKS",
        required_tokens=[
            "Vorgehen bei einem Vorbezug oder einer Verpfändung",
            "Für einen Vorbezug bzw. eine Verpfändung benötigt die PKS",
            "FÜR EINEN VORBEZUG",
            "<EMAIL>",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PKS",
        required_tokens=[
            "Für eine Beteiligung an Wohneigentum",
            "des Vorsorgezweckes im Original bei der PKS zu hinterlegen",
            "FÜR EINE VERPFÄNDUNG",
            "Pfandvertrag mit dem Pfandgläubiger sowie Kopie",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PKS",
        required_tokens=[
            "Richtlinien der Pensionskasse SRG SSR (PKS) über die Finanzierung",
            "Vorbezug oder Verpfändung",
            "Eigenbedarf",
            "<EMAIL>",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PKS",
        required_tokens=[
            "Eigenkapital durch Vorbezug",
            "Über die Höhe der Kürzung informiert die PKS. Um eine Einbusse",
            "die PKS die versicherte Person über die Möglichkeiten einer Risikozusatzversicherung",
        ],
    ),
    # Some 5 pages more here.... needs to be detected as a full doc
    SmartPensionCertificatePageOnePageParser(
        company="PKTG",
        titles=["Leistungsausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="www.pktg.ch",
            text_bottom="Leistungsausweis",
            x_range=PercentageRange(0.5, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nummer",
            text_bottom="Versicherten-Nr.",
            text_left="Geburtsdatum",
            text_right="Grundlohn PK",
        ),
        use_ahv_new=True,
        document_date_label="Austrittsabrechnung per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Pensionskasse Thurgau",
            "www.pktg.ch",
            "Beiträge und Gutschriften",
            "Invalidenrente gemäss",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "PKTG Two",
        [
            "www.pktg.ch",
            "Pensionskasse Thurgau",
            "Eingebrachte Freizügigkeitsleistungen",
            "Stand Freizügigkeit bei Heirat",
        ],
    ),
    GenericPensionCertificatePageOnePageParser(
        "Post",
        ["Vorsorgeausweis per"],
        "Vorsorgeausweis per",
        [
            "Pensionskasse Post",
            "Basisdaten",
            "Altersguthaben",
            "Möglicher Vorbezug für Wohneigentum gemäss",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Pensionskasse Post Two",
        ["Vorsorgeleistungen", "Pensionskasse Post", "Risikoleistungen"],
    ),
    GenericPensionCertificatePageThreePageParser(
        "Pensionskasse Post Three",
        ["Stand Zusatz-Sparkonti inkl. Zins", "Pensionskasse Post", "Bemerkung"],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Prevanto Two",  # Axpo, only page 2, page 1 is common
        [
            "Monatlicher Beitrag Arbeitnehmer",
            "Austrittsleistung per Stichtag",
            "Weitere Informationen",
            "Vertretung des Arbeitgebers",
            "Erstellt durch Prevanto",
        ],
    ),
    PrevisPensionCertificatePageOnePageParser(),
    PrevisPensionCertificatePageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="Pro Medico",
        titles=["Vorsorgeausweis"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vorsorgeausweis",
            text_left="Versicherte Person",
            text_bottom="Geburtsdatum",
            text_right="Versicherungsbeginn",
            converter=CleanNameConverter(),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherte Person",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
            text_right="Versicherungsbeginn",
        ),
        use_ahv_new=True,
        document_date_label="gültig am:",
        required_tokens=[
            "VERBANDSVORSORGE DER 2. SÄULE",
            "Erreichen Pensionsalter",
            "Massgebender Jahreslohn",
            "Vorsorgeleistungen",
            "www.promedico.ch",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Promea",
        titles=["Vorsorgeausweis"],
        se_fullname=create_labeled_field_vertical(
            "VERTRAULICH", offset_right=15, offset_bottom=3
        ).with_converter(CleanNameConverter(max_num_lines_valid=2)),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherte Person",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
            text_right="Versicherungsbeginn",
        ),
        use_ahv_new=True,
        document_date_label="Gültig ab:",
        required_tokens=[
            "PROMEA Pensionskasse",
            "www.promea.ch",
            "Berechnungsgrundlagen",
            "Leistungen bei Tod",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Promea Page Two",
        [
            "Promea",
            "Ergänzende Bestmmungen",
            "zum aktuellen Reglement",
            "Voraussichtliche Rentenumwandlungssätze",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        desc="New version with date 2021 (old version as separate class)",
        company="Publica",
        titles=["Vorsorgeausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="3007 Bern",
            x_range=PercentageRange(0.5, 1),
            text_bottom="Vorsorgeausweis per",
        ),
        se_date_of_birth=SearchElementLabeledField(
            None, None, label="Geburtsdatum", converter=DateConverter()
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        required_tokens=[
            "Pensionskasse des Bundes PUBLICA",
            "Persönliche Daten",
            "publica.ch",
            "3007 Bern",
        ],
    ),
    # PublicaPensionCertificatePageOnePageParserOld(),
    # PublicaPensionCertificatePageTwoPageParser(),
    GenericPensionCertificatePageOnePageParser(
        "Revor",
        ["Persönlicher Vorsorgeausweis"],
        "Stand am",
        [
            "REVOR",
            "Sammelstiftung",
            "Prämienbefreiungsgrad",
            "Altersleistungen",
            "Vorhandenes Altersguthaben",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Revor Page Two",
        [
            "REVOR",
            "Sammelstiftung",
            "Gesetzliche Angaben",
            "Mitglieder der paritätischen ",
            "Total Vorbezug für Wohneigentum",
            "Total Verpfandung für Wohneigentum",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Panvica",
        titles=["Vorsorgeausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="www.panvica.ch",
            text_bottom="Vorsorgeausweis",
            x_range=PercentageRange(0.5, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherte Person",
            text_bottom="Versicherungsbeginn",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Kontakt",
            text_bottom="Vorsorgeausweis",
            text_left="Datum",
            converter=MostRecentDateConverter(),
        ),
        required_tokens=[
            "www.panvica.ch",
            "Vorsorgeausweis",
            "BVG-Vorsorge",
            "Leistungen im Todesfall",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
        company="Panvica",
        titles=["Akontobeiträge für Selbständigerwerbende"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="www.panvica.ch",
            text_bottom="Vorsorgeausweis",
            x_range=PercentageRange(0.5, 1),
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Kontakt",
            text_bottom="Akontobeiträge",
            text_left="Datum",
            converter=MostRecentDateConverter(),
        ),
        required_tokens=["www.panvica.ch", "Persönlicher Beitrag SE"],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Pax",
        titles=["Vorsorgeausweis per"],
        se_fullname=SearchElementLabeledField(
            FIELD_FULLNAME.name, None, label="VersichertePerson"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Geburtsdatum",
            text_bottom="AHV-Nr",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        required_tokens=[
            "Pax",
            "Aeschenplatz 13, 4002 Basel",
            "Vorsorgeausweis per",
            "Versicherte Person",
            "Arbeitgeber",
            "Arbeitnehmerbeitrag",
            "Risikobeitrag",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Pax Two",
        [
            "Im Auftrag der PAX Sammelstiftung",
            "Leistungen",
            "Bei ordentlicher Pensionierung",
            "Informationen über Zinssätze",
            "Maximal für Wohneigentum zur Verfügung stehender Betrag",
        ],
    ),
    GenericPensionCertificatePageThreePageParser(
        "Pax Three",
        [
            "Hinweise",
            "Gesetzliche und reglementarische",
            "Vorsorge-",
            "Projizierung/Projizierte",
            "sind erst wieder möglich",
            "Im Auftrag der Pax Sammelstiftung",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CREDIT_NOTE,
        page_cat=PageCat.GENERIC_PAGE,
        company="PKBS",
        titles=["Eingang einer Austrittsleistung"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_bottom="Personal-Nr.", x_range=PercentageRange(0, 0.5)
        ),
        use_ahv_new=False,
        document_date_label="Datum: ",
        required_tokens=[
            "Pensionskasse Basel-Stadt",
            "@pkbs.ch",
            "Wir haben folgende Austrittsleistung zu Ihren Gunsten",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_PAGE,
        company="PKG",
        titles=["Detailinformationen"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Detailinformationen",
            text_bottom="Geb.-Datum",
            text_left="Vorname Name",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vorname Name",
            text_bottom="Gemeldeter Jahreslohn",
            text_left="Geb-Datum",
        ),
        use_ahv_new=False,
        document_date_label="per",
        required_tokens=[
            "PKG",
            "Die Pensionskasse für KMU",
            "Bei einem allfälligen Einkauf",
            "verändern sich die",
            "bisher",
            "nachher",
            "Diese Berechnungen sind grundsätzlich unverbindlich",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Pensionskasse Hirslanden Two",
        [
            "Pensionskasse Hirslanden",
            "Theoretisches Altersguthaben",
            "Finanzierung der Leistungen",
            "Einkaufs- und Vorbezugmöglichkeit",
            "Dieser Versicherungsausweis ersetzt alle bisherigen",
        ],
    ),
    # Contains just an address
    GenericPensionCertificatePageTwoPageParser(
        "PK Landi Two",
        [
            "PENSIONSKASSE LANDI REGION ZENTRALSCHWEIZ",
            "Arbeitgeber: Landi",
            "Name Lebenspartner",
            "Dieser Vorsorgeausweis hat informativen",
        ],
    ),
    # Contains just an address
    GenericPensionCertificatePageTwoPageParser(
        "PK Kirche Two",
        [
            "Pensionskasse römisch-katholische Landeskirche",
            "Sei",
            "te 2",
            "von 2",
            "Abendweg 1",
            "Luzern",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="PK SBV",
        titles=["AUSWEIS ÜBER DIE VORSORGE-LEISTUNGEN PER"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Allgemeine Angaben",
            text_bottom="Geburtsdatum",
            text_left="Name, Vorname",
            text_right="Arbeitgeber",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name, Vorname",
            text_bottom="Versicherten Nr.",
            text_left="Geburtsdatum",
            text_right="Arbeitgeber",
        ),
        use_ahv_new=True,
        document_date_label="AUSWEIS ÜBER DIE VORSORGE-LEISTUNGEN PER",
        required_tokens=[
            "Pensionskasse SBV",
            "Lohndaten/Kapitaldaten",
            "Total Jahresbeiträge",
            "Arbeitgeber",
            "Bereits bezogenes Kapital für Wohneigentum",
            "Bemerkungen",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Perkos",
        titles=["Ausweis über die Vorsorgeleistungen per"],
        document_date_label="Vorsorgeleistungen per",
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_left="Versicherte Person",
            text_top="PERKOS",
            text_bottom="Geburtsdatum",
            x_range=PercentageRange(0.3, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherte Person",
            text_left="Geburtsdatum",
            text_bottom="AHV-Nummber",
        ),
        use_ahv_new=True,
        required_tokens=[
            "Pensionskasse",
            "PERKOS",
            "Versicherte Person",
            "Versicherte Leistungen",
            "Diverse Informationen",
            "www.perkos.ch",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Perspectiva",
        titles=["Persönliches Orientierungsblatt per"],
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönliches Orientierungsblatt",
            text_bottom="Personendaten",
            converter=MostRecentDateConverter(),
        ),
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            x_range=PercentageRange(0.4, 1),
            text_top="Kategorie",
            text_bottom="Geburtsdatum",
            converter=CleanNameConverter(),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherte Person",
            text_left="Geburtsdatum",
            text_bottom="Zivilstand",
        ),
        use_ahv_new=False,
        required_tokens=[
            "Perspectiva Sammelstiftung",
            "berufliche Vorsorge",
            "Personendaten",
            "Hochgerechnetes Altersguthaben",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Perspectiva Two",
        [
            "Zusatzinformationen",
            "Perspectiva Sammelstiftung",
            "Verzinszung Altersguthaben",
            "Dieses Orientierungsblatt gilt nicht als Deckungszusage",
            "Bei Unterdeckung können",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "PKSG Two",
        [
            "Leistungen in CHF im Alter",
            "Voraussichtliche Altersleistung",
            "Leistungen für Hinterbliebene",
            "Maximal möglicher Vorbezug für Wohneigentumsförderung",
            "Pensionskasse St. Galler Gemeinden",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="PK Pro",
        titles=["Vorsorgeausweis"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personalien",
            text_bottom="Lohndaten",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Eintritt in die Stiftung",
            text_bottom="Beschäftigungsgrad",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Gültig ab:",
        required_tokens=[
            "pensionskasse pro",
            "<EMAIL>",
            "Vorsorgeausweis",
            "Personalien",
            "Leistungen im Alter",
            "Leistungen im Todesfall",
            "Jährliche Waisenrente",
        ],
    ),
    # Second version e.g. for Silhoutte
    SmartPensionCertificatePageOnePageParser(
        company="PK Pro",
        se_product=SearchElementLabeledField(None, None, label="Kategorie:"),
        titles=["Vorsorgeausweis"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personalien",
            text_bottom="Lohndaten",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Eintritt in die Stiftung",
            text_bottom="Beschäftigungsgrad",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Gültig ab:",
        required_tokens=[
            "pensionskasse pro",
            "managed by Tellco",
            "pkpro.ch",
            "Vorsorgeausweis",
            "Personalien",
            "Leistungen im Alter",
            "Leistungen im Todesfall",
            "Jährliche Waisenrente",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "pensionskasse pro Two",
        [
            "pensionskasse pro",
            "Vorsorgeausweis",
            "Wohneigentumsförderung",
            "Altersguthaben",
            "in Prozent des gemeldeten Jahreslohns",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="PKSO",
        titles=["Vorschlag zum Bezug von Mitteln aus der beruflichen Vorsorge (WEFV)"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_left="@pk.so.ch", text_bottom="WEFV"
        ),
        se_document_date=SearchElementConstrainedArea(None, None, text_bottom="WEFV"),
        required_tokens=[
            "Pensionskasse Kanton Solothurn",
            "selbstgenutztem Wohneigentum",
            "Sofern Sie an einem Vorbezug interessiert sind",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="PKSV",
        required_tokens=[
            "ERLÄUTERUNGEN ZUM VORSORGEAUSWEIS",
            "Der anrechenbare AHV-Jahreslohn entspricht dem voraussichtlichen",
            "Unter www.pksv.ch sowie über",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Providus Two",
        [
            "Versicherungsausweis per",
            "pro Jahr",
            "pro Monat",
            "und Nachkäufe im laufenden Jahr",
            "Reglements. Sie bietet keine Gewähr, dass",
            "Eingebrachte Freizügigkeitsleistungen und Einlagen",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="PROVIT",
        titles=["LEISTUNGSAUSWEIS PROVIT"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="LEISTUNGSAUSWEIS PROVIT",
            text_bottom="Sparkapital per",
            text_left="Für:",
            x_range=PercentageRange(0, 0.6),
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None, None, text_top="Wird vollumfänglich durch den Arbeitgeber finanziert"
        ),
        required_tokens=[
            "PROVIT-Personalvorsorgestiftung",
            "Sparkapital per",
            "Versicherter Lohn",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="PTV",
        titles=["VERSICHERTENAUSWEIS per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönlich/Vertraulich",
            text_bottom="VERSICHERTENAUSWEIS per",
            x_range=PercentageRange(0.5, 1),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name, Vorname",
            text_bottom="AHV-Nr.",
            text_left="Geburtsdatum",
            text_right="Beschäftigungsgrad",
        ),
        use_ahv_new=True,
        document_date_label="VERSICHERTENAUSWEIS per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "ptv cpat",
            "VERSICHERTENAUSWEIS per",
            "Eintritt in die Pensionskasse",
            "Jahresbeiträge",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "PTV Two",
        [
            "VERSICHERTENAUSWEIS per",
            "Zusätzliche Informationen",
            "Einkaufssumme per Stichtag Ausweis",
            "www.ptv.ch",
        ],
    ),
    RochePensionCertificatePageOnePageParser(),
    RochePensionCertificatePageTwoPageParser(),
    SbbPensionCertificatePageOnePageParser(),
    SbbPensionCertificatePageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="Schindler",
        titles=["Leistungsausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personaldaten",
            text_bottom="Leistungsausweis",
            x_range=PercentageRange(0, 0.5),
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Leistungsausweis",
            text_bottom="Altersleistungen",
            x_range=PercentageRange(0, 0.5),
            converter=DateConverter(),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personal-Nr.",
            text_bottom="Eintritt in PK",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        required_tokens=[
            "Schindler Pensionskasse",
            "Altersleistungen",
            "Altersguthaben",
            "Zusätzliche Informationen",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="SEV",
        titles=["Vorsorgeausweis"],
        document_date_label="Vorsorgeausweis per",
        se_fullname=SearchElementConstrainedArea(
            None, None, text_left="Pensionskasse SEV", text_bottom="Steinerstrasse"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Beschäftigungsgrad",
            text_left="Geburtsdatum",
            text_bottom="Stand des Altersguthabens",
            text_right="AHV-Lohn",
        ),
        use_ahv_new=True,
        required_tokens=[
            "Pensionskasse",
            "SEV",
            "Vorsorgeausweis",
            "Versicherte Leistungen per",
            "Voraussichtl. Altersleistungen",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="PKZH",
        titles=["Austrittsabrechnung"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="www.pkzh.ch", text_bottom="Austrittsabrechnung"
        ),
        se_document_date=SearchElementConstrainedArea(
            None, None, text_top="www.pkzh.ch", text_bottom="Austrittsabrechnung"
        ),
        use_ahv_new=True,
        required_tokens=[
            "www.pkzh.ch",
            "Stadt Zürich",
            "Anspruch bei Austritt",
            "Mindestbetrag gemäss FZG",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Siemens Two",
        [
            "Pensionskasse",
            "Siemens-Gesellschaften in der Schweiz",
            "Einkaufspotential / max. möglicher WEF-Bezug",
            "Voraussichtliche Leistung bei Invalidität",
        ],
    ),
    SpsJelmoliPensionCertificatePageOnePageParser(),
    SulzerPensionCertificateSinglePagePageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="Swissbroke",
        titles=["Leistungsausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="Rheinfelsstr", text_bottom="Versicherten-Nr"
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Chur",
            text_left="Stichtag der Berechnung",
            text_bottom="Arbeitgeber",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Zivilstand",
            text_left="Geburtstag",
            text_bottom="Beschäftigungsgrad",
            text_right="Sozialvers.-Nr.",
        ),
        use_ahv_new=True,
        required_tokens=[
            "swissbroke vorsorgestiftung",
            "Leistungsausweis",
            "Altersguthaben",
            "Aktuelles Altersguthaben",
        ],
    ),
    # Swisscanto UPC
    SmartPensionCertificatePageOnePageParser(
        company="Swisscanto",
        product="UPC",
        titles=["Persönlicher Ausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Swisscanto Vorsorge AG",
            text_bottom="Persönlicher Ausweis",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nr.",
            text_bottom="Zivilstand",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherter Jahreslohn",
            text_bottom="Arbeitgeber",
            text_left="per",
            converter=MostRecentDateConverter(),
        ),
        required_tokens=[
            "Swisscanto Vorsorge AG",
            "Jährlicher Gesamtbeitrag",
            "Pensionierungsdatum",
            "Möglicher Vorbezug für Wohneigentum",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Swisscanto Two",
        [
            "Jährliche Invaliden-Leistungen",
            "Weitere Vorsorgeinformationen",
            "finden Sie unter www.projektionszins.ch",
            "www.swisscanto-stiftungen.ch",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Swisscanto Two V2",
        [
            "Seite 2 von 2",
            "Jährliche Invaliden-Leistungen",
            "Weitere Vorsorgeinformationen",
            "finden Sie unter www.projektionszins.ch",
            "www.swisscanto-sammelstiftung.ch",
        ],
    ),
    GenericPensionCertificatePageThreePageParser(
        "Swisscanto Three",
        [
            "Bemerkungen",
            "Seite 3 von 3",
            "finden Sie unter www.projektionszins.ch",
            "www.swisscanto-sammelstiftung.ch",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
        company="Panvica",
        titles=["Akontobeiträge für Selbständigerwerbende"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="www.panvica.ch",
            text_bottom="Vorsorgeausweis",
            x_range=PercentageRange(0.5, 1),
        ),
        use_ahv_new=False,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Kontakt",
            text_bottom="Akontobeiträge",
            text_left="Datum",
            converter=MostRecentDateConverter(),
        ),
        required_tokens=["www.panvica.ch", "Persönlicher Beitrag SE"],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_ONE,
        company="Suva",
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="Versicherungsausweis per", text_bottom="Personalien"
        ),
        ranked_titles_all=[RankedTitle("Versicherungsausweis per", 2)],
        required_tokens=[
            "Stiftung Vorsorge-Einrichtung der Suva",
            "Fondation pour l'institution de Prévoyance de la Suva",
            "Personalien",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Suva Two",
        [
            "Seite 2",
            "Einkauf fehlender Versicherungsjahre",
            "Verfügbarer Betrag für Wohneigentums",
            "suva.vorsorgeeinrichtung",
            "@suva.ch",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="Swisscanto",
        required_tokens=[
            "Swisscanto Sammelstiftung",
            "Hiermit informieren wir Sie als versicherte Persion der Swisscanto Sammelstifung",
            "Reglement",
        ],
    ),
    SmartLetterPageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="Swisscanto",
        required_tokens=[
            "www.swisscanto-stiftungen.ch",
            "Basisvorsorge",
            "Zusatzvorsorge",
            "Allgemeine Reglementsbestimmungen",
        ],
    ),
    SwissLifePensionCertificateLetterPageParser(),
    SwissLifePensionCertificatePageOnePageParser(),
    # SwissLifePensionCertificatePageTwoPageParser(),
    # Alternative version with slightly different layout, found e.g. in 2016
    SwissLifePensionCertificateV2PageOnePageParser(),
    SwissLifePensionCertificateV2PageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="SwissLife",
        product="Zusatzvorsorge",
        titles=["Persönlicher Vorsorgeausweis"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönlich",
            text_bottom="Persönlicher Vorsorgeausweis",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherten-Nr.",
            text_bottom="Beschäftigungsgrad",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="gültig ab",
        required_tokens=[
            "Sammelstiftung Zusatzvorsorge Swiss Life",
            "www.swisslife.ch/protectzusatz",
            "Vorhandenes Altersguthaben",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="SwissLife",
        titles=["Zahlungsauftrag"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Zahlungsauftrag",
            text_left="Name/Vorname",
            text_bottom="Versicherten-Nr.",
        ),
        use_ahv_new=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            y_range=PercentageRange(0.2, 0.5),
            converter=MostRecentDateConverter(),
        ),
        required_tokens=[
            "Swiss Life AG",
            "Verwendung meiner Freizügigkeitsleistung per",
            "Ich wünsche",
            "Eine Barauszahlung kann nur erfolgen",
        ],
    ),
    GenericPensionCertificatePageOnePageParser(
        "SwissLife BVG Austrittsabrechnung",
        ["Abrechnung - Erstellungsgrund: Austritt per"],
        None,
        [
            "BVG-Sammelstiftung Swiss Life",
            "Reglementarische Freizügigkeitsleistung per Austrittsdatum",
            "Altersguthaben",
        ],
    ),
    GenericPensionCertificatePageOnePageParser(
        "SwissLife Zusatz Austrittsabrechnung",
        ["Abrechnung - Erstellungsgrund: Austritt per"],
        None,
        [
            "Sammelstiftung Zusatzvorsorge Swiss Life",
            "Reglementarische Freizügigkeitsleistung per Austrittsdatum",
            "Altersguthaben",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="SwissRe",
        titles=["Versicherungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_right="Swiss Re", text_bottom="Ihre Referenz"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherungsausweis per",
            text_bottom="Zivilstand",
            text_left="Ihr Geburtsdatum",
        ),
        use_ahv_new=False,
        document_date_label="Versicherungsausweis per",
        required_tokens=[
            "Pensionskasse",
            "Swiss Re",
            "Versicherungsausweis per",
            "Versicherter Lohn",
            "Altersgutschrift",
            "Risikobeitrag Arbeitgeber",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="SwissRe",
        product="Kapitalplan",
        titles=["Kontoauszug Kapitalplan per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_right="www.pensionskasse-swissre.ch",
            text_bottom="Kontoauszug",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Ihre Versicherten-Nummer",
            text_bottom="Eintritt in Pensionskasse",
            text_left="Ihr Geburtsdatum",
            text_right="Zivilstand",
        ),
        use_ahv_new=False,
        document_date_label="Kontoauszug Kapitalplan per",
        required_tokens=[
            "Pensionskasse",
            "Swiss Re",
            "Pensionierungsdatum",
            "Kapitalwert",
            "Bewertungsdatum",
        ],
    ),
    # No page 1 yet
    SyngentaPensionCertificatePageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        desc="Tellco PK Info 1/2 (includes PK page 1 text)",
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="Tellco",
        ranked_titles_all=[
            RankedTitle(title="Ihr Tellco pkPRO Vorsorgeausweis", rank=4),
            RankedTitle(
                title="Damit Sie bestens über die Beiträge und Leistungen Ihrer Pensionskasse",
                rank=4,
            ),
        ],
        use_ahv_new=False,
        required_tokens=["Jährliche Invalidenrente"],
        hamming_dist=4,
    ),
    SmartPensionCertificatePageOnePageParser(
        desc="Tellco PK Info 2/2 (includes PK page 2 text)",
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
        company="Tellco",
        ranked_titles_all=[
            RankedTitle(title="Wussten Sie schon?", rank=2),
            RankedTitle(title="Maximal möglicher Vorbezug", rank=99),
        ],
        use_ahv_new=False,
        required_tokens=["www.tellco.ch/de/Blog", "Wissenswertes rund um"],
        hamming_dist=4,
    ),
    # SmartPensionCertificatePageOnePageParser(
    #     desc='Tellco PK #2',
    #     page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
    #     company="Tellco",
    #     titles=['Vorsorgeausweis'],
    #     use_ahv_new=False,
    #     required_tokens=['<EMAIL>', 'Steuerabzugsfähige Einlage', 'in die Vorsorge', 'dient einzig Ihrer Information', 'Bahnhofstrasse 4', 'Postfach'],
    #     hamming_dist=4
    # ),
    SmartPensionCertificatePageOnePageParser(
        company="Transgourmet",
        titles=["Vorsorgeausweis gültig ab"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vorsorgeausweis gültig ab",
            text_bottom="1. Grundlagen",
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="gültig ab",
            text_left="gültig ab",
            text_bottom="1. Grundlagen",
        ),
        required_tokens=[
            "Pensionskasse Transgourmet Schweiz AG",
            "Grundlagen",
            "Altersleistung",
            "Vorhandenes Altersguthaben",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Transgourmet Page Two",
        [
            "Zusäzliche Informationen",
            "Ausweis Sparkapital",
            "Wir bitten Sie, den Vorsorgeausweis auf seine Richtigkeit",
            "Dieser Ausweis ersetzt alle bisherigen",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Trianon",
        titles=["Leistungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="PERSÖNLICH & VERTRAULICH",
            text_bottom="1. Lohn",
            text_left="für den Leistungsausweis",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-Nr.",
            text_bottom="Geschlecht",
            text_left="Geburtsdatum",
            text_right="PERSÖNLICH & VERTRAULICH",
        ),
        use_ahv_new=True,
        document_date_label="Leistungsausweis per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Sammelstiftung Tria",
            "Ch. de la Rueyre",
            "Leistungsausweis per",
            "Individuelle Angaben",
            "Unternehmen",
            "Austrittsleistung",
            "Todesfall vor Pensionierung",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Trianon Two",
        [
            "1) Diese Beiträge decken die Risiken für Todesfall und Invalidität",
            "2) Aufgrund eines Gesundheitsvorbehalts",
            "3) Leistungen im Fall von Krankheit",
            "Trianon SA (Administrativer Verwalter der Stiftung)",
        ],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="UBS",
        titles=["Simulation Altersleistungen"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Personal-Nr.",
            text_left="Name, Vorname(n)",
            text_bottom="Geburtsdatum",
            converter=CleanNameConverter(),
        ),
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_left="Daten per",
            text_top="Pensionierungsdatum für die Simulation",
            text_bottom="Simulierte Beitragsvariante",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name, Vorname",
            text_bottom="Geschlecht",
            text_left="Geburtsdatum",
        ),
        required_tokens=[
            "UBS",
            "Simulation Altersleistungen",
            "bei voller Alterspensionierung",
            "Simulierte Entwicklung AHV-Altersrente",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Valora",
        titles=["Leistungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None, None, text_top="www.valora.com", text_bottom="Leistungsausweis"
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Zivilstand",
            text_bottom="Grunddaten",
            text_left="Geburtsdatum",
            text_right="AHV-Nummer",
        ),
        use_ahv_new=True,
        document_date_label="Leistungsausweis per",
        # Name is Trianon SA but might be cut off
        required_tokens=[
            "Valora Pensionskasse",
            "www.valora.com",
            "Grunddaten",
            "Gemeldeter Jahreslohn",
        ],
    ),
    # VSAO version employed
    SmartPensionCertificatePageOnePageParser(
        company="VSAO",
        titles=["Versicherungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="",
            text_bottom="Versicherungsausweis per",
            text_left="ASMAC",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-ID",
            text_bottom="Eintrittsdatum",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Versicherungsausweis per",
        required_tokens=[
            "Vorsorgestiftung VSAO",
            "AHV-Jahreslohn",
            "Vorhandenes Alterssparkapital",
        ],
    ),
    # VSAO version employed
    GenericPensionCertificatePageTwoPageParser(
        "VSAO Two Alternative",
        [
            "Wohneigentumsförderung",
            "Vorsorgestiftung VSAO",
            "Hinterlassenenleistungen",
            "Dieser Versicherungsausweis ersetzt alle früheren Ausweise.",
        ],
    ),
    # VSAO version self-employed
    SmartPensionCertificatePageOnePageParser(
        company="VSAO",
        titles=["Versicherungsausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherungsausweis per",
            text_bottom="Versicherte Person",
            text_left="www",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="AHV-ID",
            text_bottom="Eintrittsdatum",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Versicherungsausweis per",
        required_tokens=[
            "VSAO Stiftung für Selbständigerwerbende",
            "AHV-Bruttolohn",
            "Koordinationsabzug",
            "Altersguthaben",
            "Altersrente",
            "Vorsorgeguthaben",
        ],
    ),
    # VSAO version self-employed page 2
    GenericPensionCertificatePageOnePageParser(
        "VSAO",
        ["Versicherungsausweis per"],
        None,
        [
            "VSAO Stiftung für Selbständigerwerbende",
            "Vorsorgestiftung VSAO",
            "AHV-Bruttolohn",
            "Altersguthaben",
            "Altersleistungen",
            "Anrechenbarer Jahreslohn",
            "Vorsorgeguthaben",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "VSAO Two",
        [
            "Versicherungsausweis per",
            "Vertrags-Nr.",
            "Versicherte Person",
            "Summe der Vorbezüge abzüglich der getätigten Rückzahlungen",
            "Projizierte Altersleistungen - Vorsorgeplan",
            "Gesetzliche Freizügigkeitsleistung gemäss Art. 17 FZG",
            "Leistungsgrundlage bilden der Vorsorgeplan",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "VSAO Two Alternative 2",
        [
            "Sparbeitrag Arbeitnehmer",
            "Risikobeitrag Arbeitgeber",
            "Maximal mögliche persönliche Einlage",
            "www.vorsorgestiftung-vsao.ch",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="VSM",
        titles=["Vorsorgeausweis per"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Medizinalpersonen",
            text_left="Versicherte Person",
            text_right="info@",
            text_bottom="Versicherten-Nr.",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherten-Nr.",
            text_bottom="Zivilstand",
            text_left="Geb.-Datum",
            text_right="info@",
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        required_tokens=[
            "Sammelstiftung",
            "für Medizinalpersonen",
            "Eintritt bei der VSM",
            "Gemeldeter Jahreslohn",
            "Jährlicher Gesamtbeitrag",
        ],
    ),
    ZGPKPensionCertificateLetterPageParser(),
    ZGPKPensionCertificatePageOnePageParser(),
    ZGPKPensionCertificatePageTwoPageParser(),
    SmartPensionCertificatePageOnePageParser(
        company="VZ",
        titles=["Vorsorgeausweis per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherten-Nummer",
            text_bottom="Gemeldeter Jahreslohn",
            text_left="Persönliche",
            text_right="Versicherten-Nummer",
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Versicherungsbeginn",
            text_left="Geburtsdatum",
            text_bottom="Alter per Stichtag",
        ),
        use_ahv_new=True,
        document_date_label="Vorsorgeausweis per",
        required_tokens=[
            "VZ BVG Sammelstiftung",
            "Versicherten-Nummer",
            "Sparguthaben",
            "Pensionierten-Kinderrente",
            "Sparguthaben am",
            "Überobligatorium",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "VZ Two",
        [
            "Sämtliche Leistungen gelten vorbehaltlich der reglementarischen ",
            "Bestimmungen und der gesetzlichen Rahmenbedingungen.",
            "Arbeitnehmervertreter ",
            "Seite 2/2",
            "ausgestellt am",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        desc="wincoLink-pro Report #1/2",
        doc_cat=DocumentCat.PENSION_CERTIFICATE,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_ONE,
        company=None,
        ranked_titles_all=[
            RankedTitle("Versichertenauskunft"),
            RankedTitle("Vertrag"),
            RankedTitle("Versicherte Person"),
            RankedTitle("Leistungen"),
        ],
        required_tokens=["wincoLink-pro", "Erreichen Pensionsalter am"],
        use_ahv_new=True,
        se_company=SearchElementLabeledField(
            None,
            None,
            label="Arbeitgeber",
            converter=ParagraphConverter(max_num_lines=1, max_num_lines_valid=2),
        ),
        document_date_label="Daten gültig per",
        se_lastname=SearchElementLabeledField(None, None, label="Name"),
        se_firstname=SearchElementLabeledField(None, None, label="Vorname"),
        se_date_of_birth=SearchElementLabeledField(None, None, label="Geburtsdatum"),
    ),
    GenericPensionCertificatePageTwoPageParser(
        "wincoLink-pro #2/2",
        ["wincoLink-pro", "2 / 2", "Monatsbeitrag auf der Basis von"],
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="Würth-Gruppe",
        titles=["Austrittsabrechnung per"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Persönlich",
            text_bottom="Austrittsabrechnung",
            x_range=PercentageRange(0, 0.5),
        ),
        use_ahv_new=True,
        document_date_label="Austrittsabrechnung per",
        required_tokens=[
            "Vorsorgewerk",
            "Würth AG",
            "Berechnung gemäss folgender Vergleichsrechnung",
            "Freizügigkeitsanspruch",
            "dem grössten der drei Beträge",
        ],
    ),
    SmartPensionCertificatePageOnePageParser(
        company="Zurich",
        titles=["Vorsorgeausweis"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Stand am",
            text_left="Name/Vorname",
            text_bottom="Geburtsdatum",
            converter=CleanNameConverter(),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="Name/Vorname",
            text_bottom="Versicherten-Nr.",
            text_left="Geburtsdatum",
        ),
        use_ahv_new=True,
        document_date_label="Stand am",
        required_tokens=[
            "ZURICH",
            "Stand am",
            "Lohndaten",
            "Risikoleistungen",
            "Überschuss",
            "Verpfändung von Wohneigentumsförderung",
        ],
    ),
    # # Page one is separate file
    # ZurichPensionCertificatePageOnePageParser(),
    GenericPensionCertificatePageTwoPageParser(
        "Zurich Vita No-Plus Two",
        [
            "Risikoleistungen",
            "Austrittsleistung",
            "Maximal mögliche Einkaufssumme",
            "Verpfändung für Wohneigentumsförderung",
            "Kapitaloption eingereicht",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Zurich Vita Plus Two",
        [
            "Jährlicher Arbeitnehmerbeitrag",
            "Freizügigkeitsleistung",
            "Maximal mögliche Einkaufssumme",
            "Verpfändung für Wohneingentumsförderung",
            "Help Point BVG, Telefon Nr. 0800 80 80 80",
        ],
    ),
    GenericPensionCertificatePageTwoPageParser(
        "Zurich Vita Plus Two V2",
        [
            "Risikoleistungen",
            "Austrittsleistung",
            "Maximal mögliche Einkaufssumme",
            "Sammelstiftung Vita",
            "www.vita.ch",
        ],
    ),
    # Must be after all Page Two because similar content but less
    GenericPensionCertificatePageTwoPageParser(
        "Zurich Vita No-Plus Two V3",
        # Matching of page number must be exact, therefore short string
        [
            "Sei",
            "te 2",
            "Zur Sicherstellung der Leistungen im Todes- und Invaliditätsfall",
            "Zürich Lebensversicherungs",
            "Sammelstiftung Vita",
            "Help Point BVG",
        ],
    ),
    # Must be after all Page Two because similar content but less
    GenericPensionCertificatePageThreePageParser(
        "Zurich Vita No-Plus Three",
        # Matching of page number must be exact, therefore short string
        [
            "Sei",
            "te 3",
            "Zur Sicherstellung der Leistungen im Todes- und Invaliditätsfall",
            "Zürich Lebensversicherungs",
            "Sammelstiftung Vita",
            "Help Point BVG",
        ],
    ),
    TemplatePageParser(
        desc="BVK almost empty page 2/2",
        doc_cat=DocumentCat.PENSION_CERTIFICATE,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        max_num_chars_alpha=500,
        required_text_conditions=[
            FromStartTextCond(
                "Diese Vorsorgedaten wurden aufgrund der im Kundenportal myBVK",
                num_lines=15,
            )
        ],
        required_tokens=["Obstgartenstrasse 21", "www.bvk.ch"],
    ),
    TemplatePageParser(
        desc="Gemini Verpfändung Bestimmungen",
        doc_cat=DocumentCat.PENSION_CERTIFICATE_INFO,
        page_cat=PageCat.GENERIC_PAGE,
        ranked_titles_all=[
            RankedTitle(
                "Wichtige Bestimmungen im Zusammenhang mit einer Verpfändung",
                2,
                min_length_title=5,
            )
        ],
        required_tokens=["Gemini Sammelstiftung", "www.gemini.ch"],
    ),
    # Regular Certificates FR
    # AllianzPensionCertificatePageOneFRParser(),
    AllianzPensionCertificatePageTwoFRParser(),
    SicpaPensionCertificatePageOneFRParser(),
    CpevPensionCertificatePageOneFRParser(),
    CpevPensionCertificatePageTwoFRParser(),
    CommonPensionCertificatePageTwoFRParser(),
]

parsers_fr = []

parsers_en = []


parsers_it = []


def get_parsers_pension_certificate():
    return parsers_i18n + parsers_de + parsers_fr + parsers_en + parsers_it
