from dataclasses import dataclass

# There is a specification here: https://www.elohnausweis-ssk.ch/de/assets/documents/Benutzerhandbuch_eLA.pdf
# https://www.elohnausweis-ssk.ch/de/assets/documents/SalaryDeclarationElohnOnline.xsd
from typing import Dict

from hypodossier.core.domain.PageData import StandardPageData
from hypodossier.core.domain.SemanticField import (
    SemanticField,
    FieldTitles,
    ExtractionType,
    fields_as_dict,
)

FIELD_EMAIL_FROM = SemanticField(
    "email_from",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Absender", "Sender"),
    desc_de="E-Mail Absender",
)
FIELD_EMAIL_TO = SemanticField(
    "email_to",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Empfänger", "Recipient"),
    desc_de="E-Mail Empfänger",
)
FIELD_EMAIL_DATE = SemanticField(
    "email_date",
    extraction_type=ExtractionType.DATE,
    titles=FieldTitles("Versanddatum", "Sending Date"),
    desc_de="Datum (ohne Uhrzeit), an dem die E-Mail versendet wurde",
)
FIELD_EMAIL_SUBJECT = SemanticField(
    "email_subject",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Betreff", "Subject"),
    desc_de="Betreff / Titel der E-Mail",
)

FIELDS_EMAIL = fields_as_dict(
    [FIELD_EMAIL_FROM, FIELD_EMAIL_TO, FIELD_EMAIL_DATE, FIELD_EMAIL_SUBJECT]
)


@dataclass
class EmailCorrespondencePageData(StandardPageData):
    email_from: str = None
    email_to: str = None
    email_date: str = None
    email_subject: str = None

    def __str__(self):
        return f"Email[from: {self.email_from}, date: {self.email_date}]"

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        d = super().get_semantic_fields()
        return {**d, **FIELDS_EMAIL}
