import logging
import structlog

from rapidfuzz.distance.Levenshtein import distance

from a<PERSON>yplumber.converter.ValueConverter import ParagraphConverter
from hypodossier.util.basis_string_util import find_nth
from mortgageparser.util.string_utils import (
    format_page_numbers,
    extract_word_after,
    contains_string,
    calc_min_dist,
    substring_after,
    substring_before,
    substring_after_list,
    calc_min_dist_fuzzysearch,
    remove_accents_and_simplify,
    contains_at_least_one_string,
    remove_empty_lines,
)

logging.basicConfig(level=logging.DEBUG)


logger = structlog.getLogger(__name__)


def test_converter():
    s1 = "ABCDE\nFGHIJK LMNOP\nFGHIJK LMNOP 2342\n8001 Zürich"
    s2 = "      □"

    s_all = s1 + s2

    pc = ParagraphConverter(min_char_per_line=4, max_num_spaces_per_line=4)

    assert s1 == pc.convert(s_all)

    logger.info(f"s2={s2}")


def test_find_nth():
    s = "123 456 789 101112"

    assert -1 == find_nth(s, " ", 0)
    assert 3 == find_nth(s, " ", 1)
    assert 7 == find_nth(s, " ", 2)
    assert 11 == find_nth(s, " ", 3)
    assert -1 == find_nth(s, " ", 4)


def test_format_page_numbers():
    assert "ABC Seite 1/2 XYZ" == format_page_numbers("ABC Seite     1/2 XYZ")

    assert "ABC Seite 77/118 XYZ" == format_page_numbers("ABC Seite 77/118 XYZ")
    assert "ABC Seite 1/1 XYZ" == format_page_numbers("ABC Seite 1/1 XYZ")
    assert "ABC Seite 1/1 XYZ" == format_page_numbers("ABC Seite     1/1 XYZ")
    assert "ABC Seite 77/118 XYZ" == format_page_numbers(
        "ABC Seite     77   /   118 XYZ"
    )
    assert "ABC Pagina 77/118 XYZ" == format_page_numbers(
        "ABC Pagina     77   /   118 XYZ"
    )
    assert "ABC Page 77/118 XYZ" == format_page_numbers("ABC Page     77   /   118 XYZ")


def test_word_after():
    text = "asdf Kanton Zurich xyz"
    assert "Zurich" == extract_word_after(text, "Kanton")


def test_distance():
    print(calc_min_dist("oder Veriiustscheine", "Verlustscheine"))
    print(contains_string("abcde fghij", "abcde"))
    print(calc_min_dist("abcde fghij", "addddddddd"))

    print(calc_min_dist("abcdefg", "cdef"))

    print(substring_after("a\nb\nc\nd", "\n", False, 0))


def test_substring_after():
    text = "abcdefghijklm"

    assert substring_after(text, "kl", False, 0) == "m"
    assert substring_after(text, "kl", True, 0) == "klm"

    assert substring_after(text + text, "kl", False, 0) == "m" + text

    s = substring_after(text, "x", False, 0, return_haystack_if_not_found=False)
    assert s is None


def test_hamming():
    dist, sub, index = calc_min_dist("zzasdfjklö", "asdf")
    assert dist == 0
    assert sub == "asdf"
    assert index == 2


def test_hamming_whitespace_off():
    ret = calc_min_dist("zzasdfjklö", "as df", ignore_whitespace=False)
    logging.info(f"ret={ret}")
    assert ret[0] == 2  # distance is 2
    assert ret[1] == "asdfj"
    assert ret[2] == 2


def test_hamming_whitespace_on():
    ret = calc_min_dist("zzasdfjklö", "as df", ignore_whitespace=True)
    logging.info(f"ret={ret}")
    assert ret[0] == 0  # distance is 0 because whitespace is ignored
    assert ret[1] == "asdf"
    assert ret[2] == 2


def test_contains_string_simple():
    text = """abcdefghijklmnop"""
    needle = "abcdefg h"
    assert contains_string(text, needle)
    s = substring_after(text, needle).strip()
    assert len(s) > 0
    logging.info(f"s={s}")


def test_contains_string_advanced():
    text = """
        8050 Zürich
        Nousattestonsque,sauferreurouomission,lesactesdepoursuitesénumérésci-aprèssontenregistrésauprèsdel’Officedespoursuites
        de Genève au nom de Olivier"""
    needle = "Nous attestons que, sauf erreur ou omission, les actes de poursuites énumérés ci-après sont enregistrés auprès de l’Office des poursuites"
    assert contains_string(text, needle)
    s = substring_after(
        text, needle, ignore_whitespace_for_hamming=False, p_hamming_dist=20
    )
    assert s == "au nom de Olivier"


def test_contains_string_with_accents():
    text1 = "blablaSVA Zurichasdfasdf"
    text2 = "SVA Zurich jojojo"
    text3 = "SVA Zuich jojojo"
    text4 = "SVA Zulch jojojo"

    needle = "SVA Zürich"
    assert contains_string(text1, needle)
    assert contains_string(text2, needle)

    # has missing letter but should be ok because of hamming distance 1
    assert contains_string(text3, needle)

    # Hamming distance 2 is too large, should not match
    assert not contains_string(text4, needle)


def test_substring_before():
    assert (
        substring_before(
            "xyzabcdefgh",
            "b cd",
            ignore_whitespace_for_hamming=False,
            return_haystack_if_not_found=False,
        )
        is None
    )
    assert (
        substring_before(
            "abcdefgh",
            "b cd",
            ignore_whitespace_for_hamming=True,
            return_haystack_if_not_found=False,
        )
        == "a"
    )


def test_substring_after_list():
    text = "abcdefghijklm"

    assert substring_after_list(text, ["b", "f", "kl"], False, 0) == "m"
    assert substring_after_list(text, ["b", "f", "kl"], True, 0) == "klm"

    assert substring_after_list(text + text, ["b", "f", "kl"], False, 0) == "m" + text

    assert substring_after_list(text + text, ["b", "x", "kl"], False, 0) == ""
    assert substring_after_list(text + text, ["b", "f", "x"], False, 0) == ""


def test_simplify():
    assert remove_accents_and_simplify("äbc") == "abc"


def test_lev():
    assert distance("Haus", "Hauss") == 1
    assert distance("HAus", "Hauss") == 2

    a = "Nous attestons que, sauf erreur ou omission, les actes de poursuites énumérés ci-après sont enregistrés auprès de l’Office des poursuites"
    b = "Nousattestonsque,sauferreurouomission,lesactesdepoursuitesénumérésci-aprèssontenregistrésauprèsdel’Officedespoursuites de Genève au nom d"
    d = distance(a, b)
    logging.info(f"distance={d}")


def test_fuzzysearch():
    assert calc_min_dist_fuzzysearch("abcdefgh", "cde", max_distance=2) == (0, "cde", 2)


def test_fuzzysearch_2():
    a = "Nous attestons que, sauf erreur ou omission, les actes de poursuites énumérés ci-après sont enregistrés auprès de l’Office des poursuites"
    b = "asdfasdfNousattestonsque,sauferreurouomission,lesactesdepoursuitesénumérésci-aprèssontenregistrésauprèsdel’Officedespoursuites de Genève au nom d"
    x = calc_min_dist_fuzzysearch(b, a, max_distance=20)
    logging.info(f"x={x}")

    y = calc_min_dist_fuzzysearch(b, a, max_distance=20, ignore_whitespace=True)
    logging.info(f"y={y}")


def test_fuzzysearch_3():
    # informazionigiuridichesecondotart.46lefildebitaredev'essereescussoalsuosonoancheelencateleesecuzionisospeseequellechenondomiciliooaliasuasede.nonestatoverificalosc,ndperiodopossonoesserecontinuatepercheilterminediunannodell'articolo88lefescaduto.l'estrattoincludeinoitre!inumeroel'importodeterminante,lapersonasopracitalahaohaavutoiidomiciliototaledegllattestatidicarenzadibeniinseguitoapignoramentoolasedenel,cantoneticino.seildomicilioolasedesitrovanoo.nonancoraestinti.registraddurantegliultimi20anninelcantone.sitrovavano-tnxmaltrocantone,uo-testeattoseparatadev'essere.sqrod'aitrondemenzfonatel'aperturaela^chiusuradeifallimentirlchiestoah'ufflciod'esecuzionecompetente.iipresenteestrattocomunicadall'ufficionelcorsodegilultimicingueanni.nonsonoelencatutteleesecuzionipromossecontrolapersonasopracitatainveceelencatieventual!attestatidicarenzadibeniinseguitoanegliultimicinqueanninelcantoneticino,eccettoquellechesono:fallimento.stateritiratedalcreditoreoannuliateinseguitoadeclsionegiudiziale(art.8acpv.3lef).osservazionileinformazionldiquestodocumentosonoconfldenziali.costidiquestoestratto:chf17.00(secondoart.12aotlef)lugano.3006.2017ufficiodiesecuzlone1/1
    haystack = """gianlucaargentieri,viatesserete69,6942savosa,30.05.1980aldomicilio/airindirizzoindicatononfiguranonelregistreesecuzionioattestat!dlcarenzadlbenl."""
    needle = """al domicilio/all\'indirizzo indicato non figurano nel registro esecuzioni o attestati di carenza di beni"""
    # aldomicilio / airindirizzoindicatononfiguranonelregistreesecuzionioattestat!dlcaren

    logging.info(f"Dist={calc_min_dist_fuzzysearch(haystack, needle, 20, True)}")


def test_pagination():
    haystack = "rc: http://www.swissdec.ch/schema/                 Seite 2 / 2"
    success = contains_at_least_one_string(
        haystack, ["Seite 2/2"], hamming_dist=0
    )  # , 'Page 2/2', 'Pagina 2/2'
    assert success


def test_remove_empty_lines():
    text = "line1\n\n   \nline2"
    t2 = remove_empty_lines(text)
    assert t2 == "line1\nline2"
    t3 = remove_empty_lines(text, remove_lines_whitespace=False)
    assert t3 == "line1\n   \nline2"


def test_remove_empty_lines_2():
    text = "line1\nline2\n\n\nline3\nline4"
    t2 = remove_empty_lines(text)
    assert t2 == "line1\nline2\nline3\nline4"


def test_remove_empty_lines_big():
    text = """age                            2018
öBerufskosten 
Einzelperson/Ehemann/Pl


Kanton Nidwalden     PID-Nr. 66666         Name Heini                       Vorname Maxli
Während     Monaten wurde keine Erwerbstätigkeit ausgeübt.
Erwerbsausfallentschädigungen"""
    assert (
        remove_empty_lines(text)
        == """age                            2018
öBerufskosten 
Einzelperson/Ehemann/Pl
Kanton Nidwalden     PID-Nr. 66666         Name Heini                       Vorname Maxli
Während     Monaten wurde keine Erwerbstätigkeit ausgeübt.
Erwerbsausfallentschädigungen"""
    )
