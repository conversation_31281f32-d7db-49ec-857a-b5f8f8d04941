from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import SearchElementConstant
from hypodossier.core.documents.bank_account.BankAccountPageData import (
    FIELDS_BANK_ACCOUNT,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_COMPANY
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)
from mortgageparser.documents.parser.pageparsers.bank_account.bank_account_util import (
    BankAccountSearchElements,
)


@dataclass
class TemplateIrrevocablePromisesToPayPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.IRREVOCABLE_PROMISES_TO_PAY
    company: str = None

    se: BankAccountSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_BANK_ACCOUNT.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_irrevocable_promises_to_pay():
    parsers = [
        TemplateIrrevocablePromisesToPayPageParser(
            desc="ZKB Anmeldung Zahlungsversprechen #1/2 2021",
            # ranked_titles_all=[RankedTitle('Rahmenvertrag für Hypotheken')],
            # required_text_conditions=[
            #     FromStartTextCond("zwischen", num_lines=20),
            #     FromStartTextCond("Darlehensnehmer", num_lines=20),
            #     FromStartTextCond("Zürcher Kantonalbank (nachstehend Bank genannt)", num_lines=20),
            #
            # ],
            company="ZKB",
            required_tokens_any=[
                ["IBAN"],
                ["Sehr geehrte"],
                ["Zürcher"],
                [
                    "Auftrag für ein unwiderrufliches Zahlungsversprechen",
                    "Unwiderrufliches Zahlungsversprechen",
                ],
                [
                    "Wir verpflichten uns hiermit unwiderruflich, folgende Zahlung zu leisten"
                ],
            ],
            # se=MortgageFrameworkContractSearchElements(
            #     fullname=SearchElementConstrainedArea(None, None, text_top="zwischen", text_bottom="(nachstehend Darlehensnehmer genannt)", text_right=", geb.", text_right_offset_chars=1),
            #
            # )
        ),
        TemplateIrrevocablePromisesToPayPageParser(
            desc="ZKB Anmeldung Zahlungsversprechen #2/2 2021",
            company="ZKB",
            required_tokens=[
                "Diese Zahlungen erfolgen unter den Bedingungen, dass",
                "Dieses Zahlungsversprechen ist gültig bis",
                "Zürcher Kantonalbank",
            ],
            # se=MortgageFrameworkContractSearchElements(
            #     fullname=SearchElementConstrainedArea(None, None, text_top="zwischen", text_bottom="(nachstehend Darlehensnehmer genannt)", text_right=", geb.", text_right_offset_chars=1),
            #
            # )
        ),
    ]
    return parsers
