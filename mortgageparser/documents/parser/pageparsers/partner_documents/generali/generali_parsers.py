from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)

parsers_de = [
    SmartLetterPageParser(
        desc="Generali Antrag #1",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="Generali",
        se_lastname=SearchElementConstrainedArea(
            None,
            None,
            x_range=PercentageRange(0.3, 0.66),
            text_top="Anrede",
            text_bottom="Vorname(n)",
        ),
        se_firstname=SearchElementConstrainedArea(
            None,
            None,
            x_range=PercentageRange(0.3, 0.66),
            text_top="Name(n)",
            text_bottom="Strasse, Nr.",
        ),
        titles=["Hypothekarantrag"],
        required_tokens=[
            "Personalien Antragsteller",
            "Steuerlicher Wohnsitz(e)",
            "bei mehreren alle nennen",
            "Nationalität(en)",
            "(ledig, verheiratet, geschieden, einge",
            "Hausmann/Hausfrau, IV-Renten",
        ],
    ),
    SmartLetterPageParser(
        desc="Generali Antrag #2",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Generali",
        titles=["Präzisierung bezüglich finanziellen Verhältnisse"],
        required_tokens=[
            "Fortuna Investment AG",
            "Durchschn. Bonus der",
            "Auflistung der weiteren Verpflichtungen",
            "(Angaben des Gläubigers/Summe/Belastung",
            "derzeitigen Darlehensgebers",
        ],
    ),
    SmartLetterPageParser(
        desc="Generali Antrag #3",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Generali",
        titles=["Eigenmittel"],
        required_tokens=[
            "Fortuna Investment AG",
            "Policen-/3a-Konto Nr.",
            "Rückkaufswert/Kontostand",
            "Zusatzssicherheiten",
            "Kommentar",
        ],
    ),
    SmartLetterPageParser(
        desc="Generali Antrag #4",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Generali",
        titles=["Generali Schweiz"],
        required_tokens=[
            "Ermächtigung zur Zuteilung der Hypothek",
            "Selbstdeklaration US-Steuerpflicht",
        ],
    ),
    SmartLetterPageParser(
        desc="Generali Antrag #5",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Generali",
        titles=["Feststellung der wirtschaftlich berechtigten Personen"],
        required_tokens=[
            "Generali Schweiz resp. PKG ist gemäss Geldwäschereigesetz",
            "Herkunft des Geldes",
        ],
    ),
    SmartLetterPageParser(
        desc="Generali Antrag #6",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Generali",
        titles=["Bestätigung der Steuerkonformität"],
        required_tokens=[
            "Einwilligung zur Datenbearbeitung",
            "Datenschutzerklärung von Generali Schweiz, resp. PKB",
        ],
    ),
    SmartLetterPageParser(
        desc="Generali Antrag #7",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Generali",
        titles=["Einzureichende Dokumente"],
        required_tokens=["Unterschriften", "Per Email", "<EMAIL>"],
    ),
]


parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_generali():
    return parsers_de + parsers_en + parsers_fr + parsers_it
