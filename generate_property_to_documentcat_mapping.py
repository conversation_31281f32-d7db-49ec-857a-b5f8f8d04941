import ast
import csv
import os
from collections import defaultdict

MAPPING_FILE = "hypodossier/core/domain/PageDataMapping.py"
DOCUMENTS_ROOT = "hypodossier/core/documents"
OUTPUT_FILE = "property_to_documentcat_mapping.csv"

# 1. Parse PageDataMapping.py for DocumentCat -> class_name mapping
with open(MAPPING_FILE, "r") as f:
    tree = ast.parse(f.read(), filename=MAPPING_FILE)

doccat_to_class = {}
for node in ast.walk(tree):
    if (
        isinstance(node, ast.FunctionDef)
        and node.name == "create_doc_cat_pagedata_dict"
    ):
        for stmt in node.body:
            if isinstance(stmt, ast.Return):
                if isinstance(stmt.value, ast.Dict):
                    for key, value in zip(stmt.value.keys, stmt.value.values):
                        # Key: DocumentCat.XXX
                        if isinstance(key, ast.Attribute):
                            doccat = key.attr
                        else:
                            continue
                        # Value: ClassName()
                        if isinstance(value, ast.Call) and isinstance(
                            value.func, ast.Name
                        ):
                            class_name = value.func.id
                        elif isinstance(value, ast.Call) and isinstance(
                            value.func, ast.Attribute
                        ):
                            class_name = value.func.attr
                        else:
                            continue
                        doccat_to_class.setdefault(class_name, set()).add(doccat)

# 2. Find all class definitions in hypodossier/core/documents
class_to_fields = {}

for root, dirs, files in os.walk(DOCUMENTS_ROOT):
    for file in files:
        if file.endswith(".py"):
            path = os.path.join(root, file)
            with open(path, "r", encoding="utf-8") as f:
                try:
                    mod = ast.parse(f.read(), filename=path)
                except Exception:
                    continue
            for node in mod.body:
                if isinstance(node, ast.ClassDef):
                    class_name = node.name
                    # Only interested in dataclasses
                    is_dataclass = False
                    for deco in node.decorator_list:
                        if isinstance(deco, ast.Name) and deco.id == "dataclass":
                            is_dataclass = True
                        elif (
                            isinstance(deco, ast.Attribute) and deco.attr == "dataclass"
                        ):
                            is_dataclass = True
                    if not is_dataclass:
                        continue
                    # Get all assignments in the class body (fields)
                    fields = set()
                    for stmt in node.body:
                        if isinstance(stmt, ast.AnnAssign) and isinstance(
                            stmt.target, ast.Name
                        ):
                            fields.add(stmt.target.id)
                        elif isinstance(stmt, ast.Assign):
                            for t in stmt.targets:
                                if isinstance(t, ast.Name):
                                    fields.add(t.id)
                    if fields:
                        class_to_fields[class_name] = fields

# 3. Build property -> set(DocumentCat) mapping
property_to_doccats = defaultdict(set)
for class_name, doccats in doccat_to_class.items():
    fields = class_to_fields.get(class_name, set())
    for field in fields:
        for doccat in doccats:
            property_to_doccats[field].add(doccat)

# 4. Write to CSV
with open(OUTPUT_FILE, "w", newline="") as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(["property", "document_cats"])
    for prop, doccats in sorted(property_to_doccats.items()):
        writer.writerow([prop, ",".join(sorted(doccats))])

print(f"Wrote mapping of property to document_cats to {OUTPUT_FILE}")
