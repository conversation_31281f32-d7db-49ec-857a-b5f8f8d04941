from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog


logger = structlog.getLogger(__name__)

FOLDER_TYPE = "zlb"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/clientis/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_local_clients_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.skip(
    "Does not work because attached large xlsx ist not processed properly without office integration"
)
@pytest.mark.asyncio
async def test_250605_email_unpack_msg():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250605_email_unpack/msg",
        # source_file_filter=[""],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.skip(
    "Does not work because attached large xlsx ist not processed properly without office integration"
)
@pytest.mark.asyncio
async def test_250605_email_unpack_eml():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250605_email_unpack/eml",
        # source_file_filter=[""],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_250605_email_unpack_attachments_ok():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250605_email_unpack/attachments_ok",
        # source_file_filter=[""],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
