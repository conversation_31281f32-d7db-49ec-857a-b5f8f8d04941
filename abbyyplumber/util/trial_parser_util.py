import time
from pathlib import Path

import global_settings
from abbyyplumber.api import Page
from abbyyplumber.documentloader.DocumentLoader import (
    DocumentLoaderCidFontException,
    DocumentLoaderEmptyException,
)
from abbyyplumber.documentloader.DocumentLoaderFactory import DocumentLoaderFactory
from abbyyplumber.finereaderengine.commandline.finereader_engine_commandline import (
    transform_with_commandline_cache,
    get_commandline_cache_path,
    FinereaderEngineException,
)
from abbyyplumber.util.open_cv_util import display_search_results
from finhurdle.pageparser.finhurdle_page_processor import (
    is_valid_page_cat_for_finhurdles,
)
from finhurdle.pageparser.finhurdle_parser import (
    create_extractions_for_all_finhurdles_on_page,
)
from hypodossier.core.domain.SemanticDocument import SemanticDocument
from hypodossier.core.domain.SemanticPage import SemanticPage
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.documents.parser.pageparsers.MatchPageResult import MatchPageResult
from mortgageparser.documents.parser.pageparsers.PageParserException import (
    PageParserMatchException,
)
from mortgageparser.util.string_utils import string_utils_log

import structlog

logger = structlog.getLogger(__name__)


def parse_and_display_single_page_with_parser_manager(
    path_pdf_orig: Path,
    path_pdf_txt: Path,
    parser_manager,
    page_index: int = 0,
    display=True,
    skip_engine_fallback=False,
    client_lang="de",
    save_markup=False,
    view_scale_ratio=0.33,
    display_page_on_error=True,
):
    start_time = time.time()
    document = None
    try:
        document = DocumentLoaderFactory.load(
            path_pdf_orig,
            path_pdf_orig,
            min_page_index=page_index,
            max_page_index=page_index + 1,
            valid_empty_pages=[],
        )
    except (
        DocumentLoaderCidFontException,
        DocumentLoaderEmptyException,
        TypeError,
    ) as e:
        # For TypeError the problem is inside pdfplumber and instead of text, a PSKeyword object is retured in line
        # TypeError: 'PSKeyword' object is not iterable in this line
        # with pdfplumber.open(path_content) as pdf:

        if global_settings.RAISE_EXCEPTIONS_IN_PARSER:
            raise e

        if not skip_engine_fallback:
            try:
                document = DocumentLoaderFactory.load_with_transformation(
                    path_pdf_orig,
                    path_pdf_orig,
                    min_page_index=page_index,
                    max_page_index=page_index,
                    valid_empty_pages=[],
                    use_xml=False,
                )
            except FinereaderEngineException:
                pass
    except FinereaderEngineException:
        pass

    logger.info("--- %s seconds --- after load " % (time.time() - start_time))

    if (
        not document
        or not document.pages
        or len(document.pages) == 0
        and page_index > 0
    ):
        # problem while loading... try again with engine
        raise PageParserMatchException(
            f'Page with index {page_index} not found. Does the doc have this page? Doc="{path_pdf_orig}"'
        )
        # document = DocumentLoaderFactory.load_with_transformation(doc_path, doc_path, min_page_index=page_index,
        #                                      max_page_index=page_index, valid_empty_pages=[], use_xml=True)

    with open(path_pdf_txt) as f:
        text = f.read()

    page = document.pages[0]
    pm = parser_manager(lang=page.lang, page_source=page.page_source)
    sp: SemanticPage = pm.parse_page(
        page_index=page_index,
        page_handle=page,
        text=text,
        use_xml=True,
        skip_engine_fallback=skip_engine_fallback,
        display_page_on_error=display_page_on_error,
    )

    if global_settings.ENABLE_FINHURDLE_HANDLING:
        if is_valid_page_cat_for_finhurdles(sp.page_cat):
            fh_extractions = create_extractions_for_all_finhurdles_on_page(page)
            sp.extractions.add_extractions(fh_extractions)

    if sp:
        dc = sp.get_page_data()
        if dc:
            logger.info(
                "parsed semantic page",
                pagedata=dc.__dict__,
                page_index=sp.page_source.page_index,
                parsername=sp.parsername,
                doc_cat=sp.doc_cat,
                text_content_stats=sp.text_content_stats,
            )

            # for key, val in dc.__dict__.items():
            #    logger.info(f'    dataclass({key}) -> {val}')

        sd = SemanticDocument([sp], client_lang)
        sd.initialize(True)

        logger.info("Formatted extractions:")
        for key, extraction in sp.formatted_extractions(
            client_lang=client_lang,
            allow_multiline=True,
            empty_value="",
            show_empty_fields=False,
        ).items():
            logger.info(f"    Formatted extraction {key} -> {extraction.value}")

    else:
        logger.error("No document page returned from parser. No match?")
    logger.info("--- %s seconds --- after everything " % (time.time() - start_time))

    logger.debug("get_text=" + page.get_text())
    logger.debug("\nstring_utils_log=\n  " + "\n  ".join(string_utils_log))

    if display:
        display_search_results(
            page,
            document_page=sp,
            log_everything=True,
            show_titles_searchbox=False,
            view_scale_ratio=view_scale_ratio,
            save_markup=save_markup,
        )
    return page, sp


def run_single_parser_with_fallback(
    parser: AbstractPageParser,
    doc_path,
    page_index: int = 0,
    use_xml=True,
    use_fallback=True,
    view_scale_ratio=0.33,
    lang="de",
    show_search_results=True,
):
    valid_empty_pages = []
    start_time = time.time()
    cache_path = get_commandline_cache_path(
        doc_path, only_if_exists=False, use_xml=use_xml
    )
    cache_path_if_exists = get_commandline_cache_path(
        doc_path, only_if_exists=True, use_xml=use_xml
    )
    logger.info(f"cache_path={cache_path}, cache_path_if_exists={cache_path_if_exists}")
    valid = False
    dp: SemanticPage = None
    page: Page = None
    try:
        document = DocumentLoaderFactory.load(
            doc_path, valid_empty_pages=valid_empty_pages
        )
        logger.info(f"document={document}, doc=_path{doc_path}")

        logger.info(f"Now parse page {page_index}")
        page = document.pages[page_index]

        text = page.get_text()
        parser.update_lang(lang)
        mpr: MatchPageResult = parser.match_page(page_index, page, text)

        if mpr.matched():
            sp: SemanticPage = parser.parse_page(page_index, page, text)
            sp.match_page_result = mpr
            parser.validate_page(sp, do_raise_exception=False)
            valid = True
        else:
            raise ValueError(f'No Match for parser "{parser}"')
    except ValueError as err:
        if use_fallback:
            logger.info("Exception in regular parsing... try with engine...")
            # now we try again with FR Engine
            doc_engine = transform_with_commandline_cache(doc_path, use_xml=use_xml)
            document2 = DocumentLoaderFactory.load(
                doc_engine, doc_path, valid_empty_pages=valid_empty_pages
            )
            page = document2.pages[page_index]
            mpr: MatchPageResult = parser.match_page(page_index, page, page.get_text())

            if mpr.matched():
                sp = parser.parse_page(page_index, page, page.get_text())
                sp.match_page_result = mpr
                logger.info(f"Found document page with engine... dp={sp}")
                parser.validate_page(sp)
                valid = True
            else:
                raise PageParserMatchException(f'No Match for parser "{parser}"')

        else:
            # Error without fallback
            logger.info(f"Exception in regular parsing without fallback... err={err}")
            page.log_error()

    logger.info("--- %s seconds --- after everything " % (time.time() - start_time))
    logger.info(f"valid={valid}")
    if page.header:
        logger.info(f"header={page.header} -> {page.header.get_text()}")
    if page.main:
        logger.info(f"main={page.main} -> {page.main.get_text()}")
    if page.footer:
        logger.info(f"footer={page.footer} -> {page.footer.get_text()}")

    for sr in page.search_results:
        logger.info(f"sr={sr} -> {sr.get_text()} (label {sr.se_label})")

    if show_search_results:
        display_search_results(
            page, document_page=sp, view_scale_ratio=view_scale_ratio
        )

    return dp, page
