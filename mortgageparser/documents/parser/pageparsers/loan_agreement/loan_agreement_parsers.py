from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


def get_parsers_loan_agreement():
    parsers = [
        TemplatePageParser(
            desc="Generic Loan Agreement",
            doc_cat=DocumentCat.LOAN_AGREEMENT,
            page_cat=PageCat.GENERIC_PAGE,
            # This can cause conflicts with Mortgage contracts...
            # A real Darlehensvertrag is always rather a short document, a bank mortgage contract has lots
            # of text on that page
            ranked_titles_all=[RankedTitle("Darlehensvertrag", 1)],
            max_num_chars_alpha=1200,
        ),
    ]
    return parsers
