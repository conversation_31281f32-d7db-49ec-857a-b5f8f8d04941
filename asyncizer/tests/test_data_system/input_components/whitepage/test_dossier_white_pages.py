from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS

from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/whitepage")
dest_folder_prefix = "input_system_component_whitepage_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_white_pages_all():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "whitepage_candidates",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.CONTRACT_OF_SALE: 1,
                # DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.PLAN_ANY: 1,
                DocumentCat.UNKNOWN: 35,
                DocumentCat.WHITE_PAGES: 23,
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=60,
        ),
        client_lang="fr",
    ).run()
