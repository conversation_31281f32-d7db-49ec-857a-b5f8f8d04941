import subprocess
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Union, Tuple

import minio
import structlog

import global_settings
from asyncizer.aio import aio
from asyncizer.exceptions import PdfSplitException, TooManyPagesError
from asyncizer.pdf_password_protected import (
    PasswordProtectedFile,
    PasswordProtectedLocalFile,
)
from asyncizer.processing_config import OriginalFileProcessingConfig
from hypodossier.util.ghostscript_util import gs_check_valid_pdf_file, gs_pdf_fix
from hypodossier.util.memory_utils import get_mem_stats
from hypodossier.util.pymupdf_util import pymupdf_pdf_fix, pymupdf_get_page_count
from hypodossier.util.pypdf_util import (
    scale_pdf_page_in_place_for_processing,
    ScalePdfPageException,
)

logger = structlog.getLogger(__name__)


def fix_pdf_and_retry_split_pdfpages(
    bucket, src_object, local_file, temp_path_source, temp_dir_pages, log_mem: bool
):
    # Make one try to repair the file and split again
    local_file_repaired = process_pdftocairo_pdf_fix(local_file, temp_path_source)
    res = process_qpdf_split_pages(local_file_repaired, temp_dir_pages, log_mem)
    if res.returncode == 0:
        logger.info(
            "Repair of pdf file with warnings was successful without warnings. Use repaired file",
            local_file_repaired=local_file_repaired,
            bucket=bucket,
            src_object=src_object,
        )
    elif res.returncode == 3:
        logger.warning(
            "Repair of pdf file with warnings was successful with warnings. Use repaired file",
            local_file_repaired=local_file_repaired,
            bucket=bucket,
            src_object=src_object,
        )
    else:
        raise PdfSplitException(
            f"Could not successfully split repaired file {local_file_repaired} for original file: bucket={bucket}, src_object={src_object}"
        )

    return res


def check_and_fix_pdf_file(
    bucket: str,
    src_object: str,
    local_file: Path,
    temp_path_source: Path,
    timeout: float,
    context: str,
) -> Tuple[bool, Path]:
    """

    :param bucket:
    :param src_object:
    :param local_file:
    :param temp_path_source:
    :param timeout:
    :param context: String that will be logged and can help with handling exceptions

    raises PasswordProtectedLocalFile if the file is password protected

    :return: Tuple[bool, Path] that contains ["was the inital file valid?", path_to_file_that_was_potentially_fixed]
    """
    valid_pdf = gs_check_valid_pdf_file(local_file, timeout=timeout, context=context)

    if not valid_pdf:
        logger.warning(
            "check_and_fix_pdf_file: PDF has errors after ghostscript check, try to fix with pymupdf, then gs, then pdftocairo",
            bucket=bucket,
            src_object=src_object,
            context=context,
        )

        # First option pyMuPDF: Try to fix with pyMuPDF (to remove invalid annotations and bbox
        local_file_fixed_pymupdf, changed_pymupdf = pymupdf_pdf_fix(
            local_file, temp_path_source, suffix="_repairedmu", context=context
        )
        valid_pdf_mu = gs_check_valid_pdf_file(
            local_file_fixed_pymupdf, timeout=timeout, context=context
        )
        if valid_pdf_mu:
            return False, local_file_fixed_pymupdf
        else:
            logger.warning(
                "check_and_fix_pdf_file: First option pyMuPDF: PDF has errors after fixing",
                bucket=bucket,
                src_object=src_object,
                context=context,
            )

        # Second option Ghostscript: Try to fix with ghostscript
        local_file_fixed_gs = gs_pdf_fix(
            local_file_fixed_pymupdf,
            temp_path_source,
            suffix="_repairedgs",
            timeout=timeout,
            context=context,
        )
        if local_file_fixed_gs:
            # Check if the ghostscript-fixed file is valid
            valid_pdf_gs = gs_check_valid_pdf_file(
                local_file_fixed_gs, timeout=timeout, context=context
            )
            if valid_pdf_gs:
                logger.info(
                    "check_and_fix_pdf_file: PDF successfully fixed with ghostscript",
                    bucket=bucket,
                    src_object=src_object,
                    context=context,
                )
                return False, local_file_fixed_gs
        else:
            # There was a timeout in gs, so the repair was not successful
            logger.warning(
                "check_and_fix_pdf_file: Second option Ghostscript: : PDF has errors after fixing",
                bucket=bucket,
                src_object=src_object,
                context=context,
                local_file=local_file,
                local_file_fixed_pymupdf=local_file_fixed_pymupdf,
                local_file_fixed_gs=local_file_fixed_gs,
            )

        # Third option: Try pdftocairo
        local_file_fixed_pdftocairo = process_pdftocairo_pdf_fix(
            local_file, temp_path_source
        )
        if local_file_fixed_pdftocairo:
            return False, local_file_fixed_pdftocairo
        else:
            # There was a timeout in gs, so the repair was not successful
            logger.warning(
                "Third option pdftocairo: : PDF has errors after fixing",
                bucket=bucket,
                src_object=src_object,
                context=context,
                local_file=local_file,
                local_file_fixed_pymupdf=local_file_fixed_pymupdf,
                local_file_fixed_gs=local_file_fixed_gs,
                local_file_fixed_pdftocairo=local_file_fixed_pdftocairo,
            )

    return valid_pdf, local_file


def validate_max_num_pages_allowed_in_pdf(
    num_pages: int,
    max_num_pages_allowed_in_pdf: int,
    processing_config: OriginalFileProcessingConfig,
    bucket: str,
    src_object: str,
):
    """
    Check if the maximum number of pages is too high. Default limit can be
    overridden in the processing_config
    :param num_pages:
    :param max_num_pages_allowed_in_pdf:
    :param processing_config:
    :param bucket: Used for detail information in the error message
    :param src_object: Used for detail information in the error message
    :return: Nothing

    raises TooManyPagesError if condition not fulfilled
    """
    if processing_config.max_num_pages_allowed_input:
        max_num_pages_allowed_in_pdf = processing_config.max_num_pages_allowed_input

    if num_pages > max_num_pages_allowed_in_pdf:
        num_pages = num_pages
        raise TooManyPagesError(
            details=f"PDF has too many pages: {num_pages} for original file: bucket={bucket}, src_object={src_object} but max_num_pages_allowed_in_pdf={max_num_pages_allowed_in_pdf}",
            num_pages=num_pages,
            max_num_pages_allowed_in_pdf=max_num_pages_allowed_in_pdf,
        )


@aio
def split_pdfpages(
    *,
    bucket: str,
    src_object: str,
    dest_bucket: str,
    dest_object: str,
    processing_config: OriginalFileProcessingConfig,
    max_num_pages_allowed_in_pdf=global_settings.MAX_NUM_PAGES_ALLOWED_IN_PDF,
    timeout=global_settings.TIMEOUT_PDF_CONVERSION_SECONDS,
):
    logger.info("splitting pdf pages", bucket=bucket, src_object=src_object)
    minio_client = minio.Minio(
        global_settings.S3_HOST,
        global_settings.S3_ACCESS_KEY,
        global_settings.S3_SECRET_KEY,
        secure=global_settings.S3_SECURE,
        region=global_settings.S3_REGION,
    )

    pages = []
    with TemporaryDirectory() as temp_dir_source:
        temp_path_source = Path(temp_dir_source)
        local_file_original = temp_path_source / Path(src_object).name
        minio_client.fget_object(bucket, src_object, str(local_file_original))

        # Validate number of pages before any processing to avoid fixing a
        # file with e.g. 2000 pages which runs into a timeout (best effort)
        try:
            num_pages = pymupdf_get_page_count(local_file_original)
            validate_max_num_pages_allowed_in_pdf(
                num_pages,
                max_num_pages_allowed_in_pdf,
                processing_config,
                bucket,
                src_object,
            )
        except TooManyPagesError as e:
            # This is why we do this validation
            raise e
        except Exception as e:
            # Gracefully ignore all other exceptions
            logger.warning(
                "Failed validate_max_num_pages_allowed_in_pdf before splitting pdf. Try again after splitting",
                bucket=bucket,
                src_object=src_object,
                num_pages=num_pages,
                exception=e,
            )

        with TemporaryDirectory() as temp_dir_pages:
            # res_test = subprocess.run(["qpdf", "--version"], capture_output=True, text=True)
            LOG_MEM_QPDF_11 = False

            # Possible that vaild_pdf=False and local_file_original is returned unchanged.
            # This happens e.g. for password protected files.

            try:
                valid_pdf, local_file = check_and_fix_pdf_file(
                    bucket,
                    src_object,
                    local_file_original,
                    temp_path_source,
                    timeout,
                    processing_config.context,
                )
            except PasswordProtectedLocalFile:
                raise PasswordProtectedFile(bucket=bucket, object_name=src_object)

            res = process_qpdf_split_pages(local_file, temp_dir_pages, LOG_MEM_QPDF_11)

            # res.returncode 0: success, 3: success with warnings

            # In case of PDF warnings try to fix the file
            if res.returncode == 3:
                logger.warning(
                    "PDF Split caused warnings, try to repair the file with pdftocairo",
                    returncode=res.returncode,
                    stderr=res.stderr,
                    stdout=res.stdout,
                )
                res = fix_pdf_and_retry_split_pdfpages(
                    bucket,
                    src_object,
                    local_file,
                    temp_path_source,
                    temp_dir_pages,
                    LOG_MEM_QPDF_11,
                )

            if res.returncode not in [0, 3]:
                if res.stderr and res.stderr.endswith(": invalid password\n"):
                    # We cannot split the file because it is password protected
                    # res.returncode is 2 but not sure this is always true. Therefore just check the message
                    raise PasswordProtectedFile(bucket, src_object)
                else:
                    # Unknown returncode / unknown reason why we do not get a 0
                    # Make one try to repair the file and split again
                    res = fix_pdf_and_retry_split_pdfpages(
                        bucket,
                        src_object,
                        local_file,
                        temp_path_source,
                        temp_dir_pages,
                        LOG_MEM_QPDF_11,
                    )

            if LOG_MEM_QPDF_11:
                if res.stderr and res.returncode == 0:
                    logger.info(f"Memory consumption during pdf-split: {res.stderr}")

                text, size_mb = get_mem_stats()
                logger.info(text)

            files = list(sorted(Path(temp_dir_pages).glob("*.pdf")))

            num_pages = len(files)
            validate_max_num_pages_allowed_in_pdf(
                num_pages,
                max_num_pages_allowed_in_pdf,
                processing_config,
                bucket,
                src_object,
            )

            if global_settings.ENABLE_PDF_DOWNSCALING:
                for file in files:
                    try:
                        scaled, context = scale_pdf_page_in_place_for_processing(
                            file, timeout=timeout
                        )
                    except ScalePdfPageException as e:
                        logger.warning(
                            f"Exception during scale_pdf_page_in_place_for_processing, skip scaling: bucket={bucket}, src_object={src_object}, dest_bucket={dest_bucket}, desc_object={dest_object}, e={e}"
                        )
                        scaled = False

                    if scaled:
                        logger.info(
                            f"scaled src_object={src_object}, file={file}, context={context}"
                        )
                    else:
                        # logger.info(f"No scaling needed for src_object={src_object}, {file}")
                        pass

            num_files_digits = len(str(len(files)))  # e.g. '3' for 567 files
            for idx, file in enumerate(files):
                page_file_name = str(idx + 1).zfill(num_files_digits) + ".pdf"

                dest_page_path = f"{dest_object}/{idx}.pdf"
                minio_client.fput_object(
                    dest_bucket, dest_page_path, f"{temp_dir_pages}/{page_file_name}"
                )
                pages.append(dest_page_path)
    return pages


def process_qpdf_split_pages(
    local_file: Union[Path, str], temp_dir_pages: Union[Path, str], log_mem: bool
):
    """
    Execute qpdf split pages as a system process

    @param local_file: File to split up into single-page PDFs
    @param temp_dir_pages: Location where to store all resulting single-page PDFs
    @param log_mem:
    @return:
    """
    if log_mem:
        text, size_mb = get_mem_stats()
        logger.info(text)

        res = subprocess.run(
            [
                "qpdf",
                "--verbose",
                "--split-pages",
                "--report-memory-usage",
                str(local_file),
                f"{str(temp_dir_pages)}/%d.pdf",
            ],
            capture_output=True,
            text=True,
        )

    else:
        res = subprocess.run(
            [
                "qpdf",
                "--verbose",
                "--split-pages",
                str(local_file),
                f"{str(temp_dir_pages)}/%d.pdf",
            ],
            capture_output=True,
            text=True,
        )

        # res_test2 = subprocess.run(["ls", "-al", temp_dir], capture_output=True, text=True)

    return res


def process_pdftocairo_pdf_fix(
    local_file: Path, output_path_dir: Path, suffix: str = "_repairedcairo"
):
    """
    Try to fix a potentially broken PDF file by repairing it with pdftocairo

    pdftocairo -pdf input.pdf input_repaired.pdf

    Alternative would be e.g. this but ghostscript fixed stull worse than pdf2cairo
    gs -o repaired_cairo_then_gs.pdf -sDEVICE=pdfwrite -dPDFSETTINGS=/prepress cairo_repaired.pdf

     (see https://superuser.com/questions/278562/how-can-i-fix-repair-a-corrupted-pdf-file)


    :param local_file:
    :param output_path_dir:
    :param suffix:

    @return: Path to repaired file if successful or None if repair was unsuccessful
    """
    dest_path = output_path_dir / f"{local_file.stem}{suffix}{local_file.suffix}"

    res = subprocess.run(
        ["pdftocairo", "-pdf", str(local_file), str(dest_path)],
        capture_output=True,
        text=True,
    )

    # Error codes (source: https://docs.oracle.com/cd/E88353_01/html/E37839/pdftocairo-1.html -> EXIT CODES)
    # 0      No error.
    # 1      Error opening a PDF file.
    # 2      Error opening an output file.
    # 3      Error related to PDF permissions.
    # 4      Error related to ICC profile.
    # 99     Other error.

    if res.returncode not in [0, 3]:
        # Repair was unsuccessful
        return None

    return dest_path
