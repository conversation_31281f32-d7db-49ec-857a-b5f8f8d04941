from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "240_debt_collection_information"
path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}")
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_one_not_empty():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["Betreibungsauskunft_nicht_leer.pdf"],
        page_number=1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.DEBT_COLLECTION_INFORMATION: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_check_date():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "blacked_misc",
        source_file_filter=["240_check_date.pdf"],
        page_number=1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.DEBT_COLLECTION_INFORMATION: 1},
        ),
    ).run()
