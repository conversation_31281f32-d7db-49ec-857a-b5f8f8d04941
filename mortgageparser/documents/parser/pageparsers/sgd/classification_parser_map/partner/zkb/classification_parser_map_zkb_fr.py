from hypodossier.core.domain.DocumentCat import DocumentCat
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_util import (
    PageCfg,
)

page_configurations_zkb_fr = [
    PageCfg(
        prefix="FR/100/partner/zkb/ZKB-18401/Leasingantrag Firmenangabe",
        confidence_threshold=0.85,
        doc_cat=DocumentCat.ZKB_18401_LEASINGANTRAG_FIRMENANGABE,
    ),
    PageCfg(
        prefix="FR/100/partner/zkb/ZKB-19643/Auskunft Oberzolldirektion",
        confidence_threshold=0.85,
        doc_cat=DocumentCat.ZKB_19643_AUSKUNFT_OBERZOLLDIREKTION_OZD_FUER_LSVA,
    ),
]
