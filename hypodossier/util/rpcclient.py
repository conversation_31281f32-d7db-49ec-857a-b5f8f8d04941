import pickle
import uuid

import pika
from tblib import pickling_support

pickling_support.install()


class RPCClient(object):
    def __init__(self, rabbit_url):
        self.connection = pika.BlockingConnection(pika.URLParameters(url=rabbit_url))
        self.channel = self.connection.channel()

        result = self.channel.queue_declare(queue="", exclusive=True)
        self.callback_queue = result.method.queue

        self.channel.basic_consume(
            queue=self.callback_queue,
            on_message_callback=self.on_response,
            auto_ack=True,
        )

    def on_response(self, ch, method, props, body):
        if self.corr_id == props.correlation_id:
            self.response = body
            self.content_type = props.content_type

    def call(self, routing_key, data):
        self.response = None
        self.corr_id = str(uuid.uuid4())
        self.channel.basic_publish(
            exchange="",
            routing_key=routing_key,
            properties=pika.BasicProperties(
                reply_to=self.callback_queue,
                correlation_id=self.corr_id,
                content_type="application/json",
            ),
            body=str(data),
        )
        while self.response is None:
            self.connection.process_data_events(time_limit=10)

        if self.content_type == "pickle":
            raise pickle.loads(self.response)

        return self.response
