import os
from pathlib import Path
from tempfile import TemporaryDirectory

import pytest
import email

from constants import EXTRACTED_EMAIL_FILENAME_PREFIX
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER
import global_settings
from hypodossier.email_processor import (
    extract_msg_content,
    extract_eml_content,
    extract_email_content,
    text_to_pdf,
    convert_email_to_pdf,
    html_to_pdf,
)

from unittest import mock

from hypodossier.unpack import unpack_eml, unpack_msg

path_root_folder = Path(global_settings.PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS)


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.fixture
def sample_email_data():
    """Sample email data for testing"""
    return {
        "metadata": {
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "subject": "Test Subject",
            "date": "Wed, 1 Jan 2023 10:00:00 +0000",
        },
        "body": "This is a test email body.\nIt has multiple lines.",
        "html": "<html><body><p>This is a <b>test</b> email body.</p></body></html>",
        "is_html": True,
        "attachments": [{"filename": "test.txt", "data": b"Test attachment content"}],
    }


@pytest.fixture
def sample_plain_email_data(sample_email_data):
    """Plain text email data"""
    data = sample_email_data.copy()
    data["is_html"] = False
    data["html"] = None
    return data


@pytest.fixture
def sample_eml_file(tmp_path):
    """Create a sample .eml file for testing"""
    file_path = tmp_path / "test_email.eml"

    msg = email.message.EmailMessage()
    msg["Subject"] = "Test Email"
    msg["From"] = "<EMAIL>"
    msg["To"] = "<EMAIL>"
    msg["Date"] = "Wed, 1 Jan 2023 10:00:00 +0000"

    # Add text part
    msg.set_content("This is a test email body.")

    # Add HTML part
    msg.add_alternative(
        "<html><body><p>This is a <b>test</b> email body.</p></body></html>",
        subtype="html",
    )

    # Add attachment
    msg.add_attachment(
        b"Test attachment content",
        maintype="text",
        subtype="plain",
        filename="test.txt",
    )

    with open(file_path, "wb") as f:
        f.write(msg.as_bytes())

    return file_path


@pytest.fixture
def mock_msg_file(tmp_path):
    """Create a mock .msg file path"""
    return tmp_path / "test_email.msg"


def test_extract_eml_content(sample_eml_file):
    """Test extracting content from an .eml file"""
    content = extract_eml_content(sample_eml_file)

    assert content["metadata"]["subject"] == "Test Email"
    assert content["metadata"]["sender"] == "<EMAIL>"
    assert content["body"] is not None
    assert content["html"] is not None
    assert len(content["attachments"]) == 1
    assert content["attachments"][0]["filename"] == "test.txt"


def test_extract_msg_content_mock(mock_msg_file):
    """Test extracting content from a .msg file using mock

    Creating .msg files programmatically is complex as it's a
    proprietary Microsoft format

    hence start with a mock approach
    """
    with mock.patch("extract_msg.Message") as mock_msg_class:
        # Configure the mock
        mock_msg = mock_msg_class.return_value
        mock_msg.sender = "<EMAIL>"
        mock_msg.to = "<EMAIL>"
        mock_msg.subject = "Test Subject"
        mock_msg.date = "Wed, 1 Jan 2023 10:00:00 +0000"
        mock_msg.body = "This is a test message"
        mock_msg.htmlBody = "<p>HTML content</p>"

        # Create mock attachment
        mock_attachment = mock.MagicMock()
        mock_attachment.longFilename = "test.txt"
        mock_attachment.data = b"Test attachment content"
        mock_msg.attachments = [mock_attachment]

        # Call the function
        content = extract_msg_content(mock_msg_file)

        # Assertions
        assert content["metadata"]["subject"] == "Test Subject"
        assert content["body"] == "This is a test message"
        assert content["html"] == "<p>HTML content</p>"
        assert len(content["attachments"]) == 1
        assert content["attachments"][0]["filename"] == "test.txt"


def test_extract_email_content_with_different_files(sample_eml_file, mock_msg_file):
    """Test extract_email_content handles different file types"""
    # Test with .eml file
    with mock.patch(
        "hypodossier.email_processor.extract_eml_content", return_value={}
    ) as mock_eml:
        extract_email_content(sample_eml_file)
        mock_eml.assert_called_once_with(str(sample_eml_file))

    # Test with .msg file
    with mock.patch(
        "hypodossier.email_processor.extract_msg_content", return_value={}
    ) as mock_msg:
        extract_email_content(mock_msg_file)
        mock_msg.assert_called_once_with(str(mock_msg_file))

    # Test with unsupported file
    with pytest.raises(ValueError):
        extract_email_content(Path("unsupported.xyz"))


def test_text_to_pdf(tmp_path, sample_plain_email_data):
    """Test converting plain text email to PDF"""
    output_path = tmp_path / "output.pdf"
    created = text_to_pdf(sample_plain_email_data, output_path)
    assert created
    assert os.path.exists(output_path)
    assert os.path.getsize(output_path) > 0


def test_html_to_pdf(tmp_path, sample_email_data):
    """Test converting HTML email to PDF"""
    output_path = tmp_path / "output.pdf"
    created = html_to_pdf(sample_email_data, output_path)

    assert created
    assert os.path.exists(output_path)
    assert os.path.getsize(output_path) > 0


def test_convert_email_to_pdf_selects_correct_converter(
    sample_email_data, sample_plain_email_data, tmp_path
):
    """Test convert_email_to_pdf selects the right converter based on content"""
    output_path = tmp_path / "output.pdf"

    # Test HTML email
    with TemporaryDirectory() as temp_dir:
        output_path = Path(temp_dir) / "html.pdf"
        created = convert_email_to_pdf(sample_email_data, output_path)
        assert created
        assert output_path.exists()
        assert output_path.is_file()
        assert output_path.suffix == ".pdf"
        assert os.path.getsize(output_path) > 0

    # # Test plain text email
    # Does not work anymore as we rely on the output to actually exist
    # with mock.patch("hypodossier.email_processor.text_to_pdf") as mock_text_pdf:
    #     convert_email_to_pdf(sample_plain_email_data, output_path)
    #     mock_text_pdf.assert_called_once()


@pytest.fixture
def demo_email_path():
    """Path to demo email files"""
    return Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER}/input_samples_demo/demo_v23")


@pytest.mark.parametrize(
    "msg_file",
    [
        "email_messages/Datenlieferung Demo-Dossier 1.msg",
        "emails_without_attachment/Test Email Plan Text.msg",
        "emails_without_attachment/Test HTML Email.msg",
        "emails_without_attachment/Test Email Plan Text Unicode.msg",
        "emails_without_attachment/Test HTML Email Unicode.msg",
    ],
)
def test_extract_msg_demo_emails(demo_email_path, msg_file):
    """Test extracting content from demo .msg files"""
    file_path = demo_email_path / msg_file
    assert file_path.exists(), f"Demo file not found: {file_path}"

    content = extract_msg_content(file_path)

    # Basic validation that content was extracted
    assert content["metadata"]["sender"] is not None
    assert content["metadata"]["recipient"] is not None
    assert content["metadata"]["subject"] is not None
    assert content["body"] is not None


@pytest.mark.parametrize(
    "eml_file",
    [
        "emails_without_attachment/html_test_email.eml",
        "emails_without_attachment/plain_text_email.eml",
    ],
)
def test_extract_eml_demo_emails(demo_email_path, eml_file):
    """Test extracting content from demo .eml files"""
    file_path = demo_email_path / eml_file
    assert file_path.exists(), f"Demo file not found: {file_path}"

    content = extract_eml_content(file_path)

    # Basic validation that content was extracted
    assert content["metadata"]["subject"] is not None
    assert content["body"] is not None


@pytest.mark.parametrize(
    "msg_file,expected",
    [
        (
            "email_messages/Datenlieferung Demo-Dossier 1.msg",
            {"has_html": True, "has_attachments": True},
        ),
        (
            "emails_without_attachment/Test Email Plan Text.msg",
            {"has_html": True, "has_attachments": False},
        ),
        (
            "emails_without_attachment/Test HTML Email.msg",
            {"has_html": True, "has_attachments": False},
        ),
        (
            "emails_without_attachment/Test HTML Email Unicode.msg",
            {"has_html": True, "has_attachments": False},
        ),
    ],
)
def test_extract_msg_demo_emails_detailed(demo_email_path, msg_file, expected):
    """Test extracting content from demo .msg files with detailed assertions"""
    file_path = demo_email_path / msg_file
    assert file_path.exists(), f"Demo file not found: {file_path}"

    content = extract_msg_content(file_path)

    # Validate content structure
    assert isinstance(content, dict)
    assert all(
        key in content for key in ["metadata", "body", "html", "is_html", "attachments"]
    )

    # Validate metadata structure
    assert all(
        key in content["metadata"] for key in ["sender", "recipient", "subject", "date"]
    )

    # Check HTML content
    assert bool(content["html"]) == expected["has_html"]
    assert content["is_html"] == expected["has_html"]

    # Check attachments
    if expected["has_attachments"]:
        assert len(content["attachments"]) > 0
        assert all(isinstance(att, dict) for att in content["attachments"])
        assert all(
            key in att for key in ["filename", "data"] for att in content["attachments"]
        )
    else:
        assert len(content["attachments"]) == 0


@pytest.mark.parametrize(
    "eml_file,expected",
    [
        (
            "emails_without_attachment/html_test_email.eml",
            {"has_html": True, "has_attachments": False},
        ),
        (
            "emails_without_attachment/plain_text_email.eml",
            {"has_html": False, "has_attachments": False},
        ),
    ],
)
def test_extract_eml_demo_emails_detailed(demo_email_path, eml_file, expected):
    """Test extracting content from demo .eml files with detailed assertions"""
    file_path = demo_email_path / eml_file
    assert file_path.exists(), f"Demo file not found: {file_path}"

    content = extract_eml_content(file_path)

    # Validate content structure
    assert isinstance(content, dict)
    assert all(
        key in content for key in ["metadata", "body", "html", "is_html", "attachments"]
    )

    # Validate metadata structure
    assert all(
        key in content["metadata"] for key in ["sender", "recipient", "subject", "date"]
    )

    # Check HTML content
    assert bool(content["html"]) == expected["has_html"]
    assert content["is_html"] == expected["has_html"]

    # Check attachments
    assert len(content["attachments"]) == (1 if expected["has_attachments"] else 0)


def test_extract_email_content_invalid_file(tmp_path):
    """Test handling of invalid file types"""
    # Test with non-existent file
    with pytest.raises(FileNotFoundError):
        extract_email_content(tmp_path / "nonexistent.msg")

    # Test with unsupported extension
    invalid_file = tmp_path / "test.txt"
    invalid_file.write_text("test content")
    with pytest.raises(ValueError, match="Unsupported file extension"):
        extract_email_content(invalid_file)


def test_extract_msg_content_corrupted_file(tmp_path):
    """Test handling of corrupted .msg file"""
    corrupted_file = tmp_path / "corrupted.msg"
    corrupted_file.write_text("This is not a valid .msg file")

    with pytest.raises(
        Exception
    ):  # specific exception type depends on extract_msg library
        extract_msg_content(corrupted_file)


def test_extract_eml_content_with_encodings(tmp_path):
    """Test handling of different email encodings"""
    # Create test .eml with UTF-8 content
    utf8_file = tmp_path / "utf8.eml"
    utf8_content = """From: <EMAIL>
Subject: =?utf-8?B?4pi44pi64pi84pi8?=
Content-Type: text/plain; charset="utf-8"

Hello 你好 Привет"""
    utf8_file.write_bytes(utf8_content.encode("utf-8"))

    content = extract_eml_content(utf8_file)
    assert "你好" in content["body"]
    assert "☸☺☼☼" in content["metadata"]["subject"]


@pytest.mark.asyncio
async def test_extract_attachments_demo_emails(demo_email_path):
    """Test extracting attachments from demo emails
    This test takes about 30 sec to run, probably reportlab is slow
    """
    with TemporaryDirectory() as temp_dir:

        # Test .eml file with attachments
        eml_file = demo_email_path / "emails_without_attachment/html_test_email.eml"
        assert eml_file.exists()

        attachments = unpack_eml(eml_file, Path(temp_dir))
        assert len(attachments) > 0

        # Verify email body PDF was created
        pdf_files = [
            att
            for att in attachments
            if att.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
        ]
        assert len(pdf_files) == 1


@pytest.mark.asyncio
async def test_extract_attachments_with_pdf_disabled(demo_email_path):
    """Test extracting attachments with PDF conversion disabled"""
    with mock.patch.object(global_settings, "CONVERT_EMAIL_CONTENT_TO_PDF", False):
        with TemporaryDirectory() as temp_dir:
            # Test .eml file
            eml_file = demo_email_path / "emails_without_attachment/html_test_email.eml"
            assert eml_file.exists()

            attachments = unpack_eml(eml_file, Path(temp_dir))

            # Verify no email body PDF was created
            pdf_files = [
                att
                for att in attachments
                if att.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
            ]
            assert len(pdf_files) == 0


@pytest.mark.asyncio
async def test_unpack_msg_with_pdf_disabled(demo_email_path):
    """Test unpacking .msg with PDF conversion disabled"""
    with mock.patch.object(global_settings, "CONVERT_EMAIL_CONTENT_TO_PDF", False):
        with TemporaryDirectory() as temp_dir:
            # Test .msg file
            msg_file = (
                demo_email_path / "email_messages/Datenlieferung Demo-Dossier 1.msg"
            )
            assert msg_file.exists()

            unpack_msg(str(msg_file), temp_dir)

            # Check directory contents
            files = list(Path(temp_dir).glob("*"))
            pdf_files = [
                f for f in files if f.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
            ]
            assert len(pdf_files) == 0


@pytest.mark.asyncio
async def test_extract_attachments_with_pdf_enabled(demo_email_path):
    """Test extracting attachments with PDF conversion enabled"""
    with mock.patch.object(global_settings, "CONVERT_EMAIL_CONTENT_TO_PDF", True):
        with TemporaryDirectory() as temp_dir:
            eml_file = demo_email_path / "emails_without_attachment/html_test_email.eml"
            assert eml_file.exists()

            results = unpack_eml(eml_file, Path(temp_dir))

            # Verify email body PDF was created
            assert len(results) == 1
            assert results[0].name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)


@pytest.mark.asyncio
async def test_unpack_msg_with_pdf_enabled(demo_email_path):
    """Test unpacking .msg with PDF conversion enabled"""
    with mock.patch.object(global_settings, "CONVERT_EMAIL_CONTENT_TO_PDF", True):
        with TemporaryDirectory() as temp_dir:
            msg_file = (
                demo_email_path / "email_messages/Datenlieferung Demo-Dossier 1.msg"
            )
            assert msg_file.exists()

            unpack_msg(str(msg_file), temp_dir)

            # Check directory contents
            files = list(Path(temp_dir).glob("*"))
            pdf_files = [
                f for f in files if f.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
            ]
            assert len(pdf_files) == 1
