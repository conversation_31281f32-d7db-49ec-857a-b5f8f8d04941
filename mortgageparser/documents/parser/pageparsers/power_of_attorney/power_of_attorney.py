from dataclasses import dataclass

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplatePowerOfAttorneyPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.POWER_OF_ATTORNEY

    se: GenericLetterSearchElements = None


def get_parsers_power_of_attorney():
    parsers = [
        TemplatePowerOfAttorneyPageParser(
            desc="Generische Vollmacht, z.B. für Grundbuchgeschäfte 2021",
            min_num_chars_alpha=500,
            max_num_chars_special=100,
            ranked_titles_all=[
                RankedTitle("Spezialvollmacht", 2, 6),
                RankedTitle("Vollmacht", 2, 6),
            ],
            required_tokens_any=[
                ["bevollmächtig", "bevollmächtigen"],
                ["Vollmachtgeber"],
                ["Bevollmächtigte"],
                ["Namen und Auftrag"],
            ],
        ),
    ]
    return parsers
