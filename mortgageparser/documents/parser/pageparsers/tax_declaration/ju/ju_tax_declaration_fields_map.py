from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_PORTFOLIO,
    INCOME_UNDISTRIBUTED_INHERITANCES,
    P1_INCOME_GROSS_TOTAL,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    P1_CONTRIBUTION_PILLAR_1,
    P2_CONTRIBUTION_PILLAR_1,
    P1_CONTRIBUTION_PILLAR_2,
    P2_CONTRIBUTION_PILLAR_2,
    INCOME_NET_TOTAL,
    EXPENSE_CHILDREN_DAYCARE,
    DEDUCTIONS_ILLNESS,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_UNDISTRIBUTED_INHERITANCES,
    ASSETS_OTHER,
    ASSETS_GROSS_TOTAL,
    DEBT_PRIVATE,
    DEBT_BUSINESS,
    ASSETS_NET_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_BOARD_SEATS,
    INCOME_REAL_ESTATE_NET_PRIMARY,
    INCOME_REAL_ESTATE_NET_OTHER,
    ASSETS_REAL_ESTATE_MAIN_PROPERTY,
    P1_INCOME_OTHER,
    DEDUCTIONS_EDUCATION,
    P2_INCOME_BOARD_SEATS,
    P2_INCOME_OTHER,
    P1_INCOME_ALIMONY_PARTNER,
    P1_INCOME_ALIMONY_CHILDREN,
    P2_INCOME_ALIMONY_PARTNER,
    P2_INCOME_ALIMONY_CHILDREN,
    P2_INCOME_GROSS_TOTAL,
)

tax_ju_codes_income_p1 = {
    "100": P1_INCOME_EMPLOYED_MAIN,
    # "105": Revenu de l’activité dépendante - revenu en nature, part privée aux frais généraux,
    # "110": Revenu de l’activité dépendante - gain accessoire net,
    # "120": Revenu de l’activité dépendante - indemnités journalières AI,
    "130": P1_INCOME_BOARD_SEATS,
    "140": P1_INCOME_SELF_MAIN,
    # "150": Revenu de l’activité indépendante - résultat de l’activité agricole et forestière,
    # "160": Revenu de l’activité indépendante - résultat de société simple, en nom collectif ou en commandite,
    "170": P1_INCOME_SELF_SIDE,
    # "180": Revenu de l’activité indépendante - pertes commerciales non absorbées,
    # "182": Revenu de l’activité indépendante - cotisations personnelles AVS / AI / APG,
    # "184": Revenu de l’activité indépendante - rendement de titres compris dans le compte de pertes et profits,
    # "186": Revenu de l’activité indépendante - cotisations personnelles à un 2üme pillier (50 %),
    # "188": Revenu de l’activité indépendante - gain de liquidation compris dans revenu ordinaire,
    # "190": Revenu de l’activité indépendante - allocations familiales pour indépendants-es / agriculteurs-trices,
    "200": P1_INCOME_PENSION_AHV,
    "210": P1_INCOME_SOCIAL_SECURITY,
    "220": P1_INCOME_PENSION,
    "230": P1_INCOME_EO,
    # "240": Autres rentes et prestations,
    "250": P1_INCOME_ALIMONY_PARTNER,
    "260": P1_INCOME_ALIMONY_CHILDREN,
    "300": INCOME_REAL_ESTATE_NET_PRIMARY,
    # "310": Excédent de dépenses de la fortune immobilière privée,
    "320": INCOME_REAL_ESTATE_NET_OTHER,
    # "330": Excédent de dépenses de la fortune immobilière commerciale,
    "340": INCOME_PORTFOLIO,
    # "350": Rendement de titres ou d’autres placements dans l’entreprise,
    # "360": Droit d’habitation,
    # "370": Rentes viagères, entretien viager, etc.,
    "380": INCOME_UNDISTRIBUTED_INHERITANCES,
    # "390": Excédent de dépenses (successions non partagées, copropriétés),
    "400": P1_INCOME_OTHER,
    "480": P1_INCOME_GROSS_TOTAL,
}

tax_ju_codes_income_p2 = {
    "100": P2_INCOME_EMPLOYED_MAIN,
    # "105": Revenu de l’activité dépendante - revenu en nature, part privée aux frais généraux,
    # "110": Revenu de l’activité dépendante - gain accessoire net,
    # "120": Revenu de l’activité dépendante - indemnités journalières AI,
    "130": P2_INCOME_BOARD_SEATS,
    "140": P2_INCOME_SELF_MAIN,
    # "150": Revenu de l’activité indépendante - résultat de l’activité agricole et forestière,
    # "160": Revenu de l’activité indépendante - résultat de société simple, en nom collectif ou en commandite,
    "170": P2_INCOME_SELF_SIDE,
    # "180": Revenu de l’activité indépendante - pertes commerciales non absorbées,
    # "182": Revenu de l’activité indépendante - cotisations personnelles AVS / AI / APG,
    # "184": Revenu de l’activité indépendante - rendement de titres compris dans le compte de pertes et profits,
    # "186": Revenu de l’activité indépendante - cotisations personnelles à un 2üme pillier (50 %),
    # "188": Revenu de l’activité indépendante - gain de liquidation compris dans revenu ordinaire,
    # "190": Revenu de l’activité indépendante - allocations familiales pour indépendants-es / agriculteurs-trices,
    "200": P2_INCOME_PENSION_AHV,
    "210": P2_INCOME_SOCIAL_SECURITY,
    "220": P2_INCOME_PENSION,
    "230": P2_INCOME_EO,
    # "240": Autres rentes et prestations,
    "250": P2_INCOME_ALIMONY_PARTNER,
    "260": P2_INCOME_ALIMONY_CHILDREN,
    "400": P2_INCOME_OTHER,
    "480": P2_INCOME_GROSS_TOTAL,
}

tax_ju_codes_deductions = {
    "500": P1_EXPENSE_EMPLOYMENT,
    "500C": P2_EXPENSE_EMPLOYMENT,
    # "505": déduction en cas d’activité professionnelle des deux conjoints ou de collaboration régulière,
    "510": P1_CONTRIBUTION_PILLAR_1,
    "510C": P2_CONTRIBUTION_PILLAR_1,
    "515": P1_CONTRIBUTION_PILLAR_2,
    "515C": P2_CONTRIBUTION_PILLAR_2,
    "520": P1_CONTRIBUTION_PILLAR_3A,
    "520C": P2_CONTRIBUTION_PILLAR_3A,
    "525": INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    "530": INTEREST_PAID_ON_DEBT,
    # "535": intérêts passifs - commerciaux / agricoles,
    "540": EXPENSE_ALIMONY_PARTNER,
    "545": EXPENSE_ALIMONY_CHILDREN,
    # "548": cotisations et versements en faveur d’un parti politique,
    "549": DEDUCTIONS_EDUCATION,
    "550": DEDUCTIONS_ILLNESS,
    "555": EXPENSE_CHILDREN_DAYCARE,
    # "560": revenu net I,
    # "580": solde déductible,
    # "585": dons (max. 10% du revenu net I),
    "590": INCOME_NET_TOTAL,
    # "600": Ménage indépendant, sans enfant à charge , avec droit d’accueil de son(ses) enfant(s) mineur(s),
    # "610": Déduction en cas d’activité prof. exercée par une personne seule avec enfant à charge,
    # "620": Enfants à charge et personnes secourues - par enfant âgé de moins de 18 ans,
    # "630": Enfants à charge et personnes secourues - par enfant avec instruction au-dehors,
    # "640": Enfants à charge et personnes secourues - secours,
    # "660": apprenti-e et étudiant-e,
    # "670": personnes âgées ou infirmes,
    # "680": couples mariés ou partenariat enregistré,
    "690": INCOME_TAXABLE_LOCAL,
}

tax_ju_codes_assets = {
    "700": ASSETS_REAL_ESTATE_MAIN_PROPERTY,
    # "702": Biens fonciers - dans d’autres communes jurassiennes,
    # "708": Biens fonciers - immeubles commerciaux,
    # "710": Biens fonciers - immeubles agricoles,
    # "720": Fortune mobilière d’exploitation - bétail,
    # "725": Fortune mobilière d’exploitation - matériel d’exploitation agricole,
    # "730": Fortune mobilière d’exploitation - matériel d’exploitation non agricole,
    # "735": Fortune mobilière d’exploitation - autres actifs commerciaux,
    "740": ASSETS_PORTFOLIO_ACCOUNTS,
    # "745": Titres et autres placements engagés dans l’entreprise,
    # "750": Fortune placée dans des sociétés en nom collectif ou en commandite suisses ou étrangères,
    "755": ASSETS_UNDISTRIBUTED_INHERITANCES,
    "760": ASSETS_CARS,
    "770": ASSETS_LIFE_INSURANCE,
    "780": ASSETS_OTHER,
    "790": ASSETS_GROSS_TOTAL,
    "800": DEBT_PRIVATE,
    "810": DEBT_BUSINESS,
    # "820": Excédent de dettes à des successions non partagées,
    "840": ASSETS_NET_TOTAL,
    # "860": Déduction générale - couples mariés/partenaires,
    # "870": Déduction générale - en plus pour chaque enfant,
    "890": ASSETS_TAXABLE_LOCAL,
}
