from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "250205_testdata_with_processing_errors"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS}/swissfex/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_swissfex_processing_errors"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


# These files have repeatedly caused processing errors (processing never finished) in production.
# When triggering a "reprocessing" this worked fine.
# Added to tests to see if we can reproduce the error and fix it.
# So far, files seem to work via manual testing and in the tests.
# But we keep the tests to see if the error ever crops up again
@pytest.mark.asyncio
async def test_processing_error_example_1():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "1",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_processing_error_example_2():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "2",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PROPERTY_PHOTOS: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_processing_error_example_3():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "3",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PROPERTY_PHOTOS: 1}
        ),
    ).run()
