from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)

from mortgageparser.util.string_utils import contains_all_strings


class ZekCheckDetailsPageParser(AbstractPageParser):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "ZEK-Datenbankauszug per ",
                "Recht auf Berichtigung unrichtiger Daten (Artikel 5 DSG)",
            ],
        )
        return success

    def parse(self) -> SemanticPage:
        return create_semantic_page(
            self, DocumentCat.ZEK_CHECK, PageCat.ZEK_CHECK_DETAILS
        )
