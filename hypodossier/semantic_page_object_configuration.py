from typing import List, Optional

from pydantic import Field
from pydantic.main import BaseModel

from global_settings import DEFAULT_SPACY_BASED_PARSER_CONFIDENCE
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat

CONFIDENCE_ID_STUFF = 0.9


class PageObjectAggregation(BaseModel):
    aggregation_names: List[str] = Field(default_factory=list)
    page_coverage_factor: float = 1.0


class PageObjectConfiguration(BaseModel):
    # All page objects with the same doc_cat_group will be placed on the same (newly created) page.
    # So for each doc_cat_group a new page will be created.
    # If the doc_cat_group is None then no new page will be created for this page object (e.g. for logos)
    doc_cat_group: Optional[DocumentCat] = None

    doc_cat: DocumentCat
    page_cat: PageCat = PageCat.GENERIC_PAGE

    # Objects with a confidence of at least this threshold will be mapped to a semantic page
    # and thus override the semantic page created from the text classifier
    confidence_threshold: float = 0.8

    # A full size graphic / plan with normal borders will cover 0.8 * 0.8 = 64% of the page
    # if this page objects covers more than the threshold configured here then we ignore the
    # criteria from max_num_chars, min_percentage_chars_alpha, max_num_lines because the page object
    # is so large that it is certainly more relevant than the text
    max_page_coverage_for_text_limitations: float = 0.55

    max_num_chars: int = -1

    # e.g. 0.73 means at least 73% of all chars must be alpha (and not digit and not special chars)
    min_percentage_chars_alpha: float = -1

    # Maximum number of text lines that are accepted on the page where this object is detected
    max_num_lines: int = -1

    # Multiplier for confidence to make pages less/more significant
    confidence_weight: float = 1.0

    # Minimum width as a percentage of the full image width
    min_width: float = 0.01

    # Minimum height as a percentage of the full image height
    min_height: float = 0.01

    # Maximum width as a percentage of the full image width
    max_width: float = 1
    max_height: float = 1

    # This is the maximum confidence of the text classification that will be overridden by this page object
    # Max confidence of a Spacy result is limited by SpacyClassifierPageParser.confidence_modifier * 1.
    # So if this is  higher we can override Spacy results. If this is >1 then we can override rule based matchers
    # as well.
    # Add 0.001 to account for rounding errors. We want to be able to override a spacy page that is retured with
    # 100 percent confidence but not a rule based confidence with is highter (DEFAULT_RULE_BASED_PARSER_CONFIDENCE)
    max_confidence_override: float = DEFAULT_SPACY_BASED_PARSER_CONFIDENCE + 0.0001

    # By default, only visible page objects can dominate a page. But e.g. for logos we can configure also
    # invisible page objects to do that.
    use_only_if_visible: bool = True

    # List of document categories that will not be overr
    doc_cats_no_override: List[DocumentCat] = None

    aggregation: Optional[PageObjectAggregation] = None


# We set the confidence so high that the analyzed text page is always overridden with the identiy page object.
# So in create_semantic_pages_for_page_with_special_page_objects()  we create a new semantic page from the
# identity page object to use its derived page objects (which come from hylayoutlm)
IDENTIY_MAX_CONFIDENCE_OVERRIDE = 1.01

KERAS_CONFIDENCE = 0.85

# 210830 mt: Changed from 250
max_num_chars_prop = 400

min_dim_prop = 0.15  # at least 20% of width and height of page

max_dim_rest = 0.3  # all rest_* objects must be at most 30% of page width and height

# If the page_object covers the center of the page (x/y) then we need only HALF of this criterion
# This is to make sure that single page documents from the sales process are handled
max_page_coverage_for_text_limitations_with_center_covered = 0.45


# If a "large enough" identity object is on the page, ignore additional text (e.g. verification form of Swiss Life)
# Consider 0.5 width x 0.25 height = 0.125 coverage "large enough to consider this an identity object"
max_page_coverage_for_text_limitations_identity = 0.125

# Make this larger than 2 so a large page coverage does never lead to the text restrictions being ignored.
DISABLE_PAGE_COVERAGE = 10

MAX_NUM_LINES_IDENTITY = 40

DOC_CATS_NO_OVERRIDE_SALES_DOC = [
    DocumentCat.SALES_DOCUMENTATION,
    DocumentCat.PROPERTY_VALUATION,
]

PAGE_AGGREGATION_PHOTO = PageObjectAggregation(
    aggregation_names=[
        "photo_building_exterior",
        "photo_building_interior",
        "photo_building_visualisation",
        "photo_outdoor",
    ]
)

PAGE_AGGREGATION_PLAN_FLOOR_SIDE = PageObjectAggregation(
    aggregation_names=["plan_floor", "plan_side_view"]
)

PAGE_AGGREGATION_PLAN_ANY = PageObjectAggregation(
    aggregation_names=["plan_cadaster", "plan_aerial_photo", "plan_situation"]
)

passport_ch_config = PageObjectConfiguration(
    doc_cat_group=DocumentCat.PASSPORT_CH,
    doc_cat=DocumentCat.PASSPORT_CH,
    # min_percentage_chars_alpha=0.3,        # should be 0.6 but is wrong for bad scans
    confidence_threshold=KERAS_CONFIDENCE,
    max_num_lines=MAX_NUM_LINES_IDENTITY,
    max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
    max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
)

id_ch_front_config = PageObjectConfiguration(
    doc_cat_group=DocumentCat.ID,
    doc_cat=DocumentCat.ID,
    page_cat=PageCat.ID_CH_FRONT,
    confidence_threshold=KERAS_CONFIDENCE,
    max_num_lines=MAX_NUM_LINES_IDENTITY,
    max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
    max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
)

id_ch_back_config = PageObjectConfiguration(
    doc_cat_group=DocumentCat.ID,
    doc_cat=DocumentCat.ID,
    page_cat=PageCat.ID_CH_BACK,
    confidence_threshold=KERAS_CONFIDENCE,
    max_num_lines=MAX_NUM_LINES_IDENTITY,
    max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
    max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
)

id_other_config = PageObjectConfiguration(
    doc_cat_group=DocumentCat.ID_OTHER,
    doc_cat=DocumentCat.ID_OTHER,
    confidence_threshold=0.93,  # KERAS_CONFIDENCE,
    max_num_lines=MAX_NUM_LINES_IDENTITY,
    max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
)

page_object_configurations = {
    # 220609 mt new, contains front. Currently no samples for back but they would also go in here
    "identity_ahv": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PENSION_CERTIFICATE_AHV,
        doc_cat=DocumentCat.PENSION_CERTIFICATE_AHV,
        confidence_threshold=0.93,  # KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
    ),
    "identity_cover_misc": PageObjectConfiguration(
        doc_cat_group=DocumentCat.IDENTITY_MISC,
        doc_cat=DocumentCat.IDENTITY_MISC,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
    ),
    "identity_passport_ch00": passport_ch_config,
    "identity_passport_ch23": passport_ch_config,
    "identity_passport_germany": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PASSPORT_DE,
        doc_cat=DocumentCat.PASSPORT_DE,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_passport_france": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PASSPORT_FR,
        doc_cat=DocumentCat.PASSPORT_FR,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_passport_italy": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PASSPORT_IT,
        doc_cat=DocumentCat.PASSPORT_IT,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_passport_other": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PASSPORT_OTHER,
        doc_cat=DocumentCat.PASSPORT_OTHER,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_id_ch00_front": id_ch_front_config,
    "identity_id_ch00_back": id_ch_back_config,
    "identity_id_ch23_front": id_ch_front_config,
    "identity_id_ch23_back": id_ch_back_config,
    # No separate document category for Italian Identity Card yet
    "identity_id_ita": id_other_config,
    "identity_id_other": id_other_config,
    # Currently really bad detection, found false positive with 93%
    "identity_swiss_foreigner_id_paper_front": PageObjectConfiguration(
        doc_cat_group=DocumentCat.FOREIGN_NATIONAL_ID,
        doc_cat=DocumentCat.FOREIGN_NATIONAL_ID,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_swiss_foreigner_id_paper_back": PageObjectConfiguration(
        doc_cat_group=DocumentCat.FOREIGN_NATIONAL_ID,
        doc_cat=DocumentCat.FOREIGN_NATIONAL_ID,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_swiss_foreigner_id_paper_details": PageObjectConfiguration(
        doc_cat_group=DocumentCat.FOREIGN_NATIONAL_ID,
        doc_cat=DocumentCat.FOREIGN_NATIONAL_ID,
        max_width=0.75,  # Take this out after screenshots of ZKB are trained in pic classifier
        confidence_threshold=0.92,  # KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "identity_swiss_foreigner_id_paper_photo_page": PageObjectConfiguration(
        doc_cat_group=DocumentCat.FOREIGN_NATIONAL_ID,
        doc_cat=DocumentCat.FOREIGN_NATIONAL_ID,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    # new 220609 mt: this is the new format from 2019
    "identity_swiss_residence_permit_front": PageObjectConfiguration(
        doc_cat_group=DocumentCat.RESIDENCE_PERMIT,
        doc_cat=DocumentCat.RESIDENCE_PERMIT,
        page_cat=PageCat.RESIDENCE_PERMIT_FRONT,
        confidence_threshold=0.9,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    # new 220609 mt: this is the new format from 2019
    "identity_swiss_residence_permit_back": PageObjectConfiguration(
        doc_cat_group=DocumentCat.RESIDENCE_PERMIT,
        doc_cat=DocumentCat.RESIDENCE_PERMIT,
        page_cat=PageCat.RESIDENCE_PERMIT_BACK,
        confidence_threshold=0.9,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    # This is the old format of the residence permit until 2019
    "identity_swiss_foreigner_id_plastic_front": PageObjectConfiguration(
        doc_cat_group=DocumentCat.RESIDENCE_PERMIT,
        doc_cat=DocumentCat.RESIDENCE_PERMIT,
        page_cat=PageCat.RESIDENCE_PERMIT_FRONT,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    # This is the old format of the residence permit until 2019
    "identity_swiss_foreigner_id_plastic_back": PageObjectConfiguration(
        doc_cat_group=DocumentCat.RESIDENCE_PERMIT,
        doc_cat=DocumentCat.RESIDENCE_PERMIT,
        page_cat=PageCat.RESIDENCE_PERMIT_BACK,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_lines=MAX_NUM_LINES_IDENTITY,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        max_confidence_override=IDENTIY_MAX_CONFIDENCE_OVERRIDE,
    ),
    "photo_building_exterior": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PROPERTY_PHOTOS,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_chars=max_num_chars_prop,
        max_page_coverage_for_text_limitations=DISABLE_PAGE_COVERAGE,
        min_width=min_dim_prop,
        min_height=min_dim_prop,
        aggregation=PAGE_AGGREGATION_PHOTO,
        doc_cats_no_override=DOC_CATS_NO_OVERRIDE_SALES_DOC,
    ),
    "photo_building_interior": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PROPERTY_PHOTOS,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_chars=max_num_chars_prop,
        max_page_coverage_for_text_limitations=DISABLE_PAGE_COVERAGE,
        min_width=min_dim_prop,
        min_height=min_dim_prop,
        aggregation=PAGE_AGGREGATION_PHOTO,
        doc_cats_no_override=DOC_CATS_NO_OVERRIDE_SALES_DOC,
    ),
    "photo_building_visualisation": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PROPERTY_PHOTOS,
        confidence_threshold=KERAS_CONFIDENCE,
        # Keep visualisations only if they are quite large (larger than photos)
        max_page_coverage_for_text_limitations=DISABLE_PAGE_COVERAGE,
        min_width=min_dim_prop * 2,
        min_height=min_dim_prop * 2,
        max_num_chars=max_num_chars_prop,
        aggregation=PAGE_AGGREGATION_PHOTO,
        doc_cats_no_override=DOC_CATS_NO_OVERRIDE_SALES_DOC,
    ),
    "photo_outdoor": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PROPERTY_PHOTOS,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_chars=max_num_chars_prop,
        max_page_coverage_for_text_limitations=DISABLE_PAGE_COVERAGE,
        min_width=min_dim_prop,
        min_height=min_dim_prop,
        aggregation=PAGE_AGGREGATION_PHOTO,
        doc_cats_no_override=DOC_CATS_NO_OVERRIDE_SALES_DOC,
    ),
    "plan_side_view": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PLAN_ANY,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_chars=max_num_chars_prop,
        min_width=0.2,
        min_height=0.2,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_with_center_covered,
        aggregation=PAGE_AGGREGATION_PLAN_FLOOR_SIDE,
        doc_cats_no_override=DOC_CATS_NO_OVERRIDE_SALES_DOC,
    ),
    "plan_floor": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PLAN_FLOOR,
        confidence_threshold=0.8,
        max_num_chars=max_num_chars_prop,
        min_width=min_dim_prop,
        min_height=min_dim_prop,
        # Have a weaker requirement for the size here because "if there is a floor plan it should probably
        # dominate the page if it has 'some' reasonable size. Reasonable would be with a lot of border e.g.
        # width=0.45, height=0.37 which leads to page_coverage of 0.17. In the center of the page this should be enough
        # to make this document a floor plan
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_with_center_covered
        * 0.666,
        aggregation=PAGE_AGGREGATION_PLAN_FLOOR_SIDE,
    ),
    "plan_cadaster": PageObjectConfiguration(
        # OCR detects cadaster numbers, street names, etc.
        # Therefore no max_num_chars here but only a stricter size minimum requirement
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PLAN_CADASTER,
        confidence_threshold=0.8,
        # 211017 mt: changed min_width from 0.5 to 0.35 because there might be a border and 0.5 is too strict
        min_width=0.25,
        min_height=0.25,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_with_center_covered,
        aggregation=PAGE_AGGREGATION_PLAN_ANY,
        # PLR Cadastre is often 50% CADASTER so do not override here
        doc_cats_no_override=[DocumentCat.PLR_CADASTRE],
    ),
    "plan_aerial_photo": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PLAN_SITUATION,
        confidence_threshold=KERAS_CONFIDENCE,
        max_num_chars=max_num_chars_prop
        * 2,  # accept more text because there could be explanations about the map
        # 211017 mt: changed min_width from 0.5 to 0.35 because there might be a border and 0.5 is too strict
        min_width=0.3,
        min_height=0.25,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_with_center_covered,
        aggregation=PAGE_AGGREGATION_PLAN_ANY,
    ),
    "plan_situation": PageObjectConfiguration(
        doc_cat_group=DocumentCat.PROPERTY_DOCUMENTATION,
        doc_cat=DocumentCat.PLAN_SITUATION,
        confidence_threshold=0.8,
        max_num_chars=max_num_chars_prop
        * 2,  # accept more text because there could be explanations about the map
        # min_width=min_dim_prop, min_height=min_dim_prop,
        min_width=0.30,
        min_height=0.25,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_with_center_covered,
        aggregation=PAGE_AGGREGATION_PLAN_ANY,
    ),
    # 210916 mt: renamed from rest_handwriting
    "misc_signature": PageObjectConfiguration(
        doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.9
    ),
    # 'rest_logos': PageObjectConfiguration(doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.8),
    "rest_logos": PageObjectConfiguration(
        doc_cat_group=DocumentCat.WHITE_PAGES,
        doc_cat=DocumentCat.WHITE_PAGES,
        page_cat=PageCat.LOGO_PAGE,
        confidence_threshold=0.93,
        # Ignore pages with more than 5 lines of text
        max_num_lines=5,
        # If this logo covers a large area of space on page it could be a full page logo (-> empty page)
        # else it should not be taken into account
        # If this is configured too small a normal page with a large logo could be identified as WHITE_PAGES
        # (because the max_num_lines does not apply if the logo is larger than this)
        min_width=0.9,
        min_height=0.9,
        max_page_coverage_for_text_limitations=max_page_coverage_for_text_limitations_identity,
        use_only_if_visible=False,
        # # This must be max because the page is matched by CatchEmtptyPageParser with a confidence of 0.98888
        # max_confidence_override=1.0
    ),
    # new 220114 mt
    "rest_minergy": PageObjectConfiguration(
        doc_cat_group=DocumentCat.MINERGIE_CERTIFICATE,
        doc_cat=DocumentCat.MINERGIE_CERTIFICATE,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        confidence_threshold=KERAS_CONFIDENCE,
    ),
    # new 220609 mt
    "rest_bank_screenshots": PageObjectConfiguration(
        doc_cat_group=DocumentCat.BANK_DOCUMENT,
        doc_cat=DocumentCat.BANK_DOCUMENT,
        confidence_threshold=KERAS_CONFIDENCE,
    ),
    # new 220609 mt
    "rest_photo_trash": PageObjectConfiguration(
        doc_cat_group=DocumentCat.UNKNOWN,
        doc_cat=DocumentCat.UNKNOWN,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        confidence_threshold=KERAS_CONFIDENCE,
    ),
    "rest_misc_trash": PageObjectConfiguration(
        doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.9
    ),
    "rest_people_stock_photos": PageObjectConfiguration(
        doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.9
    ),
    "rest_qr_code": PageObjectConfiguration(
        doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.9
    ),
    "partner_mb_zek": PageObjectConfiguration(
        doc_cat_group=DocumentCat.ZEK_CHECK,
        doc_cat=DocumentCat.ZEK_CHECK,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        confidence_threshold=KERAS_CONFIDENCE,
    ),
    # # new 220114
    # 'partner_zkb_gvz_viewer': PageObjectConfiguration(doc_cat_group=DocumentCat.ZKB_GVZ_VIEWER, doc_cat=DocumentCat.ZKB_GVZ_VIEWER,
    #                                           page_cat=PageCat.GENERIC_SINGLE_PAGE,
    #                                           confidence_threshold=KERAS_CONFIDENCE),
    # # new 220114
    # 'partner_zkb_rent_calculator': PageObjectConfiguration(doc_cat_group=DocumentCat.ZKB_RENT_CALCULATOR, doc_cat=DocumentCat.ZKB_RENT_CALCULATOR,
    #                                           page_cat=PageCat.GENERIC_SINGLE_PAGE,
    #                                           confidence_threshold=KERAS_CONFIDENCE),
    # 230830 mt: disable zkb graphical object detection
    "partner_zkb_gvz_viewer": PageObjectConfiguration(
        doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.9
    ),
    "partner_zkb_rent_calculator": PageObjectConfiguration(
        doc_cat_group=None, doc_cat=DocumentCat.UNKNOWN, confidence_threshold=0.9
    ),
}
