from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_authorization_inquiries_cs_2021(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/706_authorization_inquiries"
        ),
        dest_folder_prefix="706_auth_inqu",
        source_file_filter=["authorization_inquiries_cs.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={0: {"company": "CS"}},
            expected_doc_cat_frequency={DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1},
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_authorization_inquiries_moneypark_2021(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/706_authorization_inquiries"
        ),
        dest_folder_prefix="706_auth_inqu",
        source_file_filter=["authorization_inquiries_moneypark_3.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={0: {"company": "MoneyPark"}},
            expected_doc_cat_frequency={DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1},
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_authorization_inquiries_moneypark_may_2021(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/706_authorization_inquiries"
        ),
        dest_folder_prefix="706_auth_inqu",
        source_file_filter=["authorization_inquiries_moneypark_2.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={0: {"company": "MoneyPark"}},
            expected_doc_cat_frequency={DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1},
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )
