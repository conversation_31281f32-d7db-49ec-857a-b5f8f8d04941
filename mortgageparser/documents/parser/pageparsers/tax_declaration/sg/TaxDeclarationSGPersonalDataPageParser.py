from abbyyplumber.api import Page
from abbyyplumber.converter.ValueConverter import (
    DateConverter,
    YearConverter,
    PhoneConverter,
    EmailConverter,
    ParagraphConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementLabeledField,
    SearchElementStaticText,
    SearchElementArea,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    SearchRelationAbove,
    ReferenceBoundaryHorizontal,
    SearchRelationRightOf,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FULLNAME,
    P2_FULLNAME,
    TAX_MUNICIPALITY,
    P1_DATE_OF_BIRTH,
    P2_DATE_OF_BIRTH,
    P1_PROFESSION,
    P1_EMPLOYER,
    P2_PROFESSION,
    P2_EMPLOYER,
    P1_MARITAL_STATUS,
    SECTION_CHILDREN,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_YEAR,
    FIELD_PHONE_PRIMARY,
    FIELD_PHONE_SECONDARY,
    FIELD_EMAIL,
    FIELD_DOCUMENT_DATE,
    FIELD_FIRSTNAME,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.tax_declaration_sg_fields_map import (
    create_search_elements_standard_sg,
)
from mortgageparser.util.search_element_util import create_search_elements_address


class TaxDeclarationSGPersonalDataPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA
        self.canton_short = "SG"

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = create_search_elements_standard_sg(page)

        elements += [
            SearchElementStaticText("text_canton", page.main, label="Kanton"),
            SearchElementConstrainedArea(
                FIELD_YEAR.name,
                page.fullpage,
                text_top="Formular 1",
                text_left="Steuererklärung",
                text_bottom="Register-Nr.",
                compress_whitespace=True,
                converter=YearConverter(),
            ),
            SearchElementConstrainedArea(
                FIELD_DOCUMENT_DATE.name,
                page.fullpage,
                text_top="Register-Nr.",
                text_bottom="Rückfragen an",
                text_left="Dauer bei nicht ganzjähriger",
                dist=5,
                x_range=PercentageRange(0, 0.5),
                converter=DateConverter(),
            ),
            SearchElementLabeledField(
                TAX_MUNICIPALITY.name,
                page.main,
                label="Gemeinde",
                max_l_dist=4,
                relations=[
                    SearchRelationBelow(FIELD_YEAR.name),
                    SearchRelationBelow("text_canton"),
                    SearchRelationAbove(FIELD_DOCUMENT_DATE.name),
                ],
            ),
            # For address detection
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                page.main,
                text_top="Register-Nr.",
                text_bottom="Rückfragen an:",
                text_left="für natürliche",
            ),
            SearchElementConstrainedArea(
                FIELD_PHONE_PRIMARY.name,
                page.main,
                text_top="Telefon Privat",
                text_left="Telefon Privat",
                text_bottom="Telefon Privat",
                text_right="Telefon Geschäft",
                converter=PhoneConverter(),
            ).include_vertical(),
            SearchElementConstrainedArea(
                FIELD_PHONE_SECONDARY.name,
                page.main,
                text_top="Telefon Geschäft",
                text_left="Telefon Geschäft",
                text_bottom="Telefon Geschäft",
                converter=PhoneConverter(),
            ).include_vertical(),
            SearchElementConstrainedArea(
                FIELD_EMAIL.name,
                page.main,
                text_top="E-Mail",
                text_left="E-Mail",
                text_right="Telefon Geschäft",
                text_right_offset_chars=-2,
                text_bottom="E-Mail",
                converter=EmailConverter(),
            ).include_vertical(),
            SearchElementConstrainedArea(
                "target_personal_info",
                page.main,
                extract=False,
                text_top="Personalien, Berufs-",
                text_bottom="Kinder, für deren",
                text_left="Konfession",
                text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                text_left_offset_chars=-1,
                text_right="schriftliche Vollmacht vorliegt.",
                text_right_boundary=ReferenceBoundaryHorizontal.RIGHT,
            ),
            SearchElementConstrainedArea(
                "p_left",
                page.main,
                target_name="target_personal_info",
                text_right="Ehefrau / Partner",
                text_right_offset_chars=-1,
            ),
            SearchElementArea(
                "p_right",
                page.main,
                target_name="target_personal_info",
                relations=[SearchRelationRightOf("p_left")],
            ),
            SearchElementLabeledField(
                P1_FULLNAME.name, page.main, label="Name, Vorname", target_name="p_left"
            ),
            SearchElementLabeledField(
                P2_FULLNAME.name,
                page.main,
                label="Name, Vorname",
                target_name="p_right",
                target_name_label="p_left",
            ),
            SearchElementLabeledField(
                P1_DATE_OF_BIRTH.name,
                page.main,
                label="Geburtsdatum",
                target_name="p_left",
                converter=DateConverter(),
            ),
            SearchElementLabeledField(
                P2_DATE_OF_BIRTH.name,
                page.main,
                label="Geburtsdatum",
                target_name="p_right",
                target_name_label="p_left",
                converter=DateConverter(),
            ),
            SearchElementLabeledField(
                P1_MARITAL_STATUS.name,
                page.main,
                label="Zivilstand",
                target_name="p_left",
            ),
            # SearchElementLabeledField(P2_MARITAL_STATUS.name, page.main, label="Zivilstand",
            #                           target_name="p_right"),
            SearchElementLabeledField(
                P1_PROFESSION.name, page.main, label="Beruf", target_name="p_left"
            ),
            SearchElementLabeledField(
                P2_PROFESSION.name,
                page.main,
                label="Beruf",
                target_name="p_right",
                target_name_label="p_left",
            ),
            SearchElementLabeledField(
                P1_EMPLOYER.name, page.main, label="Arbeitgeber", target_name="p_left"
            ),
            SearchElementLabeledField(
                P2_EMPLOYER.name,
                page.main,
                label="Arbeitgeber",
                target_name="p_right",
                target_name_label="p_left",
            ),
            SearchElementConstrainedArea(
                SECTION_CHILDREN.name,
                page.main,
                text_top="Schule oder Lehrfirma",
                text_right="Hauptsache aufkommen",
                text_right_offset_chars=-2,
                text_bottom="Rückzahlungen",
                converter=ParagraphConverter(),
            ),
        ] + create_search_elements_address(page.main, page)
        return elements

    # def create_content_extractor(self) -> ContentExtractor:
    #     ce = ContentExtractor(self.create_standard_search_elements(self.page) + self.create_search_elements(self.page), search_element_filter_names=[FIELD_EMAIL.name])
    #     return ce
