import minio
import urllib3
from dependency_injector import containers, providers

from asyncizer.filestores import FileStore, TemporaryFileStore
from asyncizer.unpacker import FileExtractor, Unpacker


class FileStoreContainer(containers.DeclarativeContainer):
    minio_client = providers.Singleton(
        minio.Minio,
        endpoint="minio.hypo.duckdns.org",
        access_key="S3_ACCESS_KEY",
        secret_key="S3_SECRET_KEY",
        region="ch-dk-2",
        secure=True,
    )

    file_store: FileStore = providers.Singleton(FileStore, client=minio_client)


class TemporaryFileStoreContainer(containers.DeclarativeContainer):
    file_store_container = providers.Container(FileStoreContainer)

    connection_pool = providers.Singleton(urllib3.PoolManager, maxsize=50)

    temporary_file_store = providers.Singleton(
        TemporaryFileStore,
        file_store=file_store_container.file_store,
        bucket_name="test-temporary-file-store",
    )


class FileExtractorContainer(containers.DeclarativeContainer):
    temp_store_container = providers.Container(TemporaryFileStoreContainer)

    unpacker = providers.Singleton(Unpacker, thread_pool=None)

    file_extractor = providers.Singleton(
        FileExtractor,
        unpacker=unpacker(),
        temp_store=temp_store_container.temporary_file_store,
    )
