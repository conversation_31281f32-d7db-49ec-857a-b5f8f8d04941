from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "310_tax_declaration"
source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}/single_pages_de"
)
dest_folder_prefix = "input_system_doccat_single_pages"


@pytest.mark.asyncio
async def test_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_bs_fullname():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "bs_fullname",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["0.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_gr_property():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "gr_property",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["0.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_sz_double_2021_empty_page():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "sz_barcode",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["0.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={0: {"canton_short": "SZ", "year": "2021"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_vs_lottery():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "vs_lottery",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["0.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()
