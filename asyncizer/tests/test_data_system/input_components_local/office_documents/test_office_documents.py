from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON><PERSON><PERSON><PERSON><PERSON>, DossierExpectations
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL}/office_documents"
)
dest_folder_prefix = "input_system_local_components_office_documents_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_auftragsformular_docx():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "auftragsformular_docx",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # This works, document is not expected to be recognized
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1}
        ),
    ).run()


@pytest.mark.skip(
    reason="does not work because ABBYY has an error on opening the docx file"
)
@pytest.mark.asyncio
async def test_auslaenderausweis_docx():
    # This throws
    # com.abbyy.FREngine.EngineException: An error occurred when opening the file "/tmp/ocr14691974178819415508/output/auslaenderausweis_docx/extracted_files/Auslanderausweis 1.docx".
    # frep-1  |       at com.abbyy.FREngine.IFRDocument.AddImageFile(Native Method) ~[com.abbyy.FREngine-12.0.jar!/:na]
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "auslaenderausweis_docx",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_land_register_docx():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "land_register_docx",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 3
            },
            expected_page_objects={
                0: {"land_register_id": "3442", "land_register_area": "175 m2"}
            },
        ),
    ).run()
