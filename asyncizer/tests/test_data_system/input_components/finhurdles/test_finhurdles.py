import pytest

from asyncizer.tests.util_tests import configure_test_logging
from finhurdle.client.finhurdles_client_remote import classify_finhurdles_with_spacy

text = """5. Anteile an Stockwerkeigentümergemeinschaft                                                                                                                                                                                                                                                                
* Code: G Geschäftsvermögen            N Nutzniessungsvermögen    Q Qualifizierte Beteiligung ^                                                                              ► Zu übertragen in die
Ihr Verrechnungssteueranspruch                                  davon 35% '            Steuererklärung
E Wertschriften aus Erbschaften      S Schenkung
35% von Total Bruttoertrag A                                                                       | Seite 2, <PERSON><PERSON><PERSON> 4.1"""


@pytest.mark.asyncio
@pytest.mark.parallelfails
async def test_finhurdles_remote_simple():
    classifications = await classify_finhurdles_with_spacy(text)

    assert len(classifications) == 1
    assert classifications[0].classification == "Finhurdle"
    assert 0.01 < classifications[0].confidence < 0.1

    print(classifications)


@pytest.mark.asyncio
async def test_finhurdles_remote_bach_sequential(num_iter=10):
    configure_test_logging()

    for i in range(num_iter):
        classifications = await classify_finhurdles_with_spacy(text)
        assert len(classifications) == 1
        assert classifications[0].classification == "Finhurdle"
        assert 0.01 < classifications[0].confidence < 0.99
        print(classifications)
