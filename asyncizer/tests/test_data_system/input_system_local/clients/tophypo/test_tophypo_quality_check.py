from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "quality_check"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/tophypo/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_tophypo_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_240202_first_batch_sales_doc():
    """
    This really is a valuation so classification is ok.
    """
    await DossierTest(
        use_ocr_cache=False,  # Needs to be false to re-create images
        override_existing=True,
        source_folder=path_root_folder / "240202_first_batch/sales_doc",
        source_file_filter=["cairo.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PROPERTY_VALUATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_240202_first_batch_tax():
    """
    gs -sDEVICE=nullpage -dNOPAUSE -dBATCH -dPDFSETTINGS=/prepress -sOutputFile=./repaired.pdf 1.3_Steuererklärung_2022_repaired.pdf


    """
    await DossierTest(
        use_ocr_cache=False,  # Needs to be false to re-create images
        override_existing=True,
        source_folder=path_root_folder / "240202_first_batch/tax",
        source_file_filter=["1.3_Steuererklärung_2022_inkl.Beilagen.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION: 2,
                DocumentCat.SALARY_CERTIFICATE: 2,
                DocumentCat.TAX_DECLARATION: 1,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_240202_first_batch_password():
    """
    File is password proctected but did not raise the correct exception

    """
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "240202_first_batch/password",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(expected_num_processing_exceptions=1),
    ).run()


@pytest.mark.asyncio
async def test_250610_msg_1():
    """
    File is password proctected but did not raise the correct exception

    """
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "250610_msg/01",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_250610_msg_02():
    """
    File is password proctected but did not raise the correct exception

    """
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "250610_msg/02",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
