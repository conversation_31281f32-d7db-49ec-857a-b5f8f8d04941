from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_PORTFOLIO,
    INCOME_OTHER,
    P1_EXPENSE_EMPLOYMENT,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    P1_CONTRIBUTION_PILLAR_2,
    INCOME_NET_TOTAL,
    EXPENSE_CHILDREN_DAYCARE,
    DEDUCTIONS_DONATIONS,
    DEDUCTIONS_ILLNESS,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_OTHER,
    ASSETS_GROSS_TOTAL,
    DEBT_PRIVATE,
    ASSETS_NET_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_BOARD_SEATS,
    INCOME_REAL_ESTATE_GROSS,
    INCOME_GROSS_TOTAL,
    ASSETS_CASH_GOLD,
    ASSETS_REAL_ESTATE_TOTAL_GROSS,
    P1_INCOME_OTHER,
    P1_INCOME_ALIMONY_TOTAL,
    DEDUCTIONS_EDUCATION,
    P2_INCOME_BOARD_SEATS,
    P2_INCOME_OTHER,
    P2_INCOME_ALIMONY_TOTAL,
    ASSETS_GROSS_BUSINESS,
)

tax_ne_codes_income_p1 = {
    "1.11": P1_INCOME_EMPLOYED_MAIN,
    "1.12": P1_INCOME_EMPLOYED_SIDE,
    # "1.13": Revenu d’une activité dépendante - indemnités non comprises dans le certificat de salaire,
    "1.21": P1_INCOME_SELF_MAIN,
    # "1.23": Revenu d’une activité indépendante - principale hors du canton,
    "1.24": P1_INCOME_SELF_SIDE,
    "1.31": P1_INCOME_BOARD_SEATS,
    # "1.32": Autres revenus d’activité - Sociétés : simple / en nom collectif / en commandite,
    "1.33": P1_INCOME_OTHER,
    "1.41": P1_INCOME_EO,
    "1.42": P1_INCOME_SOCIAL_SECURITY,
    "2.1": P1_INCOME_PENSION_AHV,
    "2.2": P1_INCOME_PENSION,
    # "2.3": Rentes et pensions - 3ème Pilier A : prévoyance individuelle liée,
    # "2.4": Rentes et pensions - 3ème Pilier B : autres rentes et pensions (viagère 40 %, SUVA 100 %, etc.),
    "2.5": P1_INCOME_ALIMONY_TOTAL,
    # "2.6": Total des revenus de l’activité, rentes et pensions (chiffres 1.11 à 2.5),
    # "2.7": Total des revenus de l’activité, rentes et pensions de l’épouse ou partenaire (report du chiffre 2.6),
    "3.1": INCOME_PORTFOLIO,
    "4.1": INCOME_REAL_ESTATE_GROSS,
    # "4.2": Immeubles hors canton et à l’étranger,
    "5.1": INCOME_OTHER,
    "5.5": INCOME_GROSS_TOTAL,
    # actually, including pillar 3a
    "6.1": P1_CONTRIBUTION_PILLAR_2,
    "6.2": INTEREST_PAID_ON_DEBT,
    "6.3": EXPENSE_ANNUITY_CONTRIBUTIONS,
    "6.4": P1_EXPENSE_EMPLOYMENT,
    # "6.5": Frais pour activité dépendante accessoire (chiffre 1.12),
    # "6.6": Report de perte de l’activité indépendante des exercices antérieurs,
    # "6.7": Cotisations AVS / AI / APG / AC versées par des assurés sans activité lucrative,
    "6.8": INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    # "6.9": Déduction sur l’un des revenus du travail des conjoints,
    # actually, partner and children
    "6.10": EXPENSE_ALIMONY_PARTNER,
    # "6.11": Frais liés à un handicap,
    # "6.12": Versements aux partis politiques,
    "6.13": EXPENSE_CHILDREN_DAYCARE,
    "6.14": DEDUCTIONS_EDUCATION,
    # "6.15": Total des déductions (chiffres 6.1 à 6.14),
    # "6.16": Revenu et fortune nets 1 (chiffre 5.5 moins chiffre 6.15),
    "6.17": DEDUCTIONS_ILLNESS,
    "6.18": DEDUCTIONS_DONATIONS,
    "6.19": INCOME_NET_TOTAL,
    # "7.1": Déductions sociales - Epoux vivant en ménage commun / personne seule,
    # "7.2": Déductions sociales - Enfant(s) à charge,
    # "7.3": Déductions sociales - Personne(s) nécessiteuse(s) à charge,
    # "7.4": Total des déductions sociales (chiffres 7.1 à 7.3),
    "11": INCOME_TAXABLE_LOCAL,
}

tax_ne_codes_income_p2 = {
    "1.11": P2_INCOME_EMPLOYED_MAIN,
    "1.12": P2_INCOME_EMPLOYED_SIDE,
    # "1.13": Revenu d’une activité dépendante - indemnités non comprises dans le certificat de salaire,
    "1.22": P2_INCOME_SELF_MAIN,
    # "1.23": Revenu d’une activité indépendante - principale hors du canton,
    "1.24": P2_INCOME_SELF_SIDE,
    "1.31": P2_INCOME_BOARD_SEATS,
    # "1.32": Autres revenus d’activité - Sociétés : simple / en nom collectif / en commandite,
    "1.33": P2_INCOME_OTHER,
    "1.41": P2_INCOME_EO,
    "1.42": P2_INCOME_SOCIAL_SECURITY,
    "2.1": P2_INCOME_PENSION_AHV,
    "2.2": P2_INCOME_PENSION,
    # "2.3": Rentes et pensions - 3ème Pilier A : prévoyance individuelle liée,
    # "2.4": Rentes et pensions - 3ème Pilier B : autres rentes et pensions (viagère 40 %, SUVA 100 %, etc.),
    "2.5": P2_INCOME_ALIMONY_TOTAL,
    # "2.6": Total des revenus de l’activité, rentes et pensions (chiffres 1.11 à 2.5),
}

tax_ne_codes_assets = {
    "1.21": ASSETS_GROSS_BUSINESS,
    "3.1": ASSETS_PORTFOLIO_ACCOUNTS,
    "3.2": ASSETS_CASH_GOLD,
    "3.3": ASSETS_OTHER,
    "4.1": ASSETS_REAL_ESTATE_TOTAL_GROSS,
    # "4.2": Immeubles hors canton et à l’étranger,
    "5.2": ASSETS_LIFE_INSURANCE,
    "5.3": ASSETS_CARS,
    # "5.4": Autres biens (bateaux, oeuvres d’art, collections, etc.)
    "5.5": ASSETS_GROSS_TOTAL,
    "6.2": DEBT_PRIVATE,
    # "6.16": Revenu et fortune nets 1 (chiffre 5.5 moins chiffre 6.15),
    "6.19": ASSETS_NET_TOTAL,
    "11": ASSETS_TAXABLE_LOCAL,
}
