import re

STRING_TAB = "      "  # 6 spaces


def remove_multiple_spaces(str_in: str) -> str:
    # return re.sub(' +', ' ', str_in)

    large_tab = STRING_TAB + STRING_TAB + STRING_TAB

    # # 1-3 chars are compressed to 1 char
    # t1 = re.sub('[ ]{1,3}', ' ', str_in)

    # The first char has been already compressed from 3. Therefore  chars -> STRING_TAB
    t2 = re.sub("[ ]{6,14}", STRING_TAB, str_in)

    # Everything larger than a tab will be a large_tab
    t3 = re.sub("[ ]{7,}", large_tab, t2)

    # Strip whitespace in empty lines but keep the empty lines
    t4 = "\n".join(line.rstrip() for line in t3.splitlines())

    return t4


def clean_text_for_classification(text, do_remove_multiple_spaces=True):
    # \u200b wnsp = zero-width space
    # xad = soft hypen (shy)
    # 2022 = round bullet
    t = (
        text.replace("\u200b", "")
        .replace("\xad", "-")
        .replace("—", "-")
        .replace("\u2022", "*")
        .replace("’", "'")
        .replace("´", "'")
        .replace("\t", STRING_TAB)
        .replace("■", "")
        .replace("Ç", "C")
        .replace("Ù", "U")
        .replace("Ô", "O")
        .replace("«", '"')
        .replace("»", '"')
        .replace("”", '"')
    )

    t2 = filter_characters(t)

    if do_remove_multiple_spaces:
        t2 = remove_multiple_spaces(t2)
    return t2


def filter_characters(value):
    allowed_characters = " 0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZöäüëßÄÖÜ.:,&@§*#%+-?!_'\"()[]{}/çéàèêèêÁÃÀÉÊẼ\n"
    return "".join(c for c in value if c in allowed_characters).strip()
