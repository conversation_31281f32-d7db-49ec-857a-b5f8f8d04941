from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_components/pdfcairo_fix"
)
dest_folder_prefix = "input_components/test_pdf_cairo_repair"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_pdf_cairo_repair_pass(monkeypatch):
    monkeypatch.setattr(
        "asyncizer.dossier_processor.global_settings.MAX_TRIES_FREP_FOR_PDF", 1
    )
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "broken_file_that_needs_pdftocairo",
        source_file_filter=["test_file_requires_pdfcairo_to_pass_frep.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_SITUATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_pdf_cairo_repair_dummy_file_that_needs_reprocessing_original(
    monkeypatch,
):
    monkeypatch.setattr(
        "asyncizer.dossier_processor.global_settings.MAX_TRIES_FREP_FOR_PDF", 1
    )
    await DossierTest(
        use_ocr_cache=False,
        override_existing=True,
        source_folder=path_root_folder
        / "dummy_file_that_needs_reprocessing"
        / "original",
        source_file_filter=["DocTest_2bis_2pages.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_pdf_cairo_repair_dummy_file_that_needs_reprocessing_fixed(monkeypatch):
    monkeypatch.setattr(
        "asyncizer.dossier_processor.global_settings.MAX_TRIES_FREP_FOR_PDF", 1
    )
    await DossierTest(
        use_ocr_cache=False,
        override_existing=True,
        source_folder=path_root_folder / "dummy_file_that_needs_reprocessing" / "fixed",
        source_file_filter=["fixed.pdf"],
        dossier_expectations=DossierExpectations(),
    ).run()
