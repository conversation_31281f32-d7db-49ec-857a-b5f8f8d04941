from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL}/email/misc_email_with_eml_part"
)
dest_folder_prefix = "input_system_components_local_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_misc_email_with_eml_part():
    # This is an eml with a pension certificate and a property photo attached
    # Also it includes an attached eml email which is included as "part-000"
    # in the eml.
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                # Email with empty body is ignored, duplicate PK attachment
                # is removed
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PENSION_CERTIFICATE: 3,
                DocumentCat.PENSION_CERTIFICATE_LETTER: 1,
                DocumentCat.PROPERTY_PHOTOS: 1,
            }
        ),
    ).run()
