from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssie<PERSON><PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "220_id"
path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}")
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_one_doc():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["ID CH Manuel Thiemann.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ID: 1},
            expected_page_objects={
                0: {
                    "document_date": "25.10.2013",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "C7011658",
                    "date_of_birth": "04.09.1977",
                    "sex": "M",
                    "document_validity_end_date": "24.10.2023",
                    "hometown": "Ringgenberg BE",
                    # "person_height": "180.0",
                    "mrz": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_doc_ch23_front():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["id_ch23_front.png"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ID: 1},
            expected_page_objects={
                0: {
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    # TODO: OCR error O should be 0 "person_id": "KOP52W16",
                    "date_of_birth": "12.04.2013",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_doc_ch23_back():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["id_ch23_back.png"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ID: 1}
        ),
    ).run()
