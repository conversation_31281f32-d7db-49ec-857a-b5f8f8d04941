from pathlib import Path

import pytest

from asyncizer.processing_config import (
    OriginalFileProcessingConfig,
    SemanticDocumentSplittingStyle,
)
from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/processing_config"
path_root_folder = Path(folder)
dest_folder_prefix = "input_system_components_processing_config_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_one_pic_with_processing_params_doc_cat():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "one pic",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=["310 Steuererklärung IMG-5710.pdf"],
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
        ),
        processing_config=OriginalFileProcessingConfig(
            force_document_category_key=DocumentCat.TAX_DECLARATION.name
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_pic_with_processing_params_doc_cat_and_suffix():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "one pic",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=["269 Scheidung Diverses ho ha hi.pdf"],
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.DIVORCE_MISC: 1},
        ),
        processing_config=OriginalFileProcessingConfig(
            force_document_category_key=DocumentCat.DIVORCE_MISC.name,
            force_title_elements=["ho", "ha", "hi"],
        ),
    ).run()


@pytest.mark.asyncio
async def test_three_pics_with_processing_params_doc_cat_and_suffix():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "three pics",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Caution: these filenames are special in the offline version and do not
            # correspond to the normal processing. Also the 3 numbers 001, 002, 003
            # seem to jump between the documents
            # expected_filenames=["269 Scheidung Diverses ho ha hi 001 IMG-5713.pdf", "269 Scheidung Diverses ho ha hi 002 IMG-5710.pdf", "269 Scheidung Diverses ho ha hi 003 IMG-5710.pdf"],
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.DIVORCE_MISC: 3},
        ),
        processing_config=OriginalFileProcessingConfig(
            force_document_category_key=DocumentCat.DIVORCE_MISC.name,
            force_title_elements=["ho", "ha", "hi"],
        ),
    ).run()


@pytest.mark.asyncio
async def test_three_pics_with_processing_params_no_split():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "split_complex",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=["001 DOK DE 35x_split_2_complex.pdf"],
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.PENSION_PAYMENT_AHV: 1,
                DocumentCat.PENSION_PAYMENT_BVG: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
        processing_config=OriginalFileProcessingConfig(
            semantic_document_splitting_style=SemanticDocumentSplittingStyle.NO_SPLITTING
        ),
    ).run()
