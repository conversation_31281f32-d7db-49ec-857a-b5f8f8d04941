from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "250528_slow_big_pdf"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS}/swissfex/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_swissfex_{FOLDER_TYPE}"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_slow_big_pdf():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=False,
        source_folder=path_root_folder,
        source_file_filter=["slow_big_pdf.pdf"],
        show_page=6,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # TODO: should be SALES_DOCUMENTATION but there is bank_fr in there
                # because spacy has the wrong language (en)
                DocumentCat.UNKNOWN_FR: 1
            }
        ),
    ).run()
