import asyncio
from pathlib import Path

from asyncizer.scripts_run.scripts_util import process_folder_of_dossier_folders
from constants import BASE_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM


async def main():
    root_folder = Path(
        f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/dossiers_april_2022"
    )
    dest_root_folder = Path(
        f"{BASE_DIR}/output/asyncizer_output_bekb_dossiers_april_2022"
    )
    await process_folder_of_dossier_folders(
        root_folder, dest_root_folder, use_ocr_cache=True, client_lang="fr"
    )


if __name__ == "__main__":
    asyncio.run(main())

    print("run_bekb_folder_of_folders done.")
