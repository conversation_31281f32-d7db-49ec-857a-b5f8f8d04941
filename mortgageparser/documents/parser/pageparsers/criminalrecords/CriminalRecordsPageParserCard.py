from abbyyplumber.converter.ValueConverter import (
    Ad<PERSON><PERSON>onverter,
    DateConverter,
    NameFromAddressConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementMultiLabeledField,
    SearchElementSetChooseFirst,
    SearchElementStaticText,
    SearchElementArea,
    SearchElementReference,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationRightOf,
    SearchRelationLeftOf,
)
from abbyyplumber.util.plumberstudio_util import FieldPosition, PercentageRange
from hypodossier.core.documents.criminalrecords.CriminalRecordsPageData import (
    CR_FIELD_CONFIRMATION_EMPTY,
)
from hypodossier.core.documents.criminalrecords.CriminalRecordsSemanticPageCreator import (
    CriminalRecordsSemanticPageCreator,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_ADDRESS_BLOCK,
    FIELD_DATE_OF_BIRTH,
    FIELD_DOCUMENT_DATE,
    FIELD_NATIVE_PLACE,
    FIELD_FULLNAME,
)
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings

import structlog

logger = structlog.getLogger(__name__)


class CriminalRecordsPageParserCard(AbstractPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ALL_LANGUAGES

    # Title that should be present on all pages
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "Bundesamt für Justiz",
                "Privatauszug aus dem",
                "Schweizerischen Strafregister",
                '"ist im Strafregister nicht verzeichnet"',
            ],
            hamming_dist=3,
        )
        return success

    def parse_page_header(self):
        success = self.page.set_header_by_any_text(
            [
                "Privatauszug aus dem Schweizerischen Strafregister",
                "Privatauszug aus dem Strafregister",
                "Extracto del registro suizo de antecedentes penales",
                "Excerpt from the swiss criminal record",
            ],
            offset_vertical=15 / 29,
        )
        if not success:
            success = self.page.set_header_by_percentage(0.4)
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text(
            "ist im Strafregister nicht verzeichnet", max_l_dist=8
        )
        if not success:
            success = self.page.set_footer_by_text(
                "ne figure pas au casier judiciare",
                offset_vertical=0.5 / 26,
                max_l_dist=8,
            )
            if not success:
                success = self.page.set_footer_by_text(
                    "3003 Bern", include_pattern=True, max_l_dist=4
                )
        return success

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page

        return ContentExtractor(
            [
                SearchElementStaticText(
                    "text_column_two",
                    page.main,
                    label="Luogo risp. paese d'origine",
                    extract=False,
                ),
                SearchElementArea(
                    FIELD_ADDRESS_BLOCK.name,
                    page.main,
                    x_range=PercentageRange(0.6, 1),
                    y_range=PercentageRange(0, 0.9),
                    converter=AddressConverter(),
                    relations=[SearchRelationRightOf("text_column_two")],
                ),
                SearchElementReference(
                    FIELD_FULLNAME.name,
                    ref=FIELD_ADDRESS_BLOCK.name,
                    converter=NameFromAddressConverter(),
                ),
                SearchElementMultiLabeledField(
                    FIELD_DATE_OF_BIRTH.name,
                    page.main,
                    labels={"Fecha de nacimiento": 4, "Geburtsdatum": 4},
                    field_position=FieldPosition.BELOW_LABEL,
                    field_vertical_line_scale=50,
                    converter=DateConverter(),
                    enabled=True,
                    relations=[SearchRelationLeftOf("text_column_two")],
                ),
                SearchElementMultiLabeledField(
                    FIELD_NATIVE_PLACE.name,
                    page.main,
                    labels={
                        "Pais resp. lugarde origen": 5,
                        "Heimatort bzw. -staat": 8,
                        "Lieu resp. pays d'origine": 8,
                    },
                    field_position=FieldPosition.BELOW_LABEL,
                    field_vertical_line_scale=50,
                    relations=[
                        SearchRelationRightOf("data_of_birth_anchor"),
                        SearchRelationLeftOf(FIELD_ADDRESS_BLOCK.name),
                    ],
                ),
                SearchElementMultiLabeledField(
                    FIELD_DOCUMENT_DATE.name,
                    page.footer,
                    labels={"ID": 0, "Ref.": 1},
                    field_pos_page_horizontal=PercentageRange(0.7, 1),
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_vertical_line_scale=5,
                    field_width=PercentageRange(0, 1),
                    converter=DateConverter(),
                ),
                SearchElementSetChooseFirst(
                    [
                        SearchElementStaticText(
                            CR_FIELD_CONFIRMATION_EMPTY.name,
                            page.footer,
                            label="ist im Strafregister nicht verzeichnet",
                        ),
                        SearchElementStaticText(
                            CR_FIELD_CONFIRMATION_EMPTY.name,
                            page.footer,
                            label="ne figure pas aua casier judiciare",
                        ),
                        SearchElementStaticText(
                            CR_FIELD_CONFIRMATION_EMPTY.name,
                            page.footer,
                            label="is not registered in the criminal record",
                        ),
                    ]
                ),
            ]
        )

    def parse(self) -> SemanticPage:
        self.create_content_extractor().find(self.page)
        return CriminalRecordsSemanticPageCreator(PageCat.CRIMINAL_RECORDS).create(self)


"""

        if success:
            v1 = contains_all_strings(text, ['Überprüfung/Verification', 'Verificazione/Validation'], hamming_dist=8)
            if not v1:
                v2 = contains_all_strings(text, ['Dieses elektronische Dokument', 'ist nur in elektronischer Form', 'gültig und überprüfbar.'], hamming_dist=6)
                return v2
            return True
            
            """
