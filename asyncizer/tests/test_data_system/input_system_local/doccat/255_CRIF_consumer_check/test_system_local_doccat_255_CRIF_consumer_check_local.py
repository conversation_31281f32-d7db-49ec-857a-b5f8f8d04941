from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

crif_folder = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/255_CRIF_consumer_check"
)
path_root_folder = Path(crif_folder)
dest_folder_prefix = "input_system_local_doccat_255_CRIF_consumer_check_"


@pytest.mark.asyncio
async def test_local_paths():
    source_folder = Path(f"{crif_folder}")
    assert (
        source_folder.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_crif_de():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "CRIF_DE",
        source_file_filter=["CRIF_DE.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={
                # was mistakenly categorized as tax decl. before
                DocumentCat.CRIF_QUICK_CONSUMER_CHECK: 1
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_crif_en():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "CRIF_EN",
        source_file_filter=["CRIF_EN.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={
                # was mistakenly categorized as tax decl. before
                DocumentCat.CRIF_QUICK_CONSUMER_CHECK: 1
            },
        ),
    ).run()
