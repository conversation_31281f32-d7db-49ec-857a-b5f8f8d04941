from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


def get_parsers_confirmation_residence():
    parsers = [
        TemplatePageParser(
            doc_cat=DocumentCat.CONFIRMATION_OF_RESIDENCE,
            page_cat=PageCat.GENERIC_SINGLE_PAGE,
            desc="BE Steuerverwaltung Ansässigkeit",
            ranked_titles_all=[
                RankedTitle("Ansässigkeitsbescheinigung", rank=1, min_length_title=8)
            ],
            required_text_conditions=[
                FromStartTextCond("Steuerverwaltung", num_lines=5),
            ],
            debug_breakpoint=False,
        ),
    ]
    return parsers
