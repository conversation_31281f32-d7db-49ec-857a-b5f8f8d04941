from abbyyplumber.converter.ValueConverter import DateConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementLabeledField,
    SearchElementArea,
)
from abbyyplumber.util.plumberstudio_util import FieldPosition
from mortgageparser.documents.parser.pageparsers.debtcollectioninformation.DebtCollectionInformationPageParser import (
    DebtCollectionInformationPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings


class DebtCollectionInformationPageOneJuraFRPageParser(
    DebtCollectionInformationPageParser
):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        if contains_all_strings(text, ["Office des poursuites"]):
            if contains_all_strings(
                text,
                [
                    "pas de poursuite en cours",
                    "pas débitrice d'acte de défaut de biens",
                    "pas été déclarée en faillite",
                ],
            ):
                return True

    def parse_page_header(self):
        return self.page.set_header_by_any_text(["Attestation"], max_l_dist=4)

    def parse_page_footer(self):
        return self.page.set_footer_by_percentage(6 / 27)

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        return ContentExtractor(
            [
                SearchElementLabeledField(
                    "addressline",
                    page.main,
                    label="Prénom / Nom:",
                    max_l_dist=5,
                    field_vertical_line_scale=3,
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                ),
                SearchElementArea(
                    "document_date", page.header, converter=DateConverter()
                ),
            ]
        )
