import shutil
from pathlib import Path

import pytest

from asyncizer.dossier_processor import unpack_path
from asyncizer.tests.util_tests import (
    DossierExpectations,
    DossierTest,
    configure_test_logging,
)
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.unpack import FileExtraction

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "221010_macos_zip"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_bekb_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/220929_softlaunch_check"
)


def test_unpack_msg():
    configure_test_logging()

    p = path_root_folder / "email" / "email.msg"
    assert p.exists()

    # Copy the file somewhere else because unpacking happens in-place
    p2 = p.parent.parent / "zip" / "zip_unpacked.msg"

    if p2.exists():
        shutil.rmtree(str(p2))

    p2.parent.mkdir(parents=True, exist_ok=True)
    shutil.copy(p, p2)

    file_extraction: FileExtraction = unpack_path(p2)

    assert file_extraction
    # assert len(file_extraction.extracted_files) == 12


@pytest.mark.asyncio
async def test_221007_signed_email_1():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email",
        # source_file_filter=['email.msg'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
