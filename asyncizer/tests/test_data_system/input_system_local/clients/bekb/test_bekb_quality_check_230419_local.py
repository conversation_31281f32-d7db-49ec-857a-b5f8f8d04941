from pathlib import Path

import pytest

from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.small_files_filter import create_small_files_filter

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "230419_processing_error"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_bekb_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_processing_error_single_page_ok():
    pc = OriginalFileProcessingConfig()
    pc.small_files_filter = create_small_files_filter({".pdf": 500})
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "34",
        # This file is a PDF with only 799 bytes but can be processed with pdftocairo
        source_file_filter=["34.pdf"],
        show_page=-1,
        processing_config=pc,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # This is a invalid PDF because this is not fulfilled:
            # "Horizontal and vertical resolution of the image must be equal."
            # But pdf2cairo makes a valid PDF out of it (white page).
            expected_doc_cat_frequency_per_page={DocumentCat.WHITE_PAGES: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_processing_error_single_page():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "34",
        # source_file_filter=["34.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # The small files filter removes the PDF so result is empty.
            expected_doc_cat_frequency_per_page={}
        ),
    ).run()


@pytest.mark.asyncio
async def test_processing_error_korrigiert():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["Docs_perso_1_55.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={}
        ),
    ).run()


@pytest.mark.asyncio
async def test_processing_error_korrigiert_2():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "perso_60_79",
        source_file_filter=["Docs_perso_60_79.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.UNKNOWN_FR: 1,
                DocumentCat.WHITE_PAGES: 1,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_processing_full_original():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "perso_full",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Page 34 is broken and fixed on the fly by pdftocairo
            expected_doc_cat_frequency={
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.SALARY_CERTIFICATE: 4,
                DocumentCat.PAYSLIP: 3,
                DocumentCat.WHITE_PAGES: 1,
            }
        ),
    ).run()
