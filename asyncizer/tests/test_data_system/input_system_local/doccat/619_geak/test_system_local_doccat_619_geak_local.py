from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

crif_folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/619_geak"
path_root_folder = Path(crif_folder)
dest_folder_prefix = "input_system_local_doccat_619_geak"


@pytest.mark.asyncio
async def test_local_paths():
    source_folder = Path(f"{crif_folder}")
    assert (
        source_folder.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_geak_fr():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder,
        source_file_filter=["619_geak.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.GEAK_CERTIFICATE: 1},
        ),
    ).run()
