from dataclasses import dataclass

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplateAgreementChargeImmovablePropertyPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.AGREEMENT_CHARGE_IMMOVABLE_PROPERTY
    # company: str = None

    se: GenericLetterSearchElements = None

    # def update_search_elements(self):
    #     super().update_search_elements_generic(self.se, FIELDS_BANK_ACCOUNT.keys())
    #
    #     if self.company:
    #         self.search_elements.append(SearchElementConstant(FIELD_COMPANY.name, self.company))


def get_parsers_agreement_charge_immovable_property():
    parsers = [
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift #1a/x 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            ranked_titles_all=[
                RankedTitle("Grundpfandvertrag"),
                RankedTitle("Errichtung"),
                RankedTitle("Schuldbrief"),
            ],
            required_text_conditions=[
                FromStartTextCond("Urschrift", num_lines=10),
            ],
            required_tokens_any=[
                [
                    "Errichtung eines Register-Schuldbriefes",
                    "Errichtung eines Schuldbriefes",
                ]
            ],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift #1b/x 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            required_text_conditions=[
                FromStartTextCond("Urschrift", num_lines=10),
                FromStartTextCond("Grundpfandvertrag", num_lines=10),
                FromStartTextCond("Errichtung", num_lines=10),
                FromStartTextCond("Schuldbrief", num_lines=10),
            ],
            required_tokens_any=[
                [
                    "Errichtung eines Register-Schuldbriefes",
                    "Errichtung eines Schuldbriefes",
                ]
            ],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift #1c/x 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            ranked_titles_all=[RankedTitle("Grundpfandvertrag", 2, 8)],
            required_text_conditions=[
                FromStartTextCond("Urschrift", num_lines=10),
            ],
            required_tokens_any=[["beurkundet"], ["Notar"]],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift #2a/x 2021",
            required_tokens_any=[
                [
                    "Im Grundbuch ist ein Höchstzinsfuss von 10% einzutragen",
                    "Im Grundbuch ist ein Maximalzinsfuss von 10% einzutragen",
                ],
                [
                    "Grundstücksbeschreibung",
                    "Für diesen Register-Schuldbrief",
                    "In diesen neu zu errichtenden Schuldbrief",
                    "Schuldbrief",
                ],
                ["Grundbuchblatt", "Grundbuchamt"],
            ],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift #2b/x 2021",
            required_tokens_any=[
                [
                    "Im Grundbuch ist ein Höchstzinsfuss",
                    "Im Grundbuch ist ein Maximalzinsfuss",
                ],
                [
                    "Zur Eintragung des hiervor begründeten Register-Schuldbriefs",
                    "Diese Urschrift",
                    "diese Urkunde",
                ],
                ["Notar"],
            ],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift x1/y 2021",
            required_text_conditions=[
                FromStartTextCond("Grundpfandvertrag", num_lines=3),
            ],
            required_tokens_any=[["Verurkundung", "Notar", "zweitausend"]],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift x2/y 2021",
            required_tokens_any=[
                ["wesentliche"],
                ["Unterbrechung"],
                ["Grundbuchamt"],
                ["Schuldner", "Gesuchsteller"],
                ["Notar"],
                ["tausend"],
            ],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift (second) last page 2021",
            required_tokens_any=[
                ["Schlussbestimmungen", "Schlussverbal"],
                ["Diese Urschrift ist für das Grundbuchamt"],
                ["Verurkundung"],
            ],
        ),
        TemplateAgreementChargeImmovablePropertyPageParser(
            desc="BE Grundpfand Urschrift Löschung 2021",
            required_tokens_any=[
                ["beurkundet"],
                ["Urschrift"],
                ["Notariatsregister"],
                ["Schuldbrief"],
                ["Grundbuch"],
            ],
        ),
    ]
    return parsers
