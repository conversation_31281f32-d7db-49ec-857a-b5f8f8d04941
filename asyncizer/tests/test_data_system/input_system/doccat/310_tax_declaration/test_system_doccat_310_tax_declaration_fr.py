from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, PO_EXISTS, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "310_tax_declaration"
source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}/cantons_fr"
)
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_be_double_fr_2021():
    """
    Page 3 (personal) is detected but has limited extraction in french.
    Page 5 (income) is detected, fixed an extraction problem.
    Pages 1, 2, 7, 8, 17, 18, 20 were UNKNOWN_FR.
    Page 10 is still UNKNOWN_FR as it has no BE token and is immaterial.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BE",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_be_double_fr_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 18,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2021",
                    "p1_fullname": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 41'415",
                    "p2_income_employed_main": "CHF 44'332",
                    "p1_income_employed_side": "CHF 32'321",
                    "p1_income_board_seats": "CHF 11'223",
                    "p2_income_pension_ahv": "CHF 4'115",
                    "p2_income_pension": "CHF 5'234",
                    "p2_income_eo": "CHF 43'643",
                    "p1_income_social_security": "CHF 3'636",
                    "p1_expense_employment": "CHF 2'314",
                    "p1_expense_employment_other": "CHF 3'454",
                    "p2_expense_employment_other": "CHF 2'000",
                    "p2_deductions_education": "CHF 87'879",
                    "deductions_illness": "CHF 50'000",
                    "deductions_donations": "CHF 32'314",
                    "assets_portfolio": "CHF 437'026",
                    "assets_other": "CHF 25'200",
                    "debt_total": "CHF 4'568",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_ju_double_2021():
    """
    TODO: OCR has a hard time with the little lines around the digits in combination with the digits 1, 7.
    Added extraction for personal, income, deductions, and assets.
    The whole document was not recognized as tax declaration.
    Pages 5, 9, 14, 18 were UNKNOWN_FR.
    Page 16 was misclassified as "FR/310/FR/Annexe 01 Wertschriftenverzeichnis Front".
    Pages 6, 7, 8, 10-13, 15 are still UNKNOWN_FR as they have no JU tokens and are immaterial.
    Page 19 is misclassified as "FR/310/BE/Zusammenzug TaxMe - Récapitulatif TaxMe".

    Many of the extracted values are wrong because OCR has problems with the formatting (and recognises too many "1")
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "JU",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ju_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 15,
                DocumentCat.TAX_CALCULATION: 2,
                DocumentCat.UNKNOWN_FR: 4,
            },
            expected_page_objects={
                "0": {
                    # OCR has a hard time with the little lines around the digits in combination with the digits 1, 7.
                    "canton_short": "JU",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8000",
                    "p1_fullname": PO_EXISTS,
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_employer": "Arbeitgeber 1",
                    "p2_employer": "Arbeitgeber 2",
                    "section_children": PO_EXISTS,
                    # The raw value for p1_income_employed_main is "j[4 2jJ 9 8".
                    # "p1_income_employed_main": "CHF 4'298",   # should be 42'198
                    "p2_income_employed_main": "CHF 43'204",
                    "p1_income_self_main": "CHF 45'023",
                    "p2_income_board_seats": "CHF 4'308",
                    "p2_income_social_security": "CHF 2'342",
                    "p1_income_gross_total": "CHF 215'697",
                    "p2_income_gross_total": "CHF 52'759",
                    "income_portfolio": "CHF 20'660",
                    "income_undistributed_inheritances": "CHF 32'535",
                    "income_real_estate_net_primary": "CHF 18'828",
                    # "expense_children_daycare": "CHF 7",   # should be blank
                    # "p1_contribution_pillar_1": "CHF 341'232",   # should be 34'232
                    "p2_contribution_pillar_1": "CHF 325",
                    # "p1_contribution_pillar_3a": "CHF 1'532",   # should be 532
                    "p2_contribution_pillar_3a": "CHF 325",
                    "p1_expense_employment": "CHF 4'876",
                    "p2_expense_employment": "CHF 3'849",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 0",
                    "interest_paid_on_debt": "CHF 4'115",
                    "income_net_total": "CHF 217'702",
                    "income_taxable_local": "CHF 208'802",
                    # "assets_portfolio": "CHF 1'955'543",   # should be 955'543
                    # "assets_cars": "CHF 42",   # should be 4'214
                    "assets_real_estate_main_property": "CHF 3'535",
                    "assets_gross_total": "CHF 1'183'774",
                    # "assets_net_total": "CHF 118'056",   # should be 1'180'561
                    # "assets_taxable_local": "CHF 109'956",  # should be 1'099'561
                    # "debt_private": "CHF 31'213",   # should be 3'213
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_ne_double_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "NE",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ne_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 21,
                DocumentCat.UNKNOWN_FR: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "NE",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p1_fullname": PO_EXISTS,
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    # date of birth has a special format, for which no parser exists yet
                    "p1_profession": "Job 1",
                    "p2_profession": "Job2",
                    # slight vertical offset and dotted lines cause problems
                    "p1_employer": "Ar.b®ltg.®b®r..1",
                    "p2_employer": "Arb®!tg®.b.®r. 2",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 32'197",
                    "p1_income_employed_side": "CHF 387",
                    "p1_income_self_main": "CHF 43'245",
                    "p1_income_self_side": "CHF 35'315",
                    "p1_income_social_security": "CHF 2'435",
                    "p2_income_employed_main": "CHF 31'207",
                    "p2_income_employed_side": "CHF 32'139",
                    "p2_income_self_main": "CHF 41'241",
                    "p2_income_social_security": "CHF 31'414",
                    "income_portfolio": "CHF 16'517",
                    "income_gross_total": "CHF 279'202",
                    "p1_expense_employment": "CHF 6'400",
                    "income_net_total": "CHF 268'815",
                    "income_taxable_local": "CHF 262'815",
                    "assets_portfolio": "CHF 763'902",
                    "assets_life_insurance": "CHF 12'341",
                    "assets_gross_business": "CHF 41'241",
                    "assets_gross_total": "CHF 817'484",
                    "assets_net_total": "CHF 817'484",
                    "assets_taxable_local": "CHF 817'484",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_vs_double_fr_2021():
    """
    Reworked the extraction personal and added address block.
    Page 7 is still UNKNOWN_FR --> already added to Golden FR/310/CH/Misc.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "VS",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_vs_double_fr_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_ASSESSMENT: 6,
                DocumentCat.TAX_CALCULATION: 4,
                DocumentCat.TAX_DECLARATION: 22,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "VS",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_lastname": PO_EXISTS,
                    # AHV numbers are not extracted although the regions are recognized
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_profession": "Job 1",
                    "p2_profession": "Job 2",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 1'245",
                    "p1_income_self_main": "CHF 552'537",
                    "p1_income_pension_ahv": "CHF 4'124",
                    "p2_income_self_main": "CHF 322'026",
                    "p2_income_pension": "CHF 52'353",
                    "income_portfolio": "CHF 0",
                    "income_real_estate_gross": "CHF 64'623",
                    "p1_deductions_education": "CHF 1'245",
                    "p2_deductions_education": "CHF 2'000",
                    "deductions_total": "CHF 9'881",
                    "income_net_total": "CHF 1'596'566",
                    "income_taxable_local": "CHF 981'709",
                    "assets_portfolio": "CHF 210'903",
                    "assets_cars": "CHF 734",
                    "assets_gross_total": "CHF 685'853",
                    "assets_taxable_local": "CHF 90'720",
                    "debt_private": "CHF 535'133",
                    "debt_total": "CHF 595'133",
                    "interest_paid_on_debt": "CHF 6'425",
                }
            },
        ),
    ).run()
