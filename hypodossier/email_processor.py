# For handling email files
import email
import logging
import os
import queue
import re
import tempfile
import time
from datetime import datetime
from email.header import make_header, decode_header
from pathlib import Path
from typing import Optional, Union, Tuple
from uuid import uuid4

import chardet
import extract_msg  # For .msg files
import mailparser
import structlog
from bs4 import BeautifulSoup
from pypdf import PdfReader

# For PDF generation
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from weasyprint import HTML  # For HTML to PDF conversion

from constants import EXTRACTED_EMAIL_FILENAME_PREFIX
from global_settings import (
    MAX_NUM_PAGES_EMAIL_CONTENT_TO_PDF,
    EMAIL_CONTENT_TO_PDF_PROCESSING_TIMEOUT_SECONDS,
)

import multiprocessing
from typing import Any


# If email content url is not available (e.g. no network access)
# suppress the error message
class IgnoreCIDURLErrorFilter(logging.Filter):
    def filter(self, record):
        return not (
            "Failed to load image at 'cid:" in record.getMessage()
            and "URLError: <urlopen error unknown url type: cid>" in record.getMessage()
        )


weasyprint_logger = logging.getLogger("weasyprint")
weasyprint_logger.addFilter(IgnoreCIDURLErrorFilter())
weasyprint_logger.setLevel(logging.ERROR)

logging.getLogger("extract_msg").setLevel(logging.ERROR)
logging.getLogger("fontTools").setLevel(logging.ERROR)

logger = structlog.getLogger(__name__)


def extract_email_content(file_path: Path) -> dict:
    """Extract content from .msg or .eml file"""
    file_ext = file_path.suffix.lower()
    if file_ext == ".msg":
        return extract_msg_content(str(file_path))
    elif file_ext == ".eml":
        return extract_eml_content(str(file_path))
    else:
        raise ValueError(f"Unsupported file extension: {file_ext}")


def remove_img_tags(html: str) -> str:
    soup = BeautifulSoup(html, "html.parser")
    for img in soup.find_all("img"):
        img.decompose()  # completely removes the tag
    return str(soup)


MAX_LENGTH_SUPPORTED_HTML_BODY = 48000


def get_html_body_which_can_hang_indefinitely(q: queue.Queue, msg_file_path: Path):
    # This may hang and not return at all if the msg is not correct.
    # so we need to call this with a timeout.
    msg = None
    try:
        msg = extract_msg.Message(msg_file_path)
        html_body = msg.htmlBodyPrepared

        if html_body and isinstance(html_body, bytes):
            html_body = fix_html_encoding(html_body)

        html_body = str(html_body)

        len_html_body = len(html_body)
        logger.info("Found html body", len_html_body=len_html_body)

        # There is a limit to how much content the queue can return. If we put
        # in a string that is too long it will hang until the timeout is triggered.
        # Some html bodies are very long (e.g. 700kb) because of embedded images
        # Solution: try to remove images. If below 20k then use it else return
        # no html so the plain text version should be used.
        if len(html_body) > MAX_LENGTH_SUPPORTED_HTML_BODY:
            html_body = remove_img_tags(html_body)
            len_html_body_after_images = len(html_body)
            logger.info(
                "html body after removal of images",
                len_html_body=len_html_body,
                len_html_body_after_images=len_html_body_after_images,
            )
        else:
            len_html_body_after_images = len_html_body

        if len_html_body_after_images <= MAX_LENGTH_SUPPORTED_HTML_BODY:
            q.put((True, html_body))
        else:
            short_html_body = (
                html_body[0:MAX_LENGTH_SUPPORTED_HTML_BODY] if html_body else None
            )
            logger.warning(
                "html body was too long for queue. msg cannot be transformed to pdf",
                short_html_body=short_html_body,
                len_html_body_after_images=len_html_body_after_images,
            )
            # Do not return any html
            q.put((False, None))
        logger.info("Pushed html body to queue", len_html_body=len_html_body)
        msg.close()
        time.sleep(0.1)  # make sure everything is written to queue
        # # This is a known issue with extract_msg — internally
        # # it uses OLE / COM unpackers, which on some.msg files:
        # os._exit(0)
    except Exception as e:
        q.put((False, e))
        if msg:
            msg.close()


def run_with_timeout_process(
    timeout: float, func, *args, **kwargs
) -> Tuple[bool, Optional[Any]]:
    """
    Executes a function in a separate process and enforces a timeout.
    This uses a queue to return the result. This hangs indefinitely if the
    returned content is too large (something larger than 20k bytes but we
    do not know how large)

    :param timeout: Timeout in seconds
    :param func: The function to run
    :param args: Positional arguments passed to func
    :param kwargs: Keyword arguments passed to func
    :return: (success: bool, result_or_exception: Any)
    """
    q = multiprocessing.Queue()
    p = multiprocessing.Process(target=func, args=(q, *args), kwargs=kwargs)
    p.start()
    p.join(timeout)

    if p.is_alive():
        p.terminate()
        p.join()
        return False, None

    if q.empty():
        return True, None

    success, result = q.get()
    if not success:
        if result:
            raise result  # Re-raise the exception
        else:
            raise TimeoutError("run_with_timeout_process: exception raised")
    return True, result


def extract_msg_content(
    msg_file_path: str, timeout: float = EMAIL_CONTENT_TO_PDF_PROCESSING_TIMEOUT_SECONDS
) -> dict:
    """Extract content from .msg file"""
    msg = extract_msg.Message(msg_file_path)

    # Attempt to extract body content with proper decoding
    body_text = msg.body
    if isinstance(body_text, bytes):
        body_text = body_text.decode("utf-8", errors="replace")

    # Add timeout for htmlBody extraction because this can hang
    try:
        success, html_body = run_with_timeout_process(
            timeout, get_html_body_which_can_hang_indefinitely, msg_file_path
        )

        if not success:
            logger.warning(
                f"Failed to extract html content from msg file. Probably msg file not with correct syntax. has_plain_text_body={body_text is not None and body_text != ''}",
                msg_file_path=msg_file_path,
            )
    except TimeoutError:
        # Handle this gracefully, we just use the plain body.
        html_body = None

    # Extract metadata
    metadata = {
        "sender": msg.sender,
        "recipient": msg.to,
        "subject": msg.subject,
        "date": msg.date,
    }

    # Extract attachments
    attachments = []
    for attachment in msg.attachments:
        attachments.append(
            {"filename": attachment.longFilename, "data": attachment.data}
        )

    return {
        "metadata": metadata,
        "body": body_text,
        "html": html_body,
        "is_html": bool(html_body),
        "attachments": attachments,
    }


def sanitize_filename(filename):
    """
    Removes invalid characters from a filename.

    Args:
      filename: The original filename string.

    Returns:
      The sanitized filename string.
    """
    # The regex matches any of the characters: \ / : * ? " < > |
    # re.sub replaces all occurrences of the matched characters with an empty string.
    sanitized_filename = re.sub(r'[\\/:*?"<>|]', "", filename)
    return sanitized_filename


def is_nested_eml_file(file_path: str) -> bool:
    try:
        mail = mailparser.parse_from_file(file_path)
        if not mail.body:
            return False
        return "\n\n--- mail_boundary ---\n\n" in mail.body
    except AttributeError as e:
        # Known bug: payload is None
        logger.info(f"Failed to parse email at {file_path}: {e}")
        return False
    except Exception as e:
        logger.warning(f"Unexpected error parsing email at {file_path}: {e}")
        return False


def extract_eml_content(eml_file_path: str) -> dict:
    """Extract content from .eml file"""
    with open(eml_file_path, "rb") as f:
        msg = email.message_from_binary_file(f)

    is_nested = is_nested_eml_file(eml_file_path)

    # Helper function to decode MIME-encoded headers
    def decode_mime_header(header_value: Optional[str]) -> str:
        if not header_value:
            return ""
        return str(make_header(decode_header(header_value)))

    # Extract metadata with proper decoding
    metadata = {
        "sender": decode_mime_header(msg.get("From", "")),
        "recipient": decode_mime_header(msg.get("To", "")),
        "subject": decode_mime_header(msg.get("Subject", "")),
        "date": decode_mime_header(msg.get("Date", "")),
    }

    # Extract body content and attachments
    body_text = ""
    html_body = None
    attachments = []

    for part in msg.walk():
        content_type = part.get_content_type()
        disposition = str(part.get("Content-Disposition", ""))

        # Attempt extract text/plain body with better encoding handling
        if content_type == "text/plain" and "attachment" not in disposition:
            body_part = part.get_payload(decode=True)
            if body_part:
                # Get the charset from the Content-Type header
                charset = part.get_content_charset()
                if charset is None:
                    # If no charset specified, try to detect it
                    # Common email encodings in order of likelihood
                    for encoding in ["utf-8", "latin-1", "ascii", "windows-1252"]:
                        try:
                            body_text = body_part.decode(encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                else:
                    # Use the specified charset with a fallback
                    try:
                        body_text = body_part.decode(charset)
                    except (UnicodeDecodeError, LookupError):
                        # If specified charset fails, fallback to latin-1
                        body_text = body_part.decode("latin-1", errors="replace")
            if is_nested:
                break

        # Attempt extract HTML body with better encoding handling
        elif content_type == "text/html" and "attachment" not in disposition:
            html_part = part.get_payload(decode=True)
            if html_part:
                charset = part.get_content_charset()
                if charset is None:
                    for encoding in ["utf-8", "latin-1", "ascii", "windows-1252"]:
                        try:
                            html_body = html_part.decode(encoding)
                            break
                        except UnicodeDecodeError:
                            continue
                else:
                    try:
                        html_body = html_part.decode(charset)
                    except (UnicodeDecodeError, LookupError):
                        html_body = html_part.decode("latin-1", errors="replace")

            if is_nested:
                break

    # Extract attachments
    for part in msg.walk():
        disposition = str(part.get("Content-Disposition", ""))
        if "attachment" in disposition or "inline" in disposition:
            filename = part.get_filename()
            if filename:
                attachments.append(
                    {"filename": filename, "data": part.get_payload(decode=True)}
                )

    return {
        "metadata": metadata,
        "body": body_text,
        "html": html_body,
        "is_html": bool(html_body),
        "attachments": attachments,
    }


def create_email_body_filename(content_hash=None):
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    id_email = str(uuid4())[:5]  # use 5 random chars from a uuid

    # Base filename without hash
    base_filename = f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_{timestamp}"

    # If content hash is provided, append it to the filename
    if content_hash:
        # Use the full 64-character SHA-256 hash for maximum uniqueness
        pdf_filename = f"{base_filename}_{content_hash}.pdf"
    else:
        pdf_filename = f"{base_filename}_{id_email}.pdf"

    # Ensure filename doesn't exceed max length (typically 255 chars on most filesystems)
    max_length = 240  # Leave some room for path
    if len(pdf_filename) > max_length:
        # Truncate the base part of the filename, but keep the hash if present
        if content_hash:
            # Preserve the hash portion
            hash_part = f"_{content_hash}.pdf"
            base_part = pdf_filename[: -len(hash_part)]
            truncated_base = base_part[: max_length - len(hash_part)]
            pdf_filename = truncated_base + hash_part
        else:
            # Simple truncation
            pdf_filename = pdf_filename[: max_length - 4] + ".pdf"

    return pdf_filename


def convert_email_to_pdf(email_content: dict, output_path: Path) -> bool:
    """
    Convert HTML email to PDF

    Args:
        email_content (dict): Dictionary containing email metadata and HTML content
        output_path (str): Path where the PDF should be saved

    Returns:
        str: Path to the generated PDF file

    Raises:
        ValueError: If the generated PDF exceeds 50 pages
    """
    created = False
    if email_content["is_html"]:
        if email_content["html"]:
            # This is an html email
            created = html_to_pdf(email_content, output_path)
    else:
        # This is a plain text email
        created = text_to_pdf(email_content, output_path)

    if created:
        if not output_path.exists():
            raise ValueError(
                f"Could not create email body PDF for email_content={email_content}, output_path={output_path}"
            )
        # Check the number of pages in the generated PDF
        reader = PdfReader(output_path)
        num_pages = len(reader.pages)
        if num_pages > MAX_NUM_PAGES_EMAIL_CONTENT_TO_PDF:
            # Delete the output file if it exceeds the maximum pages
            try:
                os.remove(output_path)
                return False
            except:
                ...
            raise ValueError(
                f"Generated PDF has {num_pages} pages, which exceeds the maximum allowed 50 pages."
            )

    return created


def escape_html(text: Optional[str]) -> Optional[str]:
    """Escape HTML special characters in text"""
    if not text:
        return text
    return (
        str(text)
        .replace("&", "&amp;")
        .replace("<", "&lt;")
        .replace(">", "&gt;")
        .replace('"', "&quot;")
        .replace("'", "&#39;")
    )


def detect_encoding(text: Union[str, bytes]) -> str:
    """Detect the encoding of text"""
    if isinstance(text, str):
        # Convert string to bytes for detection
        text = text.encode()

    # Detect encoding
    result = chardet.detect(text)
    return result["encoding"]


def text_to_pdf(email_content: dict, output_path: Path) -> bool:
    """Convert plain text email to PDF"""

    body = email_content["body"]
    if not body:
        # Don't create a PDF if all content is empty
        return False

    pdfmetrics.registerFont(TTFont("DejaVuSans", "DejaVuSans.ttf"))
    pdfmetrics.registerFont(TTFont("DejaVuSans-Bold", "DejaVuSans-Bold.ttf"))

    doc = SimpleDocTemplate(str(output_path), pagesize=A4, encoding="utf-8")
    styles = getSampleStyleSheet()
    normal_style = ParagraphStyle(
        "CustomNormal", fontName="DejaVuSans", fontSize=10, parent=styles["Normal"]
    )
    story = []

    # Add metadata header only for non-empty fields
    metadata = email_content["metadata"]
    if metadata.get("sender"):
        story.append(
            Paragraph(f"<b>From:</b> {escape_html(metadata['sender'])}", normal_style)
        )
    if metadata.get("recipient"):
        story.append(
            Paragraph(f"<b>To:</b> {escape_html(metadata['recipient'])}", normal_style)
        )
    if metadata.get("subject"):
        story.append(
            Paragraph(
                f"<b>Subject:</b> {escape_html(metadata['subject'])}", normal_style
            )
        )
    if metadata.get("date"):
        story.append(
            Paragraph(f"<b>Date:</b> {escape_html(metadata['date'])}", normal_style)
        )

    # Only add divider if we have metadata or body
    if (
        metadata.get("sender")
        or metadata.get("recipient")
        or metadata.get("subject")
        or metadata.get("date")
        or body
    ):
        story.append(Spacer(1, 12))
        story.append(Paragraph("<hr/>", normal_style))
        story.append(Spacer(1, 12))

    # Add body text if not empty
    if body:
        for line in body.split("\n"):
            # Escape HTML special characters and ensure non-empty lines
            escaped_line = escape_html(line) or "&nbsp;"
            story.append(Paragraph(escaped_line, normal_style))
            story.append(Spacer(1, 6))

    # Only build the document if we have content
    if story:
        doc.build(story)
        return True

    return False


def extract_html_body_content(html_content):
    """Extract just the body content from a complete HTML document"""
    try:
        soup = BeautifulSoup(html_content, "html.parser")

        # Extract styles from the original HTML
        styles = ""
        style_tags = soup.find_all("style")
        for style in style_tags:
            styles += style.string if style.string else ""

        # Extract body content
        body = soup.body
        if body:
            # Get any conditional comments
            html_str = str(html_content)
            conditional_comments = re.findall(
                r"<!--\[if.*?\]>.*?<!\[endif\]-->", html_str, re.DOTALL
            )

            return {
                "body_content": "".join(str(tag) for tag in body.contents if tag),
                "styles": styles,
                "conditional_comments": "\n".join(conditional_comments),
            }
        return None
    except Exception:
        # If parsing fails, assume it's not a complete HTML document
        return None


def fix_html_encoding(html_bytes: bytes) -> str:
    """
    Fix encoding issues in HTML extracted from .msg files by re-decoding
    using the declared charset (e.g., iso-8859-1) if necessary.
    """
    # Try to find charset from meta tag
    soup = BeautifulSoup(html_bytes, "html.parser")
    charset = "utf-8"  # default fallback

    meta = soup.find("meta", attrs={"http-equiv": "Content-Type"})
    if meta and "charset=" in meta.get("content", ""):
        charset = meta["content"].split("charset=")[-1].split(";")[0].strip().lower()

    # decode correctly but best effort
    try:
        html = html_bytes.decode(charset, errors="replace")
    except (LookupError, UnicodeDecodeError):
        html = str(html_bytes)  # fallback: return as-is

    return html


def html_to_pdf(email_content: dict, output_path: Path) -> bool:
    """
    Convert HTML email to PDF
    """
    metadata = email_content["metadata"]

    header_line_fields = [
        ("From", "sender"),
        ("To", "recipient"),
        ("Subject", "subject"),
        ("Date", "date"),
    ]
    header_lines = {}
    for title, key in header_line_fields:
        header_lines[key] = ""
        if key in metadata:
            val = metadata[key]
            if val:

                if key in ["sender", "recipient"]:
                    # Ensure email addresses are properly displayed by using text content instead of raw HTML
                    val = val.replace("<", "&lt;").replace(">", "&gt;")
                header_lines[key] = f"<p><b>{title}:</b> {val}</p>"

    html = email_content["html"]

    # Parse the HTML and remove any outer <html>, <head>, and <body> tags if present
    soup = BeautifulSoup(html, "html.parser")
    if soup.body:
        html_body = (
            soup.body.decode_contents()
        )  # Extract only the content inside the <body>
    else:
        html_body = str(soup)  # Use the whole content if no <body> tag is found

    # Create a complete HTML document with metadata header
    subject = metadata["subject"] if "subject" in metadata else ""
    html_content = f"""<!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <title>{subject}</title>
          </head>
          <body>
            <div class="email-header">"""

    for line in header_lines.values():
        if line:
            html_content += f"{line}\n"

    html_content += f"""
              &nbsp;<br/>
            </div>
            <div class="email-body">
              {html_body}
            </div>
          </body>
        </html>"""

    # Create a temporary HTML file
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_html_path = os.path.join(temp_dir, "temp.html")
        # Write HTML content to file
        with open(temp_html_path, "wb") as f:
            f.write(html_content.encode("utf-8"))
        # Convert HTML to PDF
        HTML(filename=temp_html_path).write_pdf(output_path)

    return True
