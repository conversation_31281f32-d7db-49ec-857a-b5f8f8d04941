from dataclasses import dataclass
from enum import Enum
from typing import Dict

from hypodossier.core.domain.DocumentCatElement import I18NElement
from hypodossier.core.domain.PageData import StandardPageData
from hypodossier.core.domain.SemanticField import (
    fields_as_dict,
    FIELD_FULLNAME,
    FIELD_DOCUMENT_DATE,
    SemanticField,
    ExtractionType,
    FieldTitles,
    FIELD_DATE_OF_BIRTH,
    FIELD_FIRSTNAME,
    FIELD_ZIP,
    FIELD_CITY,
    SemanticFieldStatus,
)

ADDRESSLINE_MIN_LENGTH = 10

ADDRESSLINE_UNKNOWN = "ADR_UNKNOWN"


class DebtCollectionStatus(Enum):
    EMPTY = I18NElement(
        "Keine Betreibungen", "No Entry", "aucune entrée", "nessuna entrata"
    )
    NOT_EMPTY = I18NElement(
        "Mit Betreibungen", "With Entries", "avec entrées", "con entrate"
    )
    UNKNOWN = I18NElement(
        "Unbekannter Status", "Unknown Status", "statut inconnu", "stato sconosciuto"
    )

    def trans(self, lang: str = "de"):
        return self.value.trans(lang)


def extract_name_from_addressline(addressline):
    n = addressline
    if n.startswith("Herr "):
        n = n[5:]
    if "," in n:
        return n.split(",")[0].strip()


DCI_FIELD_CONFIRMATION_NOT_EMPTY = SemanticField(
    "confirmation_not_empty",
    ExtractionType.STRING,
    FieldTitles("Mit Betreibungen", "With Entries", "avec entrées", "con entrate"),
    desc_de="Feld, das nur gesetzt ist, wenn Betreibungen vorliegen. Wert ist dann der gefundene Text",
    status=SemanticFieldStatus.INFO_ONLY,
)

DCI_FIELD_CONFIRMATION_EMPTY = SemanticField(
    "confirmation_empty",
    ExtractionType.STRING,
    FieldTitles("Keine Betreibungen", "No Entry", "aucune entrée", "nessuna entrata"),
    desc_de="Feld, das nur gesetzt ist, wenn keine Betreibungen vorliegen. Wert ist dann der gefundene Text",
    status=SemanticFieldStatus.INFO_ONLY,
)
DCI_FIELD_ADDRESS_LINE = SemanticField(
    "addressline",
    ExtractionType.STRING,
    FieldTitles("Name und Adresse", "Name and Address"),
    desc_de="Adresszeile, wie sie in der Betreibungsauskunft formuliert ist; mit/ohne Geburtsdatum und Adresse, z.B. 'Max Mustermann, Geb. Dat 31.12.1999, Beispielstrasse 7, 8001 Zürich'",
    status=SemanticFieldStatus.INFO_ONLY,
)
DCI_FIELD_ADDRESS_LINE_FULLNAME = SemanticField(
    "addressline_fullname",
    ExtractionType.STRING,
    FieldTitles("Name", "Name"),
    desc_de="Name, wie er aus der Addresszeile extrahiert wurde, z.B. 'Max Mustermann'",
)

DCI_FIELD_STATUS = SemanticField(
    "status",
    ExtractionType.OBJECT,
    FieldTitles("Status", "Status", "État", "Stato"),
    desc_de="Status der Betreibungsauskunft, liegt für jede Betreibungsauskunft vor. Ein Wert aus 'DebtCollectionStatus.EMPTY' / 'DebtCollectionStatus.NOT_EMPTY' / 'DebtCollectionStatus.UNKNOWN'",
)

FIELDS_DEBT_COLLECTION_INFORMATION = fields_as_dict(
    [
        DCI_FIELD_STATUS,
        FIELD_DOCUMENT_DATE,
        DCI_FIELD_ADDRESS_LINE_FULLNAME,
        FIELD_DATE_OF_BIRTH,
        FIELD_FULLNAME,
        FIELD_FIRSTNAME,
        FIELD_ZIP,
        FIELD_CITY,
        DCI_FIELD_ADDRESS_LINE,
        DCI_FIELD_CONFIRMATION_EMPTY,
        DCI_FIELD_CONFIRMATION_NOT_EMPTY,
    ]
)


@dataclass
class DebtCollectionPageData(StandardPageData):
    #  Best effort firstname - first that is found on the page
    firstname: str = None

    # Best effort fullname from addressline. This would be the best source for the name
    addressline_fullname: str = None

    # A single line String that contains Name and Address and optionally date of birth
    # Target of this extract
    addressline: str = None

    # Best effort from addressline
    date_of_birth: str = None

    # Address as multi line string that this extract is sent to (either same as addressline or e.g. Cresura)
    # address_block: str = None

    # Standard fullname as extracted from address
    fullname: str = None

    status: DebtCollectionStatus = None

    # This should contain some text which confirms that the person has no entries in the debt register
    confirmation_empty: str = None

    # Some text that confirms that there are entries in the debt register
    confirmation_not_emtpy: str = None

    # Amount of debt or total sum of items that are on the list
    amount: float = -1

    address: str = None
    street: str = None
    zip: str = None
    city: str = None

    def get_invalidity_reason(self):
        msg = None
        # do not check for confirmation_empty here as it won't be there if not empty
        if not self.addressline and not self.address:
            msg = "addressline is missing"
        elif not self.status:
            msg = "status (empty/not empty) is missing"
        else:
            if self.addressline:
                if len(self.addressline) > 10:
                    self.addressline = self.addressline.replace("\n", ", ").strip()
                else:
                    msg = f"address line is shorter than {ADDRESSLINE_MIN_LENGTH}."
            if self.address:
                if len(self.address) <= 10:
                    msg = f"address is shorter than {ADDRESSLINE_MIN_LENGTH}."
        return msg

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        # d = super().get_semantic_fields()
        # return {**d, **FIELDS_DEBT_COLLECTION_INFORMATION}
        return FIELDS_DEBT_COLLECTION_INFORMATION
