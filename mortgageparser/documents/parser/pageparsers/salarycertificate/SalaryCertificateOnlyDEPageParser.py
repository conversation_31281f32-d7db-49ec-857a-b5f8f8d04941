"""
 X Lohnausweis
a
 Rentenbescheinigung
b
C 756.2222.3333.44 Unentgeltliche Beförderung zwischen Wohn- und Arbeitsort
AHV-Nr
D 2015  E 01.01.15  31.12.15 Q  Kantinenverpflegung/Lunch-Checks
<PERSON><PERSON><PERSON>  von  bis
H
Herr
MAAD001 LASTNAME FIRSTNAME
El. Ing. FH
STREET
ZIP CITY
CHF
123710
1. <PERSON>hn  soweit nicht unter Ziffer 2-7 aufzuführen
2  . Gehaltsnebenleistungen 2.1 Verpflegung. Unterkunft_________________________________
2.2  Privatanteil Geschäftswagen______________________________________________________
2.3  Andere
3  . Unregelmässige Leistungen
4  Kapitalleistungen
5  Beteiligungsrechte gemäss Beiblatt
6.yerwaltungsratsentschadigjngen
7  Andere Leistungen
123710
8  Bruttolohn Total _____________________________________________________________________________________ =
-8491
9.  Beiträge AHV/IV/EO/ALV/NBUV•
-5513
10.  Berufliche Vorsorge 2. Säule 10.1 Ordentliche Beiträge -
10.2 Beiträge für den Einkauf •
109704
11.  Nettolohn________________________________________________________________________________________________________=
in die Steuererklärung übertragen
12.  Quellensteuerabzug _________________________________________________________________________________________
13.  Spesenvergütungen
Nicht im Bruttolohn (gemäss Ziffer 8) enthalten
13.1 Effektive Spesen 13.1.1 Reise. Verpflegung. Übernachtung X
13.1.2 Übrige
13.2 Pauschalspesen 13.2.1 Repräsentation
13.2.2 Auto___________________________________________________________
13.2.3 Übrige
13.3 Beiträge an die Weiterbildung___________________________________________________________________________________
14. Weitere Gehaltsnebenleistungen
15 Bemerkungen
Beitrag AN an KTGV 493
I Ort und Datum Die Richtigkeit und Vollständigkeit bestätigt  COMPANY_NAME
mki. Genauer Anschnft und Telefonnummer des Arbeitgebers COMPANY_STREET
COMPANY_ZIP COMPANY_CITY. 21.01.2016
COMPANY_TEL
"""

from dataclasses import dataclass

from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.PageLayoutInfo import PageLayoutInfo
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from hypodossier.core.domain.SemanticPage import SemanticPage
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    SemanticPageCreator,
)
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardPageParser,
)
from mortgageparser.util.search_element_util import create_search_elements_address
from mortgageparser.util.string_utils import (
    contains_string,
    contains_all_strings,
    contains_at_least_one_string,
)


@dataclass
class SalaryCertificateOnlyDEPageParser(StandardPageParser):
    doc_cat: DocumentCat = DocumentCat.SALARY_CERTIFICATE
    page_cat: PageCat = PageCat.SALARY_CERTIFICATE

    def match_page_by_titles(self, page_layout_info: PageLayoutInfo, test: str) -> bool:
        titles_2 = page_layout_info.get_titles_by_size(2)
        if len(titles_2) >= 2:
            if contains_string(titles_2[0], "Lohnausweis"):
                if contains_string(titles_2[1], "Rentenbescheinigung"):
                    return True
        return False

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text, ["Gehaltsnebenleistungen", "Bruttolohn Total", "Beteiligungsrechte"]
        ) and not contains_at_least_one_string(
            text, ["Attestation de rentes", "Attestazione", "Salaire net/Rente"]
        )

    def parse(self) -> SemanticPage:
        self.create_content_extractor().find(self.page)
        return SemanticPageCreator(self.page_cat).create(self, self.doc_cat)

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.fullpage,
                    text_top="Kantinenverpflegung",
                    text_bottom="soweit nicht unter Ziffer",
                )
            ]
            + create_search_elements_address(self.page.fullpage, self.page)
        )
