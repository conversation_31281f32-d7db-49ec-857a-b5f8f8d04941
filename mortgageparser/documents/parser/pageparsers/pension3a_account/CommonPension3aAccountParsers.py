from dataclasses import dataclass

from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.CommonParsersDE import (
    token_list_relevant_salary,
)
from mortgageparser.util.search_element_util import (
    create_document_date_most_recent,
    create_search_elements_address,
)
from mortgageparser.util.string_utils import contains_at_least_one_string


@dataclass
class SmartPension3aAccountParser(SmartLetterPageParser):
    page_cat: PageCat = PageCat.PENSION3A_ACCOUNT_STATEMENT


# 420
class CommonPension3aAccountParser(StandardLetterPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.PENSION3A_ACCOUNT_STATEMENT
        self.doc_cat = DocumentCat.PENSION3A_ACCOUNT

    # There are cases where this matches, but it is acutally a page 2
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_at_least_one_string(
            text,
            [
                "Vorsorgeausweis",
                "Vorsorge - Ausweis",
                "Pensionskassenausweis",
                "Versicherungsausweis",
                "Versicherungsbescheinigung",
            ],
        )

        if success:
            count = 0
            if contains_at_least_one_string(
                text,
                [
                    "Versicherten-Nr.",
                    "Versichertennummer",
                    "Versicherungs-Nr.",
                    "Versicherungsnummer",
                    "Personalnummer",
                    "Sozialversicherungs-Nr",
                    "AHV-Vers-Nr",
                    "AHV-Nummer",
                ],
                hamming_dist=3,
            ) or contains_at_least_one_string(
                text, ["Pers-Nr.", "AHV-Nr."], hamming_dist=1
            ):
                count += 1
            if contains_at_least_one_string(
                text,
                [
                    "Basisdaten",
                    "Personendaten",
                    "Persönliche Daten",
                    "Persönliche Angaben",
                    "Angaben zur Person",
                    "Zivilstand",
                    "Lohnangaben",
                    "Löhne und Beiträge",
                ],
            ):
                count += 1
            if contains_at_least_one_string(
                text,
                [
                    "Vorsorgestiftung",
                    "Sammelstiftung",
                    "Personalstiftung",
                    "Personalfürsorge",
                    "Personalvorsorge",
                    "Pensionskasse",
                    "Lehrerversicherungskasse",
                    "Caisse du pensions",
                ],
            ):
                count += 1
            if contains_at_least_one_string(text, token_list_relevant_salary):
                count += 1
            if contains_at_least_one_string(
                text,
                [
                    "Leistungen im Alter",
                    "Altersrente",
                    "Altersguthaben",
                    "Altersleistung",
                    "Voraussichtliche Altersrente",
                    "Sparkapital",
                    "VORAUSSICHTLICHE LEISTUNGEN",
                ],
            ):
                count += 1
            return count >= 4

    def parse_page_header(self):
        return self.page.set_header_by_percentage(0.5)

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            create_search_elements_address(self.page.header, self.page)
            + [create_document_date_most_recent(self.page.header)]
        )
