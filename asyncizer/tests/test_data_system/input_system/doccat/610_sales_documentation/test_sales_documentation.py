from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/610_sales_documentation"
)
dest_folder_prefix = "input_system_doccat_sales_documentation_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_page1():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "page1",
        source_file_filter=["page1.jpg"],
        client_lang="de",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_VALUATION: 1},
            expected_page_objects={
                0: {
                    "property_area_living_gross": "101 m2",
                    "year_construction": "2024",
                    "property_type": "APARTMENT",
                    "availability": "01.05.2024",
                    "num_rooms_living": "3.5",
                    "num_rooms_bathroom": "1",
                    "heating_system": "HEAT_PUMP_SYSTEM",
                    "heat_distribution": "FLOOR_HEATING",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_page2():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "page2",
        source_file_filter=["page2.jpg"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            # page objects are removed because doccat of document is UNKNOWN
            # (PROPERTY_PHOTOS does not have page objects stand-alone)
            # expected_page_objects={
            #     0: {
            #         "property_address": "Niesenweg 36, 3125 Toffen",
            #         "property_area_living_default": "152 m2",
            #         "year_construction": "2004",
            #         "plot_size": "301 m2",
            #         "property_type": "SINGLE_FAMILY_HOME",
            #         "availability": "01.08.2023",
            #         "num_rooms_living": "5.5",
            #         "num_rooms_bathroom": "1",
            #     }
            # },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd1():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_06_sd1",
        # source_file_filter=["page2.jpg"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.BUILDING_DESCRIPTION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd2():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_07_sd2",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_VALUATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd3():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_08_sd3",
        client_lang="de",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.BUILDING_DESCRIPTION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd4():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_09_sd4",
        client_lang="de",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            # page objects are removed because doccat of document is UNKNOWN
            # (PROPERTY_INFO does not exist stand-alone)
            # expected_page_objects={
            #     0: {
            #         "property_area_living_default": "225 m2",
            #         "year_construction": "2003",
            #         "cubature": "1295 m3",
            #         "plot_size": "507 m2",
            #         "property_type": "SINGLE_FAMILY_HOME",
            #         "availability": "BY_ARRANGEMENT",
            #         "num_rooms_living": "6",
            #         "num_rooms_bedroom": "4",
            #         "num_rooms_bathroom": "3",
            #         "num_rooms_toilet": "3",
            #         "heating_system": "HEAT_PUMP_SYSTEM",
            #         "heat_distribution": "FLOOR_HEATING",
            #     }
            # },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd5():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_10_sd5",
        client_lang="de",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_VALUATION: 1},
            expected_page_objects={
                0: {
                    "property_address": "Saphirweg 8\n4552 Derendingen",
                    "property_value_ratio": "195/1000",
                    "property_area_living_net": "132 m2",
                    "year_construction": "2004",
                    "property_type": "GARDEN_LEVEL_APARTMENT",
                    "num_rooms_living": "5.5",
                    "num_rooms_bathroom": "2",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd6():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_11_sd6",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_page_objects={0: {}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd7():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_12_sd7",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_page_objects={
                0: {
                    "property_address": "Les Savagnières 12, 2610 Les Pontins",
                    "property_type": "SINGLE_FAMILY_HOME",
                    "property_price": "890000",
                    "num_rooms_living": "4.5",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd8():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_13_sd8",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_VALUATION: 1},
            # expected_page_objects={
            #     0: {
            #         "property_address": "A5 1. OG",
            #         "property_value_ratio": "62/1000",
            #         "property_area_living_net": "70 m2",
            #         "plot_size": "921 m2",
            #         "property_type": "APARTMENT",
            #     }
            # },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd9():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_14_sd9",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_page_objects={
                0: {
                    "property_address": "Strasse 2, 4112 Flüh (SO)",
                    "property_area_living_default": "532 m2",
                    "year_construction": "2022",
                    "cubature": "1535 m3",
                    "plot_size": "603 m2",
                    "property_type": "MULTI_FAMILY_HOME",
                    "property_price": "3039000",
                    "availability": "01.01.2023",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd10():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_15_sd10",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            # page objects are removed because doccat of document is UNKNOWN
            # (PROPERTY_INFO does not exist stand-alone)
            # expected_page_objects={
            #     0: {"num_rooms_living": "4.5", "num_rooms_wetroom": "2"}
            # },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd11_0():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_16_sd11" / "0",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_page_objects={
                0: {
                    "property_type": "ROW_CORNER_HOUSE",
                    "num_rooms_living": "6.5",
                    "heating_system": "DISTRICT_HEATING_SYSTEM",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sd11_1():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "dataset_16_sd11" / "1",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                # DocumentCat.PROPERTY_INFO: 1,
                DocumentCat.SALES_DOCUMENTATION: 1
            },
            expected_page_objects={
                0: {
                    # bis is wrong and has low confidence. Does not get
                    # extracted if test is run directly in hylayoutlm. Dunno why.
                    "property_address": "Hagenacherstrasse 16 bis",
                    "year_construction": "1949",
                    "property_type": "SINGLE_FAMILY_HOME",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sample_emmen():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "sample_emmen",  # haus SK privat
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_page_objects={},
        ),
    ).run()


@pytest.mark.asyncio
async def test_sample_herrliberg_pdf_problem():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "sample_herrliberg_pdf_problem",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_page_objects={},
        ),
    ).run()
