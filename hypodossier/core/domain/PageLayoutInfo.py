from typing import List, <PERSON><PERSON>, Optional

from pydantic import BaseModel, Field


class PageLayoutInfo(BaseModel):
    # All lines that are significantly larger than the normal text (default 110%)
    titles: List[str] = Field(default_factory=list)

    # All lines that are very large (150% normal text)
    top_titles: List[str] = Field(default_factory=list)

    # dummy_tuple: Optional[List[Optional[Tuple[str, float]]]] = []

    #
    # # All lines that are significantly larger than the normal text (default 110%)
    titles_with_size: Optional[List[Optional[Tuple[str, float]]]] = Field(
        default_factory=list
    )
    #
    # # All lines that are very large (150% normal text)
    top_titles_with_size: Optional[List[Optional[Tuple[str, float]]]] = Field(
        default_factory=list
    )

    def get_top_titles_with_size_by_size(self):
        titles_with_size = sorted(
            self.top_titles_with_size, key=lambda x: x[1], reverse=True
        )
        return titles_with_size

    def get_titles_with_size_by_size(self):
        titles_with_size = sorted(
            self.titles_with_size, key=lambda x: x[1], reverse=True
        )
        return titles_with_size

    def get_titles_by_size(self, max_num_items=0, min_length_title=4):
        """
        return a list of all titles sorted by size descending
        """
        titles_with_size = self.get_titles_with_size_by_size()
        titles = [tup[0] for tup in titles_with_size if len(tup[0]) >= min_length_title]
        if max_num_items and max_num_items < len(titles):
            return titles[0:max_num_items]
        else:
            return titles

    def get_top_titles_by_size(self, max_num_items=0, min_length_title=4):
        titles_with_size = self.get_top_titles_with_size_by_size()
        titles = [tup[0] for tup in titles_with_size if len(tup[0]) >= min_length_title]
        if max_num_items and max_num_items < len(titles):
            return titles[0:max_num_items]
        else:
            return titles
