from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "220907_foundation_long"
path_root_folder = Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS) / "hbl"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_hbl_220907_foundation_long(
    override_existing=True,
    use_ocr_cache=True,
    webbrowser=True,
    show_page=-1,
    show_filename=None,
    client_lang="de",
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=path_root_folder / FOLDER_TYPE,
        source_file_filter=["foundation_plans_land_register.pdf"],
        dest_folder_prefix="hbl_quality_check",
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
        ),
        use_ocr_cache=use_ocr_cache,
        webbrowser=webbrowser,
        show_page=show_page,
        show_filename=show_filename,
        client_lang=client_lang,
    )
