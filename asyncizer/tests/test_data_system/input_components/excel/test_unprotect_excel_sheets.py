from pathlib import Path

import pytest

from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.excel_util import excel_unprotect

excel_folder = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/excel/excel_protected_sheets/"
)
path_root_folder = Path(excel_folder)
dest_folder_prefix = "input_system_components_excel_sheets"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


def test_unprotect_sheets(tmp_path):
    p = Path(excel_folder) / "Finanzierungspotenzial_FK_Sample_Stauffer.xlsx"
    assert p.exists()

    p_out_protected = tmp_path / "protected.xlsx"
    p_out_unprotected = tmp_path / "unprotected.xlsx"

    excel_unprotect(
        p, p_out_protected, unprotect_workbook=False, unprotect_sheets=False
    )
    excel_unprotect(p, p_out_unprotected)
