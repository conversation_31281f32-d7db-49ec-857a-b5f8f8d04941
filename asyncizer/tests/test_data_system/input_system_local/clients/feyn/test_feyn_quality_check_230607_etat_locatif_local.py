from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "230607_etat_locatif"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/feyn/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_feyn_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_simple_fr():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "simple_fr",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TENANT_DIRECTORY: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_complex_fr():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "complex_fr",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TENANT_DIRECTORY: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_wrong_as_ten_agreement_fr():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "wrong_as_ten_agreement",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TENANT_DIRECTORY: 2},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_prop_accounts():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "prop_accounts",
        # source_file_filter=['prop_accounts_21.pdf'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PROPERTY_ACCOUNTS: 2},
            expected_num_processing_exceptions=0,
        ),
    ).run()
