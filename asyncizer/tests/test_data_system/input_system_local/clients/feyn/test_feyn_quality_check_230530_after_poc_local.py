from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "230530_poc"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/feyn/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_feyn_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_heic():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "heic",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_tax_sz():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "tax_sz",
        source_file_filter=["tax_sz_year_missing.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                "310 Steuererklärung Spalinger Marcel SZ 2022.pdf": {
                    "canton_short": "SZ",
                    "year": "2022",
                    "address_block": PO_EXISTS,
                    "zip": "8834",
                    "city": "Schindellegi",
                    "p1_fullname": PO_EXISTS,
                    "p1_income_employed_main": "CHF 287'760",
                    "p2_income_employed_main": "CHF 23'285",
                    "income_portfolio": "CHF 346",
                    "income_gross_total": "CHF 271'777",
                    "p2_contribution_pillar_3a": "CHF 6'883",
                    "p1_expense_employment": "CHF 6'900",
                    "p2_expense_employment": "CHF 4'657",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 6'800",
                    "deductions_wealth_management": "CHF 763",
                    "deductions_donations": "CHF 395",
                    "income_net_total": "CHF 264'214",
                    "income_taxable_global": "CHF 248'419",
                    "income_taxable_local": "CHF 248'419",
                    "assets_portfolio": "CHF 254'244",
                    "assets_cash_gold": "CHF 300",
                    "assets_cars": "CHF 41'310",
                    "assets_gross_total": "CHF 295'854",
                    "assets_net_total": "CHF 295'854",
                    "assets_taxable_global": "CHF 15'854",
                    "assets_taxable_local": "CHF 15'854",
                }
            },
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 19},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_multiple_plans():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "multiple_plans",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_FLOOR: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_wrong_salary_certificate():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "wrong_salary_certificate",
        source_file_filter=["page_20_wrongly_identified_as_salary_certificate.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={}, expected_num_processing_exceptions=0
        ),
    ).run()


@pytest.mark.asyncio
async def test_plan_side_view():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "plan_side_view",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={}, expected_num_processing_exceptions=0
        ),
    ).run()
