from abbyyplumber.converter.ValueConverter import (
    Paragraph<PERSON>onverter,
    MostRecentDateConverter,
    YearConverter,
    ValidNumberConverter,
    DateConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementC<PERSON>tant,
    SearchElementArea,
    SearchElementConstrainedArea,
    SearchElementLabeled<PERSON>ield,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    ReferenceBoundaryHorizontal,
    SearchRelationAbove,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    PI_FIELD_INSURANCE_AMOUNT,
    PI_FIELD_YEAR_CONSTRUCTION,
    FIELD_PROP_CUBATURE,
    FIELD_PROP_ESTIMATION_DATE,
    FIELD_PROP_PROPERTY_DESC,
    PI_FIELD_INSURANCE_YEARLY_BILL_AMOUNT,
    FIELD_PROP_CADASTER_NO,
    FIELD_PROP_POLICE_NO,
    FIELD_PROP_OWNER_FULLNAME,
    FIELD_PROP_ADDRESS_STREET,
    FIELD_PROP_ADDRESS_CITY,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_CANTON_SHORT,
    FIELD_FIRSTNAME,
    FIELD_DOCUMENT_DATE,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.CommonPropertyInsurancePageParsers import (
    StandardPropertyInsurancePageParser,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.property_insurance_util import (
    PI_CONVERTER_INSURANCE_AMOUNT,
    PI_CONVERTER_YEARLY_FEE,
    PI_CONVERTER_CUBATURE,
)
from mortgageparser.util.search_element_util import create_search_elements_address
from mortgageparser.util.string_utils import contains_all_strings


# created on version from 2013
class PropertyInsuranceSGPolicePageParser(StandardPropertyInsurancePageParser):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Versicherungsnachweis für das Jahr 20",
                "St.Gallen",
                "Eigentümer",
                "Rechnungsempfänger / Vertreter",
                "<EMAIL>",
            ],
        )

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementConstant(FIELD_CANTON_SHORT.name, "SG"),
                SearchElementArea(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.main,
                    y_range=PercentageRange(0.8, 1),
                    converter=MostRecentDateConverter(),
                ),
                SearchElementLabeledField(
                    FIELD_PROP_POLICE_NO.name,
                    self.page.main,
                    label="Vers-Nr.",
                    converter=ParagraphConverter(max_num_spaces_per_line=0),
                ),
                SearchElementConstrainedArea(
                    "target_prop",
                    self.page.main,
                    text_top="Gebäudebeschreibung",
                    text_bottom="Für Prämienrechnung relevant",
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    "target_owner",
                    self.page.main,
                    x_range=PercentageRange(0, 0.49),
                    extract=False,
                    text_bottom="Gebäudebeschreibung",
                    relations=[SearchRelationAbove("target_prop")],
                ),
                SearchElementArea(
                    "target_contact",
                    self.page.main,
                    x_range=PercentageRange(0.51, 1),
                    extract=False,
                    relations=[SearchRelationAbove("target_prop")],
                ),
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.main,
                    target_name="target_contact",
                    text_top="Kunden-Nr.",
                    text_left="Kunden-Nr.",
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_OWNER_FULLNAME.name,
                    self.page.main,
                    target_name="target_owner",
                    text_top="Kunden-Nr.",
                    text_left="Kunden-Nr.",
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_ADDRESS_STREET.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Standort",
                    text_left="Standort",
                    text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                    text_left_offset_chars=-2,
                    text_right="Grundstück-Nr",
                    text_bottom="Volumen in",
                    converter=ParagraphConverter(
                        do_capitalize_uppercase=True, max_num_lines_valid=1
                    ),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_CADASTER_NO.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Grundstück-Nr.",
                    text_left="Grundstück-Nr.",
                    text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                    text_left_offset_chars=-2,
                    text_right="Gemeinde",
                    converter=ValidNumberConverter(max=9999),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_ADDRESS_CITY.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Gemeinde",
                    text_left="Grundstück-Nr.",
                    converter=ParagraphConverter(
                        do_capitalize_uppercase=True, max_num_lines_valid=1
                    ),
                ),
                SearchElementConstrainedArea(
                    PI_FIELD_YEAR_CONSTRUCTION.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Baujahr",
                    text_right="Volumen",
                    converter=YearConverter(),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_CUBATURE.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Volumen in",
                    text_left="Baujahr",
                    text_right="Volumen in m",
                    text_right_boundary=ReferenceBoundaryHorizontal.RIGHT,
                    converter=PI_CONVERTER_CUBATURE,
                ),
                SearchElementConstrainedArea(
                    "target_estimation",
                    self.page.main,
                    text_top="Für Prämienrechnung relevant",
                    text_bottom="Anpassung an Baukostenteuerung",
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_ESTIMATION_DATE.name,
                    self.page.main,
                    target_name="target_estimation",
                    text_top="Schätzung",
                    text_right="Zweckbestimmung",
                    converter=DateConverter(),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_PROPERTY_DESC.name,
                    self.page.main,
                    target_name="target_estimation",
                    text_top="Zweckbestimmung",
                    text_left="Zweckbestimmung",
                    text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                    text_left_offset_chars=-1,
                    text_right="Index",
                    converter=ParagraphConverter(
                        max_num_lines=1, max_num_lines_valid=1
                    ),
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_AMOUNT.name,
                    self.page.main,
                    label="Versicherter Gebäudewert",
                    field_pos_page_horizontal=PercentageRange(0.75, 1),
                    converter=PI_CONVERTER_INSURANCE_AMOUNT,
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_YEARLY_BILL_AMOUNT.name,
                    self.page.main,
                    label="Total in CHF",
                    converter=PI_CONVERTER_YEARLY_FEE,
                ),
            ]
            + create_search_elements_address(self.page.main, self.page)
        )
