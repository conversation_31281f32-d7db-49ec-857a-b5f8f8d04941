from dataclasses import dataclass

# There is a specification here: https://www.elohnausweis-ssk.ch/de/assets/documents/Benutzerhandbuch_eLA.pdf
# https://www.elohnausweis-ssk.ch/de/assets/documents/SalaryDeclarationElohnOnline.xsd
from typing import Dict

from hypodossier.core.domain.PageData import FIELDS_PERSON_DATA, PersonPageData
from hypodossier.core.domain.SemanticField import (
    SemanticField,
    fields_as_dict,
    FIELD_DOCUMENT_MONTH,
    ExtractionType,
    FieldTitles,
    FIELD_DEGREE_EMPLOYMENT,
    FIELD_EMPLOYER,
    FIELD_AHV_NEW,
    merge_dicts,
)

PAY_SALARY_MONTH_NET = SemanticField(
    "salary_month_net",
    ExtractionType.CURRENCY,
    FieldTitles("Nettolohn (monatlich)", "Salary Net (monthly)"),
    "Nettolohn pro Monat",
)
PAY_SALARY_MONTH_GROSS = Seman<PERSON>Field(
    "salary_month_gross",
    ExtractionType.CURRENCY,
    FieldTitles("<PERSON><PERSON><PERSON><PERSON><PERSON> (monatlich)", "Salary Gross (monthly)"),
    "<PERSON>ruttolohn pro Monat",
)
PAY_PERIOD = SemanticField(
    "period",
    ExtractionType.STRING,
    FieldTitles("Periode", "Period"),
    desc_de="Zeitraum, auf den sich die Auszahlung bezieht, z.B. '1.1.2022-31.1.2022'",
)

PAY_BONUS = SemanticField(
    "bonus",
    ExtractionType.CURRENCY,
    FieldTitles("Bonus", "Bonus"),
    desc_de="Bonuszahlung, z.B. 'CHF 1'234'",
)

# Maybe these 2 need to be consolidated?
PAY_SALARY_PAID_AMOUNT = SemanticField(
    "salary_paid_amount",
    ExtractionType.CURRENCY,
    FieldTitles("Ausbezahlter Betrag", "Paid out amount"),
    "Betrag der ausbezahlt wurde, z.B. CHF 1'234",
)
PAY_SALARY_PAID_SALARY = SemanticField(
    "salary_paid_salary",
    ExtractionType.CURRENCY,
    FieldTitles("Ausbezahlter Lohn", "Paid out salary"),
    "Lohn der ausbezahlt wurde, z.B. CHF 1'234",
)


# Rename to "Grundlohn"?
PAY_SALARY_MONTH_UNDEFINED = SemanticField(
    "salary_month_undefined",
    ExtractionType.CURRENCY,
    FieldTitles("Monatslohn", "Salary monthly"),
    "Monatslohn (entspricht in der Regel dem monatlichen Bruttolohn evtl. ohne unregelmässige Zulagen wie Boni",
)

new_fields = fields_as_dict(
    [
        FIELD_DOCUMENT_MONTH,
        FIELD_DEGREE_EMPLOYMENT,
        FIELD_EMPLOYER,
        PAY_SALARY_MONTH_NET,
        PAY_SALARY_MONTH_GROSS,
        PAY_SALARY_MONTH_UNDEFINED,
        PAY_PERIOD,
        PAY_BONUS,
        PAY_SALARY_PAID_AMOUNT,
        PAY_SALARY_PAID_SALARY,
        FIELD_AHV_NEW,
    ]
)

FIELDS_PAYSLIP = merge_dicts(FIELDS_PERSON_DATA, new_fields)


@dataclass
class PayslipPageData(PersonPageData):
    document_month: int = None

    degree_employment: str = None

    employer: str = None

    salary_month_net: float = None

    salary_month_undefined: float = None

    salary_month_gross: float = None

    period: str = None

    bonus: str = None

    salary_paid_amount: str = None

    salary_paid_salary: str = None

    ahv_new: str = None

    def get_invalidity_reason(self):
        return None

    def __str__(self):
        return f"Payslip[{self.fullname} Date {self.document_month}]"

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        d = super().get_semantic_fields()
        return {**d, **FIELDS_PAYSLIP}
