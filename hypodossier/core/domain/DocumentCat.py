from enum import Enum

from hypodossier.core.domain.DocumentCatElement import (
    DocumentCatElement,
    DocumentCatalog,
)
from hypodossier.core.domain.DocumentTopic import DocumentTopic


# Some terms are in here in all languages and validated:
# https://www.termdat.bk.admin.ch/Search/Search


# noinspection PyPep8
class DocumentCat(Enum):
    def __lt__(self, other):
        return self.name < other.name

    # Default doccats - generated with script_create_enum_doccat.py - start here #########################################################

    UNKNOWN_DE = DocumentCatElement(
        "001",
        DocumentTopic.UNKNOWN,
        "DOK DE",
        "DOC DE",
        "DOC DE",
        "DOC DE",
        "Unbekanntes Dokument in deutscher Sprache",
    )

    UNKNOWN_EN = DocumentCatElement(
        "002",
        DocumentTopic.UNKNOWN,
        "DOK EN",
        "DOC EN",
        "DOC EN",
        "DOC EN",
        "Unbekanntes Dokument in englischer Sprache",
    )

    UNKNOWN_FR = DocumentCatElement(
        "003",
        DocumentTopic.UNKNOWN,
        "DOK FR",
        "DOC FR",
        "DOC FR",
        "DOC FR",
        "Unbekanntes Dokument in französischer Sprache",
    )

    UNKNOWN_IT = DocumentCatElement(
        "004",
        DocumentTopic.UNKNOWN,
        "DOK IT",
        "DOC IT",
        "DOC IT",
        "DOC IT",
        "Unbekanntes Dokument in italienischer Sprache",
    )

    UNKNOWN = DocumentCatElement(
        "009",
        DocumentTopic.UNKNOWN,
        "DOK",
        "DOC",
        "DOC",
        "DOC",
        "Unbekanntes Dokument (Sprache nicht bekannt)",
    )

    TAX_ATTACHMENTS = DocumentCatElement(
        "020",
        DocumentTopic.UNKNOWN,
        "Steuerbeilagen Mix",
        "Tax Declaration Attachments Misc",
        "Annexes impôts divers",
        "Allegato alle imposte",
        "Sammelposten für (unsortierte) Steuerbeilagen, die manuell bereinigt werden müssen",
    )

    PROCESSING_ERROR = DocumentCatElement(
        "090", DocumentTopic.ERROR, "Fehler", "Error", "Erreur", "Errore", ""
    )

    PASSPORT_CH = DocumentCatElement(
        "210",
        DocumentTopic.PERSON_GENERAL,
        "Pass CH",
        "Passport CH",
        "Passeport CH",
        "Passaporto CH",
        "Pass Schweiz",
    )

    PASSPORT_DE = DocumentCatElement(
        "211",
        DocumentTopic.PERSON_GENERAL,
        "Pass DE",
        "Passport DE",
        "Passeport DE",
        "Passaporto DE",
        "Pass Deutschland",
    )

    PASSPORT_IT = DocumentCatElement(
        "212",
        DocumentTopic.PERSON_GENERAL,
        "Pass IT",
        "Passport IT",
        "Passeport IT",
        "Passaporto IT",
        "Pass Italien",
    )

    PASSPORT_FR = DocumentCatElement(
        "213",
        DocumentTopic.PERSON_GENERAL,
        "Pass FR",
        "Passport FR",
        "Passeport FR",
        "Passaporto FR",
        "Pass Frankreich",
    )

    PASSPORT_OTHER = DocumentCatElement(
        "219",
        DocumentTopic.PERSON_GENERAL,
        "Pass Ausland",
        "Foreign Passport",
        "Passeport étranger",
        "Passaporto estero",
        "Diverse ausländische Pässe",
    )

    ID = DocumentCatElement(
        "220",
        DocumentTopic.PERSON_GENERAL,
        "Identitätskarte CH",
        "Identity Card CH",
        "Carte identité CH",
        "Carta d'identità CH",
        "Schweizerische Identitätskarte im Kreditkartenformat",
    )

    ID_OTHER = DocumentCatElement(
        "221",
        DocumentTopic.PERSON_GENERAL,
        "ID Karte",
        "ID Card",
        "Carte identité",
        "Carta d'identità",
        "CH Dokumente wie Fahrausweis oder KK-Karte und div. ausländische Identitätskarten (aber alle für Hypo-Prozess nicht relevant)",
    )

    CIVIL_STATUS_DOCUMENT = DocumentCatElement(
        "223",
        DocumentTopic.PERSON_GENERAL,
        "Zivilstandsdokument",
        "Civil Status Document",
        "Document état civil",
        "Documento di stato civile",
        "Sammelkategorie für Familienausweis, Partnerschaftsurkunde, Heiratsurkunde, Todesschein etc.",
    )

    CONFIRMATION_OF_RESIDENCE = DocumentCatElement(
        "229",
        DocumentTopic.PERSON_GENERAL,
        "Wohnsitzbestätigung",
        "Confirmation of Residence",
        "Attestation domicile",
        "Conferma di residenza",
        "Nachweis der Gemeinde über Ansässigkeit im Gemeindegebiet; Synonyme Meldebestätigung, Niederlassungsausweis, Niederlassungsbestätigung",
    )

    FOREIGN_NATIONAL_ID = DocumentCatElement(
        "230",
        DocumentTopic.PERSON_GENERAL,
        "Ausländerausweis",
        "Foreign nationals permit",
        "Livret pour étrangers",
        "Libretto per stranieri",
        "Veraltetes Dokument für die Bewilligung des Aufenthalts: Ausländerausweis / Livret pour étrangers. Ersetzt durch Aufenthaltstitel / Titre de sejour",
    )

    RESIDENCE_PERMIT = DocumentCatElement(
        "231",
        DocumentTopic.PERSON_GENERAL,
        "Aufenthaltstitel",
        "Residence permit",
        "Titre séjour",
        "Permesso di soggiorno",
        "Aufenthaltstitel (EU, EFTA, Andere)",
    )

    LEGITIMATION_FDFA = DocumentCatElement(
        "235",
        DocumentTopic.PERSON_GENERAL,
        "FDFA Legitimation",
        "Legitimation FDFA",
        "Légitimation DFAE",
        "Legittimazione DFAE",
        "FDFA Verwaltungsbeamte von diplomatischen Vertretungen und konsularischen Vertretungen erhalten Legitimationskarten",
    )

    IDENTITY_MISC = DocumentCatElement(
        "239",
        DocumentTopic.PERSON_GENERAL,
        "Identität Diverses",
        "Identity Miscellaneous",
        "Identité divers",
        "Documento supplementare d'identità",
        "Deckblatt Pass, Zusatzseite Pass falls ohne echtes Identitätsdokument auf einzelner Seite",
        misc_type=True,
    )

    DEBT_COLLECTION_INFORMATION = DocumentCatElement(
        "240",
        DocumentTopic.PERSON_GENERAL,
        "Betreibungsauskunft",
        "Debt Collection Information",
        "Extrait registre poursuites",
        "Estratto dal registro delle esecuzioni",
        "Aktuelle Betreilbungsauskunft (nicht älter als 3 Monate)",
    )

    CRIMINAL_RECORDS = DocumentCatElement(
        "245",
        DocumentTopic.PERSON_GENERAL,
        "Strafregisterauszug",
        "Extract of the Criminal Record",
        "Extrait casier judiciaire",
        "Estratto del casellario giudiziale",
        "Auszug aus dem Schweizerischen Strafregister (idR nicht erforderlich)",
    )

    ZEK_CHECK = DocumentCatElement(
        "250",
        DocumentTopic.PERSON_GENERAL,
        "ZEK Datenbankauskunft",
        "ZEK information",
        "ZEK info",
        "Informazioni ZEK",
        "Auszug aus dem Register der Zentralstelle für Kreditinformationen",
    )

    IKO_CHECK = DocumentCatElement(
        "251",
        DocumentTopic.PERSON_GENERAL,
        "IKO Datenbankauskunft",
        "IKO information",
        "IKO info",
        "Informazioni IKO",
        "Auszug aus dem Register der Informationsstelle für Konsumkredit",
    )

    WORLD_CHECK = DocumentCatElement(
        "253",
        DocumentTopic.PERSON_GENERAL,
        "World Check",
        "World Check",
        "World Check",
        "World Check",
        "Ergebnis des Abgleichs mit der World-Check Datenbank",
    )

    CRIF_QUICK_CONSUMER_CHECK = DocumentCatElement(
        "255",
        DocumentTopic.PERSON_GENERAL,
        "Crif Consumer Check",
        "Crif Consumer Check",
        "Crif contrôle consommateur",
        "Controllo del consumatore Crif",
        "Bonitätsauskunft von Crif",
    )

    CRIF_TELEDATA = DocumentCatElement(
        "256",
        DocumentTopic.PERSON_GENERAL,
        "Crif Teledata",
        "Crif Teledata",
        "Crif Teledata",
        "Crif Teledata",
        "Online-Bonitätsauskunft von Crif",
    )

    CRIF_DATA_INFO = DocumentCatElement(
        "257",
        DocumentTopic.PERSON_GENERAL,
        "Crif Datenauskunft",
        "Crif Data Information",
        "Crif information solvabilité",
        "Informazioni Crif",
        "Bonitätsauskunft von Crif",
    )

    SCHUFA_BONITAETSCHECK = DocumentCatElement(
        "258",
        DocumentTopic.PERSON_GENERAL,
        "Schufa Bonitätscheck",
        "Schufa Credit Check",
        "Schufa information solvabilité",
        "Rapporto di credito Schufa",
        "Bonitäts-Check Deutschland",
    )

    CREDITWORTHINESS_MISC = DocumentCatElement(
        "259",
        DocumentTopic.PERSON_GENERAL,
        "Bonität Diverses",
        "Creditworthiness Miscellaneous",
        "Solvabilité divers",
        "Affidabilità creditizia documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    DIVORCE_DOCUMENT = DocumentCatElement(
        "260",
        DocumentTopic.PERSON_GENERAL,
        "Scheidung",
        "Divorce",
        "Divorce",
        "Separazione",
        "Scheidungsdokumente wie Trennungsvereinbarung, Scheidungskonvention, Scheidungsurteil, sofern diese nicht exakt zugeordnet werden können",
    )

    DIVORCE_SEPARATION_AGREEMENT = DocumentCatElement(
        "261",
        DocumentTopic.PERSON_GENERAL,
        "Trennungsvereinbarung",
        "Separation Agreement",
        "Divorce - Convention séparation",
        "Accordo per la separazione",
        "Gerichtsurkunde zur Trennung",
    )

    DIVORCE_CONVENTION = DocumentCatElement(
        "262",
        DocumentTopic.PERSON_GENERAL,
        "Scheidungskonvention",
        "Divorce Convention",
        "Divorce - Convention",
        "Convenzione di divorzio",
        "Einvernehmliche Regelung der Scheidungsfolgen (anstelle Scheidungsurteil)",
    )

    DIVORCE_DECREE = DocumentCatElement(
        "263",
        DocumentTopic.PERSON_GENERAL,
        "Scheidungsurteil",
        "Divorce Decree",
        "Divorce - Jugement",
        "Sentenza di divorzio",
        "Gerichtsurteil zur Scheidung",
    )

    DIVORCE_MISC = DocumentCatElement(
        "269",
        DocumentTopic.PERSON_GENERAL,
        "Scheidung Diverses",
        "Divorce Miscellaneous",
        "Divorce - divers",
        "Divorzio documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    INHERITANCE_TESTAMENT = DocumentCatElement(
        "270",
        DocumentTopic.PERSON_GENERAL,
        "Erbfolgeregelung",
        "Testament",
        "Testament ou pacte successoral",
        "Testamento",
        "Diese regelt die Teilung des Erbes nach gesetzlicher (Pflichtteil) und frei verfügter Erbfolge (Testament)",
    )

    INHERITANCE_CERTIFICATE = DocumentCatElement(
        "271",
        DocumentTopic.PERSON_GENERAL,
        "Erbbescheinigung",
        "Certificat of Inheritance",
        "Certificat héritiers",
        "Certificato di eredità",
        "Erbschein, der nach dem Tod des Erblassers an die Erbberechtigten ausgestellt wird",
    )

    INHERITANCE_ADVANCE = DocumentCatElement(
        "272",
        DocumentTopic.PERSON_GENERAL,
        "Erbvorbezug",
        "Inheritance Advance",
        "Avance sur héritage",
        "Anticipo ereditario",
        "Finanzieller Nachweis einer Vorbezugs-Zahlung",
    )

    DEATH_CERTIFICATE = DocumentCatElement(
        "273",
        DocumentTopic.PERSON_GENERAL,
        "Sterbeurkunde",
        "Death certificate",
        "Acte décès",
        "Certificato di morte",
        "Rechtliches Dokument, das den Tod einer Person bestätigt (der Totenschein bestätigt die gleiche Tatsache aus medizinischer Sicht).",
    )

    MARRIAGE_CONTRACT = DocumentCatElement(
        "275",
        DocumentTopic.PERSON_GENERAL,
        "Ehevertrag",
        "Marriage contract",
        "Contrat mariage",
        "Contratto di matrimonio",
        "Ehe- oder Erbvertrag der die rechtliche Situation im Fall von Scheidung oder Tod regelt.",
    )

    DEED_OF_GIFT = DocumentCatElement(
        "278",
        DocumentTopic.PERSON_GENERAL,
        "Schenkungsvertrag",
        "Deed of Gift",
        "Contrat donation",
        "Contratto di donazione",
        "Persönlicher Schenkungsvertrag oder Angaben zu erfolgter Schenkung",
    )

    INHERITANCE_MISC = DocumentCatElement(
        "279",
        DocumentTopic.PERSON_GENERAL,
        "Erbe Schenkung Diverses",
        "Inheritance Gift Miscellaneous",
        "Donation divers",
        "Eredità Dono documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    CV_CLIENT = DocumentCatElement(
        "295",
        DocumentTopic.PERSON_GENERAL,
        "Lebenslauf",
        "Curriculum Vitae",
        "Curriculum Vitae",
        "Curriculum Vitae",
        "Lebenslauf des Kunden",
    )

    PERSON_MISC = DocumentCatElement(
        "299",
        DocumentTopic.PERSON_GENERAL,
        "Person Diverses",
        "Person Miscellaneous",
        "Personne divers",
        "Persona documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    TAX_DECLARATION = DocumentCatElement(
        "310",
        DocumentTopic.PERSON_FINANCE,
        "Steuererklärung",
        "Tax Declaration",
        "Déclaration impôt",
        "Dichiarazione d'imposta",
        "Kantonale Steuererklärung mit sämtlichen Blättern inkl. Wertschriftenverzeichnis aber ohne Beilagen",
    )

    TAX_CALCULATION = DocumentCatElement(
        "311",
        DocumentTopic.PERSON_FINANCE,
        "Steuerberechnung",
        "Tax Calculation",
        "Calcul impôt",
        "Calcolo delle tasse",
        "Einzelseite mit Berechnung der (provisorischen) Steuer. Optionaler Teil der Steuererklärung.",
    )

    TAX_ASSESSMENT = DocumentCatElement(
        "312",
        DocumentTopic.PERSON_FINANCE,
        "Steuerveranlagung",
        "Tax Assessment",
        "Taxation fiscale",
        "Decisione di tassazione",
        "Definitive oder provisorische Festlegung der Steuerfaktoren und des Steuerbetrags",
    )

    TAX_BILL = DocumentCatElement(
        "313",
        DocumentTopic.PERSON_FINANCE,
        "Steuerrechnung",
        "Tax Bill",
        "Facture fiscale",
        "Fattura fiscale",
        "Rechnung, teilweise auch mit Einzahlungsschein, aber ohne detaillierte Aufschlüsselung (das wäre Steuerveranlagung)",
    )

    TAX_LIST_FINANCIAL_ASSETS = DocumentCatElement(
        "316",
        DocumentTopic.PERSON_FINANCE,
        "Wertschriftenverzeichnis",
        "List of Financial Assets",
        "Liste des titres",
        "Elenco dei titoli",
        "Auszug aus der Steuererklärung",
    )

    TAX_DEBT_INVENTORY = DocumentCatElement(
        "317",
        DocumentTopic.PERSON_FINANCE,
        "Schuldenverzeichnis",
        "Debt Inventory",
        "Liste des dettes",
        "Elenco dei debiti",
        "Auszug aus der Steuererklärung",
    )

    TAX_BUDGET = DocumentCatElement(
        "318",
        DocumentTopic.PERSON_FINANCE,
        "Steuerbudget",
        "Tax Budget",
        "Budget fiscal",
        "Bilancio fiscale",
        "Einzelseite, die in AG und BL vorkommt als Teil der Steuererklärung. Nicht einzureichen, aber existiert gelegentlich als Einzeldokument",
    )

    TAX_MISC = DocumentCatElement(
        "319",
        DocumentTopic.PERSON_FINANCE,
        "Steuern Diverses",
        "Tax Miscellaneous",
        "Impôts divers",
        "Tassa documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    BANK_STATEMENT_OF_INTEREST_CAPITAL = DocumentCatElement(
        "321",
        DocumentTopic.PERSON_FINANCE,
        "Zins- und Kapitalausweis",
        "Statement of Interest and Capital",
        "Etat intérêts et capitaux",
        "Attestato capitale e interessi",
        "Ausweis der Bank pro Konto für Vermögensdeklaration per Jahresende",
    )

    BANK_DOCUMENT = DocumentCatElement(
        "322",
        DocumentTopic.PERSON_FINANCE,
        "Bankdokument",
        "Bank Document",
        "Document bancaire",
        "Documento bancario",
        "Kontounterlagen",
    )

    BANK_MISC = DocumentCatElement(
        "329",
        DocumentTopic.PERSON_FINANCE,
        "Bank Diverses",
        "Bank Miscellaneous",
        "Banque divers",
        "Banca documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    SALARY_CERTIFICATE = DocumentCatElement(
        "330",
        DocumentTopic.PERSON_FINANCE,
        "Lohnausweis",
        "Salary Certificate",
        "Certificat salaire",
        "Certificato di salario",
        "Bescheinigung des Arbeitgebers über jährlich ausgezahlten Lohn im standardisierten Format",
    )

    SALARY_CONFIRMATION = DocumentCatElement(
        "332",
        DocumentTopic.PERSON_FINANCE,
        "Lohnbestätigung",
        "Salary Confirmation",
        "Attestation salaire",
        "Conferma del salario",
        "Bestätigung des Arbeitgebers, wie hoch der Lohn ist (z.B. bei neuem Arbeitsverhältnis), z.B. Personalstammblatt oder Brief oder wie hoch der geänderte Lohn ist",
    )

    EMPLOYMENT_CONFIRMATION = DocumentCatElement(
        "333",
        DocumentTopic.PERSON_FINANCE,
        "Arbeitsbestätigung",
        "Employment Confirmation",
        "Confirmation employeur",
        "Conferma d'impiego",
        "Bestätigung des Arbeitgebers, dass ein Angestelltenverhältnis besteht (mit allfälligen Details zu Probezeit o.ä, aber ohne Lohn)",
    )

    EMPLOYMENT_CONTRACT = DocumentCatElement(
        "334",
        DocumentTopic.PERSON_FINANCE,
        "Arbeitsvertrag",
        "Employment Contract",
        "Contrat travail",
        "Contratto di lavoro",
        "Arbeitsvertrag der die Konditionen der Anstellung regelt",
    )

    COMPENSATION_AGREEMENT = DocumentCatElement(
        "335",
        DocumentTopic.PERSON_FINANCE,
        "Vergütungsvereinbarung",
        "Compensation Agreement",
        "Convention rémunération",
        "Accordo sulla retribuzione",
        "Vergütungsvereinbarung für variable Lohnanteile wie Aktien oder Optionen",
    )

    BONUS_REGULATIONS = DocumentCatElement(
        "336",
        DocumentTopic.PERSON_FINANCE,
        "Bonusreglement",
        "Bonus Regulations",
        "Accord bonus employeur",
        "Regolamenti dei bonus",
        "Generelle Regelungen des Arbeitgebers zum Thema Bonus (nicht personalisiert auf konkreten Arbeitnehmer)",
    )

    EXPENSE_REGULATIONS = DocumentCatElement(
        "338",
        DocumentTopic.PERSON_FINANCE,
        "Spesenreglement",
        "Expense Regulations",
        "Règlement frais professionnels",
        "Regolamenti sulle spese",
        "Generelle Regelungen des Arbeitgebers zum Thema Spesen (nicht personalisiert auf konkreten Arbeitnehmer)",
    )

    PAYSLIP = DocumentCatElement(
        "340",
        DocumentTopic.PERSON_FINANCE,
        "Salärabrechnung",
        "Payslip",
        "Fiches salaire",
        "Conteggio salariale",
        "Monatliche Lohnabrechnung des Arbeitgebers",
    )

    SALARY_ACCOUNT = DocumentCatElement(
        "342",
        DocumentTopic.PERSON_FINANCE,
        "Lohnkonto",
        "Salary Account",
        "Compte salaire",
        "Conto salario",
        "Jahresübersicht über die monatlichen Lohnzahlungen des Arbeitgebers",
    )

    SALARY_CONFIRMATION_FORM = DocumentCatElement(
        "343",
        DocumentTopic.PERSON_FINANCE,
        "Lohnbescheinigung",
        "Salary Confirmation",
        "Attestation salaire",
        "Conferma del salario",
        "Bestätigungsformular des Arbeitgebers über ausgezahlten Lohn (anderes Formular als Lohnausweis)",
    )

    SALARY_BONUS = DocumentCatElement(
        "345",
        DocumentTopic.PERSON_FINANCE,
        "Bonus",
        "Bonus",
        "Bonus",
        "Bonus",
        "Dokument das den persönlichen Bonus des Arbeitnehmers belegt",
    )

    SALARY_CONFIRMATION_13 = DocumentCatElement(
        "346",
        DocumentTopic.PERSON_FINANCE,
        "Nachweis 13. Monatslohn",
        "Confirmation 13th Monthly Salary",
        "Preuve 13ème salaire",
        "Conferma della 13a",
        "Beleg, dass der Arbeitnehmer 13 Monatslöhne erhält",
    )

    TAX_AT_SOURCE_CONFIRMATION = DocumentCatElement(
        "347",
        DocumentTopic.PERSON_FINANCE,
        "Nachweis Quellensteuer",
        "Confirmation Tax At Source",
        "Preuve impôt à la source",
        "Conferma delle tassa alla fonte",
        "Jährliche Bescheinigung über Zahlung der Quellensteuer",
    )

    PENSION_PAYMENT_AHV = DocumentCatElement(
        "351",
        DocumentTopic.PERSON_FINANCE,
        "Rentenbescheinigung AHV IV",
        "Pension Attestation AHV IV",
        "Attestation rente AVS-AI",
        "Pensione AVS-AI",
        "Rentenbescheinigung von AHV/SUVA/Ausgleichskasse, die besagt, dass dem Empfänger diese monatliche/jährliche Rente ausbezahlt wird",
    )

    PENSION_PAYMENT_BVG = DocumentCatElement(
        "355",
        DocumentTopic.PERSON_FINANCE,
        "Rentenbescheinigung BVG",
        "Pension Attestation BVG",
        "Attestation rente LPP",
        "Attestazione della pensione",
        "Bestätigung der PK über monatlichen/jährlichen Rentenbetrag der aktuell ausgezahlt wird",
    )

    UNEMPLOYMENT_SALARY_CERTIFICATE = DocumentCatElement(
        "356",
        DocumentTopic.PERSON_FINANCE,
        "Bescheinigung Arbeitslosenkasse",
        "Statement Unemployment Benefits",
        "Attestation AC",
        "Certificato AD",
        "Bescheinigung der Arbeitslosenkasse über Zahlungen für ein bestimmtes Jahr. Diese sind steuerpflichtig.",
    )

    SHORT_TIME_WORK = DocumentCatElement(
        "357",
        DocumentTopic.PERSON_FINANCE,
        "Kurzarbeit",
        "Short-time Work",
        "Attestation chômage partiel",
        "Lavoro a tempo ridotto",
        "Bestötigung zu Kurzarbeit",
    )

    DAILY_ALLOWANCES = DocumentCatElement(
        "358",
        DocumentTopic.PERSON_FINANCE,
        "Taggelder",
        "Daily Allowances",
        "Indemnités journalières",
        "Indennità giornaliere",
        "Bestätigung zu Taggeldern/Sitzungsgeldern etc.",
    )

    ASSET_INCOME = DocumentCatElement(
        "360",
        DocumentTopic.PERSON_FINANCE,
        "Vermögenserträge",
        "Asset Income",
        "Revenu fortune",
        "Reddito patrimoniale",
        "Allgemeine Aufstellung von Vermögenserträgen",
    )

    DIVIDENDS = DocumentCatElement(
        "361",
        DocumentTopic.PERSON_FINANCE,
        "Dividenden",
        "Dividends",
        "Dividendes",
        "Dividendi",
        "Informationen zu Dividendeneinkünften",
    )

    CONFIRMATION_ALIMONY = DocumentCatElement(
        "365",
        DocumentTopic.PERSON_FINANCE,
        "Bestätigung Alimente",
        "Confirmation Alimony",
        "Attestation pension alimentaire",
        "Conferma alimenti",
        "Informationen zu Einkünften aus  Alimenten",
    )

    PROOF_OF_INCOME = DocumentCatElement(
        "368",
        DocumentTopic.PERSON_FINANCE,
        "Einkommensnachweise",
        "Proof of income",
        "Justificatifs de revenus",
        "Prova di reddito",
        "Diverse Einkommensnachweise wie Lohnausweis, Salärabrechnung, Lohnkonto, Dividendenzahlungen",
    )

    INCOME_MISC = DocumentCatElement(
        "369",
        DocumentTopic.PERSON_FINANCE,
        "Einkommen Diverses",
        "Income Miscellaneous",
        "Revenus divers",
        "Reddito documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    LEASING_AGREEMENT = DocumentCatElement(
        "371",
        DocumentTopic.PERSON_FINANCE,
        "Leasingvertrag",
        "Leasing Agreement",
        "Contrat leasing",
        "Contratto leasing",
        "Verträge für Autoleasing",
    )

    CONSUMER_LOAN = DocumentCatElement(
        "372",
        DocumentTopic.PERSON_FINANCE,
        "Privatkredit",
        "Consumer Loan",
        "Crédit personnel",
        "Credito privato",
        "Privater Kreditvertrag",
    )

    CREDIT_CARD_BILL = DocumentCatElement(
        "373",
        DocumentTopic.PERSON_FINANCE,
        "Abrechnung Kreditkarte",
        "Credit Card Bill",
        "Facture carte de crédit",
        "Conto della carta di credito",
        "Aktueller Saldo des Kreditkartenkontos",
    )

    LOAN_AGREEMENT = DocumentCatElement(
        "375",
        DocumentTopic.PERSON_FINANCE,
        "Darlehensvertrag",
        "Loan Agreement",
        "Contrat prêt",
        "Contratto di prestito",
        "Privater Kreditvertrag (nicht der Darlehensvertrag mit der Bank)",
    )

    CREDIT_MISC = DocumentCatElement(
        "379",
        DocumentTopic.PERSON_FINANCE,
        "Kredit Diverses",
        "Credit Miscellaneous",
        "Crédit divers",
        "Credito documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    FINANCE_MISC = DocumentCatElement(
        "399",
        DocumentTopic.PERSON_FINANCE,
        "Finanzen Diverses",
        "Finance Miscellaneous",
        "Finances divers",
        "Finanze documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    PENSION_CERTIFICATE_AHV = DocumentCatElement(
        "401",
        DocumentTopic.PERSON_PENSION,
        "AHV Ausweis",
        "AHV Pension Certificate",
        "Certificat AVS",
        "Certificato AVS",
        "Ausweis der AHV-Versicherung / -Ausgleichskasse.",
    )

    EXTRACT_AHV_ACCOUNT = DocumentCatElement(
        "402",
        DocumentTopic.PERSON_PENSION,
        "AHV Auszug",
        "AHV AVS Statement",
        "Extrait compte AVS",
        "Dichiarazione dell'AVS",
        "Kontoauszüge des AHV Guthabens",
    )

    PILLAR_ONE_CALCULATION = DocumentCatElement(
        "403",
        DocumentTopic.PERSON_PENSION,
        "AHV Kalkulation",
        "Pillar 1 Calculation",
        "Calcul AVS",
        "Calcolo del primo pilastro",
        "Allgemeine Berechnung der AHV (keine Rentenvorausberechnung",
    )

    PENSION_SIMULATION1 = DocumentCatElement(
        "405",
        DocumentTopic.PERSON_PENSION,
        "AHV Rentenvorausberechnung",
        "Pillar 1 Simulation",
        "Estimation rentes AVS",
        "Proiezione della pensione AVS",
        "Simulation der zu erwartenden Altersrente der AHV",
    )

    PILLAR_ONE_MISC = DocumentCatElement(
        "409",
        DocumentTopic.PERSON_PENSION,
        "Säule 1 Diverses",
        "Pillar 1 Miscellaneous",
        "AVS divers",
        "Primo pilastro documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    PENSION_CERTIFICATE = DocumentCatElement(
        "410",
        DocumentTopic.PERSON_PENSION,
        "PK Ausweis",
        "Pension Certificate",
        "Certificat LPP",
        "Certificato della cassa pensione",
        "Ausweis der Pensionskasse mit versichertem Lohn, Pensionierungsbedingungen",
    )

    PENSION_REGULATIONS = DocumentCatElement(
        "411",
        DocumentTopic.PERSON_PENSION,
        "Pensionskassenreglement",
        "Pension Fund Regulations",
        "Règlement caisse de pension LPP",
        "Regolamento della cassa pensione",
        "Standarddokument der PK, kein kundenspezifischer Inhalt",
    )

    STATEMENT_PENSION = DocumentCatElement(
        "412",
        DocumentTopic.PERSON_PENSION,
        "Auszug Vorsorgeguthaben",
        "Statement of pension savings",
        "Extrait avoir LPP",
        "Estratto conto di previdenza",
        "Speziell erstelltes Dokument, in dem das persönliche aktuell verfügbare Vorsorgeguthaben ausgewiesen wird",
    )

    PENSION_WITHDRAWL = DocumentCatElement(
        "413",
        DocumentTopic.PERSON_PENSION,
        "PK Vorbezug",
        "Pension Advance Withdrawal",
        "Retrait anticipée LPP",
        "Prelievo anticipato cassa pensione",
        "Abrechnung über den Vorbezug von Pensionskassenvermögen für Wohneigentum oder Pensionierung",
    )

    PENSION_CERTIFICATE_SIM_ALL = DocumentCatElement(
        "414",
        DocumentTopic.PERSON_PENSION,
        "PK Simulation",
        "Pension Simulation",
        "Simulation LPP",
        "Simulazione pensionamento",
        "Berechnung bzw. Simulation der künftigen Alters- und Risikoleistungen - i.d.R. nach dem Bezug von Guthaben zu Kauf von Wohneigentum",
    )

    PENSION_CERTIFICATE_LETTER = DocumentCatElement(
        "415",
        DocumentTopic.PERSON_PENSION,
        "PK Brief",
        "Pension Letter",
        "Lettre LPP",
        "Lettera della cassa pensione",
        "Brief mit individueller Anrede / individuellen Angaben von der PK",
    )

    PENSION_CERTIFICATE_INFO = DocumentCatElement(
        "416",
        DocumentTopic.PERSON_PENSION,
        "PK Information",
        "Pension Information",
        "Information LPP",
        "Informazioni PF",
        "Info-Blatt, Merkblatt mit allgemeinen PK-Informationen, nicht spezifisch für diesen Versicherten",
    )

    PENSION_CERTIFICATE_CREDIT_NOTE = DocumentCatElement(
        "417",
        DocumentTopic.PERSON_PENSION,
        "PK Freizügigkeitsleistung",
        "Pension Credit Note",
        "Attestation transfert libre passage LPP",
        "PF Prestazioni di libero passaggio",
        "Brief der bestätigt, dass FZL erhalten wurde (und auf das pers. PK-Konto übertragen wurde)",
    )

    PENSION_CERTIFICATE_CLOSING_STATEMENT = DocumentCatElement(
        "418",
        DocumentTopic.PERSON_PENSION,
        "PK Austritt",
        "Pension Closing Account",
        "Sortie LPP",
        "Ritiro PF",
        "Brief der die Schliessung des PK-Kontos bestätigt und den Transfer auf ein Freizügigkeitskonto oder eine andere PK",
    )

    VESTED_BENEFITS_ACCOUNT = DocumentCatElement(
        "424",
        DocumentTopic.PERSON_PENSION,
        "Freizügigkeitskonto",
        "Vested Benefit Account",
        "Compte libre passage LPP",
        "Conto di libero passaggio",
        "Vorsorgeguthaben auf einem Freizügigkeitskonto der 2. Säule",
    )

    VESTED_BENEFITS_STATEMENT = DocumentCatElement(
        "425",
        DocumentTopic.PERSON_PENSION,
        "Freizügigkeitspolice",
        "Vested Benefit Statement",
        "Police libre passage LPP",
        "Polizza di libero passaggio",
        "Vorsorgeguthaben auf einer Freizügigkeitspolice der 2. Säule",
    )

    VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT = DocumentCatElement(
        "428",
        DocumentTopic.PERSON_PENSION,
        "Freizügigkeit Austrittsabrechnung",
        "Vested Benefits Closing Statement",
        "Attestation sortie libre passage LPP",
        "Calcolo prestazione di libero passaggio",
        "Abrechnung, dass Freizügigkeitsguthaben an Versicherten oder neue BVG-Kasse ausgezahlt wird",
    )

    PILLAR_TWO_MISC = DocumentCatElement(
        "429",
        DocumentTopic.PERSON_PENSION,
        "Pensionskasse Diverses",
        "Pension fund miscellaneous",
        "LPP divers",
        "Fondo pensione documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    PENSION3A_ACCOUNT = DocumentCatElement(
        "430",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgekonto Säule 3",
        "Pillar 3 Account",
        "Compte 3ème pilier",
        "Conto di previdenza 3",
        "Säule 3a oder 3b Nachweis Kontoguthaben",
    )

    PENSION3A_CREDIT_NOTE = DocumentCatElement(
        "435",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgekonto Säule 3 Gutschriftsanzeige",
        "Pillar 3 Account Credit Note",
        "Compte 3ème pilier avis crédit",
        "Avviso di bonifico conto di previdenza pilastro 3",
        "Zahlungseingang auf Säule 3a oder 3b  Konto",
    )

    PENSION3A_INSURANCE_CONTRACT = DocumentCatElement(
        "440",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgepolice Säule 3",
        "Pillar 3 Insurance Policy",
        "Police prévoyance 3ème pilier",
        "Polizza di previdenza 3",
        "Versicherungspolice Säule 3a oder 3b",
    )

    PENSION3A_INSURANCE_LETTER_REDEMPTION = DocumentCatElement(
        "441",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgepolice Säule 3 Rückkaufswert",
        "Pillar 3 Insurance Redemption Value",
        "Valeur rachat 3ème pilier",
        "Valore di riscatto del polizza di previdenza 3",
        "Brief / Bestätigung wie hoch der aktuelle Rückkaufswert der Säule 3 Police ist",
    )

    PENSION3A_INSURANCE_STATEMENT = DocumentCatElement(
        "442",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgepolice Säule 3 Auszug",
        "Pillar 3 Insurance Statement",
        "Relevé 3ème pilier",
        "Estratto polizza di previdenza 3",
        "Regelmässiger (jährlicher) Auszug der den aktuellen Wert der Police ausweist",
    )

    PENSION3_REGULATIONS = DocumentCatElement(
        "443",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgepolice Säule 3 Reglement",
        "Pillar 3 Insurance Regulations",
        "Règlement 3ème pilier",
        "Regolamento polizza di previdenza 3",
        "Reglement der Pensionskasse",
    )

    PENSION3_INSURANCE_APPLICATION = DocumentCatElement(
        "445",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgepolice Säule 3 Antrag",
        "Pillar 3 Insurance Policy Application",
        "3ème pilier divers",
        "Polizza di previdenza 3 Richiesta",
        "Antragsformular für Versicherungspolice Säule 3a oder 3b",
    )

    PILLAR_THREE_MISC = DocumentCatElement(
        "449",
        DocumentTopic.PERSON_PENSION,
        "Säule 3 Diverses",
        "Pillar 3 Miscellaneous",
        "Pilier 3 divers",
        "Pilastro 3 documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    PENSION_CONTRIBUTION_CONFIRMATION = DocumentCatElement(
        "450",
        DocumentTopic.PERSON_PENSION,
        "Vorsorge Bescheinigung Beiträge",
        "Pension Contribution Confirmation",
        "Attestation cotisations prévoyance",
        "Certificato di pensione contributi",
        "Bescheinigung für Steuererklärung über Einzahlungen in BVG und/oder 3a für ein Steuerjahr",
    )

    PENSION_WITHDRAWL_PURPOSE_CONFIRMATION = DocumentCatElement(
        "453",
        DocumentTopic.PERSON_PENSION,
        "Vorsorge Bestätigung Mittelverwendung",
        "Pension Withdrawl Purpose Confirmation",
        "Attestation EPL LPP",
        "Conferma dell'uso dei fondi pensione",
        "Bescheinigung des finanzierenden Instituts an das Vorsorgeinstitut, dass die vorbezogenen Mittel für WEF verwendet werden",
    )

    RISK_LIFE_INSURANCE = DocumentCatElement(
        "480",
        DocumentTopic.PERSON_PENSION,
        "Risiko-Lebensversicherung",
        "Risk Life Insurance",
        "Assurance-vie risque",
        "Assicurazione sulla vita",
        "Risiko-Lebensversicherungspolice (ohne Sparanteil)",
    )

    RETIREMENT_ANALYSIS = DocumentCatElement(
        "491",
        DocumentTopic.PERSON_PENSION,
        "Vorsorgeanalyse",
        "Retirement Analysis",
        "Analyse prévoyance",
        "Analisi del pensionamento",
        "Planungs- oder Offertdokument für die Planung des Ruhestands",
    )

    PENSION_MISC = DocumentCatElement(
        "499",
        DocumentTopic.PERSON_PENSION,
        "Vorsorge Diverses",
        "Pension Miscellaneous",
        "Prévoyance divers",
        "Pensione documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    CORPORATE_BYLAWS = DocumentCatElement(
        "510",
        DocumentTopic.COMPANY,
        "Statuten Firma",
        "Corporate bylaws",
        "Statuts entreprise",
        "Statuto aziendale",
        "Gesellschaftsvertrag der Firma, auch Satzung oder Statuten genannt",
    )

    INCORPORATION_COMPANY = DocumentCatElement(
        "511",
        DocumentTopic.COMPANY,
        "Gründungsunterlagen Firma",
        "Incorporation documents of company",
        "Création de l'entreprise",
        "Costituzione della società",
        "",
    )

    HRA = DocumentCatElement(
        "513",
        DocumentTopic.COMPANY,
        "Handelsregisterauszug",
        "Extract from Commercial Register",
        "Extrait RC",
        "Estratto del registro di commercio",
        "Auszug aus dem Handelsregister",
    )

    SHARE_REGISTER = DocumentCatElement(
        "522",
        DocumentTopic.COMPANY,
        "Aktienregister",
        "Share register",
        "Registre actions",
        "Registro azionario",
        "Auflistung der Aktien der Firma mit ihren jeweiligen wirtschaftlich Berechtigten",
    )

    FINANCIAL_STATEMENT_COMPANY = DocumentCatElement(
        "530",
        DocumentTopic.COMPANY,
        "Jahresrechnung Firma",
        "Annual Financial Statement of Company",
        "Bilan & PP entreprise",
        "Bilancio conto economico",
        "Bilanz und Erfolgsrechnung von Firmen/Selbständigen",
    )

    BALANCE_SHEET_COMPANY = DocumentCatElement(
        "531",
        DocumentTopic.COMPANY,
        "Bilanz Firma",
        "Balance Sheet of Company",
        "Bilan entreprise",
        "Bilancio dell'azienda",
        "Bilanz von Firma",
    )

    INCOME_STATEMENT_COMPANY = DocumentCatElement(
        "533",
        DocumentTopic.COMPANY,
        "Erfolgsrechnung Firma",
        "Income Statement of Company",
        "Compte résultat entreprise",
        "Conto economico della società",
        "Erfolgsrechnung von Firma",
    )

    LIQUIDITY_PLAN_COMPANY = DocumentCatElement(
        "534",
        DocumentTopic.COMPANY,
        "Liquiditätsplan",
        "Liquidity plan",
        "Plan de liquidités",
        "Piano di liquidità",
        "Verfügbare Liquidität für einen konkreten Zeitpunkt",
    )

    CASH_FLOW_COMPANY = DocumentCatElement(
        "535",
        DocumentTopic.COMPANY,
        "Kapitalflussrechnung",
        "Cash Flow Statement",
        "Tableau flux trésorerie",
        "Rendiconto finanziario",
        "Kapitalflussrechnung von Firma",
    )

    INVESTMENT_PLAN_COMPANY = DocumentCatElement(
        "536",
        DocumentTopic.COMPANY,
        "Investitionsplan",
        "Investment plan",
        "Plan investissement",
        "Piano di investimenti",
        "Übersicht der geplanten Investitionen zu verschiedenen Zeitpunkten",
    )

    ANNUAL_REPORT_COMPANY = DocumentCatElement(
        "538",
        DocumentTopic.COMPANY,
        "Jahresbericht Firma",
        "Annual Report of Company",
        "Rapport annuel entreprise",
        "Relazione annuale della società",
        "Jahresbericht der Firma",
    )

    ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY = DocumentCatElement(
        "541",
        DocumentTopic.COMPANY,
        "Debitoren- und Kreditorenliste",
        "Debtors and creditors list",
        "Liste débiteurs et créanciers",
        "Elenco dei crediti e dei debiti",
        "",
    )

    TURNOVER_COMPANY = DocumentCatElement(
        "543",
        DocumentTopic.COMPANY,
        "Umsatzliste",
        "Turnover list",
        "Liste chiffres d'affaires",
        "Elenco dei fatturati",
        "",
    )

    BUDGET_CONTROL_COMPANY = DocumentCatElement(
        "546",
        DocumentTopic.COMPANY,
        "Soll-Ist-Vergleich Budget",
        "Budget target-actual comparison",
        "Comparaison budget prévus-réalisés",
        "Confronto obiettivi di bilancio effettivi",
        "",
    )

    FIN_REPORTING_COMPANY = DocumentCatElement(
        "548",
        DocumentTopic.COMPANY,
        "Finanzierungsreporting",
        "Financing reporting",
        "Rapport financement",
        "Rendicontazione dei finanziamenti",
        "",
    )

    BUSINESS_PLAN_COMPANY = DocumentCatElement(
        "550",
        DocumentTopic.COMPANY,
        "Businessplan Firma",
        "Business Plan of Company",
        "Business plan",
        "Business plan dell'azienda",
        "Businessplan von Firma",
    )

    BUDGET_PLANNING = DocumentCatElement(
        "555",
        DocumentTopic.COMPANY,
        "Budget und Planungsunterlagen Firma",
        "Budget and planning documents of the company",
        "Budget et planification",
        "Documenti di bilancio e di pianificazione dell'azienda",
        "Unterlagen zur Budgetiereung und Planung einer Firma",
    )

    AUDIT_REPORT_COMPANY = DocumentCatElement(
        "561",
        DocumentTopic.COMPANY,
        "Revisionsbericht Firma",
        "Audit report company",
        "Rapport révision entreprise",
        "Relazione del revisore società",
        "Jährlicher Bericht des Revisors zu den Prüfergebnissen der Jahresrechnung",
    )

    LOAN_AGREEMENT_SHAREHOLDER_COMPANY = DocumentCatElement(
        "575",
        DocumentTopic.COMPANY,
        "Darlehensvertrag Aktionär",
        "Loan agreement shareholder",
        "Contrat prêt actionnaire",
        "Contratto di finanziamento soci",
        "Darlehen Aktionär/Gesellschafter an Gesellschaft oder Gesellschaft an Aktionär/Gesellschafter",
    )

    COMPANY_MISC = DocumentCatElement(
        "599",
        DocumentTopic.COMPANY,
        "Firma Diverses",
        "Company Miscellaneous",
        "Entreprise divers",
        "Azienda documento aggiuntivo",
        "Diverses zu Firma",
        misc_type=True,
    )

    PROPERTY_INFO = DocumentCatElement(
        "600",
        DocumentTopic.PROPERTY,
        "Liegenschaftsinformationen",
        "Property Misc Information",
        "Informations sur les biens immobiliers",
        "LiegenschaftsInformazioni sulla proprietà",
        "",
    )

    PLAN_ANY = DocumentCatElement(
        "603",
        DocumentTopic.PROPERTY,
        "Liegenschaftsplan",
        "Property Plan",
        "Plan bien immobilier",
        "Piano di proprietà",
        "Diverse Pläne (Grundriss, Seitenansicht, Situation, …) ohne weitere Liegenschaftsdokumente",
    )

    PLAN_FLOOR = DocumentCatElement(
        "604",
        DocumentTopic.PROPERTY,
        "Grundriss",
        "Floor Plan",
        "Plan étage",
        "Planimetria del immobile",
        "Grundriss- und Schnittpläne",
    )

    PLAN_SITUATION = DocumentCatElement(
        "606",
        DocumentTopic.PROPERTY,
        "Situationsplan",
        "Survey Map",
        "Plan de situation",
        "Piano di situazione",
        "Umgebungskarte der Liegenschaft oder Kartenausschnitt (Google Maps) oder Luftbild, das kein Katasterplan ist",
    )

    PLAN_CADASTER = DocumentCatElement(
        "607",
        DocumentTopic.PROPERTY,
        "Katasterplan",
        "Copy of Cadaster",
        "Plan cadastral",
        "Copia della mappa catastale",
        "Katasterplan, auf dem auch Grundstücksnummern ersichtlich sind",
    )

    GIS_INFO = DocumentCatElement(
        "608",
        DocumentTopic.PROPERTY,
        "GIS Info",
        "GIS Info",
        "Extrait SIG",
        "GIS Info",
        "GIS Auszug",
    )

    PROPERTY_DOCUMENTATION = DocumentCatElement(
        "609",
        DocumentTopic.PROPERTY,
        "Liegenschaftsdokumentation",
        "Property Documentation",
        "Documentation biens",
        "Documentazione della proprietà",
        "Wie eine Verkaufsdokumentation, aber ohne Verkaufsabsicht. Also z.B. Exposé für Hypothekarablösung",
    )

    SALES_DOCUMENTATION = DocumentCatElement(
        "610",
        DocumentTopic.PROPERTY,
        "Verkaufsdokumentation",
        "Sales Documentation",
        "Documentation vente",
        "Documentazione di vendita",
        "Verkaufs- oder Objektdokumentation auch oft Exposé genannt",
    )

    EXTRACT_FROM_LAND_REGISTER = DocumentCatElement(
        "611",
        DocumentTopic.PROPERTY,
        "Grundbuchauszug",
        "Extract from Land Register",
        "Extrait registre foncier",
        "Estratto del registro fondiario",
        "Aktuell (nicht älter als 6 Monate), mit Angabe der Grundpfandrechte",
    )

    EASEMENT_CONTRACT = DocumentCatElement(
        "612",
        DocumentTopic.PROPERTY,
        "Dienstbarkeitsvertrag",
        "Easement Contract",
        "Contrat servitudes foncières",
        "Contratto di servitú",
        "Dienstbarkeiten im Rahmen des Grundbuchauszugs",
    )

    PROPERTY_PHOTOS = DocumentCatElement(
        "615",
        DocumentTopic.PROPERTY,
        "Fotos Liegenschaft",
        "Photos Property",
        "Photos bien immobilier",
        "Foto dell immobilie",
        "Farbfotos der Liegenschaft innen und aussen (innen mindestens Küche, Nasszellen und Wohnzimmer)",
    )

    LIST_OF_RENOVATIONS = DocumentCatElement(
        "616",
        DocumentTopic.PROPERTY,
        "Liste Renovationen",
        "List of Renovations",
        "Liste rénovations",
        "Elenco delle ristrutturazioni",
        "Wertvermehrende und werterhaltende Investitionen der letzten 10-20 Jahre",
    )

    PROPERTY_INSURANCE = DocumentCatElement(
        "617",
        DocumentTopic.PROPERTY,
        "Gebäudeversicherungsausweis",
        "Insurance Certificate on Building",
        "Attestation assurance bâtiments",
        "Certificato assicurazione dello stabile",
        "Kantonal/privat, Datenauskunft Kanton BE mit Angabe des Gebäudevolumens (m3) und Angabe des Baujahres",
    )

    MINERGIE_CERTIFICATE = DocumentCatElement(
        "618",
        DocumentTopic.PROPERTY,
        "Minergie-Zertifikat",
        "Minergy Certificate",
        "Certificat Minergie",
        "Certificato Minergia",
        "Offizielles Zertifikat (definitiv oder provisorisch) das die Einhaltung der Kriterien bescheinigt",
    )

    GEAK_CERTIFICATE = DocumentCatElement(
        "619",
        DocumentTopic.PROPERTY,
        "Gebäudeenergieausweis",
        "Building Energy Certificate",
        "CECB",
        "Certificato Energetico degli Edifici",
        "Gebäudeenergieausweis der Kantone GEAK (geak.ch)",
    )

    PROPERTY_VALUATION = DocumentCatElement(
        "620",
        DocumentTopic.PROPERTY,
        "Liegenschaftsschatzung",
        "Property Valuation",
        "Estimation de la valeur",
        "Stima immobiliare",
        "Hedonische oder physische Liegenschaftsschatzung mit Errechnung des erwarteten Verkehrswerts",
    )

    VOLUME_CALCULATION_SIA = DocumentCatElement(
        "621",
        DocumentTopic.PROPERTY,
        "Kubische Berechnung SIA",
        "Volume Calculation SIA",
        "Volume SIA",
        "Calcoli della cubatura secondo SIA",
        "Volumensberechnung eines Hauses in m3 gemäss SIA-Norm",
    )

    SAFETY_CERTIFICATE_ELECTRICAL = DocumentCatElement(
        "622",
        DocumentTopic.PROPERTY,
        "Sicherheitsnachweis Elektroinstallation",
        "Safety Certificate for Electrical Installation",
        "Certificat sécurité installation électrique",
        "Certificato di sicurezza per l'installazione elettrica",
        "Obligatorischer Nachweis der Sicherheit, durch Elektriker erstellt; wird bei Eigentumswechsel benötigt",
    )

    ENERGY_CERTFICATE = DocumentCatElement(
        "624",
        DocumentTopic.PROPERTY,
        "Energienachweis",
        "Energy Certificate",
        "Certificat énergétique",
        "Certificato energetico",
        "Nachweis zum Energieverbrauch gemäss Konferenz kantonaler Energiefachstellen",
    )

    PROPERTY_VALUATION_GOV = DocumentCatElement(
        "625",
        DocumentTopic.PROPERTY,
        "Amtlicher Wert",
        "Official Property Value",
        "Estimation valeur officielle",
        "Valore di perizia ufficiale",
        "Deklaration des offiziellen Werts, festgelegt durch die Gebäudeversicherung. Oft deutlich unter Verkehrswert. Auch Katasterschätzung genannt.",
    )

    PLR_CADASTRE = DocumentCatElement(
        "626",
        DocumentTopic.PROPERTY,
        "ÖREB-Kataster",
        "PLR Cadastre",
        "Cadastre RDPPF",
        "Catasto RDPP",
        "Auszug Kataster der öffentlich-rechtlichen Eigentumsbeschränkungen",
    )

    PROPERTY_ACCOUNTS = DocumentCatElement(
        "628",
        DocumentTopic.PROPERTY,
        "Liegenschaftsabrechnung",
        "Property Accounts",
        "Décompte immobilier",
        "Conteggio della proprietà",
        "Nebenkosten und Unterhaltsabrechnung privat oder Stockwerkeigentum",
    )

    USER_REGULATIONS_CONDOMINIUM = DocumentCatElement(
        "630",
        DocumentTopic.PROPERTY,
        "Nutzungs- und Verwaltungsreglement",
        "User Regulations Condominium",
        "Règlement PPE",
        "Regolamento d'uso e d'amministrazione",
        "Regeln des Zusammenlebens in der Stockwerkeigentümergemeinschaft, teilweise mit Begründung im Anhang",
    )

    FOUNDATION_CERTIFICATE_CONDOMINIUM = DocumentCatElement(
        "631",
        DocumentTopic.PROPERTY,
        "Begründung Stockwerkeigentum",
        "Foundation Certificate Condominium",
        "Constitution PPE",
        "Costituzione PPP",
        "Urkunde zur Begründung der Stockwerkeigentümergemeinschaft, teilweise mit Reglement im Anhang",
    )

    MEETING_MINUTES_CONDOMINIUM = DocumentCatElement(
        "632",
        DocumentTopic.PROPERTY,
        "Protokoll Stockwerkeigentümerversammlung",
        "Meeting Minutes Condominium Owners",
        "Protocole assemblée PPE",
        "Verbale Assemblea",
        "Protokoll der Jahresversammlung der Stockwerkeigentümer",
    )

    RENOVATION_FUND = DocumentCatElement(
        "633",
        DocumentTopic.PROPERTY,
        "Erneuerungsfonds",
        "Renovation Fund",
        "Fonds rénovation",
        "Fondo di rinnovo",
        "Kontoauszug oder Auszug der Verwaltung über den Erneuerungsfonds der Gemeinschaft und evtl. den Anteil eines einzelnen Eigentümers",
    )

    STATEMENT_VALUE_RATIO_PROPERTY = DocumentCatElement(
        "634",
        DocumentTopic.PROPERTY,
        "Aufstellung Wertquote",
        "Statement Value Ratio",
        "Cahier PPE",
        "Elenco di quota di valore",
        "Übersicht der Verteilung der Wertquote zwischen den Eigentümern der Stockwerkeigentümergemeinschaft (i.d.R. proportional zur Fläche und/oder Wert)",
    )

    CONDOMINIUM_MIX = DocumentCatElement(
        "638",
        DocumentTopic.PROPERTY,
        "Stockwerkeigentum Begründung/Reglement Mix",
        "Condominium Foundation/User Regulations Mix",
        "Copropriété mix",
        "Proprietà condominiale mix",
        "",
    )

    CONDOMINIUM_MISC = DocumentCatElement(
        "639",
        DocumentTopic.PROPERTY,
        "Stockwerkeigentum Diverses",
        "Condominium Miscellaneous",
        "PPE divers",
        "Proprietà condominiale documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    TENANT_DIRECTORY = DocumentCatElement(
        "640",
        DocumentTopic.PROPERTY,
        "Mieterspiegel",
        "Tenant Directory",
        "Etat locatif",
        "Contratto di locazione",
        "Aktuelle Mietzinsaufstellung mit Angabe der Nettomietzinseinnahmen, Anzahl Wohnungen evtl. inkl. Grösse",
    )

    TENANCY_AGREEMENT = DocumentCatElement(
        "641",
        DocumentTopic.PROPERTY,
        "Mietvertrag",
        "Tenancy Agreement",
        "Contrat bail",
        "Contratto di locazione",
        "Mietvertrag / Mietverträge einer Renditeliegenschaft",
    )

    RENTAL_MISC = DocumentCatElement(
        "649",
        DocumentTopic.PROPERTY,
        "Renditeliegenschaft Diverses",
        "Rental Miscellaneous",
        "Immeuble rendement divers",
        "Immobi di rendita documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    BUILDING_RIGHTS_AGREEMENT = DocumentCatElement(
        "650",
        DocumentTopic.PROPERTY,
        "Baurechtsvertrag",
        "Building Right Agreement",
        "Contrat superficie",
        "Contratto di diritto di superficie",
        "Benötigt falls Liegenschaft im Baurecht; inkl. Heimfallentschädigung und aktueller Baurechtszins",
    )

    BUILDING_RIGHT_INTEREST = DocumentCatElement(
        "654",
        DocumentTopic.PROPERTY,
        "Baurecht Zinsen",
        "Building right interest",
        "Rente droit superficie",
        "Canone del diritto di superficie",
        "Zinsabrechnung oder Zinsaufstellung für Baurecht",
    )

    BUILDING_RIGHTS_MISC = DocumentCatElement(
        "659",
        DocumentTopic.PROPERTY,
        "Baurecht Diverses",
        "Building Right Miscellaneous",
        "Droit superficie divers",
        "Diritto di superficie documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    CONTRACT_OF_SALE = DocumentCatElement(
        "660",
        DocumentTopic.PROPERTY,
        "Kaufvertrag",
        "Contract of Sale",
        "Acte achat vente",
        "Contratto di compravendita",
        "Kaufvertrag/Verkaufsvertrag mit/ohne Unterschriften",
    )

    DRAFT_CONTRACT_OF_SALE = DocumentCatElement(
        "661",
        DocumentTopic.PROPERTY,
        "Kaufvertragsentwurf",
        "Draft Contract of Sale",
        "Projet contrat achat vente",
        "Bozza del contratto di compravendita",
        "Entwurf des Kaufvertrages - kann bis zur Unterschrift noch Änderungen erfahren",
    )

    RESERVATION_CONTRACT = DocumentCatElement(
        "662",
        DocumentTopic.PROPERTY,
        "Reservationsvertrag",
        "Reservation Contract",
        "Promesse achat vente",
        "Contratto di riservazione",
        "Vertrag, der die Liegenschaft für den Käufer reserviert (mit/ohne Unterschriften)",
    )

    RESERVATION_PAYMENT = DocumentCatElement(
        "664",
        DocumentTopic.PROPERTY,
        "Reservationszahlung",
        "Reservation Payment",
        "Paiement réservation",
        "Pagamento di riservazione",
        "Bestätigung der erfolgten Reservationszahlung (Brief des Empfängers oder Bankauszug Überweisung)",
    )

    PURCHASE_CONTRACT_REGISTRATION = DocumentCatElement(
        "665",
        DocumentTopic.PROPERTY,
        "Anmeldung zum Kaufvertrag",
        "Purchase Contract Registration",
        "Enregistrement contrat achat",
        "Registrazione per il contratto di acquisto",
        "Antragsformular für die Erstellung des Kaufvertrages",
    )

    PURCHASE_PRICE_LIST_PROPERTY = DocumentCatElement(
        "667",
        DocumentTopic.PROPERTY,
        "Preisliste Wohneinheiten",
        "Purchase Price List Property Units",
        "Liste prix promotions PPE/Villas",
        "Listino prezzi unità residenziali",
        "Liste von Verkaufspreisen für Wohnungen in Verkaufsdokumentation oder als Zusatzdokument",
    )

    PURCHASE_MISC = DocumentCatElement(
        "669",
        DocumentTopic.PROPERTY,
        "Kauf Diverses",
        "Purchase Miscellaneous",
        "Achat divers",
        "Acquisto documento aggiuntivo",
        "Diverses",
        misc_type=True,
    )

    BUILDING_DESCRIPTION = DocumentCatElement(
        "670",
        DocumentTopic.PROPERTY,
        "Baubeschrieb",
        "Construction Description",
        "Descriptif construction",
        "Descrizione della costruzione",
        "Detaillierter Baubeschrieb/Projektbeschrieb",
    )

    CONSTRUCTION_REQUEST = DocumentCatElement(
        "671",
        DocumentTopic.PROPERTY,
        "Baugesuch",
        "Construction Application",
        "Demande permis de construire",
        "Domanda di concessione edilizia",
        "Baugesuch, wie es in der Gemeinde eingereicht wird",
    )

    CONSTRUCTION_PERMIT = DocumentCatElement(
        "672",
        DocumentTopic.PROPERTY,
        "Baubewilligung",
        "Construction Permit",
        "Permis de construire",
        "Permesso di costruzione",
        "Bewilligung des Baugesuchs durch Gemeinde",
    )

    CONSTRUCTION_REGULATIONS = DocumentCatElement(
        "673",
        DocumentTopic.PROPERTY,
        "Baureglement",
        "Construction Regulations",
        "Règlement construction",
        "Regolamenti di costruzione",
        "Generelle Bedingungen für Erstellung der Liegenschaft",
    )

    ARCHITECT_CONTRACT = DocumentCatElement(
        "674",
        DocumentTopic.PROPERTY,
        "Architektenvertrag",
        "Architect Contract",
        "Contrat architecte",
        "Contratto con l'architetto",
        "Vertrag zwischen Architekt und Bauherr",
    )

    CONTRACT_GENERAL_CONTRACTOR = DocumentCatElement(
        "675",
        DocumentTopic.PROPERTY,
        "Generalunternehmervertrag",
        "General Contractor Agreement",
        "Contrat entreprise générale",
        "Contratto d'impresa generale",
        "Vertrag zwischen Bauherr und Generalunternehmer",
    )

    CONTRACT_TOTAL_CONTRACTOR = DocumentCatElement(
        "676",
        DocumentTopic.PROPERTY,
        "Totalunternehmervertrag",
        "Total Contractor Agreement",
        "Contrat entreprise totale",
        "Contratto con l'impresa totale",
        "Vertrag zwischen Bauherr und Totalunternehmer",
    )

    CONSTRUCTION_CONTRACT = DocumentCatElement(
        "677",
        DocumentTopic.PROPERTY,
        "Werkvertrag",
        "Work Contract",
        "Contrat construction",
        "Contratto d'appalto",
        "Vertrag zwischen Bauherr und Ersteller",
    )

    PROPERTY_TRUSTEE_CONTRACT = DocumentCatElement(
        "678",
        DocumentTopic.PROPERTY,
        "Bautreuhändervertrag",
        "Property trustee contract",
        "Contrat mandataire construction",
        "Contratto fiduciario di proprietà",
        "Vertrag zwischen Bauherr und Bautreuhänder",
    )

    CONSTRUCTION_COMPANY_LIST = DocumentCatElement(
        "679",
        DocumentTopic.PROPERTY,
        "Unternehmerverzeichnis",
        "List of Contractors",
        "Annuaire entreprises",
        "Elenco delle imprese",
        "Verzeichnis von Handwerkern und am Bau beteiligten Unternehmen mit Kontaktangaben",
    )

    CONSTRUCTION_COST_ESTIMATE = DocumentCatElement(
        "680",
        DocumentTopic.PROPERTY,
        "Kostenschätzung",
        "Cost Estimate",
        "Estimation coûts",
        "Stima dei costi",
        "Grobe/pauschale Schätzung der Kosten für die Erstellung",
    )

    CONSTRUCTION_QUOTATION = DocumentCatElement(
        "681",
        DocumentTopic.PROPERTY,
        "Offerte",
        "Construction Quotation",
        "Devis",
        "Preventivo dei costi",
        "Allgemeine Offerte für Küche/Innenausbau/Garten/...",
    )

    PROJECT_BUDGET = DocumentCatElement(
        "682",
        DocumentTopic.PROPERTY,
        "Projektkostenplan",
        "Project Budget",
        "Plan financier par CFC",
        "Piano dei costi del progetto",
        "Detaillierte Planung der KProjektkosten nach BKP und Zahlungstermine",
    )

    CONSTRUCTION_COST_SUMMARY = DocumentCatElement(
        "683",
        DocumentTopic.PROPERTY,
        "Kostenzusammenstellung",
        "Cost Summary",
        "Récapitulatif coûts",
        "Sommario dei costi",
        "Zusammenstellung der Kosten; i.d.R. nach Erstellung inkl. allfällige Abweichungen zur Planung",
    )

    CONSTRUCTION_ACCOUNT = DocumentCatElement(
        "684",
        DocumentTopic.PROPERTY,
        "Bauabrechnung",
        "Construction Accounting",
        "Décompte construction",
        "Contabilità dell'edilizia",
        "Formale Abrechnung der gesamten Baukosten inkl. allfällige Abweichungen zur Planung",
    )

    CONSTRUCTION_PLAN = DocumentCatElement(
        "685",
        DocumentTopic.PROPERTY,
        "Bauplan",
        "Construction Plan",
        "Planning construction",
        "Piano di construzione",
        "Plandokumente für den Bau (ausser Grundriss)",
    )

    CONSTRUCTION_INSURANCE = DocumentCatElement(
        "687",
        DocumentTopic.PROPERTY,
        "Bauversicherung",
        "Construction Insurance",
        "Assurance construction",
        "Assicurazione del periodo di costruzione",
        "Versicherung während der Bauphase, auch Bauzeitversicherung genannt",
    )

    ADDITIONAL_COST_ACCOUNT = DocumentCatElement(
        "688",
        DocumentTopic.PROPERTY,
        "Mehrkostenabrechnung",
        "Additional Cost Account",
        "Décompte frais supplémentaires",
        "Fatturazione di costi aggiuntivi",
        "Abrechnung der zusätzlichen Kosten zu Bau oder Renovation",
    )

    CONSTRUCTION_MISC = DocumentCatElement(
        "689",
        DocumentTopic.PROPERTY,
        "Bau Diverses",
        "Construction Miscellaneous",
        "Construction divers",
        "Documento supplementare sulla costruzione",
        "Diverses",
        misc_type=True,
    )

    PROPERTY_BILL = DocumentCatElement(
        "690",
        DocumentTopic.PROPERTY,
        "Rechnung Liegenschaft",
        "Invoice property",
        "Facture immeuble",
        "Fattura di proprietà",
        "Rechnung für Kosten im Zusammenhang mit einer Liegenschaft (nicht Liegenschaftsabrechnung)",
    )

    RENOVATIONS = DocumentCatElement(
        "692",
        DocumentTopic.PROPERTY,
        "Renovationen",
        "Renovations",
        "Rénovations",
        "Ristrutturazioni",
        "",
    )

    REGISTRATION_LAND_REGISTER = DocumentCatElement(
        "694",
        DocumentTopic.PROPERTY,
        "Anmeldung Grundbuch",
        "Registration Land Register",
        "Demande inscription registre foncier",
        "Registrazione registro fondiario",
        "Anmeldung der Eigentumsänderung im Grundbuchamt",
    )

    LAND_REGISTER_BILL = DocumentCatElement(
        "697",
        DocumentTopic.PROPERTY,
        "Rechnung Grundbuchamt",
        "Bill Land Register",
        "Facture registre foncier",
        "Fattura registro fondario",
        "Rechnung für Grundbuchauszug",
    )

    LAND_REGISTER_MISC = DocumentCatElement(
        "698",
        DocumentTopic.PROPERTY,
        "Grundbuch Diverses",
        "Land Register Miscellaneous",
        "Registre foncier divers",
        "Documento aggiuntivo dello registro fondiario",
        "Diverses",
        misc_type=True,
    )

    PROPERTY_MISC = DocumentCatElement(
        "699",
        DocumentTopic.PROPERTY,
        "Liegenschaft Diverses",
        "Property Miscellaneous",
        "Immeuble divers",
        "Documento aggiuntivo immobiliare",
        "Diverses",
        misc_type=True,
    )

    MORTGAGE_REQUEST_FORM = DocumentCatElement(
        "705",
        DocumentTopic.FINANCING,
        "Hypothekaranfrage",
        "Mortgage Application",
        "Demande crédit hypothécaire",
        "Domanda di mutuo",
        "Gilt auch für Vermittler-Antragsformulare",
    )

    AUTHORIZATION_FOR_INQUIRIES = DocumentCatElement(
        "706",
        DocumentTopic.FINANCING,
        "Ermächtigung Auskünfte",
        "Authorization for Inquiries",
        "Autorisation prise renseignements",
        "Informazioni sull'autorizzazione",
        "Ermächtigung für das Einholen von Auskünften bei Ämtern/ZEK/etc. Gilt auch für Vermittler-Auskunftsvollmachten. Abgrenzung zu 768 beachten",
    )

    AUTHORIZATION_EMAIL = DocumentCatElement(
        "707",
        DocumentTopic.FINANCING,
        "Autorisierung E-Mail",
        "Authorization E-Mail",
        "Autorisation E-Mail",
        "Autorizzazione E-mail",
        "Autorisierung die Benutzung von E-Mail durch die Bank",
    )

    PROOF_OF_FUNDS = DocumentCatElement(
        "710",
        DocumentTopic.FINANCING,
        "Eigenmittelnachweis",
        "Proof of Funds for Down Payment",
        "Justificatif fonds propres",
        "Prove del capitale proprio",
        "Zusammenstellung der für die Finanzierung verwendeten Mittel, auf Basis der Bankauszüge",
    )

    STATEMENT_OF_ASSETS = DocumentCatElement(
        "711",
        DocumentTopic.FINANCING,
        "Vermögensaufstellung",
        "Statement of Assets",
        "Relevé fortune",
        "Estratto del patrimonio",
        "Zusammenstellung der insgesamt vorhandenen Mittel, auf Basis der Bankauszüge",
    )

    US_PERSON_FORM = DocumentCatElement(
        "712",
        DocumentTopic.FINANCING,
        "US-Person Formular",
        "US Person Form",
        "Formulaire personnes américaines",
        "Modulo Persona USA",
        "Erklärung zur Nationalität (Non-US-Person)",
    )

    DETERMINATION_OF_BENEFICIARY = DocumentCatElement(
        "713",
        DocumentTopic.FINANCING,
        "Feststellung des wirtschaftlich Berechtigten",
        "Determination of Beneficiary",
        "Détermination bénéficiaire effectif",
        "Determinazione del beneficiario effettivo",
        "Deklaration der Person die wirtschaftlich Anspruch auf Besitz/Mietzinszahlungen/sonstige Zahlungen hat (wegen Geldwäscherei)",
    )

    IRREVOCABLE_PROMISES_TO_PAY = DocumentCatElement(
        "715",
        DocumentTopic.FINANCING,
        "Unwiderrufliches Zahlungsversprechen",
        "Irrevocable Promises to Pay",
        "Promesse irrévocable paiement",
        "Promessa di pagamento",
        "Zahlungsversprechen das die Bank oder eine PK zuhanden des Notars ausstellt",
    )

    MORTGAGE_CONTRACT = DocumentCatElement(
        "720",
        DocumentTopic.FINANCING,
        "Hypothekarvertrag",
        "Mortgage Contract",
        "Contrat hypothécaire",
        "Contratto ipotecario",
        "Hypothekarvertrag des Kunden (inkl. Betrag, Laufzeiten)",
    )

    MORTGAGE_PRODUCT_CONFIRMATION = DocumentCatElement(
        "721",
        DocumentTopic.FINANCING,
        "Produktvereinbarung",
        "Mortgage Product Confirmation",
        "Convention produit",
        "Convenzione di prodotto",
        "Vertrag in dem das Hypothekarprodukt (z.B. 5j fest zu 1.1%) - nur 1-2 Seiten lang",
    )

    MORTGAGE_FRAMEWORK_CONTRACT = DocumentCatElement(
        "722",
        DocumentTopic.FINANCING,
        "Rahmenvertrag Hypothek",
        "Mortgage Framework Contract",
        "Contrat-cadre hypothèque",
        "Contratto quadro ipotecario",
        "Rahmenvertrag des Kunden (mit Betrag aber ohne Laufzeiten)",
    )

    FINANCING_CONFIRMATION = DocumentCatElement(
        "723",
        DocumentTopic.FINANCING,
        "Finanzierungsbestätigung",
        "Financing Confirmation",
        "Confirmation financement",
        "Conferma del finanziamento",
        "(Unverbindliche) Bestätigung der Bank, dass eine Finanzierung möglich ist (vor Detailprüfung)",
    )

    MORTGAGE_DUE_NOTICE = DocumentCatElement(
        "724",
        DocumentTopic.FINANCING,
        "Fälligkeitsanzeige",
        "Notification of payment due date",
        "Avis prélèvement",
        "Avviso scadenza rate",
        "Bankauszug, der die Zahlung der Zinsen bestätigt",
    )

    FINANCING_OFFER = DocumentCatElement(
        "725",
        DocumentTopic.FINANCING,
        "Finanzierungsofferte",
        "Financing Offer",
        "Offre financement",
        "Offerta di finanziamento",
        "Richtofferte, üblicherweise mit Auflistung möglicher Zinssätze und Zusatzbedingungen",
    )

    ACCEPTANCE_OF_MORTAGE_OFFER = DocumentCatElement(
        "726",
        DocumentTopic.FINANCING,
        "Annahme der Finanzierung",
        "Acceptance of Mortgage Offer",
        "Acceptation financement",
        "Accettazione del finanziamento",
        "Bestätigung der Offert-Annahme durch Bankkunden",
    )

    MORTGAGE_CONTRACT_CONFIRMATION = DocumentCatElement(
        "727",
        DocumentTopic.FINANCING,
        "Abschlussbestätigung Finanzierung",
        "Confirmation of Financing",
        "Confirmation financement",
        "Conferma del finanziamento",
        "Bestätigung des Hypothekarabschlusses durch das finanzierende Institut",
    )

    AFFORDABILITY_CALCULATION = DocumentCatElement(
        "728",
        DocumentTopic.FINANCING,
        "Tragbarkeitsberechnung",
        "Affordability Calculation",
        "Calcul capacité financière",
        "Calcolo dell'accessibilità",
        "Berechnung Belehnung und Tragbarkeit mit kalkulatorischem Zinssatz (sonst Finanzierungsofferte)",
    )

    MORTGAGE_TERMINATION = DocumentCatElement(
        "729",
        DocumentTopic.FINANCING,
        "Kündigung Hypothekarvertrag",
        "Mortgage Termination",
        "Résiliation contrat hypothécaire",
        "Disdetta contratto ipotecario",
        "Bestätigung der Kündigung (oder des regulären Ablaufs) der Hypothek durch die Bank",
    )

    PREPAYMENT_PENALTY = DocumentCatElement(
        "730",
        DocumentTopic.FINANCING,
        "Vorfälligkeitsentschädigung",
        "Prepayment Penalty",
        "Indemnité résiliation anticipée",
        "Risarcimento estinzione anticipata",
        "Informationen zu einer (möglichen) Vorfälligkeitsentschädigung oder Berechnung dieser",
    )

    CORRESPONDENCE_EMAIL = DocumentCatElement(
        "738",
        DocumentTopic.FINANCING,
        "Email-Korrespondenz",
        "Email Correspondence",
        "Correspondance e-mail",
        "Corrispondenza e-mail",
        "Korrespondenz via E-Mail",
    )

    CORRESPONDENCE_LETTER = DocumentCatElement(
        "739",
        DocumentTopic.FINANCING,
        "Brief-Korrespondenz",
        "Letter Correspondence",
        "Correspondance lettre",
        "Corrispondenza lettera",
        "Korrespondenz via Brief",
    )

    CREDITOR_CHANGE = DocumentCatElement(
        "741",
        DocumentTopic.FINANCING,
        "Gläubigerwechsel",
        "Creditor Change",
        "Changement créancier",
        "Cambiamento di creditore",
        "Übertragung der Forderung für die eine Grundpfandverschreibung errichtet ist",
    )

    TRANSFER_OF_SECURITY = DocumentCatElement(
        "742",
        DocumentTopic.FINANCING,
        "Sicherungsvereinbarung",
        "Transfer of Security",
        "Contrat garanties",
        "Accordo di sicurezza",
        "Heisst auch Sicherungsübereignung, ist teilweise in den Rahmenvertrag integriert",
    )

    TRANSFER_AGREEMENT = DocumentCatElement(
        "743",
        DocumentTopic.FINANCING,
        "Übertragungsvereinbarung",
        "Transfer Agreement",
        "Convention fiducie",
        "Accordo di trasferimento",
        "Dokument, das die Übertragung des Schuldscheins an eine Drittpartei ermöglicht (Verbriefung). Existiert i.d.R. nur als Ergänzung einer Sicherungsvereinbarung",
    )

    BASE_CONTRACT = DocumentCatElement(
        "744",
        DocumentTopic.FINANCING,
        "Basisvertrag",
        "Base Contract",
        "Contrat de base",
        "Contratto di base",
        "Vertrag zu Basisdienstleistungen zwischen Bank und Bankkunde",
    )

    MORTGAGE_SUBORDINATION_AGREEMENT = DocumentCatElement(
        "745",
        DocumentTopic.FINANCING,
        "Rangrücktrittserklärung",
        "Subordination agreement",
        "Déclaration postposition",
        "Dichiarazione di postergazione",
        "Dokument, das in Finanzierungs- oder Hypothekarkontexten verwendet wird, um die Rangfolge der Gläubigeransprüche im Falle einer Zahlungsunfähigkeit oder einer Insolvenz des Schuldners festzulegen",
    )

    SPECIMEN_SIGNATURE = DocumentCatElement(
        "746",
        DocumentTopic.FINANCING,
        "Unterschriftenmuster",
        "Specimen Signature",
        "Spécimen signature",
        "Modello di firma",
        "Muster der Unterschrift",
    )

    FILE_NOTE_FINANCING = DocumentCatElement(
        "747",
        DocumentTopic.FINANCING,
        "Aktennotiz Finanzierung",
        "File Note Financing",
        "Synthèse financement",
        "Memorandum di finanziamento",
        "Allgemeine Notiz zur Finanzierung",
    )

    DEBT_CERTIFICATE = DocumentCatElement(
        "748",
        DocumentTopic.FINANCING,
        "Schuldbrief",
        "Debt Certificate",
        "Cédule hypotécaire",
        "Cartella ipotecaria",
        "Persönliche Forderung, die grundpfändlich sichergestellt ist",
    )

    MORTGAGE_MISC = DocumentCatElement(
        "749",
        DocumentTopic.FINANCING,
        "Hypothek Diverses",
        "Mortgage Miscellaneous",
        "Hypothèque divers",
        "Documento supplementre dell'ipoteca",
        "Diverses zur Hypothek resp. dem Hypothekarvertrag",
        misc_type=True,
    )

    PENSION_PLEDGE = DocumentCatElement(
        "750",
        DocumentTopic.FINANCING,
        "Verpfändung Vorsorge",
        "Pension Plegde",
        "Acte de nantissement",
        "Pignoramento della avere providenziale",
        "Formular/Vertrag zur Verpfändung Säule 2 oder 3a für Finanzierung einer Liegenschaft",
    )

    PLEDGE_NOTICE = DocumentCatElement(
        "752",
        DocumentTopic.FINANCING,
        "Verpfändungsanzeige",
        "Pledge Notice",
        "Avis mise en gage",
        "Avviso di pegno",
        "Brief des Hypothekargebers an PK/Freizügigkeitsstiftung, dass Ansprüche des Hypothekarnehmers verpfändet sind",
    )

    FINANCING_CHECKLIST_DOCUMENTS = DocumentCatElement(
        "756",
        DocumentTopic.FINANCING,
        "Checkliste Unterlagen",
        "Checklist Documents",
        "Checklist documents hypothécaires",
        "Lista di controllo",
        "Liste der Dokumente, die durch den Hypothekarnehmer zu liefern sind",
    )

    FINANCING_FEES_LIST = DocumentCatElement(
        "757",
        DocumentTopic.FINANCING,
        "Gebührenübersicht",
        "Fee Summary",
        "Aperçu frais bancaires",
        "Panoramica delle tariffe",
        "Übersicht zu Bankgebühren",
    )

    TERMS_AND_CONDITIONS = DocumentCatElement(
        "758",
        DocumentTopic.FINANCING,
        "AGBs",
        "Terms and Conditions",
        "Conditions générales",
        "Termini e condizioni generali",
        "Allgemeine Geschäftsbeziehungen",
    )

    GENERAL_INFO = DocumentCatElement(
        "759",
        DocumentTopic.FINANCING,
        "Allgemeine Erläuterungen",
        "General Information",
        "Explications générales",
        "Spiegazioni generali",
        "Allgemeine Erläuterung über Business Modell, Prozesse, Strukturen etc.",
    )

    BROKER_MANDATE = DocumentCatElement(
        "767",
        DocumentTopic.FINANCING,
        "Mandat Vermittler",
        "Broker Mandate",
        "Mandat Intermédiaire",
        "Mandato Intermediario",
        "Beauftragung des Vermittlers durch den Endkunden (ggf. inkl. Auskunftsermächtigung und Ermächtigung Datenübertragung)",
    )

    BROKER_AUTHORIZATION_BANK_SECRECY = DocumentCatElement(
        "768",
        DocumentTopic.FINANCING,
        "Entbindung Bankkundengeheimnis Broker",
        "Release from Bank Secrecy Broker",
        "Déclaration levée secret bancaire",
        "Liberazione dal segreto bancario",
        "Kunde entbindet die Bank vom Bankkundengeheimnis gegenüber Vermittler und akzeptiert Entschädigung ('Vermittlerformular inkl. Bankkundengeheimnis')",
    )

    BROKER_AUTHORIZATION = DocumentCatElement(
        "769",
        DocumentTopic.FINANCING,
        "Ermächtigung Datenübermittlung Vermittler",
        "Authorization for Data Transfer to Broker",
        "Autorisation transfert des données",
        "Autorizzazione per la trasmissione di dati",
        "Ermächtigung der Datenübermittlung gegenüber Vermittler (ohne Wording Bankkundengeheimnis) aber evtl inkl Entschädigung ('Vermittlerformular')",
    )

    PLATFORM_AGREEMENT = DocumentCatElement(
        "772",
        DocumentTopic.FINANCING,
        "Plattformvereinbarung",
        "Broker Platform Agreement",
        "Conditions plate-forme intermédiaire",
        "Condizioni della piattaforma intermediaria",
        "Service agreement with platform that connects end-clients, brokers with lenders",
    )

    BROKER_MISC = DocumentCatElement(
        "779",
        DocumentTopic.FINANCING,
        "Vermittler Diverses",
        "Broker Miscellaneous",
        "Intermédiaire divers",
        "Documento aggiuntivo dell'intermediario",
        "Diverses",
        misc_type=True,
    )

    ASSUMPTION_DEBT_NOTICE = DocumentCatElement(
        "783",
        DocumentTopic.FINANCING,
        "Anzeige Schuldübergang",
        "Notice of Transfer of Debt",
        "Avis transfert dette",
        "Avviso di trasferimento del debito",
        "Anzeige Schuldübernahme (neuer Schuldner akzeptiert die Schuldbriefe) oder Anzeige Schuldübergang (im Erbfall werden die Schuldbriefe auf Erben übertragen)",
    )

    AGREEMENT_CHARGE_IMMOVABLE_PROPERTY = DocumentCatElement(
        "785",
        DocumentTopic.FINANCING,
        "Grundpfandvertrag",
        "Constitutive Deed of Mortgage Certificate",
        "Acte constitutif cédule",
        "Atto costitutivo di ipoteca",
        "Errichtungsurkunde / Erstellung Schuldbrief Grundschuld",
    )

    LETTER_COMMITMENT_NOTARY = DocumentCatElement(
        "786",
        DocumentTopic.FINANCING,
        "Verpflichtungserklärung Notar",
        "Letter of Commitment Notary",
        "Lettre engagement notaire",
        "Lettera impegno del notaio",
        "Formale Bestätigung des Notars zu einer Hypothekartransaktion. Enthält u.a. Bestätigung, dass Hypothekarverpflichtung der Bank korrekt auf das Grundstück eingetragen wird.",
    )

    CORRESPONDENCE_NOTARY = DocumentCatElement(
        "788",
        DocumentTopic.FINANCING,
        "Korrespondenz Notar",
        "Correspondence Notary",
        "Currier - email notaire",
        "Corrispondenza notaio",
        "Austausch von Briefen / Emails / Dokumenten mit dem Notar",
    )

    POWER_OF_ATTORNEY = DocumentCatElement(
        "796",
        DocumentTopic.FINANCING,
        "Vollmacht",
        "Power of Attorney",
        "Procuration",
        "Procura",
        "Vollmachtsdokument (z.B. Vertretung während Notariatstermin)",
    )

    NOTARY_MISC = DocumentCatElement(
        "798",
        DocumentTopic.FINANCING,
        "Notariat Diverses",
        "Notary Miscellaneous",
        "Notaire divers",
        "Documento aggiuntivo notaio",
        "Diverses",
        misc_type=True,
    )

    FINANCING_MISC = DocumentCatElement(
        "799",
        DocumentTopic.FINANCING,
        "Finanzierung Diverses",
        "Financing Miscellaneous",
        "Financement divers",
        "Documento aggiuntivo per il finanziamento",
        "Diverses",
        misc_type=True,
    )

    MISC_CAT = DocumentCatElement(
        "900",
        DocumentTopic.MISC,
        "Varia",
        "Misc",
        "Autre",
        "Altro",
        "Diverse Dokumente, die keiner anderen Kategorie zugeordnet werden können",
        misc_type=True,
    )

    IMMUTABLE_XLS = DocumentCatElement(
        "910", DocumentTopic.MISC, "XLS", "XLS", "XLS", "XLS", ""
    )

    BILL_MISC = DocumentCatElement(
        "920",
        DocumentTopic.MISC,
        "Rechnung",
        "Bill Misc",
        "Facture",
        "Fattura",
        "Diverse Rechnungen, nicht auf Liegenschaft bezogen (z.B. Weiterbildung)",
    )

    DEBT_COLLECTION_INFORMATION_BILL = DocumentCatElement(
        "925",
        DocumentTopic.MISC,
        "Betreibungsauskunft Rechnung",
        "Debt Collection Register Bill",
        "Facture registre poursuites",
        "Fattura registro d'esecuzione e fallimento",
        "Rechnung des Betreibungsamtes für den Betreibungsauszug",
    )

    DEBT_COLLECTION_INFORMATION_RECEIPT = DocumentCatElement(
        "926",
        DocumentTopic.MISC,
        "Betreibungsauskunft Quittung",
        "Debt Collection Register Bill",
        "Quittance registre poursuites",
        "Fattura registro delle esecuzioni",
        "Quittung des Betreibungsamtes für die Betreibungsauskunft",
    )

    DEBT_COLLECTION_INFORMATION_ORDER_BETREIBUNGSCHALTER_PLUS = DocumentCatElement(
        "930",
        DocumentTopic.MISC,
        "Betreibungsschalter Plus Info",
        "Betreibungsschalter Plus Info",
        "Guichet poursuites plus info",
        "Sportello esecuzioni piu info",
        "Informationsblatt des Service 'Betreibungsschalter Plus'",
    )

    DEBT_COLLECTION_INFORMATION_ORDER_CRESURA = DocumentCatElement(
        "931",
        DocumentTopic.MISC,
        "Cresura Bestellung Betreibungsauskunft",
        "Cresura Order Debt Collection Register",
        "Commande poursuites Cresura",
        "Cresura ordinazione registro d'esecuzione e fallimento",
        "Bestellformular für Einholung Betreibungsauskunft via Cresura",
    )

    DEBT_COLLECTION_INFORMATION_ORDER_TELEDATA = DocumentCatElement(
        "933",
        DocumentTopic.MISC,
        "Teledata Bestellung Betreibungsauskunft",
        "Teledata Order Debt Collection Register",
        "Commande poursuites Teledata",
        "Teledata ordinazione registro d'esecuzione e fallimento",
        "Bestellformular für Einholung Betreibungsauskunft via Teledata",
    )

    YELLOW_IDENTIFICATION_POST = DocumentCatElement(
        "934",
        DocumentTopic.MISC,
        "Gelbe Identifikation Post",
        "Yellow Identification Post",
        "Identification Jaune Poste",
        "Identificazione postale",
        "Formular / Gutschein für Identifikation via Post",
    )

    ZEK_INFO = DocumentCatElement(
        "940",
        DocumentTopic.MISC,
        "Zek Allgemeine Informationsseite",
        "Zek Informations",
        "Zek General Information",
        "Zek Informazione",
        "Zek Info",
    )

    HEALTH_INSURANCE = DocumentCatElement(
        "951",
        DocumentTopic.MISC,
        "Krankenversicherung",
        "Health Insurance",
        "Assurance maladie",
        "Assicurazione sanitaria",
        "Police/Auszug/Prämienrechnung der Krankenkasse (Steuerbeilagen)",
    )

    DAYCARE_CONFIRMATION = DocumentCatElement(
        "952",
        DocumentTopic.MISC,
        "Kinderbetreuung Bescheinigung",
        "Daycare Children Confirmation",
        "Attestation garde enfants",
        "Conferma dell'assistenza ai bambini",
        "Steuerbeilage für Abzug Kinderbetreuung durch Dritte",
    )

    DONATION_CONFIRMATION = DocumentCatElement(
        "953",
        DocumentTopic.MISC,
        "Spendenbescheinigung",
        "Donation Confirmation",
        "Attestation dons",
        "Ricevuta della donazione",
        "Steuerbeilage für Abzug Spenden",
    )

    WHITE_PAGES = DocumentCatElement(
        "999",
        DocumentTopic.MISC,
        "Leere Seiten",
        "Empty Pages",
        "Pages vierges",
        "Pagine bianche",
        "Seiten, die als leer erkannt wurden",
    )

    # CUSTOM doccats - not yet migrated - start here ###############################################################################################

    BEKB_EKD108 = DocumentCatElement(
        "500-EKD108",
        DocumentTopic.PARTNER,
        "Finanzierungspotential FK",
        "Financing Potential Corporate Clients",
        "Potentiel de financement des entreprises",
        "Potenziale di finanziamento delle aziende",
        document_catalog=DocumentCatalog.BEKB,
    )

    # 241023 mt: added
    BEKB_EKD142 = DocumentCatElement(
        "600-EKD142",
        DocumentTopic.PROPERTY,
        "myky-Dossier",
        "myky-Dossier",
        "Dossier myky",
        "myky-Dossier",
        document_catalog=DocumentCatalog.BEKB,
    )

    BEKB_EKD120 = DocumentCatElement(
        "200",
        DocumentTopic.FINANCING,
        "FinPla: Checkliste / Korrespondenz",
        "FinPla: Checkliste / Korrespondenz",
        "PlaFi: Liste de pointage / correspondance",
        "",
        "",
    )
    BEKB_FIPLA_RESULT = DocumentCatElement(
        "400",
        DocumentTopic.FINANCING,
        "Finanzplanung",
        "Financial Planning",
        "Planification financière",
        "Pianificazione finanziaria",
        "",
    )

    BEKB_FIPLA_FORM = DocumentCatElement(
        "200",
        DocumentTopic.PERSON_GENERAL,
        "Fragebogen Finanzplanung",
        "Financial Planning Questionnaire",
        "Questionnaire de planification financière",
        "Questionario di pianificazione finanziaria",
        "Fragebogen für den Kunden, der eine Gesamtsicht der finanziellen Situation darstellt.",
        document_catalog=DocumentCatalog.BEKB,
    )

    CLIENTIS_DCB_ARBEITSHILFE_FIN = DocumentCatElement(
        "700-DCB50",
        DocumentTopic.FINANCING,
        "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
        "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
        "Contrôle des versements - du bouclement",
        "Arbeitshilfe Auszahlungs- und Schlusskontrolle",
        document_catalog=DocumentCatalog.CLIENTIS,
    )

    FS24_FACTSHEET = DocumentCatElement(
        "100-FS24",
        DocumentTopic.PARTNER,
        "FS24 Factsheet",
        "FS24 Factsheet",
        "FS24 Factsheet",
        "FS24 Factsheet",
        document_catalog=DocumentCatalog.LEGACY,
    )
    FS24_INFO_DOSSIER = DocumentCatElement(
        "101-FS24",
        DocumentTopic.PARTNER,
        "FS24 Infoblatt",
        "FS24 Information",
        "FS24 Info",
        "FS24 Info",
        document_catalog=DocumentCatalog.LEGACY,
    )
    FS24_AUTHORIZATION = DocumentCatElement(
        "120-FS24",
        DocumentTopic.PARTNER,
        "FS24 Ermächtigung",
        "FS24 Authorization Form",
        document_catalog=DocumentCatalog.LEGACY,
    )
    FS24_ZINSANNAHME = DocumentCatElement(
        "160-FS24",
        DocumentTopic.PARTNER,
        "FS24 Zinsannahmebestätigung",
        "FS24 Offer Acceptance Form",
        document_catalog=DocumentCatalog.LEGACY,
    )
    HYPOGUIDE_ZINSANNAHME = DocumentCatElement(
        "161-FS24",
        DocumentTopic.PARTNER,
        "Hypoguide Zinsannahmebestätigung",
        "Hypoguide Offer Acceptance Form",
        document_catalog=DocumentCatalog.LEGACY,
    )
    # Any document with full address body but not specifically matched before
    FS24_MISC = DocumentCatElement(
        "199-FS24",
        DocumentTopic.PARTNER,
        "FS24",
        "FS24",
        "FS24",
        "FS24",
        document_catalog=DocumentCatalog.LEGACY,
    )

    MB_TOTAL_ENGAGEMENT = DocumentCatElement(
        "101-MB",
        DocumentTopic.PARTNER,
        "Gesamtengagement",
        "Total Engagement",
        document_catalog=DocumentCatalog.LEGACY,
    )
    MB_TOTAL_ENGAGEMENT_PROTOCOL = DocumentCatElement(
        "102-MB",
        DocumentTopic.PARTNER,
        "Protokoll GE",
        "Protocol Total Engagement",
        document_catalog=DocumentCatalog.LEGACY,
    )
    MB_KINFO = DocumentCatElement(
        "105-MB",
        DocumentTopic.PARTNER,
        "Kinfo",
        "Kinfo",
        document_catalog=DocumentCatalog.LEGACY,
    )
    MB_EKB = DocumentCatElement(
        "120-MB",
        DocumentTopic.PARTNER,
        "Elektronische Kreditbewilligung",
        "Electronic Credit Approval",
        document_catalog=DocumentCatalog.LEGACY,
    )
    MB_SOKO = DocumentCatElement(
        "122-MB",
        DocumentTopic.PARTNER,
        "SoKo-Antrag",
        "Special Rate Request",
        document_catalog=DocumentCatalog.LEGACY,
    )
    MB_CREDIT_APPROVAL_REQUEST = DocumentCatElement(
        "140-MB",
        DocumentTopic.PARTNER,
        "Kreditvorlage",
        "Credit Approval Request",
        document_catalog=DocumentCatalog.LEGACY,
    )
    MB_INTERNET_BANKING_MESSAGE = DocumentCatElement(
        "190-MB",
        DocumentTopic.PARTNER,
        "IB Kunden-Mails",
        "IB Client Mails",
        document_catalog=DocumentCatalog.LEGACY,
    )

    HBL_PRICING = DocumentCatElement(
        "120-HBL",
        DocumentTopic.PARTNER,
        "Pricingblatt",
        "Pricing Sheet",
        document_catalog=DocumentCatalog.HBL,
    )
    HBL_DARLEHENSZUSICHERUNG = DocumentCatElement(
        "131-HBL",
        DocumentTopic.PARTNER,
        "Darlehenszusicherung",
        "Land Register Modification Request",
        document_catalog=DocumentCatalog.HBL,
    )
    HBL_DIREKTAUFTRAG = DocumentCatElement(
        "133-HBL",
        DocumentTopic.PARTNER,
        "Auftrag Finanzieren",
        "Order to Process Mortgage",
        document_catalog=DocumentCatalog.HBL,
    )
    HBL_MORTGAGE_RENEWAL = DocumentCatElement(
        "136-HBL",
        DocumentTopic.PARTNER,
        "Auftrag Finanzieren Verlängerung",
        "Order to Process Mortgage Renewal",
        document_catalog=DocumentCatalog.HBL,
    )

    # CUSTOM doccats VZ - manually added - start here ###############################################################################################

    VZ_GREY_CASE_APPLICATION = DocumentCatElement(
        "700-30",
        DocumentTopic.FINANCING,
        "Graufall Antrag",
        "Grey Case Application",
        "Demande de cas gris",
        "Domanda di caso grigio",
        "",
        document_catalog=DocumentCatalog.VZ,
    )

    # CUSTOM doccats ZKB - generated - start here ###############################################################################################

    ZKB_19747_UNTERNEHMENSPROFIL = DocumentCatElement(
        "598-10",
        DocumentTopic.COMPANY,
        "ZKB Unternehmensprofil",
        "ZKB Unternehmensprofil",
        "ZKB Unternehmensprofil",
        "ZKB Unternehmensprofil",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_19807_ERFASSUNGSGESCHAEFT_HANDELSGESCHAEFTE = DocumentCatElement(
        "598-60",
        DocumentTopic.COMPANY,
        "Erfassungsblatt Handelsgeschäfte",
        "Erfassungsblatt Handelsgeschäfte",
        "Erfassungsblatt Handelsgeschäfte",
        "Erfassungsblatt Handelsgeschäfte",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_19833_LEASING_RAHMENLIMITE = DocumentCatElement(
        "598-62",
        DocumentTopic.COMPANY,
        "Leasing Rahmenlimite",
        "Leasing Rahmenlimite",
        "Leasing Rahmenlimite",
        "Leasing Rahmenlimite",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_17627_COSTNG_PRICING_IGL = DocumentCatElement(
        "598-64",
        DocumentTopic.COMPANY,
        "Costing-Pricing IGL",
        "Costing-Pricing IGL",
        "Costing-Pricing IGL",
        "Costing-Pricing IGL",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_18589_OBJEKTUNTERLAGEN_IGL = DocumentCatElement(
        "598-66",
        DocumentTopic.COMPANY,
        "Objektunterlagen IGL",
        "Objektunterlagen IGL",
        "Objektunterlagen IGL",
        "Objektunterlagen IGL",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_19643_AUSKUNFT_OBERZOLLDIREKTION_OZD_FUER_LSVA = DocumentCatElement(
        "598-68",
        DocumentTopic.COMPANY,
        "Auskunft Oberzolldirektion OZD für LSVA",
        "Auskunft Oberzolldirektion OZD für LSVA",
        "Auskunft Oberzolldirektion OZD für LSVA",
        "Auskunft Oberzolldirektion OZD für LSVA",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_18401_LEASINGANTRAG_FIRMENANGABE = DocumentCatElement(
        "598-70",
        DocumentTopic.COMPANY,
        "Leasingantrag Firmenangabe",
        "Leasingantrag Firmenangabe",
        "Leasingantrag Firmenangabe",
        "Leasingantrag Firmenangabe",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_18190_JAHRESRECHNUNG_KUNDE_LEASING = DocumentCatElement(
        "598-72",
        DocumentTopic.COMPANY,
        "Jahresrechnung Kunde Leasing",
        "Jahresrechnung Kunde Leasing",
        "Jahresrechnung Kunde Leasing",
        "Jahresrechnung Kunde Leasing",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_17557_BONITAETSDOKUMENT_ALLGEMEIN_IGL = DocumentCatElement(
        "598-74",
        DocumentTopic.COMPANY,
        "Bonitätsdokument allgemein IGL",
        "Bonitätsdokument allgemein IGL",
        "Bonitätsdokument allgemein IGL",
        "Bonitätsdokument allgemein IGL",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_17988_FINANZUNTERLAGE_IGL = DocumentCatElement(
        "598-76",
        DocumentTopic.COMPANY,
        "Finanzunterlage IGL",
        "Finanzunterlage IGL",
        "Finanzunterlage IGL",
        "Finanzunterlage IGL",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_30021_KORRESPONDENZ_IGL_ALLGEMEIN = DocumentCatElement(
        "598-78",
        DocumentTopic.COMPANY,
        "Korrespondenz IGL allgemein",
        "Korrespondenz IGL allgemein",
        "Korrespondenz IGL allgemein",
        "Korrespondenz IGL allgemein",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    ZKB_VBV = DocumentCatElement(
        "600-VBV",
        DocumentTopic.PROPERTY,
        "Vereinfachtes Bewertungsverfahren",
        "Vereinfachtes Bewertungsverfahren",
        "Vereinfachtes Bewertungsverfahren",
        "Vereinfachtes Bewertungsverfahren",
        "",
        document_catalog=DocumentCatalog.ZKB,
    )

    # CUSTOM doccats ZKB - generated - end here ###############################################################################################

    # 230830 mt: disable custom zkb doc dection from 2022
    # ZKB_GVZ_VIEWER = DocumentCatElement("121-ZKB", DocumentTopic.PARTNER, "GVZ-Viewer", "GVZ-Viewer", "GVZ-Viewer", "GVZ-Viewer")
    # ZKB_RENT_CALCULATOR = DocumentCatElement("125-ZKB", DocumentTopic.PARTNER, "Mietrechner", "Rent Calculator")
    # ZKB_DOSSIER_SUMMARY = DocumentCatElement("100-ZKB", DocumentTopic.PARTNER, "Dossier-Zusammenfassung", "Dossier Summary")

    def __repr__(self):
        return self.name
