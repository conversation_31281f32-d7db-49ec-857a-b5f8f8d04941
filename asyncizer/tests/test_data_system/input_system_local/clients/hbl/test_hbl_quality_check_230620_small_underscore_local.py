from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS

source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS}/hbl/230620_small_underscore"
)


@pytest.mark.asyncio
async def test_230620_small_underscore_grundriss():
    await DossierTest(
        override_existing=True,
        source_folder=source_folder / "grundriss_special_char",
        dest_folder_prefix="test_hbl_small_underscore",
        # source_file_filter=['_Gebaêudeversicherungsnachweis.pdf'],
        show_page=-1,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={},
            expected_num_extracted_file_exceptions=0,  # all skipped
        ),
    ).run()
