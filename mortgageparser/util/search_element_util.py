from typing import List, Dict

from abbyyplumber.api import SearchR<PERSON><PERSON>, Page, DEFAULT_FUZZY_DIST
from abbyyplumber.converter.ValueConverter import (
    AddressConverter,
    DateConverter,
    MostRecentDateConverter,
    CleanNameConverter,
    ValidZipCodeConverter,
    CurrencyConverter,
    CleanStreetConverter,
    CleanCityConverter,
    BirthDateConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    Search<PERSON>lementArea,
    SearchElementMultiStaticText,
    SearchElementReference,
    SearchElementLabeledField,
    SearchElementConstant,
    SearchElementFirstname,
    SearchElementZipCity,
    SearchElementRegex,
    SearchElementOuterBoundingBox,
    SearchElementAhvNew,
    SearchElementConstrainedArea,
    SearchElementMultiLabeledField,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    ReferenceBoundaryVertical,
    SearchRelationAbove,
    SearchRelationRightOf,
    ReferenceBoundaryHorizontal,
    SearchRelationLeftOf,
    SearchRelationInside,
)
from abbyyplumber.util.plumberstudio_util import Per<PERSON>ageRang<PERSON>, RANGE_LEFT
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FIRSTNAME,
    P1_LASTNAME,
    P1_FULLNAME,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_FULLNAME,
    FIELD_ADDRESS_BLOCK,
    FIELD_DOCUMENT_DATE,
    FIELD_PRODUCT,
    FIELD_COMPANY,
    FIELD_FIRSTNAME,
    FIELD_ZIP_CITY,
    FIELD_ZIP,
    FIELD_CITY,
    FIELD_STREET,
    FIELD_CANTON_SHORT,
    FIELD_AHV_NEW,
    FIELD_LASTNAME,
    FIELD_YEAR,
    FIELD_DATE_OF_BIRTH,
)
from hypodossier.util.basis_string_util import TOKENS_LEADING_TITLE
from hypodossier.util.constants import SPACER_CHAR

EXTRACT_DEBUG = False


def create_element_firstname_from_fullname(target):
    return SearchElementFirstname(
        FIELD_FIRSTNAME.name, target, target_name=FIELD_FULLNAME.name
    )


def create_element_company(company: str):
    return SearchElementConstant(FIELD_COMPANY.name, company)


def create_element_product(product: str):
    return SearchElementConstant(FIELD_PRODUCT.name, product)


def create_element_document_date(target):
    return SearchElementArea(
        FIELD_DOCUMENT_DATE.name, target, converter=MostRecentDateConverter()
    )


def create_search_elements_firstname_from_fullname(target):
    """Look for firstname but only inside of the fullname. This returns 2 search elements as a list"""
    return [
        SearchElementArea(
            FIELD_FIRSTNAME.sr_inside, target, target_name=FIELD_FULLNAME.name
        ),
        create_search_element_firstname(target),
    ]


def create_search_element_firstname(target, min_length=4):
    return SearchElementFirstname(
        FIELD_FIRSTNAME.name,
        target,
        compress_whitespace=True,
        min_length=min_length,
        converter=CleanNameConverter(),
    )


def create_search_element_canton(shortcode):
    """Create constant for canton. Expects shortcode like ZH"""
    return SearchElementConstant(FIELD_CANTON_SHORT.name, shortcode)


def create_search_elements_address(
    target: SearchResult, page: Page, search_width_multiplier=1, min_length_firstname=4
):
    """

    :param target:
    :param page:
    :param search_width_multiplier:
    :param min_length_firstname: Choose 4 if searching in a broad setting (e.g. full page) and 3 if searching inside an address
    :return:
    """
    TEXT_PERSON_TITLE = "text_person_title"

    labels = {}
    tokens = TOKENS_LEADING_TITLE
    tokens = list(tokens)
    for t in tokens:
        dist = 0
        if len(t) >= 10:
            dist = 3
        elif len(t) >= 7:
            dist = 1
        labels[t]: dist
        labels[f"{t} "] = dist
        labels[f"{t}\n"] = dist
        labels[f"{t}{SPACER_CHAR}"] = dist
        labels[f"{t}"] = dist

    se_title = SearchElementMultiStaticText(
        TEXT_PERSON_TITLE,
        target,
        target_name=FIELD_FIRSTNAME.sr_inside,
        extract=EXTRACT_DEBUG,
        labels=labels,
        # Restrict this with target_name (mandatory) and not with relation
        # relations=[SearchRelationInside(FIELD_FIRSTNAME.sr_inside)]
    )
    se_firstname = create_search_element_firstname(
        target, min_length=min_length_firstname
    )
    se_firstname.target_name = FIELD_FIRSTNAME.sr_inside

    # If there is a title then the firstname is either on the same line or on the next line
    se_firstname.relations += [
        SearchRelationRightOf(
            TEXT_PERSON_TITLE,
            ref_boundary=ReferenceBoundaryHorizontal.LEFT,
            offset=page.width_chars(-2),
        ),
        SearchRelationLeftOf(
            TEXT_PERSON_TITLE,
            ref_boundary=ReferenceBoundaryHorizontal.RIGHT,
            offset=page.width_chars(25 * search_width_multiplier),
        ),
        SearchRelationBelow(
            TEXT_PERSON_TITLE,
            ref_boundary=ReferenceBoundaryVertical.TOP,
            offset=page.width_chars(-0.2),
        ),
    ]
    # se_firstname.relations.append(SearchRelationBelow(TEXT_PERSON_TITLE, ref_boundary=ReferenceBoundaryVertical.TOP,
    #                                                  offset=-20))  # Sometimes Title and name are on the same line

    se_zip_city_inside = SearchElementArea(
        FIELD_ZIP_CITY.sr_inside,
        target,
        extract=EXTRACT_DEBUG,
        relations=[
            SearchRelationInside(FIELD_FIRSTNAME.sr_inside, mandatory=True),
            SearchRelationBelow(
                FIELD_FIRSTNAME.name,
                ref_boundary=ReferenceBoundaryVertical.TOP,
                offset=-page.height_lines(-0.2),
            ),
            SearchRelationRightOf(
                FIELD_FIRSTNAME.name,
                ref_boundary=ReferenceBoundaryHorizontal.LEFT,
                offset=-page.width_chars(25 * search_width_multiplier),
            ),
            SearchRelationLeftOf(
                FIELD_FIRSTNAME.name,
                ref_boundary=ReferenceBoundaryHorizontal.RIGHT,
                offset=page.width_chars(25 * search_width_multiplier),
            ),
            SearchRelationAbove(
                FIELD_FIRSTNAME.name,
                ref_boundary=ReferenceBoundaryVertical.BOTTOM,
                offset=page.height_lines(8),
            ),
        ],
    )  # Should be 4 but make it a little larger due to potentially low median line height

    fields_zip_city = create_fields_zip_city(target, page, mandatory=True)

    fields = (
        [se_title, se_firstname, se_zip_city_inside]
        + fields_zip_city
        + [
            SearchElementOuterBoundingBox(
                FIELD_ADDRESS_BLOCK.name,
                target,
                element_names=[
                    TEXT_PERSON_TITLE,
                    FIELD_FIRSTNAME.name,
                    FIELD_ZIP_CITY.name,
                    FIELD_ZIP.name,
                    FIELD_CITY.name,
                ],
                elements_required=False,
                offset_top=page.height_lines(-0.2),
                offset_left=page.width_chars(-4),
                offset_right=page.width_chars(25),
                converter=AddressConverter(),
                relations=[
                    SearchRelationInside(FIELD_FIRSTNAME.sr_inside, mandatory=True)
                ],
            ),
            SearchElementArea(
                FIELD_STREET.name,
                target=target,
                target_name=FIELD_ADDRESS_BLOCK.name,
                converter=CleanStreetConverter(),
                relations=[
                    # SearchRelationRequires(FIELD_ADDRESS_BLOCK.name),
                    SearchRelationInside(FIELD_ZIP_CITY.sr_inside, mandatory=True),
                    SearchRelationBelow(
                        FIELD_FIRSTNAME.name,
                        ref_boundary=ReferenceBoundaryVertical.CENTER,
                    ),
                    SearchRelationAbove(
                        FIELD_ZIP.name, ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                    SearchRelationAbove(
                        FIELD_CITY.name, ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                ],
            ),
            SearchElementMultiStaticText(
                "field_co_address",
                labels={"C/O": 0, "c/o": 0},
                target=target,
                target_name=FIELD_ADDRESS_BLOCK.name,
                relations=[
                    # SearchRelationRequires(FIELD_ADDRESS_BLOCK.name),
                    SearchRelationInside(FIELD_ZIP_CITY.sr_inside, mandatory=True),
                    SearchRelationBelow(
                        FIELD_FIRSTNAME.name,
                        ref_boundary=ReferenceBoundaryVertical.CENTER,
                    ),
                    SearchRelationAbove(
                        FIELD_ZIP.name, ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                    SearchRelationAbove(
                        FIELD_CITY.name, ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                    SearchRelationAbove(
                        FIELD_STREET.name, ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                    SearchRelationLeftOf(
                        FIELD_ZIP.name, ref_boundary=ReferenceBoundaryHorizontal.RIGHT
                    ),
                ],
            ),
            SearchElementArea(
                FIELD_FULLNAME.name,
                target,
                target_name=FIELD_ADDRESS_BLOCK.name,
                converter=CleanNameConverter(max_num_lines=1, min_alpha_per_line=6),
                relations=[
                    SearchRelationAbove(FIELD_ZIP_CITY.name),
                    SearchRelationAbove(
                        FIELD_STREET.name, ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                    SearchRelationAbove(FIELD_ZIP.name),
                    SearchRelationAbove(FIELD_CITY.name),
                    SearchRelationAbove(
                        "field_co_address",
                        ref_boundary=ReferenceBoundaryVertical.CENTER,
                    ),
                ],
            ).align_vertical_with(FIELD_FIRSTNAME.name),
        ]
    )
    return fields


def create_fields_zip_city(
    target: SearchResult, page: Page, target_name=None, mandatory=False
):
    return [
        SearchElementZipCity(
            FIELD_ZIP_CITY.name,
            target,
            target_name=target_name,
            extract=EXTRACT_DEBUG,
            empty_result=False,
            relations=[
                SearchRelationInside(FIELD_ZIP_CITY.sr_inside, mandatory=mandatory)
            ],
        ),
        SearchElementRegex(
            FIELD_ZIP.name,
            target=target,
            target_name=FIELD_ZIP_CITY.name,
            regex="(\d[ ]?\d[ ]?\d[ ]?\d)",
            compress_whitespace=True,
            converter=ValidZipCodeConverter(),
            relations=[
                SearchRelationInside(FIELD_ZIP_CITY.sr_inside, mandatory=mandatory)
            ],
        ),
        SearchElementArea(
            FIELD_CITY.name,
            target=target,
            converter=CleanCityConverter(),
            relations=[
                SearchRelationInside(FIELD_ZIP_CITY.sr_inside, mandatory=mandatory),
                SearchRelationRightOf(FIELD_ZIP.name),
                SearchRelationBelow(
                    FIELD_ZIP.name,
                    ref_boundary=ReferenceBoundaryVertical.TOP,
                    offset=page.height_lines(-0.2),
                ),
                SearchRelationAbove(
                    FIELD_ZIP.name,
                    ref_boundary=ReferenceBoundaryVertical.BOTTOM,
                    offset=page.height_lines(0.2),
                ),
            ],
        ),
    ]


def create_field_document_date(target_name=None, target=None):
    return SearchElementArea(
        FIELD_DOCUMENT_DATE.name,
        target,
        target_name=target_name,
        converter=MostRecentDateConverter(),
    )


def create_fields_person_from_p1(main, page):
    return [
        SearchElementReference(FIELD_FIRSTNAME.name, ref=P1_FIRSTNAME.name),
        SearchElementReference(FIELD_LASTNAME.name, ref=P1_LASTNAME.name),
        SearchElementReference(FIELD_FULLNAME.name, ref=P1_FULLNAME.name),
    ]


def create_address_region(
    range: PercentageRange = RANGE_LEFT, texts_bottom: List[str] = None
):
    return SearchElementConstrainedArea(
        FIELD_FIRSTNAME.sr_inside, None, texts_bottom=texts_bottom, x_range=range
    )


def create_search_element_recent_year(page: Page, name=FIELD_YEAR.name):
    return SearchElementRegex(
        name,
        page.fullpage,
        regex="(2010|2011|2012|2013|2014|2015|2016|2017|2018|2019|2020|2021)",
        relations=[SearchRelationInside(FIELD_YEAR.sr_inside)],
    )


def find_page_header(
    page: Page,
    titles: List[str],
    x_range=PercentageRange(0, 0.9),
    y_range=PercentageRange(0, 0.4),
):
    sr = SearchElementArea(
        "page_title_area", None, x_range=x_range, y_range=y_range, extract=False
    ).find(page)
    success = False
    for title in titles:
        success = page.set_header_by_text(title, bbox=sr.bbox, include_pattern=False)
        if success:
            break
    return success


def create_document_date_most_recent(target: SearchResult):
    return SearchElementArea(
        FIELD_DOCUMENT_DATE.name,
        target,
        converter=MostRecentDateConverter(),
        relations=[SearchRelationInside(FIELD_DOCUMENT_DATE.sr_inside)],
    )


def create_birth_date(target: SearchResult):
    return SearchElementArea(
        FIELD_DATE_OF_BIRTH.name,
        target,
        converter=BirthDateConverter(),
        relations=[SearchRelationInside(FIELD_DATE_OF_BIRTH.sr_inside)],
    )


def create_document_date_element(document_date_label: str, target: SearchResult):
    return SearchElementLabeledField(
        FIELD_DOCUMENT_DATE.name,
        target,
        label=document_date_label,
        converter=DateConverter(),
        relations=[SearchRelationInside(FIELD_DOCUMENT_DATE.sr_inside)],
    )


def createSearchElementAhvNew(target: SearchResult):
    return SearchElementAhvNew(FIELD_AHV_NEW.name, target)


def add_two_fields(
    page: Page,
    label=None,
    dict_fields={},
    vertical_line_scale=3,
    converter=CurrencyConverter(),
    max_l_dist=DEFAULT_FUZZY_DIST,
    target_name=None,
    labels=None,
):
    """add two fields with the same label in different columns"""
    elements = []
    for field_name, p_range in dict_fields.items():
        if label and isinstance(label, str):
            elements.append(
                SearchElementLabeledField(
                    field_name,
                    page.main,
                    max_l_dist=max_l_dist,
                    field_vertical_line_scale=vertical_line_scale,
                    label=label,
                    field_pos_page_horizontal=p_range,
                    target_name=target_name,
                    converter=converter,
                )
            )
        elif labels is not None and isinstance(labels, Dict):
            elements.append(
                SearchElementMultiLabeledField(
                    field_name,
                    page.main,
                    field_vertical_line_scale=vertical_line_scale,
                    labels=labels,
                    field_pos_page_horizontal=p_range,
                    target_name=target_name,
                    converter=converter,
                )
            )

    return elements


def create_search_element_p1_fullname_from_address():
    return SearchElementReference(
        P1_FULLNAME.name,
        FIELD_FULLNAME.name,
        converter=CleanNameConverter(max_num_lines=1, max_num_lines_valid=10),
    )
