from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/email_with_photos_html"
)
dest_folder_prefix = "input_system_components_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_with_photos_html():
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # No CORRESPONDENCE_EMAIL because input is pdf, not email
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_EN: 1}
        ),
    ).run()
