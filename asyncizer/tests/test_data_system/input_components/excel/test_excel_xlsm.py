from pathlib import Path

import pytest
from openpyxl.reader.excel import load_workbook
from openpyxl.workbook import Workbook

from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS

excel_folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/excel/excel_xlsm/"
path_root_folder = Path(excel_folder)
dest_folder_prefix = "input_system_components_excel_xlsm"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


def test_unprotect_sheets(tmp_path):
    p = Path(excel_folder) / "Berechnung_Kapital_68.xlsm"
    assert p.exists()

    p_out_with_macro = tmp_path / "with_macro.xlsm"
    p_out_without_macro = tmp_path / "without_macro.xlsx"

    wb: Workbook = load_workbook(str(p), keep_vba=False)
    wb.save(str(p_out_with_macro))
    wb.save(str(p_out_without_macro))
