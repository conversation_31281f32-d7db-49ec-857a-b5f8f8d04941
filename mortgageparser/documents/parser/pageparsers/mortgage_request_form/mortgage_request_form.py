from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    TemplateGenericLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
    FromBottomTextCond,
)

parsers_de = [
    TemplateGenericLetterPageParser(
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        desc="MoneyPark summary printout 13 pages 2021",
        company="MoneyPark",
        required_text_conditions=[
            FromStartTextCond("MMP administration", num_lines=1),
            FromBottomTextCond(
                "https://partners.moneypark.ch/partners/printrequest/", num_lines=1
            ),
        ],
    ),
    TemplateGenericLetterPageParser(
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="Vontobel BVT Finanzierungsantrag DE 2025 1/6",
        company="BVT Finanzierungsantrag",  # Hack, this should be just the suffix but company is Vontobel
        required_text_conditions=[
            FromStartTextCond("Vontobel", num_lines=2),
            FromStartTextCond(
                [
                    "Kundenanfrage für eine Immobilienfinanzierung",  # this is endclient
                    "Antrag für eine Immobilienfinanzierung",  # this is employee
                ],
                num_lines=5,
            ),
            FromBottomTextCond("Finanzielle Verpflichtungen", num_lines=10),
        ],
    ),
]


parsers_en = [
    TemplateGenericLetterPageParser(
        supported_languages=["en"],
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="Vontobel BVT Finanzierungsantrag EN 2025 1/6",
        company="BVT Financing Request",  # Hack, this should be just the suffix but company is Vontobel
        required_text_conditions=[
            FromStartTextCond("Vontobel", num_lines=2),
            FromStartTextCond("Client enquiry for real estate financing", num_lines=5),
            # page one has less info in EN than in DE (different page break)
            FromBottomTextCond("Number of minor children", num_lines=20),
        ],
    )
]


parsers_fr = [
    TemplateGenericLetterPageParser(
        supported_languages=["fr"],
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="Vontobel BVT Finanzierungsantrag FR 2025 1/6",
        company="BVT Questionnaire financement",  # Hack, this should be just the suffix but company is Vontobel
        required_text_conditions=[
            FromStartTextCond("Vontobel", num_lines=2),
            FromStartTextCond("Questionnaire client pour", num_lines=5),
            FromStartTextCond(
                # First version is endclient, second version is employee
                ["financement d’un bien immobilier", "immobilier pour employé"],
                num_lines=5,
            ),
            # page one has less info in EN than in DE (different page break)
            FromBottomTextCond("Début d'activité", num_lines=20),
        ],
    )
]

parsers_it = [
    TemplateGenericLetterPageParser(
        supported_languages=["it"],
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="Vontobel BVT Finanzierungsantrag IT 2025 1/6",
        company="BVT Richiesta finanziamento",  # Hack, this should be just the suffix but company is Vontobel
        required_text_conditions=[
            FromStartTextCond("Vontobel", num_lines=2),
            FromStartTextCond(
                # First version is endclient, second version is employee
                ["Richiesta cliente", "Domanda per personale"],
                num_lines=5,
            ),
            FromStartTextCond("finanziamento immobiliare", num_lines=5),
            # page one has less info in EN than in DE (different page break)
            FromBottomTextCond("Impegni finanziari", num_lines=20),
        ],
    )
]

parsers_multi = [
    TemplateGenericLetterPageParser(
        supported_languages=ALL_LANGUAGES,
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="feyn Selbstauskunft 2023 #1/3",
        company="feyn",
        ranked_titles_any=[
            RankedTitle("Selbstauskunft", rank=3),
            RankedTitle("Self-declaration", rank=3),
            RankedTitle("Donées sur la", rank=3),
            RankedTitle("Dati sur la situazione", rank=3),
        ],
        required_text_conditions=[
            FromStartTextCond(["Am Schanzengraben 25", "<EMAIL>"], num_lines=10),
            FromStartTextCond("feyn", num_lines=3),
        ],
        min_num_chars_alpha=400,
        max_num_chars_alpha=1000,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=ALL_LANGUAGES,
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        desc="feyn Selbstauskunft 2023 #2,#3/3",
        company="feyn",
        ranked_titles_any=[
            RankedTitle("Finanzielle Verhältnisse", rank=1),
            RankedTitle("Objekt / Finanzierung", rank=1),
            RankedTitle("Financial situation", rank=1),
            RankedTitle("Property / financing", rank=1),
            RankedTitle("Situation financière", rank=1),
            RankedTitle("Objet immobilier", rank=1),
            RankedTitle("Situazione finanzaria", rank=1),
            RankedTitle("Oggetto immobiliare", rank=1),
        ],
        required_text_conditions=[
            FromStartTextCond(
                [
                    "Jahreseinkommen, brutto",
                    "Land / Wohnung",
                    "Other periodic Income",
                    "Land / appartment",
                    "Autre revenu régulier",
                    "Terrain/appartement",
                    "Altr reddito regolare",
                    "Terreno/appartemento",
                ],
                num_lines=10,
            ),
            FromBottomTextCond("feyn", num_lines=3),
        ],
        min_num_chars_alpha=400,
        max_num_chars_alpha=1000,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=["en", "fr", "it"],
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="Swiss Life Mortgage Application Form EN,FR,IT 2022 #1/x",
        company="Swiss Life",
        ranked_titles_any=[
            RankedTitle("Mortgage application"),
            RankedTitle("Demande de prêt hypothécaire"),
            RankedTitle("Domanda per un prestito ipotecario"),
        ],
        required_text_conditions=[
            FromStartTextCond(
                [
                    "Mortgage application",
                    "Demande de prêt hypothécaire",
                    "Domanda per un prestito",
                ],
                num_lines=3,
            ),
            FromStartTextCond("Swiss Life", num_lines=3),
            FromBottomTextCond(
                [
                    "Have you been subject to debt collection",
                    "Avez-vous fait l'objet de poursuites",
                    "Negli ultimi tre anni sono state",
                ],
                num_lines=10,
            ),
        ],
        min_num_chars_alpha=800,
        max_num_chars_alpha=1400,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=["en", "fr", "it"],
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        desc="Swiss Life Mortgage Application Form EN,FR,IT 2022 #2/x",
        company="Swiss Life",
        required_text_conditions=[
            FromStartTextCond(
                [
                    "Mortgage application",
                    "Demande de prêt hypothécaire",
                    "Domanda per un prestito",
                ],
                num_lines=1,
            ),
            FromStartTextCond(
                [
                    "Annual rental income (excl.",
                    "Etat locatif net annuel",
                    "Redditi annuali provenienti",
                ],
                num_lines=10,
            ),
            FromStartTextCond(
                [
                    "Minergie certificate or GEAK certificate existing?",
                    "Certificat Minergie ou certificat",
                    "Certificato Minergie o certificato GEAK",
                ],
                num_lines=20,
            ),
            FromBottomTextCond(
                [
                    "Existing building loan",
                    "Drédit de construction",
                    "Credito di costruzione",
                ],
                num_lines=10,
            ),
        ],
        min_num_chars_alpha=1000,
        max_num_chars_alpha=1400,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=["en", "fr"],  # IT is different here
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        desc="Swiss Life Mortgage Application Form EN,FR 2022 #3/x",
        company="Swiss Life",
        required_text_conditions=[
            FromStartTextCond(
                ["Mortgage application", "Demande de prêt hypothécaire"], num_lines=1
            ),
            FromStartTextCond("Home Option", num_lines=10),
            FromStartTextCond(
                ["eligible for Swiss Life", "Swiss Life part du principe"], num_lines=20
            ),
            FromBottomTextCond("Swiss Life", num_lines=1),
        ],
        min_num_chars_alpha=2000,
        max_num_chars_alpha=3000,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=["it"],  # EN is different here
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        desc="Swiss Life Mortgage Application Form IT 2022 #3/x",
        company="Swiss Life",
        required_text_conditions=[
            FromStartTextCond(["Domanda per un prestito ipotecario"], num_lines=1),
            FromStartTextCond(
                "Swiss Life parte dal presupposto che communicando", num_lines=15
            ),
            FromBottomTextCond("Swiss Life SA", num_lines=1),
        ],
        min_num_chars_alpha=2000,
        max_num_chars_alpha=2800,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=["en", "fr", "it"],
        doc_cat=DocumentCat.FINANCING_CHECKLIST_DOCUMENTS,
        page_cat=PageCat.GENERIC_PAGE,
        desc="Swiss Life Mortgage Application Form EN, FR, IT 2022 #4/x",
        company="Swiss Life",
        required_text_conditions=[
            FromStartTextCond(
                [
                    "Mortgage application",
                    "Demande de prêt hypothécaire",
                    "Domanda per un prestito ipotecario",
                ],
                num_lines=1,
            ),
            FromStartTextCond(
                [
                    "Checklist of documents",
                    "Check-list des documents à joindre",
                    "Lista di controllo dei documententi",
                ],
                num_lines=2,
            ),
            FromStartTextCond(
                [
                    "Balance statement of pledged collateral",
                    "Relevé des avoirs de garanties",
                    "Certificato di averi di garanzie",
                ],
                num_lines=10,
            ),
            FromBottomTextCond(
                [
                    "Divorce decree, separation agreement",
                    "Convention de divorce",
                    "convenzione di divorzio",
                ],
                num_lines=5,
            ),
        ],
        min_num_chars_alpha=2000,
        max_num_chars_alpha=3000,
    ),
    TemplateGenericLetterPageParser(
        supported_languages=ALL_LANGUAGES,
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        desc="Vontobel BVT Finanzierungsantrag DE 2025 2..6/6",
        # company="",
        required_text_conditions=[
            FromStartTextCond(
                [
                    "Kundenanfrage für eine Immobilienfinanzierung",
                    "Client enquiry for real estate financing",
                    # FR header for endclient is currently German, should be fixed here as soon as they fix it
                    "Kundenanfrage für eine Immobilienfinanzierung",
                    # FR header for employee form
                    "Questionnaire client pour le financement d’un bien immobilier",
                    "Richiesta del cliente per un finanziamento immobiliare",
                ],
                num_lines=1,
            ),
            FromStartTextCond("Vontobel", num_lines=1),
        ],
    ),
]


def get_parsers_mortgage_request_form():
    return parsers_de + parsers_en + parsers_fr + parsers_it + parsers_multi
