from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "proprietary_documents"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system/clients/vontobel/{FOLDER_TYPE}"
)

path_root_folder_bvt = path_root_folder / "bvt_finanzierungsantrag"

dest_folder_prefix = f"input_system_clients_vontobel_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_bvt_de():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "endclient" / "pdf",
        show_page=-1,
        source_file_filter=["438d_Kundenanfrage+Immobilienfinanzierung_DE_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 6},
            expected_page_objects={0: {"company": "BVT Finanzierungsantrag"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_en():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "endclient" / "pdf",
        show_page=-1,
        source_file_filter=["438e_Kundenanfrage+Immobilienfinanzierung_EN_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 5},
            expected_page_objects={0: {"company": "BVT Financing Request"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_fr():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "endclient" / "pdf",
        show_page=-1,
        source_file_filter=["438f_Kundenanfrage+Immobilienfinanzierung_FR_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 5},
            expected_page_objects={0: {"company": "BVT Questionnaire financement"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_it():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "endclient" / "pdf",
        show_page=-1,
        source_file_filter=["438i_Kundenanfrage+Immobilienfinanzierung_IT_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 5},
            expected_page_objects={0: {"company": "BVT Richiesta finanziamento"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_employee_de():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "employee" / "pdf",
        show_page=-1,
        source_file_filter=["Anfrage+Immobilienfinanzierung+Mitarbeiter_DE_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 4},
            expected_page_objects={0: {"company": "BVT Finanzierungsantrag"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_employee_en():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "employee" / "pdf",
        show_page=-1,
        source_file_filter=["Anfrage+Immobilienfinanzierung+Mitarbeiter_EN_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 3},
            expected_page_objects={0: {"company": "BVT Financing Request"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_employee_fr():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "employee" / "pdf",
        show_page=-1,
        source_file_filter=["Anfrage+Immobilienfinanzierung+Mitarbeiter_FR_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 2},
            expected_page_objects={0: {"company": "BVT Questionnaire financement"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_bvt_employee_it():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder_bvt / "employee" / "pdf",
        show_page=-1,
        source_file_filter=["Anfrage+Immobilienfinanzierung+Mitarbeiter_IT_202502.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_REQUEST_FORM: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.MORTGAGE_REQUEST_FORM: 4},
            expected_page_objects={0: {"company": "BVT Richiesta finanziamento"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_credit_assessment_questions():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "credit_assessment_questions/de",
        show_page=0,
        source_file_filter=["vontobel_questions_client_de.jpg"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.CREDITWORTHINESS_MISC: 1},
            expected_page_objects={
                0: {
                    "document_title": "RM Bestätigung Kreditwürdigkeit",
                    "document_date": "02.06.2025",
                }
            },
            expected_filenames=[
                "259 Bonität Diverses RM Bestätigung Kreditwürdigkeit 2025-06-02.pdf"
            ],
        ),
    ).run()
