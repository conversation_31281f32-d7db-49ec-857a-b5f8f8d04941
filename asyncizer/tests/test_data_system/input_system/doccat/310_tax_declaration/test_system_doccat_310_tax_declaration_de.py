from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, PO_EXISTS, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "310_tax_declaration"
source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}/cantons_de"
)
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_ag_double_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "AG",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ag_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 12},
            expected_page_objects={
                "0": {
                    # first page object (canton) added manually to get the rest printed to the log
                    "canton_short": "AG",
                    "year": "2019",
                    "document_date": "02.11.2020",
                    "street": PO_EXISTS,
                    "zip": "5400",
                    "city": "Baden",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p1_date_of_birth": "21.12.2000",
                    "p1_profession": "Dr. Phil. Harm",
                    "p1_employer": "Orchester",
                    "p1_employer_location": "8055 Zürich",
                    "p1_marital_status": "verheiratet (ungetrennt)",
                    # "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_lastname": PO_EXISTS,
                    "p2_date_of_birth": "12.12.2000",
                    "p2_profession": "Harmonikerin",
                    "p1_income_employed_main": "CHF 333'333",
                    "p1_contribution_pillar_2": "CHF 4'000",
                    "p1_contribution_pillar_3a": "CHF 6'500",
                    "p2_contribution_pillar_2": "CHF 2'000",
                    "income_alimony_partner": "CHF 22'222",
                    "income_alimony_children": "CHF 12'000",
                    "income_real_estate_gross": "CHF 900'000",
                    "income_real_estate_net": "CHF 720'000",
                    "income_gross_total": "CHF 1'087'555",
                    "income_net_total": "CHF 1'036'055",
                    "income_taxable_local": "CHF 1'029'055",
                    "p1_expense_employment": "CHF 4'000",
                    "expense_alimony_partner": "CHF 26'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "property_maintenance_cost": "CHF 180'000",
                    "deductions_total": "CHF 51'500",
                    "assets_portfolio": "CHF 1'000'489",
                    "assets_gross_total": "CHF 2'000'489",
                    "assets_net_total": "CHF 1'497'089",
                    "assets_taxable_global": "CHF 1'285'089",
                    "assets_taxable_local": "CHF 1'285'089",
                    "property_type": "Einfamilienhaus",
                    "property_year": "2000",
                    "property_purchase_year": "2010",
                    # Eigenmietwert Bund
                    "property_imputed_rental_value": "CHF 1'050'300",
                    # Eigenmietwert Kanton
                    # "property_imputed_rental_value": "CHF 900'000",
                    "property_imputed_rental_value_canton": "CHF 900'000",
                    "debt_total": "CHF 503'400",
                    "interest_paid_on_debt": "CHF 5'000",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_double_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "AG",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ag_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 16,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "address_block": PO_EXISTS,
                    "zip": "8500",
                    "city": "Frauenfeld",
                    "p1_fullname": PO_EXISTS,
                    "p1_income_employed_main": "CHF 111'111",
                    "p2_income_employed_main": "CHF 222'222",
                    "p1_income_employed_side": "CHF 66'666",
                    "p2_income_employed_side": "CHF 55'555",
                    "p1_income_employed_benefits": "CHF 1'111",
                    "p2_income_employed_benefits": "CHF 2'222",
                    "p1_income_self_main": "CHF 12'345",
                    "p2_income_self_main": "CHF 524",
                    "p1_income_self_side": "CHF 6'489",
                    "p2_income_self_side": "CHF 5'482",
                    "p1_income_pension": "CHF 556",
                    "p2_income_pension": "CHF 458",
                    "p1_income_child_benefits": "CHF 456",
                    "p2_income_child_benefits": "CHF 875",
                    "income_alimony_partner": "CHF 2'124",
                    "income_alimony_children": "CHF 5'746",
                    "income_portfolio": "CHF 3'600",
                    "income_undistributed_inheritances": "CHF 135",
                    "income_other": "CHF 4'568",
                    "income_lump_sum": "CHF 315",
                    "income_gross_total": "CHF 505'360",
                    "expense_alimony_partner": "CHF 548",
                    "expense_alimony_children": "CHF 24",
                    "expense_annuity_contributions": "CHF 2'154",
                    "p1_contribution_pillar_2": "CHF 258",
                    "p2_contribution_pillar_2": "CHF 354",
                    "p1_contribution_pillar_3a": "CHF 88",
                    "p2_contribution_pillar_3a": "CHF 55",
                    "p1_expense_employment": "CHF 12'945",
                    "p2_expense_employment": "CHF 7'122",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "interest_paid_on_debt": "CHF 456",
                    "deductions_donations": "CHF 2'164",
                    "deductions_total": "CHF 30'768",
                    "income_net_total": "CHF 474'592",
                    "income_taxable_local": "CHF 467'492",
                    "assets_cash_gold": "CHF 1'260",
                    "assets_life_insurance": "CHF 568",
                    "assets_cars": "CHF 223",
                    "assets_other": "CHF 548",
                    "assets_real_estate_total_net": "CHF 65'545",
                    "assets_gross_total": "CHF 392'170",
                    "assets_net_total": "CHF 392'170",
                    "assets_taxable_global": "CHF 180'170",
                    "assets_taxable_local": "CHF 180'170",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_ai_single_2021():
    """
    Page 5 was misclassified as "DE/310/AR/Wertschriftenverzeichnis Front"
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "AI" / "ai_single_2021",
        source_file_filter=["tax_ai_single_2021.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_CALCULATION: 1,
                DocumentCat.TAX_DECLARATION: 17,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "AI",
                    "year": "2021",
                    # address parser is not perfect yet
                    # "p1_profession": "Eidg. Dipl. Inspirateur",
                    # "p1_marital_status": "ledig",
                    # "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 150'000",
                    "p1_income_employed_side": "CHF 35'000",
                    "income_portfolio": "CHF 16'620",
                    "income_real_estate_net": "CHF 11'200",
                    "income_gross_total": "CHF 205'260",
                    "income_net_total": "CHF 156'245",
                    "income_taxable_local": "CHF 156'245",
                    "p1_expense_employment": "CHF 32'080",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'900",
                    "deductions_other": "CHF 75",
                    "property_maintenance_cost": "CHF 2'240",
                    "deductions_total": "CHF 43'865",
                    "assets_portfolio": "CHF 7'560",
                    "assets_life_insurance": "CHF 130'000",
                    "assets_cars": "CHF 3'500",
                    "assets_gross_total": "CHF 7'641'060",
                    "assets_net_total": "CHF 7'635'360",
                    "assets_taxable_local": "CHF 7'585'360",
                    "assets_real_estate_total_gross": "CHF 7'500'000",
                    "debt_private": "CHF 5'700",
                    "debt_total": "CHF 5'700",
                    "interest_paid_on_debt": "CHF 5'700",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ar_double_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "AR",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ar_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 26,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "AR",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p1_fullname": PO_EXISTS,
                    "p2_fullname": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_marital_status": "verheiratet",
                    "p1_profession": "Job 1",
                    "p2_date_of_birth": "02.02.1980",
                    "p2_profession": "Job 2",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 1'561",
                    "p2_income_employed_main": "CHF 508",
                    "p1_income_employed_side": "CHF 4'562",
                    "p1_income_self_main": "CHF 4'564",
                    "p2_income_self_main": "CHF -106",
                    "p1_income_self_side": "CHF 484",
                    "p2_income_self_side": "CHF 354",
                    "p1_income_board_seats": "CHF 48",
                    "p2_income_board_seats": "CHF 24",
                    "p1_income_pension_ahv": "CHF 847",
                    "p2_income_pension_ahv": "CHF 3'185",
                    "p1_income_pension": "CHF 4'648",
                    "p2_income_pension": "CHF 4'887",
                    "p1_income_eo": "CHF 87",
                    "p2_income_eo": "CHF 31",
                    "p1_income_social_security": "CHF 34",
                    "p2_income_social_security": "CHF 87",
                    "p1_income_child_benefits": "CHF 64",
                    "income_alimony_partner": "CHF 4'864",
                    "income_alimony_children": "CHF 215",
                    "income_other": "CHF 654",
                    "income_lump_sum": "CHF 48",
                    "income_gross_total": "CHF 298'007",
                    "income_portfolio": "CHF 44'438",
                    "income_undistributed_inheritances": "CHF 21",
                    # "income_real_estate_net": "CHF 56'484",
                    # TODO: fix this part because currently SG and AR is in a single spacy group (cannot be distinguished)
                    # "expense_annuity_contributions": "CHF 31",
                    # "expense_children_daycare": "CHF 5'646",
                    # "p1_contribution_pillar_2": "CHF 468",
                    # "p2_contribution_pillar_2": "CHF 1'385",
                    # "p2_contribution_pillar_3a": "CHF 487",
                    # "p1_expense_employment": "CHF 12",
                    # "p2_expense_employment": "CHF 944",
                    # "insurance_premiums_and_interest_on_savings_accounts": "CHF 5'000",
                    # "interest_paid_on_debt": "CHF 87",
                    # "deductions_wealth_management": "CHF 6'000",
                    # "property_maintenance_cost": "CHF 5'648",
                    # "deductions_total": "CHF 45'080",
                    # "income_net_total": "CHF 248'359",
                    # "income_taxable_local": "CHF 241'359",
                    "assets_portfolio": "CHF 10'556",
                    "assets_cash_gold": "CHF 68",
                    "assets_life_insurance": "CHF 65'468",
                    "assets_gross_total": "CHF 2'129'392",
                    "assets_net_total": "CHF 2'128'928",
                    "assets_taxable_local": "CHF 1'953'928",
                    "debt_private": "CHF 464",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_double_de_2019():
    """
    The page property (form 7) is missing in this document.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BE",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_be_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 6},
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2019",
                    "municipality": "Kehrsatz",
                    "p1_fullname": PO_EXISTS,
                    "p2_income_employed_main": "CHF 222'222",
                    "p2_income_alimony_total": "CHF 12'000",
                    "p1_expense_employment_other": "CHF 3'333",
                    "p2_expense_employment_other": "CHF 4'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'020",
                    "assets_portfolio": "CHF 100'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_single_de_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BE",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_be_single_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 10},
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2021",
                    "municipality": "ßem",
                    "p1_fullname": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p2_income_employed_main": "CHF 59'000",
                    "p2_income_other": "CHF 8'000",
                    "p2_expense_employment": "CHF 59'100",
                    "p2_expense_employment_other": "CHF 2'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 3'019",
                    "deductions_wealth_management": "CHF 5",
                    "deductions_illness": "CHF 1'564",
                    "deductions_donations": "CHF 180",
                    "assets_portfolio": "CHF 3'615'888",
                    "assets_other": "CHF 977'034",
                    "debt_total": "CHF 14'700",
                    "interest_paid_on_debt": "CHF 223",
                    "debt_detail_lines": PO_EXISTS,
                    "property_imputed_rental_value_canton": "CHF 15'800",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_bl_double_2021():
    """ """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BL" / "bl_double_2021",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_bl_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 33,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "BL",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p1_fullname": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_marital_status": "verheiratet",
                    "p1_profession": "Job 1",
                    "p1_employer": "Arbeitgeber 1",
                    "p1_employer_location": "Arbeitsort 1",
                    "p2_profession": "Job 2",
                    "p2_employer": "Arbeitgeber 2",
                    "p2_employer_location": "Arbeitsort 2",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 154'687",
                    "p1_income_employed_side": "CHF 1'875",
                    "p2_income_employed_main": "CHF 321",
                    "p2_income_employed_side": "CHF 2'318",
                    "p2_income_self_main": "CHF 76'908",
                    "p2_contribution_pillar_2": "CHF 5'654",
                    "p2_contribution_pillar_3a": "CHF 1'113",
                    "p2_expense_employment": "CHF 500",
                    "p1_income_self_main": "CHF 2'895",
                    # "p1_income_pension_ahv": "CHF 48'787",   # code covered by "Kopie"
                    "p1_income_pension": "CHF 986",
                    "p1_income_eo": "CHF 43'548",
                    "income_alimony_partner": "CHF 63'900",
                    "income_other": "CHF 13'854",
                    "income_gross_total": "CHF 299'394",
                    "income_portfolio": "CHF 0",
                    "expense_alimony_children": "CHF 74",
                    "expense_alimony_partner": "CHF 78",
                    "expense_annuity_contributions": "CHF 1",
                    "expense_children_daycare": "CHF 5'555",
                    # "p1_contribution_pillar_2": "CHF 47'867",   # covered by "Kopie"
                    # "p2_contribution_pillar_2": "CHF 5'654",   # covered by "Kopie"
                    # "p1_contribution_pillar_3a": "CHF 6'487",   # covered by "Kopie"
                    # "p2_contribution_pillar_3a": "CHF 1'113",   # covered by "Kopie"
                    "p1_expense_employment": "CHF 500",
                    # "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",   # covered by "Kopie"
                    "interest_paid_on_debt": "CHF 222",
                    "deductions_other": "CHF 4'878",
                    "property_maintenance_cost": "CHF 112'233",
                    "deductions_total": "CHF 176'874",
                    "income_taxable_local": "CHF 122'520",
                    "assets_portfolio": "CHF 60'959",
                    "assets_cash_gold": "CHF 48'787",
                    "assets_cars": "CHF 8'889",
                    "assets_real_estate_total_gross": "CHF 2'314",
                    "assets_gross_total": "CHF 217'485",
                    "assets_net_total": "CHF 203'789",
                    "assets_taxable_local": "CHF 53'789",
                    # "debt_total": "CHF 1'357",
                    "property_imputed_rental_value": "CHF 58'778",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_bs_double_2021():
    """ """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BS",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_bs_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 18,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "BS",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p2_date_of_birth": "01.01.1970",
                    "p2_profession": "Job 1",
                    "p1_income_employed_main": "CHF 46'487",
                    "p2_income_employed_main": "CHF 48'788",
                    "p1_income_employed_side": "CHF 4'588",
                    "p2_income_employed_side": "CHF 3'187",
                    "p1_income_self_main": "CHF 4'548",
                    "p2_income_self_side": "CHF 18'676",
                    "p1_income_board_seats": "CHF 3'248",
                    "p2_income_board_seats": "CHF 1'877",
                    "p1_income_pension_ahv": "CHF 4'684",
                    "p2_income_pension_ahv": "CHF 315",
                    "p1_income_pension": "CHF 4'684",
                    "p1_income_social_security": "CHF 4'866",
                    "p2_income_social_security": "CHF 3'246",
                    "income_gross_total": "CHF 183'709",
                    "income_portfolio": "CHF 23'353",
                    "income_real_estate_net": "CHF 4'684",
                    "expense_alimony_children": "CHF 60'000",
                    "p1_expense_employment": "CHF 4'000",
                    "p2_expense_employment": "CHF 4'000",
                    "interest_paid_on_debt": "CHF 648",
                    "deductions_donations": "CHF 5'156",
                    "deductions_total": "CHF 85'348",
                    "income_net_total": "CHF 98'361",
                    "income_taxable_local": "CHF 44'505",
                    "assets_portfolio": "CHF 1'080'085",
                    "assets_cash_gold": "CHF 4'864",
                    "assets_life_insurance": "CHF 218",
                    "assets_undistributed_inheritances": "CHF 6'484",
                    "assets_real_estate_total_gross": "CHF 464'687",
                    "assets_gross_total": "CHF 1'625'720",
                    "assets_net_total": "CHF 1'557'256",
                    "assets_taxable_local": "CHF 1'392'256",
                    "debt_private": "CHF 68'464",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_fr_double_de_2021_page_18():
    """
    Page 22 was UNKNOWN_DE and is now TAX FR.
    Pages 6, 7, 11, 23 are misclassified to other cantons or pages but are tax.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "FR",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_fr_double_de_2021_page_18.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={"0": {"canton_short": "FR"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_fr_double_de_2021():
    """
    Page 22 was UNKNOWN_DE and is now TAX FR.
    Pages 6, 7, 11, 23 are misclassified to other cantons or pages but are tax.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "FR",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_fr_double_de_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 27,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={"0": {"canton_short": "FR"}},
        ),
    ).run()


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_gl_double_2021():
    """
    Added classification parsers for most pages and extraction for personal, income, deductions, assets.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "GL" / "gl_double_2021",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_gl_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 20,
                DocumentCat.TAX_CALCULATION: 2,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "GL",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8000",
                    "p1_fullname": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p2_email": "<EMAIL>",
                    "p2_phone_primary": PO_EXISTS,
                    "p1_marital_status": "verheiratet",
                    "p1_profession": "Job 1",
                    "p1_employer": "Arbeitgeber 1",
                    "p2_date_of_birth": "01.01.1980",
                    "p2_profession": "Job 2",
                    "p2_employer": "Arbeitgeber 2",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_side": "CHF 839",
                    "p1_income_self_side": "CHF 432",
                    "p2_income_self_side": "CHF 4'208",
                    "p1_income_pension_ahv": "CHF 420",
                    "p2_income_pension_ahv": "CHF 327",
                    "p1_income_pension": "CHF 2'190",
                    "p1_income_eo": "CHF 429",
                    "p2_income_eo": "CHF 408",
                    "p2_income_social_security": "CHF 1'719",
                    "income_child_benefits": "CHF 133",
                    "income_alimony_partner": "CHF 3'756",
                    "income_portfolio": "CHF 16'315",
                    "income_undistributed_inheritances": "CHF 42",
                    "income_real_estate_net": "CHF -1'049",
                    "income_lump_sum": "CHF 4'214",
                    "p1_contribution_pillar_1": "CHF 4'214",
                    "p1_expense_employment": "CHF 4'968",
                    "p2_expense_employment": "CHF 1'384",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 6'000",
                    "deductions_wealth_management": "CHF 1'509",
                    "deductions_other": "CHF 4'241",
                    "deductions_total": "CHF 81'285",
                    "income_net_total": "CHF 393'382",
                    "income_taxable_local": "CHF 391'382",
                    "assets_cash_gold": "CHF 43'212",
                    "assets_undistributed_inheritances": "CHF 525",
                    "assets_real_estate_total_net": "CHF 24'245",
                    "assets_gross_total": "CHF 822'844",
                    "assets_net_total": "CHF 822'844",
                    "assets_taxable_local": "CHF 672'844",
                    "property_imputed_rental_value": "CHF 4'234",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_gl_deckblatt_2019():
    """
    Detect to assign correct canton
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=0,
        source_folder=source_folder / "GL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["deckblatt_gl.png"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_gr_double_de_2021():
    """
    Page 2 and 16 are UNKNOWN_DE --> clean-up of all DE/310/GR in Spacy done, rule added
    Page 3 was misclassified as "DE/310/AR/Einkünfte".
    Extractions for personal, income, deductions, assets reworked together with italian version.
    Pages 6 and 7 were misclassified to other cantons.
    Pages 18, 19 were UNKNOWN_DE.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "GR",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_gr_double_de_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 23,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "GR",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p1_fullname": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_profession": "Job 1",
                    "p2_profession": "Job 2    ~",
                    "p1_employer": "Arbeitgeber 1",
                    "p2_employer": "Arbeitgeber 2",
                    "p1_marital_status": "verheiratet",
                    # The following extractions are missing because of OCR Problems and "Kopie"
                    # p2_date_of_birth
                    # p1_ahv_new
                    # p2_ahv_new
                    # phone_primary
                    # email - email
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 54'848",
                    "p1_income_employed_side": "CHF 154",
                    "p1_income_employed_benefits": "CHF 3'488",
                    "p1_income_self_main": "CHF 7'896",
                    "p1_income_self_side": "CHF 148",
                    "p1_income_board_seats": "CHF 8'754",
                    "p1_income_pension_ahv": "CHF 2'134",
                    "p1_income_pension": "CHF 548",
                    "p1_income_social_security": "CHF 45'648",
                    "p2_income_employed_main": "CHF 21'578",
                    "p2_income_employed_side": "CHF 48'648",
                    "p2_income_employed_benefits": "CHF 8'000",
                    "p2_income_self_main": "CHF 3'454",
                    "p2_income_self_side": "CHF 687",
                    "p2_income_board_seats": "CHF 2'365",
                    "p2_income_pension_ahv": "CHF 248",
                    "p2_income_social_security": "CHF 21",
                    "income_child_benefits": "CHF 488",
                    "income_portfolio": "CHF 0",
                    "income_alimony_partner": "CHF 94'152",
                    "income_real_estate_net": "CHF 5'487",
                    "income_other": "CHF 2'457",
                    "income_gross_total": "CHF 334'246",
                    "expense_alimony_partner": "CHF 5'856",
                    "expense_annuity_contributions": "CHF 4'866",
                    "p2_contribution_pillar_2": "CHF 1'587",
                    "p1_contribution_pillar_3a": "CHF 4'876",
                    "p2_contribution_pillar_3a": "CHF 487",
                    "p1_expense_employment": "CHF 3'254",
                    "p1_deductions_education": "CHF 5'000",
                    "p2_expense_employment": "CHF 4'560",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 0",
                    "deductions_wealth_management": "CHF 1'108",
                    "deductions_illness": "CHF 0",
                    "deductions_donations": "CHF 100",
                    "deductions_total": "CHF 87'286",
                    "income_net_total": "CHF 246'960",
                    "income_taxable_local": "CHF 238'080",
                    "assets_portfolio": "CHF 443'351",
                    "assets_cash_gold": "CHF 487",
                    "assets_cars": "CHF 25'000",
                    "assets_undistributed_inheritances": "CHF 64",
                    "assets_other": "CHF 486",
                    "assets_gross_total": "CHF 529'140",
                    "assets_net_total": "CHF 529'140",
                    "assets_taxable_local": "CHF 399'140",
                    "assets_real_estate_total_gross": "CHF 5'648",
                    "interest_paid_on_debt": "CHF 54'568",
                    "assets_gross_business": "CHF 54'104",
                    "property_imputed_rental_value": "CHF 486",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
@pytest.mark.new_spacy_model
async def test_lu_double_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=2,
        source_folder=source_folder / "LU",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_lu_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 14},
            expected_page_objects={
                "0": {
                    "canton_short": "LU",
                    "year": "2019",
                    "p2_firstname": PO_EXISTS,
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p1_date_of_birth": "21.12.1975",
                    "p2_date_of_birth": "12.12.1980",
                    "p1_profession": "Dr. Phil. Harm",
                    "p2_profession": "Harmonikerin",
                    "p1_employer": "Orchester",
                    "p2_employer": "Orchester",
                    "p1_marital_status": "verheiratet",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 111'111",
                    "p2_income_employed_main": "CHF 222'222",
                    "p2_income_employed_side": "CHF 0",
                    "income_portfolio": "CHF 0",
                    "income_alimony_partner": "CHF 24'000",
                    "income_gross_total": "CHF 357'333",
                    "expense_alimony_children": "CHF 12'000",
                    "expense_alimony_partner": "CHF 12'000",
                    "p1_contribution_pillar_3a": "CHF 6'826",
                    "p2_contribution_pillar_3a": "CHF 6'826",
                    "p1_expense_employment": "CHF 3'333",
                    "p2_expense_employment": "CHF 4'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 3'500",
                    "deductions_total": "CHF 48'485",
                    "income_net_total": "CHF 308'848",
                    "income_taxable_local": "CHF 304'148",
                    "assets_portfolio": "CHF 100'000",
                    "assets_gross_total": "CHF 100'000",
                    "assets_net_total": "CHF 100'000",
                    "assets_taxable_local": "CHF 0",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_sg_single_2019():
    """
    Page 5 is UNKNOWN_DE --> added to Golden.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "SG",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_sg_single_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 19},
            expected_page_objects={
                "0": {
                    "canton_short": "SG",
                    "year": "2019",
                    "municipality": "St. Gallen",
                    "document_date": "03.11.2020",
                    "zip": "9000",
                    "city": "St. Gallen",
                    "p1_fullname": PO_EXISTS,
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1960",
                    "p1_profession": "Maler",
                    "p1_employer": "Appenzeller Bergbahnen",
                    "p1_marital_status": "Ledig",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 78'000",
                    "p1_income_employed_side": "CHF 20'000",
                    "p1_income_self_main": "CHF 150'000",
                    "p1_income_self_side": "CHF 15'000",
                    "p1_income_board_seats": "CHF 10'000",
                    "p1_income_eo": "CHF 4'444",
                    "p1_income_social_security": "CHF 5'555",
                    "p1_income_child_benefits": "CHF 6'666",
                    "income_portfolio": "CHF 5'490",
                    "income_alimony_partner": "CHF 7'777",
                    "income_alimony_children": "CHF 8'888",
                    "income_other": "CHF 1'212",
                    "income_lump_sum": "CHF 2'222",
                    "income_undistributed_inheritances": "CHF 9'999",
                    "income_gross_total": "CHF 325'253",
                    "expense_alimony_children": "CHF 2'000",
                    "expense_alimony_partner": "CHF 1'000",
                    "expense_annuity_contributions": "CHF 2'000",
                    "p1_contribution_pillar_2": "CHF 12'000",
                    "p1_contribution_pillar_3a": "CHF 6'780",
                    "p1_expense_employment": "CHF 4'800",
                    "p1_deductions_education": "CHF 5'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 5'400",
                    "expense_children_daycare": "CHF 15'000",
                    "deductions_total": "CHF 54'180",
                    "income_net_total": "CHF 271'073",
                    "income_taxable_local": "CHF 241'400",
                    "assets_cash_gold": "CHF 10'000",
                    "assets_undistributed_inheritances": "CHF 17'000",
                    "assets_net_business": "CHF 200'000",
                    "assets_gross_total": "CHF 2'897'956",
                    "assets_net_total": "CHF 2'895'956",
                    "assets_taxable_global": "CHF 2'800'000",
                    "assets_taxable_local": "CHF 2'800'000",
                    "assets_real_estate_total_gross": "CHF 2'500'000",
                    "debt_private": "CHF 2'000",
                    "debt_total": "CHF 2'000",
                    "interest_paid_on_debt": "CHF 200",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sh_double_2021():
    """
    Page 8 was UNKNOWN and should be "DE/310/SH/Liegenschaften Verzeichnis" --> added to Golden
    added extractions for income, deductions, assets
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "SH",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_sh_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 13},
            expected_page_objects={
                "0": {
                    "canton_short": "SH",
                    "year": "2021",
                    "municipality": "Schleitheim",
                    "address_block": PO_EXISTS,
                    "zip": "5462",
                    "city": "St. Markus",
                    "p1_fullname": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1980",
                    "p2_date_of_birth": "01.01.1980",
                    "p1_income_employed_main": "CHF 180'000",
                    "p2_income_employed_main": "CHF 384'460",
                    "income_portfolio": "CHF 13'470",
                    "income_gross_total": "CHF 577'930",
                    "p1_expense_employment": "CHF 4'500",
                    "p2_expense_employment": "CHF 4'500",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 5'250",
                    "interest_paid_on_debt": "CHF 8'760",
                    "deductions_total": "CHF 23'810",
                    "income_net_total": "CHF 554'120",
                    "income_taxable_local": "CHF 554'120",
                    "assets_cash_gold": "CHF 1'643'450",
                    "assets_cars": "CHF 200'000",
                    "assets_other": "CHF 798'420",
                    "assets_real_estate_total_net": "CHF 1'876'556",
                    "debt_private": "CHF 175'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sz_double_2021():
    """
    Page 1 is (Barcode) is UNKNOWN_DE --> added to Golden.
    Consolidated parsers for income, income details, assets.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "SZ",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_sz_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 18,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "SZ",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7748",
                    "city": "Cavajone",
                    "p1_fullname": PO_EXISTS,
                    "p1_income_employed_main": "CHF 314'648",
                    "p2_income_employed_main": "CHF 1'234",
                    "p2_income_employed_side": "CHF 6'448",
                    "p1_income_employed_benefits": "CHF 2'115",
                    "p2_income_self_side": "CHF 5'566",
                    "p1_income_pension": "CHF 93'118",
                    "p2_income_pension": "CHF 94'842",
                    "p1_income_eo": "CHF 213",
                    "p1_income_social_security": "CHF 212",
                    "p1_income_child_benefits": "CHF 2",
                    "p2_income_child_benefits": "CHF 5'456",
                    "income_alimony_partner": "CHF 2'255",
                    "income_alimony_children": "CHF 6'546",
                    "income_portfolio": "CHF 0",
                    "income_undistributed_inheritances": "CHF 1'122",
                    "income_real_estate_gross": "CHF 54'688",
                    "income_gross_total": "CHF 565'193",
                    "expense_alimony_partner": "CHF 6'633",
                    "expense_alimony_children": "CHF 112'233",
                    "expense_children_daycare": "CHF 12'342",
                    "p1_contribution_pillar_2": "CHF 6'548",
                    "p2_contribution_pillar_3a": "CHF 32'155",
                    "p1_expense_employment": "CHF 0",
                    "p2_expense_employment": "CHF 2'466",
                    "p1_deductions_education": "CHF 2'255",
                    "p2_deductions_education": "CHF 6'644",
                    "deductions_illness": "CHF 34'274",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 6'800",
                    "interest_paid_on_debt": "CHF 2'224",
                    "deductions_wealth_management": "CHF 0",
                    "deductions_donations": "CHF 4'533",
                    "income_net_total": "CHF 380'432",
                    "income_taxable_global": "CHF 325'790",
                    "income_taxable_local": "CHF 325'790",
                    "assets_portfolio": "CHF 0",
                    "assets_cash_gold": "CHF 45'687",
                    "assets_life_insurance": "CHF 888",
                    "assets_cars": "CHF 999",
                    "assets_gross_total": "CHF 1'083'929",
                    "assets_net_total": "CHF 1'006'246",
                    "assets_taxable_global": "CHF 726'246",
                    "assets_taxable_local": "CHF 726'246",
                    "debt_private": "CHF 66'664",
                    "debt_business": "CHF 8'786",
                    "property_imputed_rental_value": "CHF 4'648",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_tg_double_2021():
    """
    Page 1 was recognized by Spacy and parsed with TaxDeclarationTGPersonalDataPageParser.
    The page changed a bit since 2015, so the extraction was amended.
    Page 2 was misclassified by Spacy as "DE/310/LU/Wertschriftenverzeichnis Front".
    The page changed a bit since 2015, new classification and extraction parser added that suits both.
    Page 3 was UNKNOWN_DE. It changed a bit since 2015, new classification and extraction parser added that suits both.
    Page 5 was UNKOWN_DE. It is now TAX_DECLARATION_MISC (but without canton="TG", as it is too generic).
    Page 7 was misclassified as "DE/310/AG/Anleitung" and is now TG, TAX_DECLARATION_MISC.
    Page 9 was UNKNOWN_DE and is now TG, TAX_DECLARATION_MISC.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "TG",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_tg_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 12,
                DocumentCat.TAX_CALCULATION: 3,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "TG",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "zip": "8355",
                    "city": "Aadorf",
                    "p2_fullname": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1980",
                    "p1_marital_status": "verheiratet",
                    "p1_profession": "Arbeit A",
                    "p1_employer": "Arbeitsgeber A",
                    "p2_profession": "öb B",  # because of "Kopie"
                    "p2_employer": "^Arbeitgeber B",  # because of "Kopie"
                    "p2_income_employed_main": "CHF 50'000",
                    "p1_income_employed_side": "CHF 20'000",
                    "p2_income_employed_side": "CHF 10'000",
                    "p1_income_self_main": "CHF 5'555",
                    "p2_income_self_main": "CHF 3'333",
                    "p1_income_self_side": "CHF 555",
                    "p2_income_self_side": "CHF 333",
                    "p1_income_pension_ahv": "CHF 111",
                    "p2_income_pension_ahv": "CHF 222",
                    "p1_income_pension": "CHF 1'000",
                    "p2_income_pension": "CHF 2'000",
                    "p1_income_eo": "CHF 100",
                    "p2_income_eo": "CHF 200",
                    "income_child_benefits": "CHF 50",
                    "income_alimony_partner": "CHF 156",
                    "income_alimony_children": "CHF 78",
                    "income_portfolio": "CHF 3'600",
                    "income_undistributed_inheritances": "CHF 88",
                    "income_other": "CHF 786",
                    "income_lump_sum": "CHF 952",
                    "income_gross_total": "CHF 199'119",
                    "expense_alimony_partner": "CHF 56",
                    "expense_alimony_children": "CHF 78",
                    "expense_children_daycare": "CHF 56",
                    "expense_annuity_contributions": "CHF 12",
                    "p1_contribution_pillar_2": "CHF 87",
                    "p2_contribution_pillar_2": "CHF 77",
                    "p1_contribution_pillar_3a": "CHF 11",
                    "p2_contribution_pillar_3a": "CHF 33",
                    "p1_expense_employment": "CHF 5'403",
                    "p2_expense_employment": "CHF 4'000",
                    "p1_deductions_education": "CHF 123",
                    "p2_deductions_education": "CHF 234",
                    "deductions_illness": "CHF 0",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 1'234",
                    "interest_paid_on_debt": "CHF 123",
                    "deductions_other": "CHF 99",
                    "deductions_total": "CHF 12'256",
                    "income_net_total": "CHF 186'863",
                    "income_taxable_local": "CHF 179'863",
                    "assets_portfolio": "CHF 173'340",
                    "assets_cash_gold": "CHF 50'000",
                    "assets_life_insurance": "CHF 888",
                    "assets_cars": "CHF 10'000",
                    "assets_other": "CHF 100",
                    "assets_real_estate_total_gross": "CHF 50'000",
                    "assets_gross_total": "CHF 289'827",
                    "assets_net_total": "CHF 283'143",
                    "assets_taxable_local": "CHF 0",
                    "debt_total": "CHF 6'684",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_ur_double_2021():
    """ """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "UR" / "ur_double_2021",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ur_double_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 21,
                DocumentCat.TAX_CALCULATION: 2,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "UR",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8000",
                    "p1_fullname": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_marital_status": "verheiratet",
                    "p1_profession": "Job 1",
                    "p1_employer": "Arbeitgeber 1",
                    "p1_employer_location": "Ort 1",
                    "p2_date_of_birth": "01.01.1980",
                    "p2_profession": "ib/2",
                    "p2_employer": "Arbeitgeber 2",
                    "p2_employer_location": "Ort 2",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 80'000",
                    "p2_income_employed_main": "CHF 4'390",
                    "p1_income_employed_side": "CHF 5'909",
                    "p1_income_self_main": "CHF 4'214",
                    "p1_income_pension_ahv": "CHF 41'412",
                    "p2_income_pension": "CHF 5'235",
                    "p1_income_social_security": "CHF 3'244",
                    "income_child_benefits": "CHF 4'121",
                    "income_portfolio": "CHF 19'321",
                    "p1_income_gross_total": "CHF 173'444",
                    "p1_contribution_pillar_1": "CHF 4'314",
                    "p2_contribution_pillar_1": "CHF 4'242",
                    "p1_contribution_pillar_2": "CHF 233",
                    "p2_contribution_pillar_2": "CHF 42",
                    "p1_contribution_pillar_3a": "CHF 4'000",
                    "p2_contribution_pillar_3a": "CHF 878",
                    "p2_expense_employment": "CHF 5'794",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "interest_paid_on_debt": "CHF 1'314",
                    "deductions_wealth_management": "CHF 2'681",
                    "deductions_total": "CHF 63'615",
                    "income_net_total": "CHF 109'829",
                    "income_taxable_local": "CHF 76'229",
                    "assets_portfolio": "CHF 893'605",
                    "assets_life_insurance": "CHF 523'523",
                    "assets_cars": "CHF 9'085",
                    "assets_other": "CHF 5'645",
                    "assets_real_estate_total_net": "CHF 5'353",
                    "assets_gross_total": "CHF 1'533'398",
                    "assets_net_total": "CHF 1'489'256",
                    "assets_taxable_local": "CHF 1'257'956",
                    "debt_private": "CHF 44'142",
                    "property_imputed_rental_value": "CHF 325",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_ur_single_2019():
    """ """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "UR" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ur_single_2019.pdf"],
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={
            #     DocumentCat.TAX_DECLARATION: 1
            # },
            # expected_doc_cat_frequency_per_page={
            #     DocumentCat.TAX_DECLARATION: 21,
            #     DocumentCat.TAX_CALCULATION: 2,
            # },
            #
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_vs_double_de_2021():
    """
    Page 4 has now extraction personal.
    Page 5 has now extraction income (together with french version).
    Page 6 has now extraction deductions (together with french version).
    Page 7 has now extraction assets (together with french version).
    The whole document gets recognized as TAX VS.
    Pages 3, 11, 12, 15 are UNKNOWN_DE.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=3,
        source_folder=source_folder / "VS",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_vs_double_de_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 18,
                DocumentCat.TAX_CALCULATION: 2,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_page_objects={
                "0": {
                    # Many extractions don't work on this document because of the "Demo Version" and further
                    # OCR problems.
                    "canton_short": "VS",
                    "year": "2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8000",
                    "p1_firstname": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1970",
                    "p1_profession": "Job 1",
                    "p2_profession": "J   2",
                    "section_children": PO_EXISTS,
                    "p1_income_self_main": "CHF 43'980",
                    "p2_income_self_main": "CHF 32'320",
                    "income_portfolio": "CHF 19'321",
                    "deductions_total": "CHF 1'429",
                    "income_net_total": "CHF 143'858",
                    "income_taxable_local": "CHF 120'853",
                    "assets_portfolio": "CHF 893'605",
                    "assets_cars": "CHF 4'222",
                    "assets_gross_total": "CHF 1'003'507",
                    "assets_taxable_local": "CHF 939'166",
                    "debt_private": "CHF 4'341",
                    "debt_total": "CHF 64'341",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zg_blacked():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ZG" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_zg.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # DocumentCat.TAX_DECLARATION: 1
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_zh_double_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ZH",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_zh_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 13},
            expected_page_objects={
                "0": {
                    "canton_short": "ZH",
                    "year": "2019",
                    "document_date": "20.04.2020",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8055",
                    "city": "Zürich",
                    "p1_ahv_new": PO_EXISTS,
                    "p1_fullname": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p2_phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "04.09.1967",
                    "p2_date_of_birth": "21.10.1976",
                    "p1_profession": "Informatiker",
                    "p2_profession": "Solution Consultant",
                    "p1_employer": "Beispielfirma AG",
                    "p2_employer": "ServiceFirma",
                    "p1_employer_location": "Zürich",
                    "p1_marital_status": "verheiratet",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 22'506",
                    "p1_income_employed_side": "CHF 7'502",
                    "p2_income_employed_main": "CHF 128'991",
                    "p2_income_eo": "CHF 4'388",
                    "p2_income_child_benefits": "CHF 4'800",
                    "income_portfolio": "CHF 15",
                    "income_real_estate_gross": "CHF 25'700",
                    "income_real_estate_net_primary": "CHF 20'560",
                    "income_gross_total": "CHF 188'762",
                    "p1_contribution_pillar_3a": "CHF 3'350",
                    "p2_contribution_pillar_3a": "CHF 6'700",
                    "p1_expense_employment": "CHF 7'901",
                    "p2_expense_employment": "CHF 8'850",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'800",
                    "expense_children_daycare": "CHF 19'248",
                    "property_maintenance_cost": "CHF 5'140",
                    "deductions_total": "CHF 71'006",
                    "income_net_total": "CHF 117'756",
                    "income_taxable_global": "CHF 99'756",
                    "income_taxable_local": "CHF 99'756",
                    "assets_portfolio": "CHF 2'458'532",
                    "assets_cars": "CHF 1'040",
                    "assets_other": "CHF 180'288",
                    "assets_real_estate_main_property": "CHF 1'172'000",
                    "address_real_estate_primary": PO_EXISTS,
                    "assets_gross_total": "CHF 3'811'860",
                    "assets_taxable_global": "CHF 2'676'860",
                    "assets_taxable_local": "CHF 2'676'860",
                    "debt_total": "CHF 1'135'000",
                    "interest_paid_on_debt": "CHF 11'257",
                    "debt_detail_lines": PO_EXISTS,
                    "property_imputed_rental_value": "CHF 25'700",
                }
            },
        ),
    ).run()
