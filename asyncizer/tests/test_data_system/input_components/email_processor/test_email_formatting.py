from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/email_formatting"
)
dest_folder_prefix = "input_system_components_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_eml_email_with_special_chars():
    target_folder = path_root_folder
    assert target_folder.exists()
    # Contains a single email with polish characters in the subject and body
    # so we want to make sure that the text is correctly rendered (utf-8/latin-1 and font that supports special chars)

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["GRYCZANKA_test_polish_characters.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE Karta zamówienia.pdf",
                "738 Email-Korrespondenz.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN: 1,
            },
        ),
    ).run()
