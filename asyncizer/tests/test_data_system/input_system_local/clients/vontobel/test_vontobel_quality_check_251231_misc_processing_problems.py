import shutil
from pathlib import Path
from typing import Optional

import pytest
import structlog
from extract_msg import Attachment

from asyncizer.tests.util_tests import <PERSON>ssierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.smime_p7m import decode_smime_email_with_signature
import extract_msg
from email import policy
from email.parser import BytesParser
import tempfile


logger = structlog.getLogger(__name__)

FOLDER_TYPE = "251231_misc_processing_problems"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/vontobel/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_vontobel_{FOLDER_TYPE}_"


def extract_email_content(msg_path: Path, target_path: Optional[Path] = None) -> bytes:
    """
    Extracts HTML body and attachments from a .msg file, including S/MIME signed (IPM.Note.SMIME) messages.
    Returns: dict with 'html_body', 'attachments' (list of (filename, bytes)), and 'message_class'
    """
    msg = extract_msg.Message(str(msg_path))
    msg_class = msg.classType
    result = {"html_body": None, "attachments": [], "message_class": msg_class}

    if msg_class == "IPM.Note":
        # Regular email
        result["html_body"] = msg.htmlBody or msg.body
        for att in msg.attachments:
            result["attachments"].append(
                (att.longFilename or att.shortFilename, att.data)
            )

    elif msg_class.startswith("IPM.Note.SMIME"):
        # S/MIME signed or encrypted email
        p7m_attachment: Attachment = next(
            (
                att
                for att in msg.attachments
                if att.longFilename and att.longFilename.lower().endswith(".p7m")
            ),
            None,
        )
        if not p7m_attachment:
            raise ValueError("No .p7m attachment found in S/MIME message")

        with tempfile.TemporaryDirectory() as tmpdir:
            if not target_path:
                target_path = Path(tmpdir)

            p7m_path = target_path / "smime.p7m"
            eml_path = target_path / "decoded.eml"

            # Write the .p7m file
            with open(p7m_path, "wb") as f:
                f.write(p7m_attachment.data)
            assert p7m_path.exists()

            decode_smime_email_with_signature(p7m_path, eml_path)

            assert eml_path.exists()

            # Parse the resulting .eml file
            with open(eml_path, "rb") as f:
                eml = BytesParser(policy=policy.default).parse(f)

            # Extract HTML or plain body
            for part in eml.walk():
                if part.get_content_type() == "text/html":
                    result["html_body"] = part.get_content()
                    break
            if not result["html_body"]:
                for part in eml.walk():
                    if part.get_content_type() == "text/plain":
                        result["html_body"] = part.get_content()
                        break

            # Extract attachments
            for part in eml.iter_attachments():
                filename = part.get_filename()
                if filename:
                    content = part.get_payload(decode=True)
                    result["attachments"].append((filename, content))

    else:
        raise ValueError(f"Unsupported message class: {msg_class}")

    return result


@pytest.mark.skip("Not mature - used for testing encrypted Vontobel emails.")
@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.skip("Not mature - used for testing encrypted Vontobel emails.")
def test_250602_unpack_signed_email():
    p = path_root_folder / "250602_email_problem/original/email.msg"
    assert p.exists()

    p_target = p.parent.parent / "output"
    if p_target.exists():
        shutil.rmtree(p_target)
    p_target.mkdir()

    result = extract_email_content(p, p_target)

    print("Message class:", result["message_class"])
    print("HTML snippet:", result["html_body"][:300])
    print("Attachment names:", [name for name, _ in result["attachments"]])


@pytest.mark.skip("Not mature - used for testing encrypted Vontobel emails.")
@pytest.mark.asyncio
async def test_250602_email_problem():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250602_email_problem",
        show_page=-1,
        # source_file_filter=["encoding.msg"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
