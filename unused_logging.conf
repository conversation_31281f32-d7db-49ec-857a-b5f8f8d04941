[loggers]
keys=root, luigi-interface, luigi.scheduler, luigiworkflow, some_rand_logger

[formatters]
keys=standardFormatter, consoleFormatter

[handlers]
keys=fileHandler, consoleHandler

[logger_root]
level=INFO
handlers=consoleHandler

[logger_luigi-interface]
level=INFO
handlers=consoleHandler, fileHandler
qualname=luigi-interface
propagate=0

[logger_luigi.scheduler]
level=INFO
handlers=consoleHandler, fileHandler
qualname=luigi.scheduler
propagate=0

[logger_luigiworkflow]
level=DEBUG
handlers=consoleHandler, fileHandler
qualname=luigiworkflow
propagate=0

[logger_some_rand_logger]
level=INFO
handlers=consoleHandler, fileHandler
qualname=some_rand_logger
propagate=0

[formatter_standardFormatter]
format=%(asctime)s.%(msecs)03d %(name)-16s %(levelname)-8s %(message)s
datefmt=%y-%m-%d %H:%M:%S

[formatter_consoleFormatter]
#format=- %(levelname)s - %(message)s
format=%(name)-16s %(levelname)-8s %(message)s
datefmt=

[handler_fileHandler]
class=handlers.TimedRotatingFileHandler
formatter=standardFormatter
args=("log/hyextract_logging.log","midnight",1,14)

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=consoleFormatter
args=(sys.stdout,)