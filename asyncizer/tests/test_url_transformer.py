import asyncio
from abc import ABC, abstractmethod
from pathlib import Path
from tempfile import TemporaryDirectory
from time import time
from typing import List
from urllib.parse import urlparse

import httpx
import pytest
from dependency_injector import containers, providers
from hdapii.response import Response, Error
from pdf2image import pdf2image
from pdf2image.exceptions import PDFPopplerTimeoutError, PDFPageCountError
from pydantic import BaseModel, HttpUrl

from asyncizer.containers import TemporaryFileStoreContainer
from asyncizer.filestores import TemporaryFileStore
from asyncizer.s3 import make_bucket_available
from data_demo import DATA_DEMO_PATH

import structlog

logger = structlog.getLogger(__name__)


class UrlTransformRequest(BaseModel):
    file_url: HttpUrl


class UrlTransformResponse(BaseModel):
    file_urls: List[HttpUrl]


class FileTransformerException(Exception):
    pass


class FileTransformer(ABC):
    @abstractmethod
    async def transform(self, file: Path, dest: Path) -> List[Path]:
        raise NotImplementedError()


class Pdf2JpgTransformer:
    def __init__(self):
        self.conversion_timeout = 90
        self.default_dpi = 300

    async def transform(self, file: Path, dest: Path) -> List[Path]:
        try:
            t = time()
            logger.info(
                f"start pdf2jpg transformation for {file} to {dest}",
                file=file,
                destination=dest,
            )
            paths = pdf2image.convert_from_path(
                file,
                output_folder=dest,
                dpi=self.default_dpi,
                fmt="jpg",
                paths_only=True,
                timeout=self.conversion_timeout,
            )
        except PDFPopplerTimeoutError as e:
            logger.error(
                f"Processing aborted after timeout={self.conversion_timeout} seconds. e={e}",
                timeout=self.conversion_timeout,
                e=e,
                exc_info=True,
            )
            raise e
        except PDFPageCountError as e:
            raise FileTransformerException(f"Could not transform {file}", e)

        duration = round(time() - t, 2)
        logger.info(
            f"finished convert_from_path in {duration} seconds", duration=duration
        )
        paths = [Path(path) for path in paths]
        return paths


class UrlTransformer:
    def __init__(
        self,
        file_transformer: FileTransformer,
        temporary_file_store: TemporaryFileStore,
    ):
        self.temporary_file_store = temporary_file_store
        self.file_transformer = file_transformer

    async def transform(
        self, request: UrlTransformRequest
    ) -> Response[UrlTransformResponse]:
        with TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            filename = Path(urlparse(str(request.file_url)).path).name
            file_dest = temp_path / filename

            async with httpx.AsyncClient() as client:
                try:
                    res = await client.get(str(request.file_url))
                    res.raise_for_status()
                except Exception as e:
                    return Response(error=Error(code=400, message=str(e)))

                file_dest.write_bytes(res.read())

                with TemporaryDirectory() as output_dir:
                    output_path = Path(output_dir)
                    try:
                        paths = await self.file_transformer.transform(
                            file_dest, output_path
                        )

                        tasks = [
                            asyncio.create_task(self.temporary_file_store.put(path))
                            for path in paths
                        ]
                        urls: List[HttpUrl] = await asyncio.gather(*tasks)
                        return Response(data=UrlTransformResponse(file_urls=urls))
                    except FileTransformerException as e:
                        return Response(error=Error(code=403, message=str(e)))


class UrlTransformerContainer(containers.DeclarativeContainer):
    temporary_file_store_container: TemporaryFileStoreContainer = providers.Container(
        TemporaryFileStoreContainer
    )

    pdf2jpg_file_transformer: FileTransformer = providers.Singleton(Pdf2JpgTransformer)
    pdf2jpg_url_transformer: UrlTransformer = providers.Singleton(
        UrlTransformer,
        file_transformer=pdf2jpg_file_transformer(),
        temporary_file_store=temporary_file_store_container.temporary_file_store,
    )


@pytest.mark.asyncio
@pytest.mark.integration
async def test_pdf2jpg_with_multiple_pages():
    container = UrlTransformerContainer()
    await make_bucket_available("test-temporary-file-store")
    temporary_file_store = (
        container.temporary_file_store_container.temporary_file_store()
    )
    pdf2jpg_url_transformer = container.pdf2jpg_url_transformer()

    file = DATA_DEMO_PATH / "mixed_searchable/310_max_maria_2019.pdf"
    file_url = await temporary_file_store.put(file)
    result = await pdf2jpg_url_transformer.transform(
        UrlTransformRequest(file_url=file_url)
    )
    assert len(result.data.file_urls) == 13


@pytest.mark.asyncio
@pytest.mark.integration
async def test_pdf2jpg_with_invalid_url():
    container = UrlTransformerContainer()
    await make_bucket_available("test-temporary-file-store")
    pdf2jpg_url_transformer = container.pdf2jpg_url_transformer()
    result = await pdf2jpg_url_transformer.transform(
        UrlTransformRequest(file_url="https://example.com/doesnotexist")
    )
    assert result.error.code == 400


@pytest.mark.asyncio
@pytest.mark.integration
async def test_pdf2jpg_with_invalid_pdf():
    container = UrlTransformerContainer()
    await make_bucket_available("test-temporary-file-store")
    temporary_file_store = (
        container.temporary_file_store_container.temporary_file_store()
    )
    pdf2jpg_url_transformer = container.pdf2jpg_url_transformer()

    file = DATA_DEMO_PATH / "mixed_searchable/invalid_pdf.pdf"
    file_url = await temporary_file_store.put(file)
    result = await pdf2jpg_url_transformer.transform(
        UrlTransformRequest(file_url=file_url)
    )
    assert result.error.code == 403
