from dataclasses import dataclass

# There is a specification here: https://www.elohnausweis-ssk.ch/de/assets/documents/Benutzerhandbuch_eLA.pdf
# https://www.elohnausweis-ssk.ch/de/assets/documents/SalaryDeclarationElohnOnline.xsd
from typing import Dict

from hypodossier.core.documents.land_register.LandRegisterPageData import (
    FIELD_PROP_VALUE_RATIO,
)
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    FIELD_PROP_ADDRESS,
    FIELD_PROP_ADDRESS_STREET,
    FIELD_PROP_ADDRESS_STREET_NO,
    FIELD_PROP_ADDRESS_CITY,
    FIELD_PROP_ADDRESS_ZIP,
    PI_FIELD_YEAR_CONSTRUCTION,
    FIELD_PROP_CUBATURE,
    PI_FIELD_PLOT_SIZE,
    FIELD_PROP_CADASTER_NO,
    FIELD_PROP_ESTIMATION_DATE,
    FIELD_PROP_ESTIMATION_REASON,
    FIELD_PROP_ESTIMATION_VALUE,
)
from hypodossier.core.domain.PageData import PersonPageData, FIELDS_PERSON_DATA
from hypodossier.core.domain.SemanticField import (
    SemanticField,
    FieldTitles,
    ExtractionType,
    fields_as_dict,
    merge_dicts,
    FIELD_PROPERTY_AREA_LIVING_GROSS,
    FIELD_PROPERTY_AREA_LIVING_NET,
    FIELD_PROPERTY_AREA_LIVING_DEFAULT,
)

# FIELD_PROP_CADASTER_NO = SemanticField("cadaster_no", ExtractionType.STRING, FieldTitles("Kataster Nr.", "Cadaster No."), desc_de="Katasternummer des zugehörigen Grundstücks. I. d. R. eine vierstellige Zahl, teilweise mit einem Buchstabenprefix z.B. '1234' oder 'AR1234' (Zürich)")


FIELD_PROP_TAX_VALUE_GROSS = SemanticField(
    "tax_value_gross",
    ExtractionType.STRING,
    FieldTitles("Steuerwert brutto", "Tax Value gross"),
    desc_de="Steuerwert des Grundstücks vor Abzügen/Abschlägen",
)
FIELD_PROP_TAX_VALUE_NET = SemanticField(
    "tax_value_net",
    ExtractionType.STRING,
    FieldTitles("Steuerwert netto", "Tax Value net"),
    desc_de="Steuerwert des Grundstücks nach Abzügen/Abschlägen",
)

FIELD_PROP_IMPUTED_RENTAL_VALUE_GROSS = SemanticField(
    "imputed_rental_value_gross",
    ExtractionType.STRING,
    FieldTitles("Eigenmietwert brutto", "Tax Rent gross"),
    desc_de="Eigenmietwert des Grundstücks vor Abzügen/Abschlägen",
)
FIELD_PROP_IMPUTED_RENTAL_VALUE_NET = SemanticField(
    "imputed_rental_value_net",
    ExtractionType.STRING,
    FieldTitles("Eigenmietwert netto", "Tax Rent net"),
    desc_de="Eigenmietwert des Grundstücks nach Abzügen/Abschlägen",
)


FIELD_RECIPIENT_NAME = SemanticField(
    "recipient_name",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Empfänger", "Recipient"),
    desc_de="Name des Empfängers",
)
FIELD_RECIPIENT_ADDRESS = SemanticField(
    "recipient_address",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Empfänger Adresse", "Recipient Address"),
    desc_de="Adresse des Empfängers (Strasse, Hausnummer, PLZ, Ortz)",
)


FIELD_PROPERTY_TYPE = SemanticField(
    "property_type",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Liegenschaftstyp", "Property Type"),
    desc_de="Art der Liegenschaft (Haus / Wohnung / ...)",
)

FIELD_AVAILABILITY = SemanticField(
    "availability",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Verfügbarkeit", "Availablility"),
    desc_de="Verfügbarkeit des Objekts (Nach Vereinbarung / sofort / Datum)",
)

FIELD_FLOOR_NUMBER = SemanticField(
    "floor_number",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Stockwerk", "Floor (Index)"),
    desc_de="Nummer des Stockwerks (0 = Erdgeschoss, 1 = Geschoss über Erdgeschoss, ...)",
)

FIELD_NUM_ROOMS_LIVING = SemanticField(
    "num_rooms_living",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Anzahl Zimmer", "Number of rooms"),
    desc_de="Anzahl Wohn- und Schlafzimmer",
)

FIELD_NUM_ROOMS_BEDROOM = SemanticField(
    "num_rooms_bedroom",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Anzahl Schlafzimmer", "Number of Sleeping Rooms"),
    desc_de="Anzahl Schlafzimmer (ohne Wohnzimmer)",
)

FIELD_NUM_ROOMS_BATHROOM = SemanticField(
    "num_rooms_bathroom",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Anzahl Badezimmer", "Number of Bathrooms"),
    desc_de="Anzahl Badezimmer",
)

FIELD_NUM_ROOMS_WETROOM = SemanticField(
    "num_rooms_wetroom",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Anzahl Nasszellen", "Number of wet rooms"),
    desc_de="Anzahl Nasszellen (kleines Badezimmer mit Dusche und wasserdichtem Boden, teilweise Synonym für Badezimmer)",
)

FIELD_NUM_ROOMS_TOILET = SemanticField(
    "num_rooms_toilet",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Anzahl Toiletten", "Number of Toilets"),
    desc_de="Anzahl Toiletten/WCs als separater Raum (ausserhalb Badezimmer)",
)

FIELD_HEATING_SYSTEM = SemanticField(
    "heating_system",
    ExtractionType.STRING,
    FieldTitles("Heizungstyp", "Heating System"),
    desc_de="Art der Wärmeerzeugung, z.B. 'Luft-Wasser-Wärmepumpe'",
)

FIELD_HEAT_DISTRIBUTION = SemanticField(
    "heat_distribution",
    ExtractionType.STRING,
    FieldTitles("Wärmeverteilung", "Heat Distribution"),
    desc_de="Art der Wärmeverteilung, z.B. 'Bodenheizung'",
)

FIELD_PROPERTY_PRICE = SemanticField(
    "property_price",
    ExtractionType.STRING,
    FieldTitles("Verkaufspreis", "Sales Price"),
    desc_de="Verkaufspreis der Liegenschaft, z.B. CHF 1'234'567",
)

new_fields = fields_as_dict(
    [
        # FIELD_DOCUMENT_VALIDITY_START_DATE,
        # FIELD_CANTON_SHORT,
        #
        FIELD_PROP_ADDRESS,
        FIELD_PROP_ADDRESS_STREET,
        FIELD_PROP_ADDRESS_STREET_NO,
        FIELD_PROP_ADDRESS_CITY,
        FIELD_PROP_ADDRESS_ZIP,
        # FIELD_PROP_PROPERTY_DESC,
        #
        FIELD_PROP_VALUE_RATIO,
        #
        # FIELD_PROP_OWNER_FULLNAME,
        # FIELD_PROP_OWNER_ADDRESS,
        #
        FIELD_PROPERTY_AREA_LIVING_GROSS,
        FIELD_PROPERTY_AREA_LIVING_NET,
        FIELD_PROPERTY_AREA_LIVING_DEFAULT,
        PI_FIELD_YEAR_CONSTRUCTION,
        FIELD_PROP_CUBATURE,
        PI_FIELD_PLOT_SIZE,
        FIELD_PROPERTY_AREA_LIVING_GROSS,
        FIELD_PROPERTY_AREA_LIVING_NET,
        FIELD_PROP_CADASTER_NO,
        #
        # FIELD_LAND_REGISTER_ID,
        # #FIELD_PROP_BUILDING_ID
        #
        FIELD_PROP_ESTIMATION_VALUE,
        FIELD_PROP_ESTIMATION_DATE,
        FIELD_PROP_ESTIMATION_REASON,
        FIELD_PROPERTY_TYPE,
        FIELD_PROPERTY_PRICE,
        FIELD_AVAILABILITY,
        FIELD_FLOOR_NUMBER,
        FIELD_NUM_ROOMS_LIVING,
        FIELD_NUM_ROOMS_BEDROOM,
        FIELD_NUM_ROOMS_BATHROOM,
        FIELD_NUM_ROOMS_WETROOM,
        FIELD_NUM_ROOMS_TOILET,
        FIELD_HEATING_SYSTEM,
        FIELD_HEAT_DISTRIBUTION,
    ]
)

FIELDS_PROPERTY_VALUATION = merge_dicts(FIELDS_PERSON_DATA, new_fields)


@dataclass
class PropertyValuationPageData(PersonPageData):

    property_address: str = None
    property_address_street: str = None
    property_address_street_no: str = None
    property_address_city: str = None
    property_address_zip: str = None
    property_desc: str = None

    year_construction: int = None
    cubature: int = None
    plot_size: int = None
    property_area_living_gross: int = None
    property_area_living_net: int = None
    property_area_living_default: str = None
    heating_system: str = None
    heating_distribution: str = None
    cadaster_no: str = None

    property_estimation_value: str = None
    property_estimation_date: str = None
    property_estimation_reason: str = None

    property_value_ratio: str = None

    property_type: str = None
    property_price: str = None
    availability: str = None
    floor_number: str = None
    num_rooms_living: str = None
    num_rooms_bedroom: str = None
    num_rooms_bathroom: str = None
    num_rooms_wetroom: str = None
    num_rooms_toilet: str = None

    def get_invalidity_reason(self):
        return None

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        d = super().get_semantic_fields()
        return {**d, **FIELDS_PROPERTY_VALUATION}


if __name__ == "__main__":
    print(FIELDS_PROPERTY_VALUATION)
