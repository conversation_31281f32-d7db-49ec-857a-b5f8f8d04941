from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)

parsers = [
    TemplatePageParser(
        desc="Rentenbescheinigung BL PK",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_PAGE,
        ranked_titles_any=[RankedTitle("Rentenausweis per")],
        required_text_conditions=[
            FromStartTextCond("Basellandschaftliche", num_lines=5),
            FromStartTextCond("Pensionskasse", num_lines=6),
        ],
        required_tokens_any=[
            ["blpk.ch"],
            ["Vorsorgereglement"],
            ["erstellt am"],
            ["Allfällige Rentenauszahlungen"],
        ],
    ),
    TemplatePageParser(
        desc="Rentenbescheinigung Coutts Allvisa Services PK",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_PAGE,
        ranked_titles_all=[
            RankedTitle("Rentenabrechnung für das Jahr"),
            RankedTitle("Rentenausweis für das Jahr"),
        ],
        required_text_conditions=[
            FromStartTextCond("Rentenabrechnung für das Jahr", num_lines=5),
        ],
        required_tokens_any=[
            ["Dieser Ausweis ersetzt allfällige"],
        ],
    ),
    TemplatePageParser(
        desc="Rentenbescheinigung Glarner Altersrücktritt",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_PAGE,
        ranked_titles_all=[
            RankedTitle("GLARNER"),
            RankedTitle("PENSIONSKASSE"),
            RankedTitle("VERFÜGUNG ALTERSRENTE / KAPITAL"),
        ],
        required_tokens_any=[["Sehr geehrter"], ["Altersrücktritt"]],
    ),
    TemplatePageParser(
        desc="Rentenbescheinigung Zurich",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_PAGE,
        ranked_titles_all=[
            RankedTitle("Bescheinigung ausbezahlter Rentenleistungen"),
            RankedTitle("Rentenauszug vom"),
        ],
        required_tokens_any=[
            ["Zürich Lebensversicherungs", "Zürich Schweiz"],
            ["Rentenauszug vom"],
        ],
    ),
]


def get_parsers_pension_payment_bvg():
    return parsers
