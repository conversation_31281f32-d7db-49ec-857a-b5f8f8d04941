from dataclasses import dataclass
from typing import Dict

from abbyyplumber.converter.ValueConverter import (
    Value<PERSON>onverter,
    CleanNameConverter,
    MostRecentDateConverter,
    BirthDateConverter,
    ParagraphConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElement,
)
from hypodossier.core.documents.generic_letter.GenericLetterPageData import (
    FIELDS_GENERIC_LETTER,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_COMPANY,
    Semantic<PERSON>ield,
    FIELD_FULLNAME,
    FIELD_DOCUMENT_DATE,
    FIELD_FIRSTNAME,
    FIELD_LASTNAME,
    FIELD_PRODUCT,
    FIELD_DATE_OF_BIRTH,
    FIELD_DOCUMENT_TITLE,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
    default_field,
)


@dataclass
class GenericLetterSearchElements:
    document_date: SearchElement = None
    company: SearchElement = None
    product: SearchElement = None

    document_title: SearchElement = None

    address_block: SearchElement = None

    firstname: SearchElement = None
    lastname: SearchElement = None

    # Name of person as derived from address_block
    fullname: SearchElement = None

    street: SearchElement = None
    zip: SearchElement = None
    city: SearchElement = None

    date_of_birth: SearchElement = None

    default_converters: Dict[SemanticField, ValueConverter] = default_field(
        {
            FIELD_FULLNAME.name: CleanNameConverter(max_num_lines=1),
            FIELD_FIRSTNAME.name: CleanNameConverter(max_num_lines=1),
            FIELD_LASTNAME.name: CleanNameConverter(max_num_lines=1),
            FIELD_DOCUMENT_DATE.name: MostRecentDateConverter(),
            FIELD_DOCUMENT_TITLE.name: ParagraphConverter(max_num_lines=1),
            FIELD_DATE_OF_BIRTH.name: BirthDateConverter(),
        }
    )


@dataclass
class TemplateGenericLetterPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    # This must be overridden by subclass
    # doc_cat: DocumentCat = DocumentCat.AUTHORIZATION_FOR_INQUIRIES
    company: str = None

    product: str = None

    # Title of the letter / document
    document_title: SearchElement = None

    se: GenericLetterSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_GENERIC_LETTER.keys())

        # Add these special fields as constants if they are set in the parser explicitely.
        # Only to be used if values are fixed. In general it is better to parse them from the page

        # Skip this one as it is already handled in super().update_search_elements_generic(...)
        # if self.document_title:
        #     self.search_elements.append(
        #         SearchElementConstant(FIELD_DOCUMENT_TITLE.name, self.document_title)
        #     )

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )
        if self.product:
            self.search_elements.append(
                SearchElementConstant(FIELD_PRODUCT.name, self.product)
            )
