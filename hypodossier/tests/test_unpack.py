from pathlib import Path
from shutil import copy
from tempfile import TemporaryDirectory

import pytest
from rarfile import tool_setup

from constants import EXTRACTED_EMAIL_FILENAME_PREFIX
from hypodossier.unpack import (
    FileExtraction,
    unpack_path,
    unpack_eml,
    NOT_READABLE_EXCEPTION_DETAILS,
    PASSWORD_PROTECTED_EXCEPTION_DETAILS,
    is_att_file,
)

import structlog

logger = structlog.getLogger(__name__)


test_data_mac = [
    (
        "data/unpack/Text-File_von_Mac.zip",
        FileExtraction(
            original_file="Text-File_von_Mac.zip",
            extracted_files=[
                # The commented files are included in the zip but should be ignored as they are small
                # mac os specific files without useful content
                # 'Text-File_von_Mac.zip/ATT00002.htm',
                # 'Text-File_von_Mac.zip/Lümelis_Büechli_för_Olivia.zip/__MACOSX/._Lümelis_Büechli_för_Olivia.txt',
                "Text-File_von_Mac.zip/Lümelis_Büechli_för_Olivia.zip/Lümelis_Büechli_för_Olivia.txt",
                # 'Text-File_von_Mac.zip/ATT00001.htm',
                "Text-File_von_Mac.zip/Lümelis_Büechli_för_Olivia.txt",
            ],
            exceptions={},
        ),
    )
]

test_data_email = [
    (
        "data/unpack/ATT00001.htm",
        FileExtraction(
            original_file="ATT00001.htm",
            extracted_files=[
                # No files to extract, the html should be skipped
            ],
            exceptions={},
        ),
    )
]

test_data = (
    [
        (
            "data/unpack/two_dots_two_files.zip",
            FileExtraction(
                original_file="two_dots_two_files.zip",
                extracted_files=[
                    "two_dots_two_files.zip/file_with_two_dots..pdf",
                    "two_dots_two_files.zip/file_with_two_dots..txt",
                ],
                exceptions={},
            ),
        ),
        (
            "data/unpack/sample.rar",
            FileExtraction(
                original_file="sample.rar",
                extracted_files=["sample.rar/micro_orc.fbx"],
                exceptions={},
            ),
        ),
        (
            "data/unpack/simple.zip",
            FileExtraction(
                original_file="simple.zip",
                extracted_files=["simple.zip/simple/test.txt"],
                exceptions={},
            ),
        ),
        (
            "data/unpack/error.zip",
            FileExtraction(
                original_file="error.zip",
                extracted_files=["error.zip"],
                exceptions={"error.zip": NOT_READABLE_EXCEPTION_DETAILS},
            ),
        ),
        (
            "data/unpack/simple.7z",
            FileExtraction(
                original_file="simple.7z",
                # 7z is unknown so it is treated as a normal file
                extracted_files=["simple.7z/simple.zip/simple/test.txt"],
                exceptions={},
            ),
        ),
        (
            "data/unpack/simple.tar.xz",
            FileExtraction(
                original_file="simple.tar.xz",
                extracted_files=["simple.tar.xz/simple.zip/simple/test.txt"],
                exceptions={},
            ),
        ),
        (
            "data/unpack/complex.zip",
            FileExtraction(
                original_file="complex.zip",
                extracted_files=[
                    "complex.zip/complex/complex_level1/complex_level2/simple.zip/simple/test.txt",
                    "complex.zip/complex/complex_level1/simple.zip/simple/test.txt",
                ],
                exceptions={},
            ),
        ),
        (
            "data/unpack/sample_windows.zip",
            FileExtraction(
                original_file="sample_windows.zip",
                extracted_files=[
                    "sample_windows.zip/sample_windows/folder with umlaute äöü/textfile with äöü.txt"
                ],
                exceptions={},
            ),
        ),
        (
            "data/unpack/sample_windows_with_password.zip",
            FileExtraction(
                original_file="sample_windows_with_password.zip",
                extracted_files=["sample_windows_with_password.zip"],
                exceptions={
                    "sample_windows_with_password.zip": PASSWORD_PROTECTED_EXCEPTION_DETAILS
                },
            ),
        ),
        (
            "data/unpack/just_a_text_file.txt",
            FileExtraction(
                original_file="just_a_text_file.txt",
                extracted_files=["just_a_text_file.txt"],
                exceptions={},
            ),
        ),
        (
            "data/unpack/msg_in_msg.zip",
            FileExtraction(
                original_file="msg_in_msg.zip",
                extracted_files=[
                    "msg_in_msg.zip/msg in msg.msg/2021-03-30_2000 Test msg file mit einem simplen Anhang/strafreg manu.pdf",
                    "msg_in_msg.zip/msg in msg.msg/2021-03-30_2000 Test msg file mit einem simplen Anhang/message.txt",
                ],
                exceptions={},
            ),
        ),
        (
            "data/unpack/error.zip in zip.zip",
            FileExtraction(
                original_file="error.zip in zip.zip",
                extracted_files=["error.zip in zip.zip/error.zip in zip/error.zip"],
                exceptions={
                    "error.zip in zip.zip/error.zip in zip/error.zip": NOT_READABLE_EXCEPTION_DETAILS
                },
            ),
        ),
    ]
    + test_data_mac
    + test_data_email
)


def test_att():
    assert not is_att_file("ATT00000001.xTXT")
    assert not is_att_file("ATT0x0000001.TXT")

    assert is_att_file("ATT00001.htm")
    assert is_att_file("att00001.htm")
    assert is_att_file("ATT00001.html")
    assert is_att_file("ATT00000001.txt")
    assert is_att_file("ATT00000001.TXT")


def test_unrar_is_installed():
    try:
        tool_setup()
    except:
        logger.error("unrar is not installed, try 'sudo apt install unrar'")
        pytest.fail()


@pytest.mark.parametrize("file,expected_file_extraction", test_data)
def test_unpack_simple_zipfile(file, expected_file_extraction):
    file_path = Path(__file__).parent / file
    assert file_path.exists()

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        temp_file_path = temp_path / file_path.name
        copy(file_path, temp_file_path)
        file_extraction = unpack_path(temp_file_path)
        for extracted_file in file_extraction.extracted_files:
            assert (temp_path / extracted_file).exists()

        file_extraction.extracted_files.sort()
        expected_file_extraction.extracted_files.sort()

        if file == "data/unpack/msg_in_msg.zip":
            assert set(expected_file_extraction.extracted_files).issubset(
                set(file_extraction.extracted_files)
            )
            # Email body has timestamp appended, so we can't compare exactly
            assert any(
                EXTRACTED_EMAIL_FILENAME_PREFIX in file_path
                for file_path in file_extraction.extracted_files
            )

        else:
            assert file_extraction == expected_file_extraction

        print(file_extraction.json(indent=2))


def test_unpack_eml():
    file = Path(__file__).parent / "data/unpack/mail.eml"
    with TemporaryDirectory() as temp_dir:
        attachments = unpack_eml(file, Path(temp_dir))
        attachments = [attachment.relative_to(temp_dir) for attachment in attachments]

        # Find the email body PDF file
        pdf_files = [
            att
            for att in attachments
            if att.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
            and att.suffix == ".pdf"
        ]
        assert len(pdf_files) == 1, "Should have exactly one email body PDF"

        # Remove PDF from list to check original attachment
        attachments = [att for att in attachments if att not in pdf_files]
        assert attachments == [Path("617 gvb max muster.jpg")]
