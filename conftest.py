import asyncio
import hashlib
import io
import json
import os
from pathlib import Path
import re
import uuid
from datetime import datetime
from typing import List, Optional
from uuid import UUID

import minio
import pytest
import structlog
from PIL import Image
from aiormq import Connection
from hdapii.processing.hyextract import BoundingBox
from hdapii.processing.hylayoutlm import ImagePredictResponse
from minio import S3Error, Minio
from pydantic import HttpUrl

import global_settings
from asyncizer.pageobjectclassifier import (
    PageObjectClassificationTask,
    PageObjectClassificationResponseV1,
    classify_page_objects_in_images,
)
from asyncizer.pageobjectdetector import (
    call_detect_page_objects_remote,
    PageObjectDetectionResponseV1,
)
from asyncizer.s3 import make_bucket_available
from asyncizer.s3_util import (
    get_object_from_s3,
    put_object_to_s3,
    get_object_hash,
    get_urls_content_sha256,
    object_exists_in_s3,
    upload_file_to_s3,
    get_presigned_download_url,
    get_file_hashes,
)
from data_demo import DATA_DEMO_PATH
from imagepredict.client.imagepredict_client_remote import (
    call_imagepredict_service_remote,
)

logger = structlog.getLogger(__name__)

# Constants for testing
TEST_BUCKET_PREFIX = "test-bucket-"
SAMPLE_PDF = "sample.pdf"
CORRUPTED_PDF = "corrupted.pdf"


@pytest.fixture(scope="session")
def event_loop():
    # This is needed to run all tests sequentially because RPC_CLIENT is a global variable
    # Taken from here: https://stackoverflow.com/questions/64282478/pytest-asyncio-with-singletons-causes-conflicting-event-loops
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def amqp_connection(event_loop):
    connection = await Connection.connect(global_settings.RABBIT_URL, loop=event_loop)
    yield connection
    # Ensure the connection is closed
    await connection.close()


@pytest.fixture(autouse=True, scope="function")
async def patch_frep_get_sha256sum(monkeypatch):
    # If minio is not set to generate sha256sums, we need to mock the function to failback to use md5sum
    # for caching tests to work

    monkeypatch.setattr("asyncizer.dossier_processor.get_sha256sum", get_object_hash)

    yield


def calculate_test_namespace(request) -> str:
    function_name = request.function.__name__

    class_name = request.cls.__name__ if request.cls else ""

    project_root = os.path.dirname(os.path.abspath(__file__))
    relative_path = os.path.relpath(request.module.__file__, project_root)

    test_namespace = f"{relative_path.replace('.py', '')}/{class_name}{function_name}"

    return test_namespace


async def get_test_cache_item(key: str, object_name: str, topic: str):
    cached_result = await get_object_from_s3("test-cache-bucket", object_name)
    await logger.ainfo("Using cached result", key=key, topic=topic)
    cached_data = json.loads(cached_result)
    return cached_data


async def put_test_cache_item(object_name, result_json):
    result_bytes = result_json.encode("utf-8")
    result_stream = io.BytesIO(result_bytes)
    await put_object_to_s3(
        bucket_name="test-cache-bucket",
        object_name=object_name,
        data=result_stream,
        length=len(result_bytes),
        content_type="application/json",
    )


async def clear_test_cache(prefix: str):
    minio_client = Minio(
        global_settings.S3_HOST,
        global_settings.S3_ACCESS_KEY,
        global_settings.S3_SECRET_KEY,
        secure=global_settings.S3_SECURE,
        region=global_settings.S3_REGION,
    )
    objects = minio_client.list_objects(
        global_settings.TEST_CACHE_BUCKET, prefix=prefix, recursive=True
    )
    for obj in objects:
        minio_client.remove_object(global_settings.TEST_CACHE_BUCKET, obj.object_name)


def create_valid_bucket_name(test_name: str) -> str:
    """
    Create a valid bucket name following S3/Minio naming rules
    """
    # Remove invalid characters and replace with allowed ones
    sanitized_name = re.sub(r"[^a-z0-9]", "", test_name.lower())

    # Create unique identifier
    unique_id = str(uuid.uuid4())[:8]
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")  # Removed hyphens

    # Combine parts ensuring the result is valid
    bucket_name = f"{TEST_BUCKET_PREFIX}{sanitized_name}{timestamp}{unique_id}"

    # Ensure length is within limits (3-63 characters)
    if len(bucket_name) > 63:
        bucket_name = bucket_name[:63]

    return bucket_name


@pytest.fixture
async def minio_test_bucket(request):
    """Setup unique test bucket for each test and cleanup after, so that this works in parallel
    testing"""
    # Create unique bucket name using test name and random UUID
    client = Minio(
        global_settings.S3_HOST,
        global_settings.S3_ACCESS_KEY,
        global_settings.S3_SECRET_KEY,
        secure=global_settings.S3_SECURE,
        region=global_settings.S3_REGION,
    )

    bucket_name = None
    try:
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                bucket_name = create_valid_bucket_name(request.node.name)
                client.make_bucket(bucket_name)
                break
            except Exception:
                if attempt == max_attempts - 1:
                    raise
                continue

        yield bucket_name

    finally:
        if bucket_name:
            try:
                # Cleanup
                objects = client.list_objects(bucket_name)
                for obj in objects:
                    try:
                        client.remove_object(bucket_name, obj.object_name)
                    except Exception as e:
                        await logger.awarning(
                            f"Failed to remove object {obj.object_name}: {str(e)}"
                        )

                try:
                    client.remove_bucket(bucket_name)
                except Exception as e:
                    await logger.awarning(
                        f"Failed to remove bucket {bucket_name}: {str(e)}"
                    )
            except Exception as e:
                await logger.aerror(
                    f"Cleanup failed for bucket {bucket_name}: {str(e)}"
                )


@pytest.fixture
def sample_pdf():
    """Create a sample PDF file for testing"""
    # Create a simple PDF using PIL
    img = Image.new("RGB", (100, 100), color="white")
    pdf_path = Path(SAMPLE_PDF)
    img.save(str(pdf_path), "PDF")
    yield pdf_path
    pdf_path.unlink(missing_ok=True)


@pytest.fixture
def corrupted_pdf():
    """Create a corrupted PDF file for testing"""
    pdf_path = Path(CORRUPTED_PDF)
    with open(pdf_path, "wb") as f:
        f.write(b"This is not a valid PDF file")
    yield pdf_path
    pdf_path.unlink(missing_ok=True)


# The following fixtures are used to cache the results of the services in minio
#
# Ensure alias is set
#
# mc alias set local http://127.0.0.1:9000 S3_ACCESS_KEY S3_SECRET_KEY
#
# Backup using
#
# mc cp --recursive local/test-cache-bucket /tmp/
#
# Restore using
# mc cp --recursive /tmp/test-cache-bucket local/
#
# Note inside buckets, the objects are stored with the following structure
# {service}/{test_namespace}/{key}.json
# so its possible to delete only the objects for a specific test


# @pytest.fixture()
# async def upload_test_pdf_240_betreibung_mt():
#     bucket_name = "test-temporary-file-store"
#     object_name = "mixed_searchable/240_betreibung_mt.pdf"
#
#     await make_bucket_available(bucket_name)
#
#     test_pdf = os.path.join(DATA_DEMO_PATH, "mixed_searchable/240_betreibung_mt.pdf")
#     local_file_hashes = calculate_file_hashes(test_pdf)
#
#     md5_hash = local_file_hashes["md5"]
#     sha256_hash = local_file_hashes["sha256"]
#
#     if object_exists_in_s3(bucket_name, object_name):
#         s3_object_hash = await get_object_hash(bucket_name, object_name)
#         if s3_object_hash == md5_hash or s3_object_hash == sha256_hash:
#             print(f"Object {object_name} already exists in S3 with matching hash.")
#         else:
#             print(
#                 f"Object {object_name} exists in S3 but hash doesn't match. Uploading new version."
#             )
#             await upload_file_to_s3(
#                 file=Path(test_pdf),
#                 bucket=bucket_name,
#                 object_name=object_name,
#             )
#     else:
#         print(f"Object {object_name} doesn't exist in S3. Uploading it.")
#         await upload_file_to_s3(
#             file=Path(test_pdf),
#             bucket=bucket_name,
#             object_name=object_name,
#         )
#
#     fast_url = get_presigned_download_url(bucket_name, object_name)
#     return fast_url


@pytest.fixture()
async def upload_test_file(request):
    """
    Pytest fixture to upload a test file to S3 and return its presigned download URL.

    :param request: Pytest request object to access fixture configuration.
    :return: Presigned download URL for the uploaded file.
    """
    # Get the file path from the fixture parameter or use a default
    file_path = (
        str(os.path.join(DATA_DEMO_PATH, request.param))
        if hasattr(request, "param")
        else None
    )

    if file_path is None:
        raise ValueError("File path must be provided as a parameter to the fixture.")

    # Configure bucket name (could also be passed as a parameter or read from config)
    bucket_name = "test-temporary-file-store"

    # Derive object name from file path

    object_name = os.path.relpath(file_path, start=os.path.dirname(file_path))

    await make_bucket_available(bucket_name)

    md5_hash, sha256_hash = get_file_hashes(Path(file_path))

    if object_exists_in_s3(bucket_name, object_name):
        s3_object_hash = await get_object_hash(bucket_name, object_name)
        if s3_object_hash == md5_hash or s3_object_hash == sha256_hash:
            print(f"Object {object_name} already exists in S3 with matching hash.")
        else:
            print(
                f"Object {object_name} exists in S3 but hash doesn't match. Uploading new version."
            )
            await upload_file_to_s3(
                file=Path(file_path), bucket=bucket_name, object_name=object_name
            )
    else:
        print(f"Object {object_name} doesn't exist in S3. Uploading it.")
        await upload_file_to_s3(
            file=Path(file_path), bucket=bucket_name, object_name=object_name
        )

    fast_url = get_presigned_download_url(bucket_name, object_name)
    return fast_url


@pytest.fixture(
    autouse=global_settings.TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED, scope="function"
)
async def cached_detect_page_objects(monkeypatch, request):

    await make_bucket_available("test-cache-bucket")

    async def wrapper(bucket: str, prefix_images: List[str], model_name="model_v15s"):

        force_overwrite = global_settings.TEST_CACHE_DETECT_PAGE_OBJECTS_FORCE_OVERWRITE

        # Map image paths to hashes
        prefix_images_hashes = {}

        # Map hashes to image paths
        hashes_prefix_images = {}

        for prefix_image in prefix_images:
            image_hash = await get_object_hash(bucket, prefix_image)
            prefix_images_hashes[prefix_image] = image_hash
            hashes_prefix_images[image_hash] = prefix_image

        key = hashlib.sha256(
            json.dumps(
                {
                    "bucket": bucket,
                    "hashes_prefix_images": sorted(hashes_prefix_images.keys()),
                    "model_name": model_name,
                }
            ).encode()
        ).hexdigest()

        test_namespace = calculate_test_namespace(request)

        object_name = f"{test_namespace}/gods/{model_name}/{key}.json"
        # Check if the result is already in the cache

        topic = "detect_page_objects"

        if not force_overwrite:
            try:
                cached_data = await get_test_cache_item(key, object_name, topic)

                old_prefix_images_hashes = cached_data.pop("prefix_images_hashes")

                # Now update urls with the new prefix_images
                for detection in cached_data["detections"]:

                    # Fetch hash based off old prefix image
                    current_image_hash = old_prefix_images_hashes[
                        detection["prefix_image"]
                    ]

                    # Find new image path based off hash
                    # new_image_path = 'input/pageimages/1e459897-709e-4bca-b306-cceeb88d14da/0.jpg'
                    new_image_path = hashes_prefix_images[current_image_hash]

                    if new_image_path is None:
                        # Hash not found in current images
                        # Cache is invalid
                        print("Cache invalidated due to missing image hash")
                        raise ValueError("Cache invalidated due to missing image hash")

                    # Check if new_image_path exists in minio
                    if not object_exists_in_s3(bucket, new_image_path):
                        # Cache is invalid
                        print("Cache invalidated due to missing image in MinIO")
                        raise ValueError(
                            "Cache invalidated due to missing image in MinIO"
                        )

                    # Update the prefix image
                    detection["prefix_image"] = new_image_path

                return PageObjectDetectionResponseV1.parse_obj(cached_data)
            except (S3Error, ValueError) as exc:
                if exc.code != "NoSuchKey":
                    raise exc

        await logger.ainfo("Generating cache object", key=key, topic=topic)
        result = await call_detect_page_objects_remote(
            bucket, prefix_images, model_name
        )

        result_dict = result.dict()
        result_dict["prefix_images_hashes"] = prefix_images_hashes
        result_json = json.dumps(result_dict)
        await put_test_cache_item(object_name, result_json)

        return result

    monkeypatch.setattr(
        "asyncizer.pageobjectclassifier.call_detect_page_objects_remote", wrapper
    )

    yield


@pytest.fixture(
    autouse=global_settings.TEST_CACHE_IMAGEPREDICT_ENABLED, scope="function"
)
async def cached_predict_page_objects_with_imagepredict(monkeypatch, request):
    # Caches layout llm service

    await make_bucket_available("test-cache-bucket")

    async def wrapper(
        page_uuid: UUID,
        image_url: HttpUrl,
        model_name: str,
        bbox: Optional[BoundingBox] = None,
    ):
        force_overwrite = global_settings.TEST_CACHE_IMAGEPREDICT_FORCE_OVERWRITE

        key = await get_urls_content_sha256(image_url)

        test_namespace = calculate_test_namespace(request)

        object_name = f"{test_namespace}/layoutllm/{model_name}/{key}.json"
        # Check if the result is already in the cache

        topic = "imagepredict"
        if not force_overwrite:
            # Only check cache if we're not forcing an overwrite
            try:
                cached_data = await get_test_cache_item(key, object_name, topic)
                return ImagePredictResponse.parse_obj(cached_data)
            except minio.error.S3Error as exc:
                if exc.code != "NoSuchKey":
                    raise exc

        await logger.ainfo("Generating cache object", key=key, topic=topic)
        result: ImagePredictResponse = await call_imagepredict_service_remote(
            page_uuid, image_url, model_name, bbox
        )

        await put_test_cache_item(object_name, result.json())

        return result

    monkeypatch.setattr(
        "imagepredict.processor.imagepredict_page_processor.call_imagepredict_service_remote",
        wrapper,
    )

    yield


@pytest.fixture(
    autouse=global_settings.TEST_CACHE_CLASSIFY_PAGE_OBJECTS_ENABLED, scope="function"
)
async def cached_classify_page_objects_in_images(monkeypatch, request):
    # Caches gocs service

    await make_bucket_available("test-cache-bucket")

    async def wrapper(bucket: str, tasks: List[PageObjectClassificationTask]):

        force_overwrite = (
            global_settings.TEST_CACHE_CLASSIFY_PAGE_OBJECTS_FORCE_OVERWRITE
        )

        # [PageObjectClassificationTask(uuid='0d13e02b-537f-40cf-b5e8-a9a7c89f32d5',
        # prefix_image='fotos ste/pageimages/3d070fdb-af8a-4d67-a7cd-3237ecab029c/0.jpg', bbox=BBox(x1=263, y1=2158, x2=538, y2=2254))]
        # Map image paths to hashes
        prefix_images_hashes = {}

        # Map hashes to image paths
        hashes_prefix_images = {}

        for task in tasks:
            prefix_image = task.prefix_image
            image_hash = await get_object_hash(bucket, prefix_image)
            prefix_images_hashes[prefix_image] = image_hash
            hashes_prefix_images[image_hash] = prefix_image

        key = hashlib.sha256(
            json.dumps(
                {
                    "bucket": bucket,
                    "hashes_prefix_images": sorted(hashes_prefix_images.keys()),
                }
            ).encode()
        ).hexdigest()

        test_namespace = calculate_test_namespace(request)

        object_name = f"{test_namespace}/gocs/{key}.json"
        # Check if the result is already in the cache

        topic = "classify_page_objects"

        # Only check cache if we're not forcing an overwrite of the cache
        if not force_overwrite:
            try:
                cached_data = await get_test_cache_item(key, object_name, topic)

                old_prefix_images_hashes = cached_data.pop("prefix_images_hashes")

                # Now update urls with the new prefix_images
                for classification in cached_data["classifications"]:

                    # Fetch hash based off old prefix image
                    current_image_hash = old_prefix_images_hashes[
                        classification["prefix_image"]
                    ]

                    # Find new image path based off hash
                    # 'new_image_path': 'input/pageimages/ba6d48db-ae35-480e-abe2-c00fc35a0553/0.jpg'
                    new_image_path = hashes_prefix_images[current_image_hash]

                    if new_image_path is None:
                        # Hash not found in current images
                        # Cache is invalid
                        print("Cache invalidated due to missing image hash")
                        raise ValueError("Cache invalidated due to missing image hash")

                    # Check if new_image_path exists in minio
                    if not object_exists_in_s3(bucket, new_image_path):
                        # Cache is invalid
                        print("Cache invalidated due to missing image in MinIO")
                        raise ValueError(
                            "Cache invalidated due to missing image in MinIO"
                        )

                    # Update the prefix image
                    classification["prefix_image"] = new_image_path

                return PageObjectClassificationResponseV1.parse_obj(cached_data)
            except (S3Error, ValueError) as exc:
                if exc.code != "NoSuchKey":
                    raise exc

        await logger.ainfo("Generating cache object", key=key, topic=topic)
        result = await classify_page_objects_in_images(bucket, tasks)

        result_dict = result.dict()
        result_dict["prefix_images_hashes"] = prefix_images_hashes
        result_json = json.dumps(result_dict)
        await put_test_cache_item(object_name, result_json)

        return result

    monkeypatch.setattr(
        "asyncizer.pageobjectclassifier.classify_page_objects_in_images", wrapper
    )

    yield


# @pytest.fixture(autouse=True, scope="function")
# async def get_rpc_client_fresh_for_every_function(monkeypatch):
#     """
#         Do not use the singleton of an rpc client instance.
#         Instead create a new client for every function to avoid problems
#         with parallel tests
#     """
#
#     async def rpc_wrapper() -> RpcClient:
#
#         raise Exception("mocking works")
#
#         rpc_client = await RpcClient(
#             asyncio.get_running_loop(), global_settings.RABBIT_URL
#         ).connect()
#         return rpc_client
#
#     monkeypatch.setattr("asyncizer.rpc_pika.get_rpc_client", rpc_wrapper)
#
#     yield
