from abbyyplumber.api import Page
from abbyyplumber.converter.ValueConverter import ParagraphConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementStaticText,
    SearchElementLabeledField,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    SECTION_CHILDREN,
    TAX_MUNICIPALITY,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_YEAR, FIELD_PERSON_ID
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    create_search_elements_personal_data_lu_sh,
)
from mortgageparser.util.search_element_util import create_search_element_recent_year


class TaxDeclarationSHBarcodePageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_BARCODE
        self.canton_short = "SH"

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = [
            SearchElementLabeledField(
                FIELD_PERSON_ID.name,
                page.fullpage,
                label="PID-Nr.:",
                field_width=PercentageRange(0, 0.4),
            ),
            SearchElementLabeledField(
                "person_names", page.fullpage, label="Name, Vorname"
            ),
        ]
        return elements


class TaxDeclarationSHPersonalDataPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA
        self.canton_short = "SH"

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = (
            [
                SearchElementConstrainedArea(
                    FIELD_YEAR.sr_inside,
                    page.fullpage,
                    text_left="Steuererklärung",
                    text_bottom="Eingang",
                ),
                create_search_element_recent_year(page),
                SearchElementConstrainedArea(
                    TAX_MUNICIPALITY.name,
                    page.main,
                    text_left="Gemeindesteuern",
                    # 'K' from Kanton ... cut of first char on the left to skip Numbering 1,2
                    text_top="steuern,direkte Bundessteuer",
                    text_right="Eingang",
                    converter=ParagraphConverter(
                        max_num_lines=1, max_num_spaces_per_line=5
                    ),
                ),
            ]
            + create_search_elements_personal_data_lu_sh(page)
            + [
                SearchElementConstrainedArea(
                    SECTION_CHILDREN.name,
                    page.main,
                    text_left="K",
                    # 'K' from Kanton ... cut of first char on the left to skip Numbering 1,2
                    text_top="(Ausbildungsende)",
                    text_right="deren Unterhalt Sie bestreiten",
                    text_bottom="Konkubinatspartner (Name, Vorname, Geburtsdatum)",
                    converter=ParagraphConverter(),
                ),
                SearchElementStaticText(
                    "title_dependents",
                    page.main,
                    label="Erwerbsunfähige oder beschränkt erwerbsfähige Personen",
                    max_l_dist=6,
                    extract=False,
                ),
            ]
        )

        return elements
