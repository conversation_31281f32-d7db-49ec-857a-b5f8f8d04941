from abbyyplumber.converter.ValueConverter import DoNothingConverter, CleanIBANConverter
from abbyyplumber.plumberstudio.SearchElement import (
    create_labeled_field_vertical,
    create_labeled_field,
    SearchElementLabeledField,
    SearchElementConstrainedArea,
    SearchElementArea,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    TemplateGenericLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.bank_account.bank_account_util import (
    TemplateBankAccountPageParser,
    BankAccountSearchElements,
)


def get_parsers_bank_account_de():
    return [
        TemplateBankAccountPageParser(
            desc="CS Online 2022",
            required_text_conditions=[
                FromStartTextCond("Konten und Karten", num_lines=5),
                FromStartTextCond("Sparen und Investieren", num_lines=5),
                FromStartTextCond("Finanzieren", num_lines=5),
                FromStartTextCond("Produkte und Services", num_lines=5),
            ],
        ),
        TemplateBankAccountPageParser(
            desc="BEKB Einzahlung DE 2021",
            ranked_titles_all=[RankedTitle("Einzahlung", 3)],
            required_text_conditions=[
                FromStartTextCond("www.bekb.ch", num_lines=7),
                FromStartTextCond("BIC/SWIFT", num_lines=7),
                FromStartTextCond("Für Sie persönlich zuständig", num_lines=10),
            ],
            required_tokens=["Betrag erhalten"],
            company="BEKB",
            se=BankAccountSearchElements(
                iban=create_labeled_field_vertical(
                    "Einzahlung",
                    offset_top=-10,
                    offset_bottom=-2,
                    offset_left=-1000,
                    offset_right=1000,
                ),
                document_date=create_labeled_field("Einzahlung"),
                fullname=create_labeled_field_vertical(
                    "BIC/SWIFT", offset_left=-7, offset_right=20, offset_bottom=10
                ),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    label="Betrag",
                    field_pos_page_horizontal=PercentageRange(0.7, 1),
                ),
            ),
        ),
        TemplateBankAccountPageParser(
            desc="BEKB Kontoauszug 2021",
            ranked_titles_all=[RankedTitle("Kontoauszug per", 3)],
            required_text_conditions=[
                FromStartTextCond("Berner Kantonalbank AG", num_lines=7),
                FromStartTextCond("www.bekb.ch", num_lines=7),
                FromStartTextCond("MWST-Nr.", num_lines=7),
                FromStartTextCond("Privatkonto", num_lines=10),
            ],
            company="BEKB",
            regions=[
                SearchElementConstrainedArea(
                    "region_header",
                    None,
                    text_left="Privatkonto",
                    text_bottom="Kontoauszug per",
                ).include_all()
            ],
            se=BankAccountSearchElements(
                iban=create_labeled_field(
                    "IBAN",
                    "region_header",
                    field_vertical_line_scale=2,
                    field_pos_page_horizontal=PercentageRange(0, 0.5),
                    converter=CleanIBANConverter(),
                ),
                document_date=create_labeled_field("Kontoauszug per", "region_header"),
                fullname=create_labeled_field(
                    "Lautend auf:",
                    "region_header",
                    field_vertical_line_scale=2,
                    # converter=CleanNameConverter(skip_second_name=False)
                    converter=DoNothingConverter(),
                ),
            ),
        ),
        TemplateBankAccountPageParser(
            desc="Raiffeisen Steuerverzeichnis DE 2018",
            ranked_titles_all=[RankedTitle("RAIFFEISEN", 1, min_length_title=8)],
            required_text_conditions=[
                FromStartTextCond("RAIFFEISEN", num_lines=5),
                # FromStartTextCond("Kundenname", num_lines=10),        # Can also be Kunde
                FromStartTextCond("Kunden-Nr.", num_lines=10),
                FromStartTextCond("Steuerkanton", num_lines=10),
                FromStartTextCond("Raiffeisenbank", num_lines=10),
            ],
            required_tokens_any=[
                ["Steuerverzeichnis vom"],
                ["Steuerwert", "Betrag", "Total", "Ex. für die Steuerverwaltung"],
            ],
            company="Raiffeisen",
        ),
        TemplateBankAccountPageParser(
            desc="Swissquote Transaktionsbeleg Screenshot DE 2021",
            ranked_titles_all=[RankedTitle("Swissquote", 3)],
            required_text_conditions=[
                FromStartTextCond("TRANSAKTIONSBELEG", num_lines=5),
                FromStartTextCond("Kunde:", num_lines=7),
                FromStartTextCond("IBAN:", num_lines=7),
            ],
            company="Swissquote",
        ),
        TemplateBankAccountPageParser(
            desc="Swissquote Konto DE 2021",
            required_text_conditions=[
                FromStartTextCond("Swissquote Konto", num_lines=2)
            ],
            required_tokens=[".swissquote.ch/"],
            company="Swissquote",
        ),
        TemplateBankAccountPageParser(
            desc="Belastungsanzeige (e.g. ZKB)",
            ranked_titles_any=[
                RankedTitle("Belastungsanzeige", 3, 6),
                RankedTitle("Gutschriftsanzeige", 3, 6),
            ],
            required_text_conditions=[
                FromStartTextCond("Telefon", num_lines=15),
                FromStartTextCond("Adresse", num_lines=15),
                FromStartTextCond("IBAN", num_lines=15),
            ],
            required_tokens=["Zahlungsgrund"],
        ),
        TemplateGenericLetterPageParser(
            desc="ZKB Abrechnung Hypothek 2021",
            doc_cat=DocumentCat.BANK_DOCUMENT,
            page_cat=PageCat.GENERIC_PAGE,
            company="ZKB",
            required_tokens=["Zürcher Kantonalbank", "OY5172"],
            hamming_dist=0,
        ),
    ]


def get_parsers_bank_account_fr():
    return [
        TemplateBankAccountPageParser(
            desc="SwissLife Select Pack bonheur Sparplan",
            supported_languages=["fr"],
            required_text_conditions=[
                FromStartTextCond("SwissLife", num_lines=5),
                FromStartTextCond("Select", num_lines=5),
                FromStartTextCond("Pack bonheur", num_lines=5),
            ],
            required_tokens=["Nom du dépôt: Epargne"],
            company="SwissLife Select",
            se=BankAccountSearchElements(
                iban=SearchElementArea(None, None, y_range=PercentageRange(0, 0.3))
            ),
        )
    ]


def get_parsers_bank_account():
    return get_parsers_bank_account_de() + get_parsers_bank_account_fr()
