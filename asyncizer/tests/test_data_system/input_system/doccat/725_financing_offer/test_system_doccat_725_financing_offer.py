from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "725_financing_offer"
path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}")
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
@pytest.mark.skip(
    "Migrosbank rule based parsers are disabled because to generic. Check get_parsers_migrosbank, needs work before reactivating"
)
async def test_725_migrosbank():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder,
        source_file_filter=["MB Finanzierungsvorschlag.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.FINANCING_OFFER: 1},
            expected_page_objects={
                # 0: {
                #     "document_date": "13.10.2020",
                #     "company": "Migros Bank",
                # }
            },
        ),
    ).run()
