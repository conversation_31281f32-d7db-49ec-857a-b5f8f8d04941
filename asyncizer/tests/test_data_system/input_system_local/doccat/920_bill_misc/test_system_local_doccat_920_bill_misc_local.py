from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT

source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/920_bill_misc"
)
dest_folder_prefix = "input_system_local_doccat_920_bill_misc_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_verguetungsauftrag():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["920_verguetungsauftrag.pdf"],
        dossier_expectations=DossierExpectations(),
    ).run()
