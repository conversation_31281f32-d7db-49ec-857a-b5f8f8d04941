import asyncio
import hashlib
import json
import shutil
import sys
import traceback
import zipfile
from datetime import datetime
from io import Bytes<PERSON>
from itertools import islice
from pathlib import Path
from tempfile import TemporaryDirectory
from time import time
from typing import List, Optional, AsyncIterator, Coroutine, Union, Dict, Any, Tuple
from urllib.parse import quote
from uuid import uuid4, UUID

import minio
import sentry_sdk
import structlog
from minio import S3Error
from minio.commonconfig import CopySource
from pydantic import BaseModel
from pypdf import PdfWriter, PdfReader
from structlog.contextvars import bound_contextvars

import global_settings
from abbyyplumber.util.pil_image_util import pil_gif_info_from_bytes
from asyncizer.aio import aio
from asyncizer.customize_classification import customize_classification_by_page_objects
from asyncizer.email_body import is_email_body_filename
from asyncizer.exception_handling import (
    ExceptionDetails,
    HypoDossierException,
    map_file_exception,
)
from asyncizer.exceptions import (
    FilePro<PERSON>ingEx<PERSON>,
    PdfSplitException,
    TooManyPagesError,
    UnsupportedFileType,
    OcrImageFileCorruptedException,
    OcrAbbyyException,
)
from asyncizer.hypodossier_excel_spec_data_fields import add_document_extraction_excel
from asyncizer.known_ignored_files import is_known_ignored_file_by_bytes
from asyncizer.offliner_packer import add_document_browser_viewer
from asyncizer.pageobjectclassifier import (
    detect_and_classify_page_objects_from_s3,
    create_graphical_page_objects,
)
from asyncizer.pdf2jpg import call_pdf2jpg_remote
from asyncizer.pdf_fix import process_pdftocairo_pdf_fix_by_image_export
from asyncizer.pdf_split import split_pdfpages
from asyncizer.pdf_utils import validate_pdf_in_minio
from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.randomword import randomword
from asyncizer.rotation_decider import make_rotation_decision
from asyncizer.rpc_pika import get_rpc_client, RpcClient
from asyncizer.s3 import (
    download_prefix,
    upload_folder,
    object_exists,
    remove_prefix,
    upload_file,
    META_DATA_SHA256_SUM,
    create_presigned_url,
    download_single_file,
    copy_object,
    get_sha256sum,
    copy_object_sync,
    copy_object_with_retry,
)
from asyncizer.s3_util import get_object_hash, get_file_hashes
from asyncizer.semantic_document_processing_config import (
    create_semantic_documents_processing_config,
)
from asyncizer.small_files_filter import DEFAULT_SMALL_FILES_FILTER, SmallFilesFilter
from asyncizer.utils import async_timer, TimingStats, timed_operation
from asyncizer.supported_file_extensions import ALL_SUPPORTED_FILE_SUFFIX
from classifier.spacy_classification import FILE_TEXT_SPACY_CLASSIFIER_NAME
from classifier.spacy_worker import (
    SPACY_WORKER_ROUTING_KEY,
    TextClassificationRequest,
    ClassificationResponse,
)
from finhurdle.pageparser.finhurdle_page_processor import (
    update_finhurdle_confidence,
    filter_best_finhurdle_per_page,
    filter_finhurdles_by_document_cat,
)
from hypodossier import shared
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import ExtractionType
from hypodossier.document_cat_util import (
    documentcategory_from_doccat,
    pagecategory_from_pagecat,
)
from hypodossier.mrz import MRZ_DETECTOR_KEY, MRZDetectionRequest, MRZDetection
from hypodossier.semantic import create_semantic_documents
from hypodossier.shared import (
    PageAnalysisResult,
    ProcessedPage,
    PageExtractionResult,
    PageConfidenceInfo,
    ProcessedFile,
    ProcessedFiles,
    SemanticDossier,
    PageAnalysisRequest,
    Classification,
)
from hypodossier.unpack import unpack_path, FileExtraction
from hypodossier.util.basis_string_util import count_alpha
from hypodossier.util.file_util import ensure_path_has_forward_slashes
from hypodossier.util.imagemagick_util import im_fix_image
from hypodossier.util.whitepage.white_image_detection import is_white_image_from_s3
from imagepredict.client.model_mapping import map_page_to_model_info
from imagepredict.processor.imagepredict_page_processor import (
    predict_page_objects_with_imagepredict,
)
from mortgageparser.util.file_extension import optionally_fix_fileextension


logger = structlog.getLogger(__name__)

client = minio.Minio(
    global_settings.S3_HOST,
    global_settings.S3_ACCESS_KEY,
    global_settings.S3_SECRET_KEY,
    secure=global_settings.S3_SECURE,
    region=global_settings.S3_REGION,
)
client.fget_object = aio(client.fget_object)
client.fput_object = aio(client.fput_object)
client.list_objects = aio(client.list_objects)

async_unpack_path = aio(unpack_path)
add_document_browser_viewer = aio(add_document_browser_viewer)
add_document_extraction_excel = aio(add_document_extraction_excel)


class SearchablePage(BaseModel):
    bucket: str
    dossier: str
    extracted_file: str
    page_number: int
    pdf_path: str
    text_file: str
    rotation_decision: bool


class SearchabelPages(BaseModel):
    pages: List[SearchablePage]


# Up to this filesize of the generated searchable PDF we accept the size even if it is much larger than the input PDF.
SEARCHABLE_PDF_FILESIZE_BYTES_ALWAYS_OK = 4 * 1024 * 1024

# If filesize of generated searchable PDF is larger than SEARCHABLE_PDF_FILESIZE_BYTES_ALWAYS_OK and it is larger than
# the original by more than this factor, we consider the searchable file 'useless' and keep the original
SEARCHABLE_PDF_MAX_FILESIZE_FACTOR_OK = 3


def small_files_filter_applies(
    small_files_filter: SmallFilesFilter,
    filename: str,
    filesize: int,
    do_apply_filter: bool,
) -> Tuple[Optional[str], int, int]:
    """

    :param small_files_filter:
    :param filename:
    :param filesize:
    :param do_apply_filter:
    :return: Tuple[extension, filesize, max_size_bytes]
    """
    if small_files_filter:
        for ext, max_size_bytes in small_files_filter.small_files_map.items():
            if filename.lower().endswith(ext):
                # Now check if really small
                if filesize <= max_size_bytes:
                    # This file is too small and will be silently ignored
                    return ext, filesize, max_size_bytes
    return None, -1, -1  # ext, filesize, max_size_bytes


def is_ignored_known_by_filename(file_path: str) -> bool:
    p = Path(file_path)
    filename = p.name
    if filename.lower() in ["thumbs.db"]:
        return True
    return False


def is_ignored_file_by_rules(extracted_file: str, bytes):
    extension = Path(extracted_file).suffix
    is_gif = extension.lower() == ".gif"
    if is_gif:
        animated, _ = pil_gif_info_from_bytes(bytes)
        if animated:
            logger.info("Ignore animated gif", extracted_file=extracted_file)
            return True

    known_file = False
    if not known_file:
        known_file = is_ignored_known_by_filename(extracted_file)
    if not known_file:
        known_file = is_known_ignored_file_by_bytes(bytes)
    if known_file:
        # We know this file and want to skip it silently (icons, logos, stuff)
        logger.info("Ignore file based on rules", extracted_file=extracted_file)
        return True

    return False


async def unpack_original_file(
    bucket, dossier, file, small_files_filter: SmallFilesFilter = None
) -> FileExtraction:
    async with async_timer(
        "unpack_original_file", bucket=bucket, dossier=dossier, file=file
    ):
        with TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            await client.fget_object(
                bucket, f"{dossier}/original_files/{file}", f"{temp_dir}/{file}"
            )

            p_file = temp_path / file

            ret = optionally_fix_fileextension(p_file, ALL_SUPPORTED_FILE_SUFFIX)
            if ret.changed:
                p_used = Path(ret.corrected_filename)
                logger.warning(
                    "Wrong extension found",
                    bucket=bucket,
                    dossier=dossier,
                    p_file=p_file,
                    ret=ret,
                )
            else:
                p_used = p_file

            file_extraction: FileExtraction = await async_unpack_path(p_used)

            upload_tasks = []
            ignored_files: List[str] = []

            # Keep a set of file hashes to avoid uploading duplicates
            file_hash = {}

            for extracted_file in file_extraction.extracted_files:
                p = temp_path / extracted_file
                with p.open("rb") as f:
                    bytes = f.read()  # read entire file as bytes
                    sha256sum = hashlib.sha256(bytes).hexdigest()

                    # Detect duplicates and skip them
                    if sha256sum in file_hash:
                        logger.info(
                            "Duplicate extracted file found in original file, so ignore it",
                            bucket=bucket,
                            dossier=dossier,
                            original_file=file,
                            extracted_file=extracted_file,
                            sha256sum=sha256sum,
                        )
                        ignored_files.append(extracted_file)
                        continue
                    else:
                        file_hash[sha256sum] = extracted_file

                    ignored_file = is_ignored_file_by_rules(extracted_file, bytes)
                    if ignored_file:
                        ignored_files.append(extracted_file)
                    else:
                        # Try to find a small file by size and add it to the exceptions
                        ext, filesize, max_size_bytes = small_files_filter_applies(
                            small_files_filter,
                            extracted_file,
                            len(bytes),
                            global_settings.APPLY_SMALL_FILES_FILTER,
                        )
                        if ext:
                            logger.warning(
                                "Ignore file, small_files_filter.",
                                extracted_file=extracted_file,
                                size=len(bytes),
                            )
                            m = round(max_size_bytes / 1024, 1)
                            size = round(filesize / 1024, 1)

                            if small_files_filter.skip_small_files:
                                # remove files
                                ignored_files.append(extracted_file)
                            else:
                                ed = ExceptionDetails(
                                    type=HypoDossierException.TOO_SMALL_FILE,
                                    en=f"Small file of type '{ext}' with file size is {size}KB has been ignored (minimum size {m}KB).",
                                    de=f"Kleine Datei vom Typ '{ext}' mit Dateigrösse {size}KB wurde ignoriert (Minimalgrösse {m}KB).",
                                    fr=f"Un petit fichier de type '{ext}' dont la taille est de {size} kilo-octets a été ignoré (taille minimale {m} kilo-octets)",
                                    it=f"Il file piccolo di tipo '{ext}' con dimensioni pari a {size} kilobyte è stato ignorato (dimensione minima {m} kilobyte)",
                                )
                                file_extraction.exceptions[extracted_file] = ed

                    if extracted_file not in ignored_files:
                        object_name = f"{dossier}/extracted_files/{extracted_file}"
                        upload_tasks.append(
                            client.fput_object(
                                bucket_name=bucket,
                                object_name=object_name,
                                file_path=str(temp_path / extracted_file),
                                metadata={META_DATA_SHA256_SUM: sha256sum},
                            )
                        )
            await asyncio.gather(*upload_tasks)

            # remove ignored files
            for i in ignored_files:
                file_extraction.extracted_files.remove(i)

            return file_extraction


async def get_original_files(bucket, dossier):
    prefix = f"{dossier}/original_files/"
    objects = await client.list_objects(bucket, prefix, recursive=True)
    return [str(Path(o.object_name).relative_to(prefix)) for o in objects]


FRE_RPC_CLIENT = None


async def get_custom_fre_rpc_client():
    """singleton of an rpc client instance"""
    global FRE_RPC_CLIENT
    if FRE_RPC_CLIENT is None:
        FRE_RPC_CLIENT = await RpcClient(
            asyncio.get_running_loop(), global_settings.FRE_RABBIT_URL
        ).connect()
    return FRE_RPC_CLIENT


async def perform_ocr_call(
    use_timeout: bool,
    rpc_client: RpcClient,
    message: str,
    applied_timeout: int,
    max_retries: int,
):
    """

    :param use_timeout:
    :param rpc_client:
    :param message:
    :param applied_timeout:
    :param max_retries: Number of retries in case the timeout is triggered
    :return:
    """
    ret = None
    try:
        if use_timeout:
            t = asyncio.create_task(
                rpc_client.call(
                    "frep.trial.DossierFileRequest",
                    message,
                    timeout=applied_timeout,
                    max_retries=max_retries,
                )
            )
            ret = await asyncio.wait_for(t, timeout=applied_timeout)
        else:
            ret = await rpc_client.call("frep.trial.DossierFileRequest", message)

    except TimeoutError as te:
        return json.dumps({"error": str(te)})
    except Exception as e:
        return json.dumps({"error": str(e)})
    return ret


async def validate_license() -> None:
    if not global_settings.FRE_LICENSE:
        raise RuntimeError("Called OCR without any license")
    if len(global_settings.FRE_LICENSE) != 29:
        raise RuntimeError(
            f"Invalid format for FRE_LICENSE={global_settings.FRE_LICENSE}"
        )


def create_ocr_message(
    source_bucket: str,
    src_object: str,
    dest_bucket: str,
    dest_object: str,
    rotation_decision: bool = True,
):
    return json.dumps(
        {
            "projectId": "giAVtibSxYfUwzAUuXZP",
            "licenseSerialNumber": global_settings.FRE_LICENSE,
            "sourceBucket": source_bucket,
            "sourceObjectName": src_object,
            "destBucket": dest_bucket,
            "destObjectName": dest_object,
            "rotationDecision": rotation_decision,
        }
    )


def classify_error(error) -> Optional[Dict[str, bool]]:

    is_abbyy_error = "com.abbyy.FREngine.EngineException" in error
    is_network_error = any(err in error for err in ["java.net", "javax.net", "java.io"])
    is_s3_problem = any(
        err in error
        for err in ["Internal Server Error", "Response code: 50", "Response Code: 50"]
    )
    is_abbyy_license_error = (
        "Error communicating with ABBYY Product Licensing Service" in error
    )

    return {
        "is_abbyy_error": is_abbyy_error,
        "is_network_error": is_network_error,
        "is_s3_problem": is_s3_problem,
        "is_abbyy_license_error": is_abbyy_license_error,
        "is_unknown_problem": not any(
            [is_network_error, is_abbyy_error, is_s3_problem]
        ),
    }


async def single_ocr_attempt(
    rpc_client, message: str, timeout: int, use_timeout: bool = True
) -> Dict[str, Any]:
    response = await perform_ocr_call(
        use_timeout=use_timeout,
        rpc_client=rpc_client,
        message=message,
        applied_timeout=timeout,
        max_retries=0,  # single attempt, so no retries
    )
    return json.loads(response)


async def ocr_with_retries(
    rpc_client: RpcClient,
    message: str,
    source_bucket: str,
    src_object: str,
    use_timeout: bool,
    timeout: int,
    max_tries: int,
):
    """

    :param rpc_client:
    :param message:
    :param source_bucket:
    :param src_object:
    :param use_timeout:
    :param timeout:
    :param max_tries: Total number of tries (including the first try).
    :return:
    """
    applied_timeout = timeout

    for i in range(max_tries):
        pdf_hash = await get_object_hash(bucket=source_bucket, object_name=src_object)
        await logger.ainfo(
            f"try ocr request, try {i + 1} of {max_tries}",
            retry=i,
            request=message,
            source_bucket=source_bucket,
            src_object=src_object,
            pdf_hash=pdf_hash,
        )
        try:
            result = await single_ocr_attempt(
                rpc_client=rpc_client,
                message=message,
                use_timeout=use_timeout,
                timeout=applied_timeout,
            )
            error = result.get("error")

            if not error:
                await logger.ainfo(
                    "OCR successful", source_bucket=source_bucket, src_object=src_object
                )
                return result

            error_types = classify_error(error)

            if error_types.get("is_abbyy_license_error"):
                # OCR does not work, keep all jobs pending and massively increase timeout
                applied_timeout = 3600 * 10  # 10 hours of timeout per try
                logger.error(
                    "Error during ocr: ABBYY License server not available. Try again with long timeout",
                    error=error,
                    applied_timeout=applied_timeout,
                    default_timeout=timeout,
                )
                await asyncio.sleep(60)
                continue

            if error_types.get("is_abbyy_error"):
                # Exit early if we have a known ABBYY error
                raise OcrAbbyyException(error=error)

            if error_types.get("is_known_problem") or error_types.get(
                "is_unknown_problem"
            ):
                if error_types.get("is_s3_problem"):
                    logger.warning(
                        "Error during ocr: Server Error, retry to do ocr on full document",
                        error=error,
                    )

                # Reset the timeout to the default value for these errors as it
                # might have been changed in case of a license server error
                if applied_timeout != timeout:
                    applied_timeout = timeout
                    await logger.ainfo(
                        "Reset timeout to default value",
                        applied_timeout=applied_timeout,
                    )

            delay = 5 * 1.5**i
            await asyncio.sleep(delay)

        except OcrAbbyyException as e:
            # This will not be fixed by a retry. ABBYY can not handle the file
            # Raise OcrAbbyyException to trigger fixing with pdftocairo
            raise e

        except Exception as e:
            if i >= max_tries - 1:
                raise e
            continue

    return None


async def ocr(
    *,
    source_bucket: str,
    src_object: str,
    dest_bucket: str,
    dest_object: str,
    timeout: Optional[int] = None,
    use_timeout: bool = True,
    rotation_decision: bool = True,
    max_num_tries: Optional[int] = None,
):
    await validate_license()

    message = create_ocr_message(
        source_bucket=source_bucket,
        src_object=src_object,
        dest_bucket=dest_bucket,
        dest_object=dest_object,
        rotation_decision=rotation_decision,
    )

    if global_settings.FRE_RABBIT_URL:
        rpc_client = await get_custom_fre_rpc_client()
    else:
        rpc_client = await get_rpc_client()

    if max_num_tries is not None and max_num_tries >= 0:
        max_tries = max_num_tries
    else:
        # Must be imported locally here, otherwise the monkeypatch for testing does not work
        max_tries = global_settings.MAX_TRIES_FREP_FOR_PDF

    if timeout is not None:
        timeout_used = timeout
    else:
        # Must be imported locally here, otherwise the monkeypatch for testing does not work
        timeout_used = global_settings.OCR_TIMEOUT_SECONDS

    try:
        # First attempt series
        result = await ocr_with_retries(
            rpc_client=rpc_client,
            message=message,
            source_bucket=source_bucket,
            src_object=src_object,
            use_timeout=use_timeout,
            timeout=timeout_used,
            max_tries=max_tries,
        )
        if result:
            return json.dumps(result)
    except OcrAbbyyException as ocr_error:
        error_message = str(ocr_error)
        is_pdf = src_object.endswith(".pdf")
        is_unknown_pdf_error = (
            is_pdf
            and "com.abbyy.FREngine.EngineException: Unknown error while opening"
            in error_message
        )
        if (
            is_unknown_pdf_error
            or "Invalid PDF file" in error_message
            or "JPEG codec" in error_message
            or "Internal program" in error_message
            or "Horizontal and vertical resolution of the image must be equal."
            in error_message
        ):
            logger.warning(
                "OCR failed due to known ABBYY issue. Attempting PDF Cairo repair",
                source_bucket=source_bucket,
                src_object=src_object,
                error=error_message,
            )
        else:
            # Exit immediately for other ABBYY errors. We are conservative here
            logger.warning(
                f"OCR failed due to unknown ABBYY issue. Do NOT attempt PDFCairo repair. If this type of error can be fixed with PDFCairo, add it here in the code. Raise exception to give up here. ocr_error={ocr_error}",
                source_bucket=source_bucket,
                src_object=src_object,
                error=error_message,
            )

            raise

    # If all attempts failed, try repair and retry
    logger.warning(
        "All regular OCR attempts failed. Attempting repair with pdftocairo...",
        source_bucket=source_bucket,
        src_object=src_object,
    )

    if global_settings.TRY_PDF_REPAIR_CAIRO:
        # Attempt to repair the PDF and retry OCR

        try:
            # PDF cairo can only handle pdfs. This raises InvalidPDFError
            # if the file is not a PDF (which should not happen because
            # it should have been caught before).
            await validate_pdf_in_minio(
                bucket=source_bucket,
                object_name=src_object,
                check_content=global_settings.VALIDATE_PDF_CONTENT_VIA_PYPDF,
            )

            await repair_and_reupload_pdf(
                source_bucket=source_bucket,
                source_object_name=src_object,
                timeout=timeout,
            )
            # Recreate the message incase the repair changed the file location
            message = create_ocr_message(
                source_bucket=source_bucket,
                src_object=src_object,
                dest_bucket=dest_bucket,
                dest_object=dest_object,
                rotation_decision=rotation_decision,
            )
            await logger.ainfo(
                "Attempted repair and reupload, retrying OCR",
                message=message,
                source_bucket=source_bucket,
                src_object=src_object,
            )
            # Second attempt series after repair
            result = await ocr_with_retries(
                rpc_client=rpc_client,
                message=message,
                source_bucket=source_bucket,
                src_object=src_object,
                use_timeout=use_timeout,
                timeout=timeout_used,
                max_tries=max_tries,
            )
            if result:
                return json.dumps(result)
        except Exception as repair_error:
            logger.error("Repair attempt failed", error=str(repair_error))
            raise RuntimeError(
                f"Error during OCR: Maximum retries ({max_tries}) exhausted and repair attempt failed"
            )

    raise RuntimeError("Error during OCR: All attempts failed")


async def repair_and_reupload_pdf(
    source_bucket: str, source_object_name: str, timeout: int
):
    """
    Repairs a PDF file and uploads it back to S3.
    """
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        local_file = temp_path / Path(source_object_name).name

        try:
            await download_single_file(
                bucket=source_bucket,
                prefix=source_object_name,
                dest_path=local_file,
                log_errors=False,
            )

            original_size = local_file.stat().st_size
            original_hash = await get_object_hash(
                bucket=source_bucket, object_name=source_object_name
            )

            repaired_file = process_pdftocairo_pdf_fix_by_image_export(
                local_file=local_file, output_path_dir=temp_path
            )

            repaired_size = repaired_file.stat().st_size

            repaired_hash_md5, repaired_hash_sha256 = get_file_hashes(repaired_file)

            await logger.ainfo(
                "Successfully repaired file",
                repaired_file=repaired_file,
                source_bucket=source_bucket,
                source_object_name=source_object_name,
                original_size_bytes=original_size,
                original_hash=original_hash,
                repaired_size_bytes=repaired_size,
                repaired_hash_md5=repaired_hash_md5,
                repaired_hash_sha256=repaired_hash_sha256,
                size_change_percentage=((repaired_size - original_size) / original_size)
                * 100,
            )

            await upload_file(
                bucket=source_bucket,
                object_name=source_object_name,
                file_path=repaired_file,
            )

            uploaded_file_hash = await get_object_hash(
                bucket=source_bucket, object_name=source_object_name
            )

            await logger.ainfo(
                "Successfully uploaded repaired file",
                source_bucket=source_bucket,
                source_object_name=source_object_name,
                uploaded_file_hash=uploaded_file_hash,
            )

        except Exception as e:
            await logger.aerror(
                "Failed to repair file",
                source_bucket=source_bucket,
                source_object_name=source_object_name,
                error=str(e),
            )
            raise


async def detect_mrz(bucket, object):
    rpc_client = await get_rpc_client()
    result = await rpc_client.call(
        MRZ_DETECTOR_KEY, MRZDetectionRequest(bucket=bucket, object=object).json()
    )
    return MRZDetection.parse_raw(result)


async def classify_with_spacy_rpc(text: str, top_n=5) -> ClassificationResponse:
    rpc_client = await get_rpc_client()
    result = await rpc_client.call(
        SPACY_WORKER_ROUTING_KEY,
        TextClassificationRequest(text=text, top_n=top_n).json(),
    )
    return ClassificationResponse.parse_raw(result)


async def analyse_page(
    bucket,
    object,
    page_text: str,
    page_classifications: List[Classification],
    client_lang: str,
):
    """

    :param bucket: bucket in which the pdf which should be analysed is stored
    :param object: path inside bucket to the PDF document that should be analyzed
    :param page_text: full text of the page
    :param page_classifications
    :param client_lang:
    :return:
    """
    rpc_client = await get_rpc_client()
    result = await rpc_client.call(
        global_settings.PAGE_ANALYSIS_REQUEST_NAME,
        PageAnalysisRequest(
            bucket=bucket,
            object_name=object,
            client_lang=client_lang,
            page_text=page_text,
            page_classifications=page_classifications,
        ).json(),
    )

    page_analysis_result = PageAnalysisResult.parse_raw(result)
    if page_analysis_result.exceptions is not None:
        raise RuntimeError(page_analysis_result.exceptions)
    return page_analysis_result.data[0]


async def analyse_whitepage(bucket, path_image, num_chars_alpha: int):
    is_white = await is_white_image_from_s3(
        bucket, path_image, num_chars_alpha=num_chars_alpha
    )
    if is_white:
        return (
            pagecategory_from_pagecat(PageCat.WHITE_PAGE),
            documentcategory_from_doccat(PageCat.WHITE_PAGE.value.document_cat),
        )
    return None, None


async def searchablepage2image(searchable_page: SearchablePage):
    page_image_s3_dest = f"{searchable_page.dossier}/pageimages/{uuid4()}"
    s3pdf_jpg = await call_pdf2jpg_remote(
        searchable_page.bucket,
        searchable_page.pdf_path,
        searchable_page.bucket,
        page_image_s3_dest,
    )
    return s3pdf_jpg.images[0], s3pdf_jpg.processing_duration


async def get_page_text_from_searchable_page(searchable_page: SearchablePage) -> str:
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        local_text_file = temp_path / "0.txt"
        local_text_file_str = str(local_text_file)
        await client.fget_object(
            searchable_page.bucket, searchable_page.text_file, local_text_file_str
        )
        text = local_text_file.read_text(encoding="utf-8")
        return text


TEXT_EXTRACTION_TYPE_NAMES = [
    ExtractionType.STRING.name,
    ExtractionType.PARAGRAPH.name,
    ExtractionType.ADDRESS_BLOCK.name,
    ExtractionType.DATE.name,
    ExtractionType.INT.name,
    ExtractionType.CURRENCY.name,
]


def consolidate_page_objects(
    text_analysis_page_objects,
    imagepredict_page_objects,
    keep_pageparser_po=True,
    keep_hylayoutlm=True,
    keep_duplicate_po=False,
    add_debug_prefix=True,
):
    """
    Hylayoutlm page objects have priority over rule based extractions
    """
    if imagepredict_page_objects:
        pos = []
        if keep_pageparser_po:
            imagepredict_page_object_keys = [p.key for p in imagepredict_page_objects]
            for po in text_analysis_page_objects:
                if po.key in imagepredict_page_object_keys:
                    if not keep_duplicate_po:
                        continue
                    if po.type in TEXT_EXTRACTION_TYPE_NAMES:
                        if add_debug_prefix:
                            po.value = "[DUPLI TEXT] " + po.value
                else:
                    if not keep_pageparser_po:
                        continue
                    if po.type in TEXT_EXTRACTION_TYPE_NAMES:
                        if add_debug_prefix:
                            po.value = "[EXTRA TEXT] " + po.value
                pos.append(po)

        if keep_hylayoutlm:
            # We keep all of them. If pageparser page objects are to be kept or not is decided above.
            pos += imagepredict_page_objects
        return pos
    return text_analysis_page_objects


def create_async_task(fn: Coroutine):
    async def wrap(fn):
        with sentry_sdk.start_span(op=fn.__name__):
            return await fn

    with sentry_sdk.Hub(sentry_sdk.Hub.current):
        return asyncio.create_task(wrap(fn))


async def process_page(searchable_page: SearchablePage) -> ProcessedPage:
    """analyses a single searchable page"""
    stats = TimingStats()
    with sentry_sdk.start_span(op="process_page") as span:
        span.set_data("searchable_page_number", searchable_page.page_number)
        span.set_data("extracted_file", searchable_page.extracted_file)
        span.set_data("dossier_uuid", searchable_page.dossier)

        with bound_contextvars(pid=randomword(4)):
            start = time()
            try:
                await logger.ainfo("processing page", pdf_path=searchable_page.pdf_path)
                async with timed_operation(stats, "text_extraction"):
                    # Abby/FREP
                    page_text = await get_page_text_from_searchable_page(
                        searchable_page
                    )

                spacy_task = None
                num_letters = count_alpha(page_text)
                if num_letters >= global_settings.SPACY_MIN_NUM_LETTERS:
                    async with timed_operation(stats, "spacy_classification"):
                        spacy_task = create_async_task(
                            classify_with_spacy_rpc(page_text)
                        )

                async with timed_operation(stats, "image_conversion"):
                    # Run this right now because we need it for the detection
                    pageimage_task = create_async_task(
                        searchablepage2image(searchable_page)
                    )
                    page_image_path, processing_duration = await pageimage_task

                # mrz_detection_task = asyncio.create_task(detect_mrz(searchable_page.bucket, page_image_path))

                page_objects = []

                page_object_detection_task = None
                if global_settings.ENABLE_DETECTRON2_OBJECT_DETECTION:
                    page_object_detection_task = create_async_task(
                        # Detects and classifies page objects in the image
                        detect_and_classify_page_objects_from_s3(
                            searchable_page.bucket, [page_image_path]
                        )
                    )

                # mrz_detections = await mrz_detection_task
                # page_objects.extend(mrzdetection2page_object(mrz_detections, searchable_page.page_number))

                if spacy_task:
                    spacy_classification_response = await spacy_task
                    spacy_page_classifications = (
                        spacy_classification_response.classifications
                    )
                else:
                    spacy_page_classifications = []

                text_analysis_task = asyncio.create_task(
                    analyse_page(
                        searchable_page.bucket,
                        searchable_page.pdf_path,
                        page_text,
                        spacy_page_classifications,
                        "de",
                    )
                )

                text_analysis_page_objects = []
                if text_analysis_task:
                    page_extraction_result: PageExtractionResult = PageExtractionResult(
                        **(await text_analysis_task)
                    )

                    text_analysis_page_objects = page_extraction_result.page_objects

                    doc_cat: shared.DocumentCategory = page_extraction_result.doc_cat
                    page_cat: shared.PageCategory = page_extraction_result.page_cat
                    if page_extraction_result.page_cat.name == PageCat.EMPTY_PAGE.name:
                        await logger.ainfo(
                            "Found (text) empty page", path=page_image_path
                        )
                        num_chars_alpha = (
                            page_extraction_result.text_content_stats.num_chars_alpha
                        )
                        page_cat_white, doc_cat_white = await analyse_whitepage(
                            searchable_page.bucket, page_image_path, num_chars_alpha
                        )
                        if page_cat_white:
                            await logger.ainfo(
                                "Found fully white page", path=page_image_path
                            )
                            page_cat = page_cat_white
                            doc_cat = doc_cat_white

                imagepredict_page_objects = []
                if global_settings.ENABLE_IMAGEPREDICT_PROCESSING:
                    async with timed_operation(stats, "layout_llm_task"):
                        # LAYOUTLLM
                        model_info = map_page_to_model_info(doc_cat, page_cat)
                        imagepredict_page_objects = await predict_page_objects_with_imagepredict(
                            model_info,
                            searchable_page.bucket,
                            page_image_path,
                            imagepredict_min_confidence_threshold=global_settings.IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD,
                        )

                async with timed_operation(stats, "consolidate_page_objects"):
                    page_objects = consolidate_page_objects(
                        text_analysis_page_objects,
                        imagepredict_page_objects,
                        add_debug_prefix=global_settings.USE_IMAGEPREDICT_DEBUG_PREFIX,
                    )

                # Await this last because it will probably take the longest
                if page_object_detection_task:
                    async with timed_operation(stats, "page_object_detection_task"):
                        (
                            page_object_classifications,
                            detection_model,
                            classification_model,
                        ) = await page_object_detection_task
                    async with timed_operation(stats, "create_graphical_page_objects"):

                        (
                            graphical_page_objects,
                            derived_page_objects,
                        ) = await create_graphical_page_objects(
                            page_object_classifications,
                            searchable_page.page_number,
                            show_graphical_page_objects=global_settings.DETECTRON_PAGE_OBJECTS_VISIBLE,
                            confidence_factor=global_settings.GRAPHICAL_PAGE_OBJECT_CONFIDENCE_FACTOR,
                            find_derived_page_objects=global_settings.ENABLE_IMAGEPREDICT_PROCESSING,
                            imagepredict_min_confidence_threshold=global_settings.IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD,
                        )

                    # If we find derived page objects here we can be quite sure
                    # they are better than the page objects found based on
                    # text classification. So in this case use only the graphical
                    # page objects and ignore everything based on text classification
                    has_derived_page_objects = len(derived_page_objects) > 0
                    if has_derived_page_objects:
                        page_objects = []

                    page_objects += graphical_page_objects
                    # page_objects.extend(graphical_page_objects)

                pci = PageConfidenceInfo(
                    parsername=page_extraction_result.parsername,
                    spacy_classifications=spacy_page_classifications,
                )

                image_str = str(
                    Path(page_image_path).relative_to(searchable_page.dossier)
                )
                image_str = ensure_path_has_forward_slashes(image_str)

                # Sort page objects from top to bottom and from left to right
                # We want some tolerance on what is the vertical position of a line.
                # Therefore (x.bbox.ref_height / 160) == "half a line of tolerance"
                page_objects.sort(
                    key=lambda x: (
                        round(x.bbox.top / (x.bbox.ref_height / 160)),
                        x.bbox.left,
                    )
                )

                if global_settings.ENABLE_FINHURDLE_SENTIMENT_ANALYSIS:
                    page_objects = await update_finhurdle_confidence(
                        page_objects,
                        global_settings.FINHURDLE_MIN_VISIBLE_CONFIDENCE_THRESHOLD,
                        global_settings.ENABLE_FINHURDLE_VISIBILITY,
                    )
                if global_settings.ENABLE_FINHURDLE_BEST_ITEM_PER_PAGE:
                    page_objects = filter_best_finhurdle_per_page(page_objects)

                (
                    doc_cat_custom,
                    page_cat_custom,
                    reason,
                ) = customize_classification_by_page_objects(
                    doc_cat, page_cat, page_objects
                )
                if doc_cat_custom or page_cat_custom or reason:
                    await logger.ainfo(
                        "Customize classification based on page objects.",
                        doc_cat_old=doc_cat.name,
                        page_cat_old=page_cat.name,
                        doc_cat_custom=doc_cat_custom.name,
                        page_cat_custom=page_cat_custom.name,
                        reason=reason,
                    )
                    doc_cat = doc_cat_custom
                    page_cat = page_cat_custom

                # Hardcode EMAIL_CORRESPONDENCE for things processed by convert_email_to_pdf
                # essentially any email body that is converted to a pdf should be automatically
                # classified as CORRESPONDENCE_EMAIL
                # extracted_file is the file that has the special prefix.
                if is_email_body_filename(searchable_page.extracted_file):
                    doc_cat = documentcategory_from_doccat(
                        DocumentCat.CORRESPONDENCE_EMAIL
                    )
                    page_cat = pagecategory_from_pagecat(PageCat.GENERIC_PAGE)

                return ProcessedPage(
                    number=searchable_page.page_number,
                    lang=page_extraction_result.lang,
                    document_category=doc_cat,
                    page_category=page_cat,
                    confidence=page_extraction_result.classification_confidence,
                    confidence_info=pci,
                    page_objects=page_objects,
                    text_content_stats=page_extraction_result.text_content_stats,
                    page_layout_info=page_extraction_result.page_layout_info,
                    image=image_str,
                    searchable_txt=searchable_page.text_file,
                    searchable_pdf=searchable_page.pdf_path,
                )
            finally:
                await logger.ainfo(
                    "processed page",
                    duration=time() - start,
                    timing_summary=stats.get_summary(),
                )


class TooManyPagesInFileException(Exception):
    """Exception raised if the file has too many pages.
    This can e.g. happen if the file is corrupt or if a malicous file is sent intentionally.
    """

    def __init__(
        self, bucket, object_name, num_pages: int, max_num_pages_allowed_in_pdf: int
    ) -> None:
        self.bucket = bucket
        self.object_name = object_name
        self.message = f"The file {self.object_name} in bucket {self.bucket} has {num_pages} pages but a maximum of {max_num_pages_allowed_in_pdf} pages is allowed per document. Maybe the file is corrupt."
        super().__init__(self.message)


async def classify_searchable_pages(searchable_pages: List[SearchablePage]):
    """
    Loop over list of SearchablePage, concatenate text of all pages and classify it
    :param searchable_pages:
    :return: List of classifications with confidence for text of all pages
    """

    def sort_key(searchable_page):
        return searchable_page.page_number

    searchable_pages.sort(key=sort_key)
    texts = []
    for searchable_page in searchable_pages:
        with TemporaryDirectory() as temp_dir:
            local_file = Path(f"{temp_dir}/{searchable_page.page_number}.txt")
            await download_single_file(
                searchable_page.bucket, searchable_page.text_file, local_file
            )
            text = Path(local_file).read_text(encoding="utf-8")
            if text:
                text = text.strip()
            texts.append(text)
    full_text = "\n\n".join(texts)
    return await classify_with_spacy_rpc(full_text, 5)


async def make_generic_file_searchable(
    *,
    bucket: str,
    dossier: str,
    extracted_file: str,
    processing_config: OriginalFileProcessingConfig,
    use_ocr_cache=True,
):
    target_pdf_object_name = f"{dossier}/searchable/{extracted_file}/full.pdf/out.pdf"
    full_page_dest = f"{dossier}/searchable/{extracted_file}/full.pdf"

    # check if there is a cached version
    extractedfile_objectname = f"{dossier}/extracted_files/{extracted_file}"

    client = minio.Minio(
        global_settings.S3_HOST,
        global_settings.S3_ACCESS_KEY,
        global_settings.S3_SECRET_KEY,
        secure=global_settings.S3_SECURE,
        region=global_settings.S3_REGION,
    )

    stats_extracted = client.stat_object(bucket, extractedfile_objectname)

    metadata = stats_extracted.metadata

    filesize_bytes_extracted = stats_extracted.size
    sha256sum = {k.lower(): v for k, v in metadata.items()}.get(META_DATA_SHA256_SUM)

    cache_bucket = global_settings.S3_CACHE_BUCKET
    cache_prefix = sha256sum
    cache_pdf = f"{cache_prefix}/out.pdf"

    if use_ocr_cache and await object_exists(cache_bucket, cache_pdf):
        await logger.ainfo(
            "using cached file",
            extractedfile_objectname=extractedfile_objectname,
            sha256sum=sha256sum,
            cache_bucket=cache_bucket,
            cache_pdf=cache_pdf,
        )
    else:
        await logger.ainfo(
            "cached version does not exists (or cache not used), ocr extracted file",
            use_ocr_cache=use_ocr_cache,
            extractedfile_objectname=extractedfile_objectname,
            sha256sum=sha256sum,
            cache_bucket=cache_bucket,
            cache_pdf=cache_pdf,
            max_num_tries=processing_config.ocr_max_num_tries,
        )
        await ocr(
            source_bucket=bucket,
            src_object=extractedfile_objectname,
            dest_bucket=cache_bucket,
            dest_object=sha256sum,
            rotation_decision=True,
            max_num_tries=processing_config.ocr_max_num_tries,
        )
        await logger.ainfo(
            "ocr done",
            extractedfile_objectname=extractedfile_objectname,
            sha256sum=sha256sum,
        )
        ret = await object_exists(cache_bucket, cache_pdf)
        await logger.ainfo(
            "file exists in cache",
            extractedfile_objectname=extractedfile_objectname,
            sha256sum=sha256sum,
            ret=ret,
        )

    # Check size of result in cache
    stats_result = client.stat_object(cache_bucket, cache_pdf)
    filesize_bytes_result = stats_result.size

    filesize_factor = filesize_bytes_result / filesize_bytes_extracted

    searchable_pdf_object_name = (
        f"{dossier}/searchable/{extracted_file}/full.pdf/out_with_ocr.pdf"
    )

    # Copy processed file always here
    copy_object_sync(
        bucket, searchable_pdf_object_name, CopySource(cache_bucket, cache_pdf)
    )

    # Target file is either original file or processed file
    if global_settings.REPLACE_LARGE_OCR_RESULTS_WITH_ORIGINAL and (
        filesize_bytes_result > SEARCHABLE_PDF_FILESIZE_BYTES_ALWAYS_OK
        and filesize_factor > SEARCHABLE_PDF_MAX_FILESIZE_FACTOR_OK
    ):
        # Have 2 files: out.pdf (which is the original file) and out_with_ocr (which is the ocr'ed file)
        copy_object_sync(
            bucket, target_pdf_object_name, CopySource(bucket, extractedfile_objectname)
        )
        logger.warning(
            "Result is too large, keep extracted file instead",
            size_extracted_file_mb=round(filesize_bytes_extracted / 1024 / 1024, 1),
            size_searchable_file_mb=round(filesize_bytes_result / 1024 / 1024, 1),
            bucket=bucket,
            extracted_file=extractedfile_objectname,
        )

    else:
        # Have 2 files: out.pdf and out_with_ocr (which are identical the ocr'ed file)

        copy_object_sync(
            bucket, target_pdf_object_name, CopySource(cache_bucket, cache_pdf)
        )
        # searchable_pdf_object_name = target_pdf_object_name

    objects = client.list_objects(cache_bucket, f"{sha256sum}/txt/")
    for object in objects:
        copy_object_sync(
            bucket,
            f"{full_page_dest}/txt/{Path(object.object_name).name}",
            CopySource(cache_bucket, object.object_name),
        )

    # Always use version with ocr here as otherwise the ocr on the page will fail
    # for documents that use the original file for target_pdf_object_name
    pages = await split_pdfpages(
        bucket=bucket,
        src_object=searchable_pdf_object_name,
        dest_bucket=bucket,
        dest_object=f"{dossier}/searchable/{extracted_file}/pages",
        processing_config=processing_config,
    )
    searchable_pages = []
    for idx, page in enumerate(pages):
        searchable_pages.append(
            SearchablePage(
                bucket=bucket,
                dossier=dossier,
                extracted_file=extracted_file,
                page_number=idx,
                pdf_path=page,
                text_file=f"{full_page_dest}/txt/{idx}.txt",
                rotation_decision=True,
            )
        )
    return searchable_pages


async def try_to_fix_image_file(
    bucket_in,
    object_name_in,
    bucket_out,
    object_name_out,
    timeout: Union[float, None] = None,
) -> bool:
    """
    Download file from S3, try to fix with Imagemagick. If successful re-upload to destination
    @param bucket:
    @param extractedfile_objectname:
    @return:
    """
    with TemporaryDirectory() as temp_dir:
        try:
            filepath = Path(object_name_in)
            temp_path_in = Path(temp_dir) / filepath.name
            await client.fget_object(bucket_in, object_name_in, str(temp_path_in))
            temp_path_out = (
                Path(temp_dir) / f"{filepath.stem}_converted{filepath.suffix}"
            )
            success, _ = im_fix_image(temp_path_in, temp_path_out, timeout=timeout)
            if success:
                await client.fput_object(
                    bucket_out, object_name_out, str(temp_path_out)
                )

            return success
        except Exception as e:
            logger.error(e)
            return False


async def make_image_searchable(
    bucket,
    dossier,
    extracted_file,
    processing_config: OriginalFileProcessingConfig,
    try_to_fix_file: bool = False,
):
    extractedfile_objectname = f"{dossier}/extracted_files/{extracted_file}"

    if try_to_fix_file:
        await try_to_fix_image_file(
            bucket, extractedfile_objectname, bucket, extractedfile_objectname
        )

    url = create_presigned_url(bucket, extractedfile_objectname)
    rotation_decision = await make_rotation_decision(url)
    dest_object_name = f"{dossier}/searchable/{extracted_file}"
    try:
        await ocr(
            source_bucket=bucket,
            src_object=extractedfile_objectname,
            dest_bucket=bucket,
            dest_object=dest_object_name,
            rotation_decision=rotation_decision,
            max_num_tries=processing_config.ocr_max_num_tries,
        )
    except OcrImageFileCorruptedException as e:
        if try_to_fix_file:
            # This has been retried already -> give up
            raise e
        else:
            # Retry once, in this retry the file will be fixed (best effort)
            # We intentionally do not catch Exceptions in this try
            return await make_image_searchable(
                bucket,
                dossier,
                extracted_file,
                processing_config=processing_config,
                try_to_fix_file=True,
            )

    return SearchablePage(
        bucket=bucket,
        dossier=dossier,
        extracted_file=extracted_file,
        page_number=0,
        pdf_path=f"{dest_object_name}/out.pdf",
        text_file=f"{dest_object_name}/txt/0.txt",
        rotation_decision=rotation_decision,
    )


async def make_pdf_searchable(
    bucket: str,
    dossier: str,
    extracted_file: str,
    processing_config: OriginalFileProcessingConfig,
) -> AsyncIterator[SearchablePage]:
    extractedfile_objectname = f"{dossier}/extracted_files/{extracted_file}"
    pages = await split_pdfpages(
        bucket=bucket,
        src_object=extractedfile_objectname,
        dest_bucket=bucket,
        dest_object=f"{dossier}/extracted_file_split/{extracted_file}/pages",
        processing_config=processing_config,
    )

    searchable_page_tasks = []
    for idx, page in enumerate(pages):
        searchable_page_tasks.append(
            asyncio.create_task(
                _make_searchable_page_for_single_pdf_page(
                    bucket,
                    dossier,
                    extracted_file,
                    idx,
                    page,
                    processing_config=processing_config,
                )
            )
        )
    for page in asyncio.as_completed(searchable_page_tasks):
        yield await page


async def _make_searchable_page_for_single_pdf_page(
    bucket,
    dossier,
    extracted_file,
    idx,
    page,
    processing_config: OriginalFileProcessingConfig,
):
    dest_object_name = f"{dossier}/searchable/{extracted_file}/{idx}"
    dest_bucket = bucket
    source_bucket = bucket
    source_page_pdf = page
    dest_page_image = f"{dossier}/extracted_file_images/{extracted_file}/pages/{idx}"
    image = await call_pdf2jpg_remote(
        source_bucket, source_page_pdf, dest_bucket, dest_page_image
    )
    url = create_presigned_url(source_bucket, image.images[0])
    rotation_decision = await make_rotation_decision(url)
    await ocr(
        source_bucket=source_bucket,
        src_object=page,
        dest_bucket=dest_bucket,
        dest_object=dest_object_name,
        rotation_decision=rotation_decision,
        max_num_tries=processing_config.ocr_max_num_tries,
    )
    searchable_page = SearchablePage(
        bucket=dest_bucket,
        dossier=dossier,
        extracted_file=extracted_file,
        # 211213 mt This links to the full pdf, the first page of which is at
        # relative position {dest_object_name}/pdf/0.pdf
        page_number=idx,
        pdf_path=f"{dest_object_name}/out.pdf",
        # Careful this links only to first page text not to text of full document
        # (but this will be identical for single page ocr)
        text_file=f"{dest_object_name}/txt/0.txt",
        rotation_decision=rotation_decision,
    )
    return searchable_page


async def cached_make_searchable(
    *,
    bucket,
    dossier,
    extracted_file,
    processing_config: OriginalFileProcessingConfig,
    use_ocr_cache: bool = True,
) -> AsyncIterator[SearchablePage]:
    extractedfile_objectname = f"{dossier}/extracted_files/{extracted_file}"
    cache_key = await get_sha256sum(bucket, extractedfile_objectname)
    try:
        if not use_ocr_cache:
            # Delete folder if it exists
            await logger.ainfo(
                "removing existing data from ocr cache",
                bucket=bucket,
                cache_key=cache_key,
            )
            if cache_key:
                exists = await object_exists(global_settings.S3_CACHE_BUCKET, cache_key)
                if exists:
                    await remove_prefix(global_settings.S3_CACHE_BUCKET, cache_key)

        if not global_settings.SEARCHABLE_PAGE_CACHE_ENABLED:
            await logger.ainfo(
                "make extracted file searchable now",
                SEARCHABLE_PAGE_CACHE_ENABLED=global_settings.SEARCHABLE_PAGE_CACHE_ENABLED,
                bucket=bucket,
                extracted_file=extractedfile_objectname,
            )
            searchable_pages: List[SearchablePage] = []
            async for searchable_page in make_searchable(
                bucket=bucket,
                dossier=dossier,
                extracted_file=extracted_file,
                processing_config=processing_config,
            ):
                yield searchable_page
                searchable_pages.append(searchable_page)
            await cache_searchable_pages(cache_key, searchable_pages)

        exists = await object_exists(global_settings.S3_CACHE_BUCKET, cache_key)
        if exists:
            async for searchable_page in load_searchable_pages_from_cache(
                bucket,
                cache_key,
                dossier,
                extracted_file,
                processing_config=processing_config,
            ):
                yield searchable_page
        else:
            searchable_pages: List[SearchablePage] = []
            async for searchable_page in make_searchable(
                bucket=bucket,
                dossier=dossier,
                extracted_file=extracted_file,
                processing_config=processing_config,
            ):
                yield searchable_page
                searchable_pages.append(searchable_page)
            await cache_searchable_pages(cache_key, searchable_pages)
    except S3Error as e:
        # This is not an error, this just that the file is not in the OCR cache
        if e.code == "NoSuchKey":
            await logger.ainfo(
                "could not find cached version, directly running make_searchable",
                object_name=f"{bucket}/{extractedfile_objectname}",
                exc_info=False,
            )
            searchable_pages: List[SearchablePage] = []
            async for searchable_page in make_searchable(
                bucket=bucket,
                dossier=dossier,
                extracted_file=extracted_file,
                processing_config=processing_config,
            ):
                yield searchable_page
                searchable_pages.append(searchable_page)
            await cache_searchable_pages(cache_key, searchable_pages)
        else:
            raise e


async def load_searchable_pages_from_cache(
    bucket,
    cache_key,
    dossier,
    extracted_file,
    processing_config: OriginalFileProcessingConfig,
):
    await logger.ainfo("Loading searchable pages from cache")

    with TemporaryDirectory() as temp_dir:
        temp_file = Path(temp_dir) / "searchable_pages.json"

        # If file is not in cache this will throw an S3Error as an expected response
        # Therefore no error logging
        await download_single_file(
            global_settings.S3_CACHE_BUCKET,
            f"{cache_key}/searchable_pages.json",
            temp_file,
            log_errors=False,
        )
        searchable_pages: SearchabelPages = SearchabelPages.parse_file(temp_file)

        if processing_config.max_num_pages_allowed_input:
            max_num_pages_allowed = processing_config.max_num_pages_allowed_input
        else:
            max_num_pages_allowed = global_settings.MAX_NUM_PAGES_ALLOWED_IN_PDF

        if max_num_pages_allowed:
            if len(searchable_pages.pages) > max_num_pages_allowed:
                num_pages = len(searchable_pages.pages)
                raise TooManyPagesError(
                    details=f"PDF has too many pages: {num_pages} for original file: bucket={bucket}, dossier={dossier} but max_num_pages_allowed={max_num_pages_allowed}",
                    num_pages=num_pages,
                    max_num_pages_allowed_in_pdf=max_num_pages_allowed,
                )

        for searchable_page in searchable_pages.pages:
            pdf_path = f"{dossier}/searchable_pages/{extracted_file}/{searchable_page.page_number}.pdf"
            await copy_object_with_retry(
                bucket,
                pdf_path,
                CopySource(searchable_page.bucket, searchable_page.pdf_path),
            )
            text_file = f"{dossier}/searchable_pages/{extracted_file}/{searchable_page.page_number}.txt"
            await copy_object_with_retry(
                bucket,
                text_file,
                CopySource(searchable_page.bucket, searchable_page.text_file),
            )
            yield SearchablePage(
                bucket=bucket,
                dossier=dossier,
                extracted_file=extracted_file,
                page_number=searchable_page.page_number,
                pdf_path=pdf_path,
                text_file=text_file,
                rotation_decision=searchable_page.rotation_decision,
            )


async def cache_searchable_pages(cache_key, searchable_pages):
    cached_searchable_page_tasks = []
    for searchable_page in searchable_pages:
        cached_searchable_page = asyncio.create_task(
            cache_searchable_page(cache_key, searchable_page)
        )
        cached_searchable_page_tasks.append(cached_searchable_page)
    searchable_pages: List[SearchablePage] = await asyncio.gather(
        *cached_searchable_page_tasks
    )

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        temp_file_path = temp_path / "searchable_pages.json"
        temp_file_path.write_text(SearchabelPages(pages=searchable_pages).json())
        object_name = f"{cache_key}/searchable_pages.json"
        await logger.ainfo(
            "write page to cache",
            bucket=global_settings.S3_CACHE_BUCKET,
            object_name=object_name,
            file_path=temp_file_path,
        )
        await upload_file(global_settings.S3_CACHE_BUCKET, object_name, temp_file_path)


async def cache_searchable_page(cache_key, searchbale_page):
    pdf_object_name = f"{cache_key}/searchable_pdf_{searchbale_page.page_number}.pdf"
    await copy_object(
        global_settings.S3_CACHE_BUCKET,
        pdf_object_name,
        CopySource(searchbale_page.bucket, searchbale_page.pdf_path),
    )
    text_file_object_name = f"{cache_key}/text_file_{searchbale_page.page_number}.txt"
    await copy_object(
        global_settings.S3_CACHE_BUCKET,
        text_file_object_name,
        CopySource(searchbale_page.bucket, searchbale_page.text_file),
    )
    cached_searchable_page = searchbale_page.copy(
        update={
            "bucket": global_settings.S3_CACHE_BUCKET,
            "dossier": "n/a",
            "pdf_path": pdf_object_name,
            "text_file": text_file_object_name,
        }
    )
    return cached_searchable_page


async def make_searchable(
    *,
    bucket,
    dossier,
    extracted_file,
    processing_config: OriginalFileProcessingConfig,
    use_ocr_cache: bool = True,
) -> AsyncIterator[SearchablePage]:
    """make the file searchable and return the pages"""

    filetype = Path(extracted_file).suffix.lower()

    if filetype not in ALL_SUPPORTED_FILE_SUFFIX:
        raise UnsupportedFileType(filetype, ALL_SUPPORTED_FILE_SUFFIX)
    if filetype in [".jpg", ".jpeg", ".png", ".tif", ".tiff"]:
        yield await make_image_searchable(
            bucket, dossier, extracted_file, processing_config=processing_config
        )
    elif filetype in [".pdf"]:
        async for page in make_pdf_searchable(
            bucket=bucket,
            dossier=dossier,
            extracted_file=extracted_file,
            processing_config=processing_config,
        ):
            yield page
    else:
        for page in await make_generic_file_searchable(
            bucket=bucket,
            dossier=dossier,
            extracted_file=extracted_file,
            use_ocr_cache=use_ocr_cache,
            processing_config=processing_config,
        ):
            yield page


async def create_file_classifications(
    pages: Dict[int, ProcessedPage], searchable_pages: List[SearchablePage]
) -> List[Classification]:

    if is_email_body_filename(searchable_pages[0].extracted_file):
        # Just skip the classification of the whole file, CORRESPONDENCE_EMAIL
        # is set in every single processed page already.
        return []
    elif len(pages) == 1:
        # Use the classification of the first and only page, no request necessary
        file_classifications = pages[0].confidence_info.spacy_classifications
        return file_classifications
    else:
        # Multi-page document, concatenate the full text and classify
        file_text_classification_task = asyncio.create_task(
            classify_searchable_pages(searchable_pages)
        )
        classification_response = await file_text_classification_task
        await logger.ainfo(
            "classification response", response=classification_response.dict()
        )
        return classification_response.classifications


async def process_extracted_file(
    *,
    bucket: str,
    dossier: str,
    original_file: str,
    extracted_file: str,
    processing_config: OriginalFileProcessingConfig,
    use_ocr_cache: bool = True,
) -> ProcessedFile:
    stats = TimingStats()

    with bound_contextvars(efid=randomword(5)):
        start = time()
        try:
            searchable_pages: List[SearchablePage] = []
            process_page_task = []

            # Make all pages searchable, then process them
            async with timed_operation(stats, "process_pages"):
                async for searchable_page in cached_make_searchable(
                    bucket=bucket,
                    dossier=dossier,
                    extracted_file=extracted_file,
                    use_ocr_cache=use_ocr_cache,
                    processing_config=processing_config,
                ):
                    # needed to be disabled as sentry doesn't work with multiprocessing
                    # gives an ssl error
                    # with sentry_sdk.Hub(sentry_sdk.Hub.current):
                    process_page_task.append(
                        asyncio.create_task(process_page(searchable_page))
                    )
                    searchable_pages.append(searchable_page)

            # Time parallel page processing
            async with timed_operation(stats, "process_page_task"):
                processed_pages = await asyncio.gather(*process_page_task)

            def sort_key(processed_page: ProcessedPage):
                return processed_page.number

            processed_pages.sort(key=sort_key)
            page: ProcessedPage
            for idx, page in enumerate(processed_pages):
                page_text = str(page)[:200]
                await logger.adebug(
                    "page text", idx=idx, page_number=page.number, text=page_text
                )

            pages: Dict[int, ProcessedPage] = {
                processed_page.number: processed_page
                for processed_page in processed_pages
            }

            # Time classification
            async with timed_operation(stats, "create_file_classifications"):
                file_classifications = await create_file_classifications(
                    pages, searchable_pages
                )

            return ProcessedFile(
                original_file_path=original_file,
                file_path=extracted_file,
                file_path_url_encoded=quote(Path(extracted_file).as_posix()),
                filename=Path(extracted_file).name,
                pages=pages,
                classifications={FILE_TEXT_SPACY_CLASSIFIER_NAME: file_classifications},
            )

        except Exception as e:
            # Error handling is a little bit unusual here,
            # we map errors raised by split_pdfpages to FileProcessingException
            # where the exception raised by split_pdfpages is mapped into
            # FileProcessingException.base_exception
            # Later we will map this exception into an ExceptionDetails object
            # and publish it via the dossier_event_publisher

            exc_info = sys.exc_info()
            details = "".join(traceback.format_exception(*exc_info))

            if isinstance(e, TooManyPagesError):
                await logger.awarning(
                    "PDF Extracted File Exception: Too many pages in file",
                    message=str(e),
                    dossier=dossier,
                    original_file=original_file,
                    extracted_file=extracted_file,
                    pc_context=processing_config.context,
                )
            elif isinstance(e, PdfSplitException):
                await logger.awarning(
                    "PDF Split Exception",
                    message=str(e),
                    dossier=dossier,
                    original_file=original_file,
                    extracted_file=extracted_file,
                    pc_context=processing_config.context,
                )
            else:
                await logger.aexception(
                    f"Could not process extracted file: dossier={dossier}, original_file={original_file}, extracted_file={extracted_file}, pc_context={processing_config.context}, details={details}"
                )

            raise FileProcessingException(
                extracted_file=extracted_file,
                base_exception=e,
                message=f"Could not process extracted file {extracted_file}",
                details=details,
            )
        finally:
            # Log detailed timing information
            await logger.ainfo(
                "processed file",
                file_path=extracted_file,
                timing_summary=stats.get_summary(),
                duration=time() - start,
            )
            # Get an error after this ERROR:asyncio:Task was destroyed but it is pending!
            # task: <Task pending name='Task-77' coro=<Connection._on_reader_done.<locals>.close_writer_task() running at /home/<USER>/.cache/pypoetry/virtualenvs/hyextract-uCfUjYGy-py3.11/lib/python3.11/site-packages/aiormq/connection.py:554> wait_for=<Future finished result=None>>
            # ERROR:asyncio:Task was destroyed but it is pending!
            # Possibly due to dossier_event_publisher or rabbitmq connection
            # this error indicates we have an issue with unclosed async resources, specifically with aio_pika (RabbitMQ async client).
            # We need to properly handle the async context and cleanup in the multiprocessing environment.


create_semantic_documents_async = aio(create_semantic_documents)


async def process_dossier(
    bucket,
    dossier_name,
    source_file_filter: Optional[List[str]] = None,
    use_ocr_cache=True,
    account_name: Optional[str] = None,
    client_lang: str = "de",
    processing_config: OriginalFileProcessingConfig = OriginalFileProcessingConfig(),
):
    small_files_filter = (
        processing_config.small_files_filter
        if processing_config.small_files_filter
        else DEFAULT_SMALL_FILES_FILTER
    )

    files = await get_original_files(bucket, dossier_name)

    extraction_tasks = []
    for file in files:
        task = unpack_original_file(
            bucket, dossier_name, file, small_files_filter=small_files_filter
        )
        extraction_tasks.append(task)

    processed_file_tasks = []
    file_extractions = {}

    for extraction_task in asyncio.as_completed(extraction_tasks):
        file_extraction: FileExtraction = await extraction_task
        file_extractions[file_extraction.original_file] = file_extraction
        for extracted_file in file_extraction.extracted_files:
            # do not process file which are extraction errors
            if extracted_file in file_extraction.exceptions.keys():
                continue

            use_file = True
            if source_file_filter:
                use_file = False
                for filename in source_file_filter:
                    if filename in extracted_file:
                        use_file = True
                        break
            if use_file:
                processed_file_task = process_extracted_file(
                    bucket=bucket,
                    dossier=dossier_name,
                    original_file=file_extraction.original_file,
                    extracted_file=extracted_file,
                    use_ocr_cache=use_ocr_cache,
                    processing_config=processing_config,
                )
                processed_file_tasks.append(asyncio.create_task(processed_file_task))

    num_processed = len(processed_file_tasks)
    if source_file_filter and num_processed == 0:
        raise Exception(
            f"No matching file found. source_file_filter={source_file_filter}"
        )

    processed_files: List[BaseException | Any] = await asyncio.gather(
        *processed_file_tasks, return_exceptions=True
    )

    processing_exceptions = {}
    processed_files_dict = {}
    for processed_file in processed_files:
        if isinstance(processed_file, FileProcessingException):
            exception_details = await map_file_exception(processed_file)
            processing_exceptions[processed_file.extracted_file] = exception_details
        elif isinstance(processed_file, ProcessedFile):
            processed_files_dict[processed_file.file_path] = processed_file
        else:
            raise Exception(
                f"Unexpected type '{type(processed_file)}' of processed file response={str(processed_file)}"
            )
    semdoc_processing_config = create_semantic_documents_processing_config(
        processing_config
    )
    semantic_documents = await create_semantic_documents_async(
        ProcessedFiles(processed_files=processed_files_dict),
        client_lang,
        company_name=account_name,
        processing_config=semdoc_processing_config,
    )

    if global_settings.ENABLE_FINHURDLE_SENTIMENT_ANALYSIS:
        semantic_documents = await filter_finhurdles_by_document_cat(semantic_documents)

    if is_valid_uuid4(dossier_name):
        dossier_uuid = dossier_name
    else:
        dossier_uuid = str(uuid4())

    semantic_dossier = SemanticDossier(
        uuid=dossier_uuid,
        name=dossier_name,
        company=account_name,
        extracted_files=file_extractions,
        processing_exceptions=processing_exceptions,
        processed_files=processed_files_dict,
        semantic_documents=semantic_documents,
    )

    await generate_package(bucket, dossier_name, semantic_dossier)
    return semantic_dossier


@aio
def package_documents(
    semantic_dossier: SemanticDossier,
    extracted_files: Path,
    temp_path: Path,
    dest_path: Path,
):
    dest_path.mkdir(parents=True, exist_ok=True)

    for original_file, fileextraction in semantic_dossier.extracted_files.items():
        for file, details in fileextraction.exceptions.items():
            dest_file = dest_path / "090 HypoDossier Problems" / file
            dest_file.parent.mkdir(parents=True, exist_ok=True)
            assert Path(extracted_files / file).exists()

            shutil.copy(extracted_files / file, dest_file)

    for extracted_file, _ in semantic_dossier.processing_exceptions.items():
        dest_file = dest_path / "090 HypoDossier Problems" / extracted_file
        dest_file.parent.mkdir(parents=True, exist_ok=True)

        assert Path(extracted_files).exists()

        assert Path(extracted_files / extracted_file).exists()

        shutil.copy(extracted_files / extracted_file, dest_file)

    for document in semantic_dossier.semantic_documents:
        document_file_path = dest_path / document.filename_offline_version

        if document.immutable_file:
            # The extracted file should be used without changes. Use path from first semantic page
            p = document.semantic_pages[0].source_file_path
            full_path = extracted_files / p

            shutil.copy(full_path, document_file_path)
        else:
            # compose dynamic PDF from pages
            writer = PdfWriter()
            for page in document.semantic_pages:
                processed_page = semantic_dossier.processed_files[
                    page.source_file_path
                ].pages[page.source_page_number]
                pdf_page_path = Path(processed_page.searchable_pdf)

                local_pdf_page_path = temp_path / pdf_page_path.relative_to(
                    list(pdf_page_path.parents)[-2]
                )

                if not local_pdf_page_path.exists():
                    pass

                assert (
                    local_pdf_page_path.exists()
                ), f"Could not find local_pdf_page_path at {local_pdf_page_path}"

                pdf_reader = PdfReader(str(local_pdf_page_path))
                writer.add_page(pdf_reader.pages[0])

            with document_file_path.open("wb") as fp:
                writer.write(fp)


@aio
def create_package_zip(temp_path, package_path):
    with zipfile.ZipFile(temp_path / "package.zip", "w") as zf:
        for file in package_path.glob("**/*"):
            zf.write(file, file.relative_to(package_path))


async def generate_package(bucket, dossier_name, semantic_dossier: SemanticDossier):

    # unique_id = uuid.uuid4()
    # temp_path = Path(f"/tmp/temp_directory_{unique_id}")
    # temp_path.mkdir(parents=True, exist_ok=True)

    # try:

    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        extracted_files = temp_path / "extracted_files"
        await download_prefix(
            bucket, f"{dossier_name}/extracted_files", extracted_files
        )

        # 211209 check path here: with cache it must be "searchable_pages", without it must be "searchable"
        f1 = f"{dossier_name}/searchable_pages/"
        if await object_exists(bucket, f1):
            await download_prefix(bucket, f1, temp_path / "searchable_pages")
        f2 = f"{dossier_name}/searchable/"
        if await object_exists(bucket, f2):
            await download_prefix(bucket, f2, temp_path / "searchable")

        package_path = temp_path / "package"
        await package_documents(
            semantic_dossier, extracted_files, temp_path, package_path
        )

        semantic_data = semantic_dossier.json()
        data = BytesIO(semantic_data.encode())
        client.put_object(
            bucket,
            f"{dossier_name}/semantic-document.json",
            data,
            len(semantic_data.encode()),
        )

        # add document browser
        page_images_path = temp_path / "pageimages"
        await download_prefix(bucket, f"{dossier_name}/pageimages", page_images_path)
        await add_document_browser_viewer(
            semantic_dossier, page_images_path, package_path
        )

        await add_document_extraction_excel(semantic_dossier, package_path)

        await create_package_zip(temp_path, package_path)
        await upload_file(
            bucket, f"{dossier_name}/package.zip", temp_path / "package.zip"
        )
        await upload_folder(bucket, f"{dossier_name}/package", package_path)

    # finally:
    #     # Check if the temporary directory exists before trying to remove it
    #     if temp_path.exists() and temp_path.is_dir():
    #         shutil.rmtree(temp_path)


async def add_dossier_file(bucket: str, dossier: str, file: Path):
    await client.fput_object(bucket, f"{dossier}/original_files/{file.name}", str(file))


async def main():
    # setup rpc client, so there is just one instance
    await get_rpc_client()

    # base_path = Path("/home/<USER>/Documents/hypodossier/exoscale/data_fs24_201208")
    # base_path = Path("/home/<USER>/Documents/hypodossier/hypodossier-upload-hbl/zips")
    base_path = Path("/home/<USER>/Documents/hypodossier/demo_dossier/input")
    bucket = "dossier"
    for file in islice(base_path.glob("*.zip"), 1, 2):
        filename = file.name
        dossier = f'{datetime.now().strftime("%Y%m%d-%H%M%S")} {filename}'
        await add_dossier_file(bucket, dossier, file)
        await process_dossier(bucket, dossier)


async def process_local_dossier(
    file,
    bucket,
    dossier_name,
    dest_folder,
    refresh_cache=False,
    company_name: Optional[str] = None,
):
    await get_rpc_client()
    await remove_prefix(bucket, f"{dossier_name}")
    await upload_file(bucket, f"{dossier_name}/original_files/{file.name}", file)
    await process_dossier(
        bucket, dossier_name, use_ocr_cache=not refresh_cache, account_name=company_name
    )
    shutil.rmtree(dest_folder / dossier_name, ignore_errors=True)
    (dest_folder / dossier_name).mkdir(parents=True)
    await download_prefix(bucket, f"{dossier_name}", dest_folder / dossier_name)


def is_valid_uuid4(uuid_string):
    """
    Validate that a UUID string is in
    fact a valid uuid4.
    Happily, the uuid module does the actual
    checking for us.
    It is vital that the 'version' kwarg be passed
    to the UUID() call, otherwise any 32-character
    hex string is considered valid.
    """

    try:
        val = UUID(uuid_string, version=4)
    except ValueError:
        # If it's a value error, then the string
        # is not a valid hex code for a UUID.
        return False

    # If the uuid_string is a valid hex code,
    # but an invalid uuid4,
    # the UUID.__init__ will convert it to a
    # valid uuid4. This is bad for validation purposes.
    return val.hex == uuid_string.replace("-", "")
