from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticPage import SemanticPage
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.util.string_utils import (
    contains_string,
    substring_after,
    substring_before,
)


class AbstractCrifQuickConsumerCheckPageParser(AbstractPageParser):
    token2 = "Crif AG"  # Crif AG, Hagenholzstrasse 81, CH-8050 Zürich, 0848 333 222, <EMAIL>

    # Return true if this parser can handle the page
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        if contains_string(text, self.token2, p_hamming_dist=1):
            return True


class CrifQuickConsumerCheckGreenPageParser(AbstractCrifQuickConsumerCheckPageParser):
    token1 = "Quick Check Consumer"

    token3 = "GRÜN"
    token4 = "\nDie Farbe Grün signalisiert, dass keines der aufgeführten Kriterien als möglicherweise risikobehaftet kategorisiert wurde."
    token_header = "\nAusgewählt Beurteilung\n"

    # Return true if this parser can handle the page
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = super().match_page_by_text(page_index, text)
        if (
            success
            and contains_string(text, self.token1)
            and contains_string(text, self.token3, p_hamming_dist=0)
            and contains_string(text, self.token4)
        ):
            return True

    # Return Page including page_cat and metainfo
    def parse(self) -> SemanticPage:
        t = substring_after(self.page.get_text(), self.token_header)
        t = t.replace(self.token3, "")
        t = substring_before(t, self.token4)
        addressline = ""
        for line in t.split("\n"):
            if addressline:
                addressline += ", "
            addressline += line.strip()
        return create_semantic_page(
            self,
            DocumentCat.CRIF_QUICK_CONSUMER_CHECK,
            PageCat.CRIF_QUICK_CONSUMER_CHECK_FIRST_PAGE,
        )


class CrifQuickConsumerCheckEmptyPagePageParser(
    AbstractCrifQuickConsumerCheckPageParser
):
    token_footer = "<EMAIL> 2 / 2"

    max_length = 400

    # Return true if this parser can handle the page
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = super().match_page_by_text(page_index, text)
        if success and contains_string(text, self.token_footer):
            if len(text) <= self.max_length:
                return True

    # Return Page including page_cat and metainfo
    def parse(self) -> SemanticPage:
        return create_semantic_page(
            self,
            DocumentCat.CRIF_QUICK_CONSUMER_CHECK,
            PageCat.CRIF_QUICK_CONSUMER_CHECK_EMPTY_PAGE,
        )
