from abbyyplumber.converter.ValueConverter import (
    Address<PERSON>onverter,
    DateConverter,
    ParagraphConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementLabeledField,
    SearchElementMultiLabeledField,
    SearchElementSetChooseFirst,
    SearchElementStaticText,
)
from abbyyplumber.util.plumberstudio_util import (
    FieldPosition,
    HorizontalAlignment,
    PercentageRange,
)
from hypodossier.core.documents.criminalrecords.CriminalRecordsPageData import (
    CR_FIELD_CONFIRMATION_EMPTY,
)
from hypodossier.core.documents.criminalrecords.CriminalRecordsSemanticPageCreator import (
    CriminalRecordsSemanticPageCreator,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_ADDRESS_BLOCK,
    FIELD_LASTNAME,
    FIELD_FIRSTNAME,
    FIELD_DATE_OF_BIRTH,
    FIELD_NATIVE_PLACE,
    FIELD_NATIONALITY,
    FIELD_DOCUMENT_DATE,
)
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings

import structlog

logger = structlog.getLogger(__name__)


class CriminalRecordsPageParserFullPage(AbstractPageParser):
    token_title = "Privatauszug aus dem Schweizerischen Strafregister"
    token_email = "<EMAIL>"

    LINE_HEIGHT_FACTOR = 1.2

    def __init__(self):
        super().__init__()
        self.supported_languages = ALL_LANGUAGES

    # Title that should be present on all pages
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text, [self.token_title, self.token_email]
        ) and contains_all_strings(
            text, ["Geburtsdatum / Date de naissance", "Nationalität / Nationalité"]
        )

    def parse_page_header(self):
        success = self.page.set_header_by_text(
            "Privatauszug aus dem Schweizerischen Strafregister",
            offset_vertical=-1 / 29,
        )
        if not success:
            success = self.page.set_header_by_text(
                "www.criminal-records.ch", offset_vertical=0.6 / 29
            )
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text("ist im Strafregister nicht verzeichnet")
        if not success:
            success = self.page.set_footer_by_text("ne figure pas au casier judiciare")
        return success

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page

        pos_data_col = PercentageRange(0.46, 1)
        return ContentExtractor(
            [
                SearchElementSetChooseFirst(
                    [
                        SearchElementLabeledField(
                            FIELD_ADDRESS_BLOCK.name,
                            page.header,
                            label="-Priority",
                            max_l_dist=0.4,
                            field_position=FieldPosition.BELOW_LABEL,
                            field_alignment_horizontal=HorizontalAlignment.CENTER,
                            field_vertical_line_scale=50,
                            field_width=PercentageRange(0, 0.55),
                            line_height_factor=self.LINE_HEIGHT_FACTOR * 10,
                            enabled=True,
                            converter=AddressConverter(),
                            compress_whitespace=False,
                        ),
                        SearchElementLabeledField(
                            FIELD_ADDRESS_BLOCK.name,
                            page.header,
                            label="www.casier-judicaire.admin.ch",
                            field_position=FieldPosition.RIGHT_OF_LABEL,
                            field_alignment_horizontal=HorizontalAlignment.CENTER,
                            field_vertical_line_scale=13,
                            field_pos_page_horizontal=pos_data_col,
                            converter=AddressConverter(),
                            enabled=True,
                        ),
                    ]
                ),
                SearchElementMultiLabeledField(
                    FIELD_LASTNAME.name,
                    page.main,
                    labels={"Name / Nom / Cognome / Name": 6, "Cognome": 2},
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_alignment_horizontal=HorizontalAlignment.CENTER,
                    field_vertical_line_scale=4,
                    field_pos_page_horizontal=pos_data_col,
                    line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                    optional=True,
                    converter=ParagraphConverter(max_num_spaces_per_line=5),
                ),
                SearchElementMultiLabeledField(
                    FIELD_FIRSTNAME.name,
                    page.main,
                    labels={"Vorname / Prénom": 4, "Name / First name": 4},
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_vertical_line_scale=4,
                    field_pos_page_horizontal=pos_data_col,
                    line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                ),
                SearchElementMultiLabeledField(
                    FIELD_DATE_OF_BIRTH.name,
                    page.main,
                    labels={"Geburtsdatum / Date de naissance": 7, "Date of birth": 4},
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_vertical_line_scale=4,
                    field_pos_page_horizontal=pos_data_col,
                    line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                    converter=DateConverter(),
                ),
                SearchElementMultiLabeledField(
                    FIELD_NATIVE_PLACE.name,
                    page.main,
                    labels={"Heimatort": 4, "Native place": 4},
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_vertical_line_scale=4,
                    field_pos_page_horizontal=pos_data_col,
                    line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                ),
                SearchElementMultiLabeledField(
                    FIELD_NATIONALITY.name,
                    page.main,
                    labels={"Nationalität": 4, "Nationality": 4},
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_vertical_line_scale=4,
                    field_pos_page_horizontal=pos_data_col,
                    line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                ),
                SearchElementMultiLabeledField(
                    FIELD_DOCUMENT_DATE.name,
                    page.footer,
                    labels={"ID": 0, "Ref.": 1, "1/1": 0},
                    field_pos_page_horizontal=PercentageRange(0.7, 1),
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                    field_vertical_line_scale=5,
                    field_width=PercentageRange(0, 1),
                    line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                    converter=DateConverter(),
                ),
                SearchElementSetChooseFirst(
                    [
                        SearchElementStaticText(
                            CR_FIELD_CONFIRMATION_EMPTY.name,
                            page.footer,
                            label="ist im Strafregister nicht verzeichnet",
                            max_l_dist=6,
                        ),
                        SearchElementStaticText(
                            CR_FIELD_CONFIRMATION_EMPTY.name,
                            page.footer,
                            label="ne figure pas aua casier judiciare",
                            max_l_dist=6,
                        ),
                        SearchElementStaticText(
                            CR_FIELD_CONFIRMATION_EMPTY.name,
                            page.footer,
                            label="is not registered in the criminal record",
                            max_l_dist=5,
                        ),
                    ]
                ),
            ]
        )

    def parse(self) -> SemanticPage:
        self.create_content_extractor().find(self.page)
        return CriminalRecordsSemanticPageCreator(PageCat.CRIMINAL_RECORDS).create(self)
