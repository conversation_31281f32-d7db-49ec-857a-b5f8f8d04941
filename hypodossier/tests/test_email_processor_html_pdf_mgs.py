import shutil
from tempfile import TemporaryDirectory

import pytest
from pathlib import Path

from asyncizer.tests.util_tests import configure_test_logging
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.email_processor import convert_email_to_pdf, extract_msg_content
import pdfplumber

from hypodossier.unpack import unpack_path


@pytest.fixture
def demo_email_path():
    """Path to demo email files"""
    return Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS) / "email_processor"


def extract_text_from_pdf(pdf_path):
    """Helper to extract text from PDF"""
    with pdfplumber.open(pdf_path) as pdf:
        return "\n".join(page.extract_text() for page in pdf.pages)


@pytest.mark.parametrize(
    "msg_file",
    [
        "email_messages_msg/Datenlieferung Demo-Dossier 1.msg",
        "emails_without_attachment/test_email_plan_text_msg/test_email_plan_text.msg",
        "emails_without_attachment/test_HTML_email_msg/test_HTML_email.msg",
        "emails_without_attachment/test_HTML_email_unicode_msg/test_HTML_email_unicode.msg",
    ],
)
def test_extract_msg_demo_emails(demo_email_path, msg_file, tmp_path):
    """Test extracting content from demo .msg_file files"""

    configure_test_logging()

    file_path = demo_email_path / msg_file
    assert file_path.exists(), f"Demo file not found: {file_path}"

    email_content = extract_msg_content(file_path)

    # Basic validation that content was extracted
    assert email_content["metadata"]["subject"] is not None
    assert email_content["body"] is not None

    pdf_path = tmp_path / "msg_demo_email.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Verify PDF content
    pdf_text = extract_text_from_pdf(pdf_path)

    if "Datenlieferung Demo-Dossier 1" in msg_file:
        assert "Lieber Kundenberater Kurt Berger" in pdf_text
    else:
        assert "Manuel Thiemann <<EMAIL>>" in pdf_text


@pytest.mark.asyncio
def test_unpack_path_msg(demo_email_path):

    test_eml = (
        demo_email_path
        / "email_messages_nested/parent email with pdf and sub attached msg with pdf.msg"
    )

    with TemporaryDirectory() as temp_dir:
        # Copy test_eml to temp_dir
        shutil.copy2(test_eml, temp_dir)

        unpack_path(
            file=Path(temp_dir)
            / "parent email with pdf and sub attached msg with pdf.msg"
        )
        # Extract using shutil

        # Method 1: Using Path.glob() for all files
        files_list = list(Path(temp_dir).glob("*"))

        # Original verification code
        assert len(files_list) > 0
