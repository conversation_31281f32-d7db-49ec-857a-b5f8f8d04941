from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250225_fipla"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_bekb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_checklist_de():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "checklist" / "de",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.BEKB_FIPLA_FORM: 8,
                DocumentCat.PROPERTY_PHOTOS: 1,
            },
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_checklist_fr():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "checklist" / "fr",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.BEKB_FIPLA_FORM: 9},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_checklist_fr_2():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "checklist" / "fr_2",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.BEKB_FIPLA_FORM: 6,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PROPERTY_PHOTOS: 1,
            },
            expected_doc_cat_frequency={DocumentCat.BEKB_FIPLA_FORM: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_bekb_ekd138_fipla_result_de():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "bekb_ekd138_fipla_result" / "de",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.BEKB_FIPLA_RESULT: 38,
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_doc_cat_frequency={DocumentCat.BEKB_FIPLA_RESULT: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_bekb_ekd138_fipla_result_fr():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "bekb_ekd138_fipla_result" / "fr",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.BEKB_FIPLA_RESULT: 37},
            expected_doc_cat_frequency={DocumentCat.BEKB_FIPLA_RESULT: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_bekb_retirement_analysis_de():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "retirement_analysis" / "de",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.RETIREMENT_ANALYSIS: 18,
            },
            expected_doc_cat_frequency={DocumentCat.RETIREMENT_ANALYSIS: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_bekb_retirement_analysis_fr():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "retirement_analysis" / "fr",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.RETIREMENT_ANALYSIS: 20},
            expected_doc_cat_frequency={DocumentCat.RETIREMENT_ANALYSIS: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()
