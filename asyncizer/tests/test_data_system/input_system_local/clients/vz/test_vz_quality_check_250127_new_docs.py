from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250127_new_docs"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/vz/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_vz_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_new_docs():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "all",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency_per_page={DocumentCat.BANK_DOCUMENT: 2},
            # expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_fisca():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "fisca",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PENSION3A_ACCOUNT: 2},
            expected_page_objects={
                0: {
                    "document_date": "30.12.2023",
                    "document_validity_start_date": "01.01.2023",
                    "document_validity_end_date": "31.12.2023",
                    "company": "UBS",
                    "total_amount": "CHF 3'982.40",
                    "iban": PO_EXISTS,
                    "account_no": PO_EXISTS,
                    "account_type": "Fiscakonto",
                    "salutation": "Herr",
                    "fullname": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "4455",
                    "city": "Zunzgen",
                    "address_block": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sbb():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "sbb",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                # We do not intend to have a class for SBB
                DocumentCat.UNKNOWN: 5,
                DocumentCat.UNKNOWN_DE: 2,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_authorization_fr():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "authorization_fr",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1
            },
            expected_page_objects={0: {"company": "VZ"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_grey_case():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "grey_case",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.VZ_GREY_CASE_APPLICATION: 3
            }
        ),
    ).run()
