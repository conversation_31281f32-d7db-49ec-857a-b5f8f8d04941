from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/unpack/deduplicate_files"
)
dest_folder_prefix = "input_system_components_unpack_duplicates_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_messages_eml_with_attachments_duplicates_are_removed_integration():

    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder / "01_eml",
        source_file_filter=[
            "Test email with duplicate pdfs and a zip containing duplicate pdfs.eml"
        ],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE multiple duplicate pdfs test Copy 2.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_hypodossier.ch.pdf",
            ],
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.UNKNOWN: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_msg_with_attachments_duplicates_are_removed():

    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder / "02_msg",
        source_file_filter=[
            "Test email with duplicate pdfs and a zip containing duplicate pdfs.msg"
        ],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE multiple duplicate pdfs test Copy 2.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_hypodossier.ch 31.03.2025.pdf",
            ],
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.UNKNOWN: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zip_duplicates_are_removed():

    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder / "03_zip",
        source_file_filter=["zip containing multiple duplicate pdfs.zip"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=["001 DOK DE multiple duplicate pdfs test Copy 2.pdf"],
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN: 1},
        ),
    ).run()
