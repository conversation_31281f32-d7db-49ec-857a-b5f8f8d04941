from abbyyplumber.api import Page
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.PageLayoutInfo import PageLayoutInfo
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.tax_declaration_sg_fields_map import (
    create_search_elements_standard_sg,
    SG_FIELD_VERTICAL_LINE_SCALE,
    sg_fields_map_page_deductions,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    create_search_elements_by_tax_code,
    create_search_elements_column_code,
)
from mortgageparser.util.string_utils import contains_all_strings


class TaxDeclarationSGDeductionsPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_DEDUCTIONS
        self.canton_short = "SG"

    def match_page_by_titles(self, page_layout_info: PageLayoutInfo, test: str) -> bool:
        titles = page_layout_info.get_titles_by_size(3, min_length_title=4)
        if len(titles) >= 1:
            if contains_all_strings(" ".join(titles), ["Abzüge", "Einkommens-"]):
                return True
        return False

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "10. Berufskosten bei unselbständiger Erwerbstätigkeit",
                "12.1 Unterhaltsbeiträge an Geschiedene oder Getrennte",
                "Säule 2 (BVG) inkl. Einkaufsbeiträge",
                "17. Zweiverdienerabzug",
                "24. Steuerbares Einkommen gesamt (Ziffer 22 abzüglich Ziffern 23.1-23.3)",
            ],
        )
        return success

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = create_search_elements_column_code(
            page, "Bescheinigung", x_range=PercentageRange(0, 0.4)
        )
        elements += create_search_elements_by_tax_code(
            self.page,
            fields_map=sg_fields_map_page_deductions,
            field_vertical_line_scale=SG_FIELD_VERTICAL_LINE_SCALE,
        )
        elements += create_search_elements_standard_sg(page)

        return elements
