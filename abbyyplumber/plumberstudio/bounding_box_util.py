from abbyyplumber.plumberstudio.BoundingBox import BoundingBox
from abbyyplumber.util.plumberstudio_util import Per<PERSON>ageRange


def create_bounding_box(
    page_bbox: BoundingBox,
    page_bbox_text: BoundingBox = None,
    x_range: PercentageRange = PercentageRange(),
    y_range: PercentageRange = PercentageRange(),
    intersect_bbox: BoundingBox = None,
    use_bbox_text: bool = True,
):
    bbox_ref = page_bbox_text if use_bbox_text and page_bbox_text else page_bbox

    bbox = BoundingBox(
        top=int(y_range.min * bbox_ref.height + bbox_ref.top),
        bottom=int(y_range.max * bbox_ref.height + bbox_ref.top),
        left=int(x_range.min * bbox_ref.width + bbox_ref.left),
        right=int(x_range.max * bbox_ref.width + bbox_ref.left),
    )
    bbox = bbox.intersect(intersect_bbox)
    return bbox
