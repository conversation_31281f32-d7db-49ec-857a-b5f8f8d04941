import re
from dataclasses import dataclass
from typing import List

from abbyyplumber.converter.ValueConverter import (
    MostRecentDateConverter,
    ValueConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementArea,
    create_labeled_field,
    SearchElementConstrainedArea,
    SearchElementSetChooseFirst,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.correspondence.CorrespondencePageData import (
    FIELD_EMAIL_FROM,
    FIELD_EMAIL_DATE,
    FIELD_EMAIL_TO,
    FIELD_EMAIL_SUBJECT,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    ALL_LANGUAGES_FIELD,
)

# Catch all pages to make sure that no document is lost
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardPageParser,
)

text = """
Ausdruckname, ASDFASDFASDFASDF, API
Von:	<EMAIL>
Gesendet:	Dienstag, 17. März 2020 21:15
An:	Heinz Emfängermann, PCZASDAASDF, API
Betreff:	AW: Aktuelle Einkommenszahlen
Kennzeichnung:	Zur Nachverfolgung
Kennzeichnungsstatus:	Erledigt
Guten Morgen Herr Empfängermann
Soeben ist mir in den Sinn gekommen, dass Ich ihnen noch eine Antwort geben sollte!
Meine Altersrente mit 65 Jahren wird Fr. 25'076.--sein!
Dazu haben wir auch einen Auszahlungsplan bei Helvetia der uns Jährlich Fr. 8921.-- in monatlichen Raten auszahlt.
Beste Grüsse aus Musterdorf,
Max Mustermann
"""

# Update mt 230517: there can be more than 2 lines of recipients... extend to 4


# No \\n at the end because it could be the last line (or OCR puts it at the bottom)
# Allow empty lines between the items:
# old: REGEX_EMAIL = r'Von:\s*(.*)\nGesendet:\s*(.*)\nAn:\s*(.*)\n(.*\n){0,2}Betreff:\s*(.*)'
# 230517 mt: there was a strange sample text that hangs during evaluation of DE regex but not during evaluation of other regex
# The effect disappeared after removing excess whitespace
REGEX_EMAIL = r"Von:\s*(.*)\n\s*\n?Gesendet:\s*(.*)\n\s*\n?An:\s*(.*)\n\s*\n?(.*\n){0,4}Betreff:\s*(.*)"

# Three arbitrary lines for Cc, Bcc: plus 1 line wrap
# (.*\n){0,3}
# Allow empty lines between the items:
REGEX_EMAIL_FR = r"De:\s*(.*)\n\s*\n?Envoy[eéë]:\s*(.*)\n\s*\n?[ÀAÁÄ]:\s*(.*)\n\s*\n?(.*\n){0,4}Objet:\s*(.*)"

# Caution: order can be different
# Three arbitrary lines for Cc, Bcc: plus 1 line wrap
# (.*\n){0,3}
REGEX_EMAIL_EN = r"From:\s*(.*)\n\s*\n?To:\s*(.*)\n\s*\n?(.*\n){0,4}Subject:\s*(.*)"

REGEX_EMAIL_IT = r"Da:\s*(.*)\n(.*\n){0,3}A:\s*(.*)\n\s*\n?(.*\n){0,4}Oggetto:\s*(.*)"


@dataclass
class EmailCorrespondencePageParser(StandardPageParser):
    doc_cat: DocumentCat = DocumentCat.CORRESPONDENCE_EMAIL
    page_cat: PageCat = PageCat.GENERIC_PAGE

    supported_languages: List[str] = ALL_LANGUAGES_FIELD

    # email_from: str = None
    # email_date: str = None
    # email_to: str = None
    # email_subject: str = None

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        if not text:
            return False

        text = compress_spaces(text)

        for r in [REGEX_EMAIL, REGEX_EMAIL_FR, REGEX_EMAIL_EN, REGEX_EMAIL_IT]:
            matches = re.search(r, text, re.VERBOSE)
            if matches:
                return True

    def create_content_extractor(self) -> ContentExtractor:
        scale = 1.5
        # Idea: region_caption is just a generous header of the page (20%).
        # But this can include a short email quoting another email in another
        # language.
        # First line is always From/Von, rest can vary in order
        # So we define region_caption_short which says "all fields must be close
        # to the From/Von that we found".
        return ContentExtractor(
            [
                SearchElementConstrainedArea(
                    "region_caption_short",
                    self.page.fullpage,
                    x_range=PercentageRange(0, 0.2),
                    y_range=PercentageRange(0, 0.3),
                    extract=False,
                ).include_bottom(3),
                SearchElementArea(
                    "region_caption_long",
                    self.page.fullpage,
                    x_range=PercentageRange(0, 0.2),
                    extract=False,
                ),
                SearchElementSetChooseFirst(
                    change_name=False,
                    elements=[
                        SearchElementConstrainedArea(
                            "region_email_header",
                            self.page.fullpage,
                            target_name="region_caption_short",
                            texts_top=["Von:", "De:", "From:", "Da:"],
                            texts_bottom=["Betreff:", "Object:", "Subject:", "Oggetto"],
                            extract=False,
                        )
                        .include_top()
                        .include_bottom(7),
                        SearchElementConstrainedArea(
                            "region_email_header",
                            self.page.fullpage,
                            target_name="region_caption_long",
                            texts_top=["Von:", "De:", "From:", "Da:"],
                            texts_bottom=["Betreff:", "Object:", "Subject:", "Oggetto"],
                            extract=False,
                        )
                        .include_top()
                        .include_bottom(7),
                    ],
                ),
                create_labeled_field(
                    ["Von:", "De:", "From:", "Da:"],
                    target_name_label="region_email_header",
                    name=FIELD_EMAIL_FROM.name,
                    target=self.page.fullpage,
                    field_vertical_line_scale=scale,
                    max_l_dist=0,
                    converter=CleanEmailContactConverter(),
                ),
                create_labeled_field(
                    ["Gesendet:", "Envoyé:", "Envoye:", "Date:", "Inviato"],
                    target_name_label="region_email_header",
                    name=FIELD_EMAIL_DATE.name,
                    target=self.page.fullpage,
                    field_vertical_line_scale=scale,
                    max_l_dist=0,  # else it will match e.g. "Daten" instead of "Date:"
                    converter=MostRecentDateConverter(),
                ).below_center(
                    FIELD_EMAIL_FROM.name
                ),  # can be after from or after to
                create_labeled_field(
                    ["An:", "À:", "Á:", "A:", "Ä:", "To:"],
                    target_name_label="region_email_header",
                    name=FIELD_EMAIL_TO.name,
                    target=self.page.fullpage,
                    field_vertical_line_scale=scale,
                    max_l_dist=0,
                    converter=CleanEmailContactConverter(),
                ).below_center(
                    FIELD_EMAIL_FROM.name
                ),  # can be after from or after date
                create_labeled_field(
                    ["Betreff:", "Object:", "Subject:", "Oggetto"],
                    target_name_label="region_email_header",
                    name=FIELD_EMAIL_SUBJECT.name,
                    target=self.page.fullpage,
                    field_vertical_line_scale=scale,
                    max_l_dist=1,
                ).below_center(FIELD_EMAIL_TO.name),
            ]
        )


class CleanEmailContactConverter(ValueConverter):
    def convert(self, s: str):
        if not s:
            return s
        match = re.search(r"[\w.+-]+@[\w-]+\.[\w.-]+", s)
        if match:
            return match.group(0)
        # if '<' in s and '>' in s:
        #     return s.split('<')[1].split('>')[0]
        if "," in s:
            return s.split(",")[0]
        return s


def compress_spaces(text: str) -> str:
    compressed_text = re.sub(r" +", " ", text)
    return compressed_text


if __name__ == "__main__":
    print(CleanEmailContactConverter().convert(None))
    print(CleanEmailContactConverter().convert("<EMAIL>"))
    print(CleanEmailContactConverter().convert("Manuel Thiemann <<EMAIL>>"))
    print(CleanEmailContactConverter().convert("Vorname Nachname, Abteilung, sonstwas"))
    text = """
    
                                                                                                                                             
                                                                                                                                           
                                                                                                                                           
Von: Max Muster <<EMAIL>>                                                                                            
Gesendet: Montag, 12. April 2021 15:23                                                                                                     
                                                                                                                                           
An: Muster, Maria <<EMAIL>>                                                                                     
Cc: hohohoccccc                                                                                               
Betreff: Some Subject for you                                                                                                           
                                                                                                                                           
Salut Ilario                         
    """
    matches = re.findall(REGEX_EMAIL, text, re.VERBOSE)
    if matches:
        email_from, email_date, email_to, email_cc, email_subject = matches[0]
    print(matches)
