[tool.poetry]
name = "hyextract"
version = "v1.0.0"
description = ""
authors = ["Hypodossier AG"]
license = "Proprietary"
package-mode = false

[tool.poetry.dependencies]
python = "^3.11"
beautifulsoup4 = "^4.13.4"
click = "^8.1.7"
ipython = "^8.32.0"
numpy = "^1.26.4"

pickleshare = "^0.7.5"
Pillow = "^10.4.0"
preshed = "^3.0.9"
prompt-toolkit = "^3.0.47"
Pygments = "^2.18.0"
python-dateutil = "^2.9.0.post0"
python-pptx = "^1.0.2"
pytz = "^2025.2"
PyWavelets = "^1.7.0"
PyYAML = "^6.0.2"
regex = "^2024.9.11"
requests = "^2.32.3"
scikit-image = "^0.25.1"
scikit-learn = "^1.5.2"
soupsieve = "^2.6"
tqdm = "^4.66.5"
traitlets = "^5.14.3"
tzlocal = "^5.3.1"
Unidecode = "^1.3.8"
urllib3 = "^2.2.3"
xlrd = "^2.0.1"
XlsxWriter = "^3.2.0"
pdf2image = "^1.17.0"
fuzzysearch = "^0.7.3"
fuzzywuzzy = "^0.18.0"
pikepdf = "^9.7.0"
pyMuPDF = "1.26.0"
opencv-python = "^*********"
matplotlib = "3.10.1"

setuptools = "^80.8.0"

# This one is not needed directly but we force here to update to new version
cryptography = "45.0.2"

Wand = "^0.6.13"
document = "^1.0"
Babel = "^2.16.0"
minio = "^7.2.8"
spacy-langdetect = "^0.1.2"
python-dotenv = "^1.1.0"

retry = "^0.9.2"
natsort = "^8.4.0"
joblib = "^1.4.2"
pika = "^1.3.2"
icecream = "^2.1.3"
kombu = "^5.5.3"
s3fs = "2025.5.0"
aio-pika = "^9.5.5"
tblib = "^3.0.0"
filemagic = "^1.6"
eml-parser = "^1"
pyspellchecker = "^0.8.3"
rarfile = "^4.2"
spellchecker = "^0.4"
pandas = "^2.2.3"
aiohttp = "3.11.18"
Levenshtein = "^0.27.1"
jsonpath-ng = "^1.6.1"
pip-audit = "^2.9.0"
spacy = "^2"
pydantic = "^1"
extract-msg = "^0.54.1"
watchgod = "^0.8.2"
hdapii = "2.5.1"
de_core_news_sm = { file = "artefact/de_core_news_sm-2.3.0.tar.gz" }
dependency-injector = "^4.42.0"
httpx = "^0.28.1"

# No direct dependency but required by httpx
httpcore = "1.0.9"

cffi = "^1.17.1"
sentry-sdk = "^2.22.0"
openpyxl = "^3.1.5"
pillow-heif = "0.22.0"
pypdf = "^5.5.0"
psutil = "^6.0.0"
structlog = "25.3.0"
pillow-avif-plugin = "^1.5.2"
py7zr = "^0.22.0"
pika-stubs = "^0.1.3"

pdfplumber = "^0.11.6"
python-magic = "^0.4.27"
reportlab = "^4.4.1"
weasyprint = "^65.1"
mail-parser = "^4.1.2"

# These 2 are needed for tiff analysis. Currently not in use for prod execution: imagecodecs, tifffile
imagecodecs = "^2025.3.30"
tifffile = "^2025.5.26"
freezegun = "^1.5.2"


[tool.poetry.group.dev.dependencies]
pylama = "^8.4.1"
black = "^25.1.0"
ruff = "^0.11.12"
# Woe be unto you if you try to update pytest and pytest-asyncio
# there be dragons
# we run into issues where the event loop gets lost when running tests in bulk
pytest-timeout = "^2.3.1"
pytest-xdist = "^3.6.1"
pytest = "8.3.2"
pytest-mock = "^3.7.0"
pytest-cov = "^4"
pytest-asyncio = "^0.21.1"


[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "PyPI"
priority = "primary"

[[tool.poetry.source]]
name = "hypodossier"
url = "https://gitlab.com/api/v4/groups/52803895/-/packages/pypi/simple"
priority = "supplemental"

#[tool.pylama]
#linters = "pycodestyle,pydocstyle,mccabe,pyflakes"
#max_line_length = 120
#
#[tool.pylama.pycodestyle]
#max_line_length = 120

[tool.black]
line_length = 88
exclude = '''
/(
  venv
  | \.pytest_cache
  | \.ipynb
)/
'''

[tool.pylama]
max_line_length = 3000
# C901 is "too complex"
# E722 is "do not use bare except"
# E501 is "line too long"
ignore = "E501,W605,E722,W291,W391,E203,W293,C901,W191,E101"


[tool.pylama.pycodestyle]
#ignore = ["E501"]
#max_line_length = 3000


[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "**/node_modules/**",
    "venv",
    ".venv",
    "**/migrations/**"
]
target-version = "py38"

[tool.ruff.lint]
# E722 is bare except
ignore = ["E722"]