from mortgageparser.documents.parser.pageparsers.vested_benefits_account.StandardVestedBenefitsParser import (
    StandardVestedBenefitsPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings


# Belongs to Credit Suisse and somehow AXA
class RenditaVestedBenefitsPageOneFRParser(StandardVestedBenefitsPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Rendita Fondation de libre passage",
                "Compte de libre passage",
                "Avoir de vieillesse LPP",
                "banking by Credit Suisse",
            ],
        )


"""
class RenditaPensionCertificatePageTwoFRParser(StandardPensionCertificatePageParser):
    def __init__(self):
        self.supported_languages = ['fr']

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(text, ["Fondation collective LPP"]) \
               and contains_at_least_one_string(text, ['Allianze Suisse']) \
               and contains_at_least_one_string(text, ["Certificat d'asssurance"]) \
               and contains_at_least_one_string(text, ["Cotisations totale par année"]) \
               and contains_at_least_one_string(text, ['Informations complémentaires'], hamming_dist=3)
               
"""
