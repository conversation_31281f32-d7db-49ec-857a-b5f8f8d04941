from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import (
    PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS,
)

import structlog


logger = structlog.getLogger(__name__)

FOLDER_TYPE = "2025_quality_check"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS}/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_bekb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_250610_msg():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250610_msg",
        show_page=-1,
        # source_file_filter=["03.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
