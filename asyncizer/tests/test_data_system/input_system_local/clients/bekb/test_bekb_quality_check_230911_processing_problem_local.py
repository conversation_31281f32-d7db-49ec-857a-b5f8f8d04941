from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "230911_processing_problem"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_bekb_{FOLDER_TYPE}_"


# gs -o repaired.pdf -sDEVICE=pdfwrite -dPDFSETTINGS=/prepress corrupted.pdf


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_msg_problem():
    await DossierTest(
        override_existing=False,
        use_ocr_cache=True,
        source_folder=path_root_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Act Wädenswil - Aussenmasse.pdf  seems to be a broken PDF.
            # Rest of documents is good.
            expected_doc_cat_frequency={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 1,
                DocumentCat.PLAN_ANY: 1,
                DocumentCat.PLAN_CADASTER: 1,
                DocumentCat.PLR_CADASTRE: 1,
                DocumentCat.PROPERTY_BILL: 1,
                DocumentCat.RESERVATION_CONTRACT: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.SALES_DOCUMENTATION: 4,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.USER_REGULATIONS_CONDOMINIUM: 1,
            },
            expected_num_processing_exceptions=1,
        ),
    ).run()
