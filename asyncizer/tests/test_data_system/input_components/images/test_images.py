from pathlib import Path

import pytest

from abbyyplumber.util.pil_image_util import pil_gif_info
from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/images"
path_root_folder = Path(folder)
dest_folder_prefix = "input_system_components_images_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


def test_animated_gif():
    p = (
        path_root_folder
        / "animation"
        / "250506_gif_email_footer"
        / "image_animated.gif"
    )
    assert (
        p
    ).exists(), f"Animated gif does not exist: path_root_folder={path_root_folder}"

    animated, frames = pil_gif_info(p)
    print(f"Animated: {animated}")
    print(f"Total frames: {frames}")
    assert (
        animated
    ), f"Animated gif is not animated: path_root_folder={path_root_folder}"
    assert frames == 137


@pytest.mark.asyncio
async def test_email_footer_gif_animated():
    """
    Animated gifs should be skipped completely.
    """
    p = path_root_folder / "animation" / "250506_gif_email_footer"
    assert p.exists()
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=p,
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # There should be no file as the gif is filtered out
            expected_doc_cat_frequency_per_page={}
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_footer_gif_non_animated():
    """
    Animated gifs should be skipped completely. Non animated gifs should be extracted.
    """
    p = path_root_folder / "animation" / "non_animated"
    assert p.exists()
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=p,
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_FLOOR: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_pic():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "one pic",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.PROPERTY_PHOTOS: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_logo():
    """
    Single logo that should be filtered out. But as we decreased the PDF file
    limit to 1kb this 4kb file is NOT filtered out anymore
    :return:
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "one logo",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[],
            expected_num_docs=1,  # this should be 0
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency_per_page={},
        ),
    ).run()
