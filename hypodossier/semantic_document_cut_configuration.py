from collections import defaultdict
from typing import Dict, List

from pydantic import BaseModel, Field

from global_settings import MIN_NUM_PAGES_UNKNOWN_OVERRIDE
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat


class DocumentCutConfiguration(BaseModel):
    # List of DocumentCat of other document categories which are to be embedded in this document (no penalty)
    valid_included_doc_cats: List[DocumentCat] = Field(default_factory=list)

    # List of DocumentCat that must not occur in this document. Hence if such a page is included in cannot be this
    # DocumentCat. E.g. a PENSION_CERTIFICATE must not be in a PENSION_REGULATION (but vice versa)
    invalid_included_doc_cats: List[DocumentCat] = Field(default_factory=list)

    # List of PageCat of other document categories which are to be embedded in this document (no penalty)
    # FEATURE NOT YET SUPPORTED
    valid_included_page_cats: List[PageCat] = Field(default_factory=list)

    # Minimum number of pages for a valid document. If there are fewer pages in the doc, consider it invalid
    # and make all pages unknown. This is only applied if the USE_MIN_NUM_PAGES flag is enabled
    min_num_pages: int = 1

    # Maximium number of pages for a valid document. If there are more pages in the doc, consider it invalid
    # and make all pages unknown
    max_num_pages: int = 999999  # default unlimited

    # Which percentage of the (unweighted) pages must the winning doc_cat have. All doc_cats are handled separately.
    # If page frequency weights are configured they are applied here. Must be 0..1
    # E.g. 0.6 means 60% of all pages must be of the winning doc_cat and 40% can be unknown or something else.
    # Similar to min_rel_dominance but the valid included doc_cats are not applied here.
    min_page_frequency_dominance: float = 0

    # For page frequency the weight of each page by default is 1. Optionally we define custom weights
    # (normally < 1) for pages which we want to have a different (normally smaller) impact.
    # See e.g. FOUNDATION_CERTIFICATE_CONDOMINIUM
    page_frequency_weights: Dict[DocumentCat, float] = Field(
        default_factory=lambda: defaultdict(float)
    )

    # Which percentage of the weighted dominance values must the winning doc_cat have. E.g. 0.6 means 60% of the pages
    # (ignoring all valid included doc_cats) must be of the winning doc_cat and 40% can be unknown or something else.
    min_rel_dominance: float = 0.6

    # If this is true and 'try_to_identify_long_document' fails for this doc_cat then make the whole document unknown
    must_be_long_document: bool = True

    # Whole document must have at least this confidence else make it unknown
    min_doc_confidence: float = 0


# In case an aggregated classifier is used, instead of all details we have PROPERTY_INFO
NORMAL_PROPERTY_ADDITION_CATS = [
    DocumentCat.PROPERTY_INFO,
    DocumentCat.PROPERTY_PHOTOS,
    DocumentCat.VOLUME_CALCULATION_SIA,
    DocumentCat.PROPERTY_VALUATION_GOV,
    DocumentCat.LIST_OF_RENOVATIONS,
    DocumentCat.PROPERTY_INSURANCE,
    DocumentCat.PROPERTY_ACCOUNTS,
    DocumentCat.EXTRACT_FROM_LAND_REGISTER,
    DocumentCat.BUILDING_DESCRIPTION,
    DocumentCat.MINERGIE_CERTIFICATE,
    DocumentCat.PLAN_FLOOR,
    DocumentCat.PLAN_SITUATION,
    DocumentCat.PLAN_CADASTER,
    DocumentCat.PLR_CADASTRE,
    DocumentCat.PLAN_ANY,
    # DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM,
    # DocumentCat.USER_REGULATIONS_CONDOMINIUM
]

doc_cut_configurations: Dict[DocumentCat, DocumentCutConfiguration] = {
    # 210
    DocumentCat.PASSPORT_CH: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.IDENTITY_MISC], min_rel_dominance=0.5
    ),
    DocumentCat.PASSPORT_DE: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.IDENTITY_MISC], min_rel_dominance=0.5
    ),
    DocumentCat.PASSPORT_IT: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.IDENTITY_MISC], min_rel_dominance=0.5
    ),
    DocumentCat.PASSPORT_FR: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.IDENTITY_MISC], min_rel_dominance=0.5
    ),
    DocumentCat.CRIF_DATA_INFO: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.DEBT_COLLECTION_INFORMATION],
        min_rel_dominance=0.75,
    ),
    # 260
    DocumentCat.DIVORCE_DOCUMENT: DocumentCutConfiguration(
        valid_included_doc_cats=[], min_rel_dominance=0.75, min_num_pages=1
    ),
    DocumentCat.DEED_OF_GIFT: DocumentCutConfiguration(
        # It can be a property as a gift. Then the contract looks a bit like a sales contract
        valid_included_doc_cats=[DocumentCat.EXTRACT_FROM_LAND_REGISTER],
        min_rel_dominance=0.75,
        min_page_frequency_dominance=0.5,
        min_num_pages=1,
    ),
    DocumentCat.TAX_DECLARATION: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.TAX_LIST_FINANCIAL_ASSETS,
            DocumentCat.TAX_BILL,
            DocumentCat.TAX_ASSESSMENT,
            DocumentCat.TAX_CALCULATION,
            DocumentCat.TAX_BUDGET,
        ],
        # We do not want to dominate the Tax Assessment if there are more pages of type tax assessment
        min_page_frequency_dominance=0.5,
    ),
    DocumentCat.TAX_ASSESSMENT: DocumentCutConfiguration(min_rel_dominance=0.75),
    # 320
    DocumentCat.TAX_ATTACHMENTS: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.PENSION3A_ACCOUNT,
            DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL,
            DocumentCat.BANK_DOCUMENT,
            DocumentCat.SALARY_CERTIFICATE,
            DocumentCat.BILL_MISC,
        ]
    ),
    # 322
    DocumentCat.BANK_DOCUMENT: DocumentCutConfiguration(
        valid_included_doc_cats=[
            # DocumentCat.PENSION3A_ACCOUNT, DocumentCat.PENSION3A_CREDIT_NOTE,
            DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL
        ]
    ),
    # 334
    DocumentCat.EMPLOYMENT_CONTRACT: DocumentCutConfiguration(
        min_rel_dominance=0.8,  # basically all pages must be classified correctly
        valid_included_doc_cats=[],
    ),
    # 371
    DocumentCat.LEASING_AGREEMENT: DocumentCutConfiguration(min_rel_dominance=0.8),
    # 372
    DocumentCat.CONSUMER_LOAN: DocumentCutConfiguration(min_rel_dominance=0.75),
    # 405
    DocumentCat.PENSION_SIMULATION1: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.EXTRACT_AHV_ACCOUNT], min_rel_dominance=0.7
    ),
    # 410
    DocumentCat.PENSION_CERTIFICATE: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.PENSION_CERTIFICATE_LETTER,
            DocumentCat.PENSION_CERTIFICATE_SIM_ALL,
            DocumentCat.PENSION_REGULATIONS,
            DocumentCat.PENSION_WITHDRAWL,
            DocumentCat.PENSION_CERTIFICATE_INFO,
        ],
        # can't have min/max because normal certificate is 1-3 pages but with letter or regulations can be 7
        min_rel_dominance=0.9,  # This is only a "long document" if we are very certain... else do the regular splitting
    ),
    # 411
    DocumentCat.PENSION_REGULATIONS: DocumentCutConfiguration(
        invalid_included_doc_cats=[DocumentCat.PENSION_CERTIFICATE],
        min_rel_dominance=0.75,
        min_page_frequency_dominance=0.5,
        min_num_pages=2,
    ),
    # 413
    DocumentCat.PENSION_WITHDRAWL: DocumentCutConfiguration(
        min_rel_dominance=0.75, min_num_pages=1
    ),
    # 415
    DocumentCat.PENSION_CERTIFICATE_LETTER: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.PENSION_REGULATIONS,
            DocumentCat.PENSION_WITHDRAWL,
            DocumentCat.PENSION_CERTIFICATE_INFO,
        ],
        min_rel_dominance=0.85,
    ),
    # 440
    DocumentCat.PENSION3A_INSURANCE_CONTRACT: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.PENSION3_REGULATIONS,
            DocumentCat.PENSION3A_INSURANCE_LETTER_REDEMPTION,
        ],
        min_page_frequency_dominance=0.51,
    ),
    # 441
    DocumentCat.PENSION3A_INSURANCE_LETTER_REDEMPTION: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PENSION3A_INSURANCE_CONTRACT],
        min_page_frequency_dominance=0.51,
    ),
    DocumentCat.PENSION3_INSURANCE_APPLICATION: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PENSION3A_INSURANCE_CONTRACT],
        min_page_frequency_dominance=0.66,
        min_rel_dominance=0.75,
    ),
    DocumentCat.RETIREMENT_ANALYSIS: DocumentCutConfiguration(
        min_page_frequency_dominance=0.75, min_rel_dominance=0.75
    ),
    # 500
    DocumentCat.ZKB_VBV: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PROPERTY_VALUATION],
        page_frequency_weights={DocumentCat.PROPERTY_VALUATION: 0.5},
        min_rel_dominance=0.1,  # very low because file classification is PROPERTY_VALUATION, so if one page is wrongly classified, this is already too strong
        min_page_frequency_dominance=0.51,
    ),
    # 513
    DocumentCat.HRA: DocumentCutConfiguration(
        min_rel_dominance=0.67, min_page_frequency_dominance=0.50
    ),
    # 530
    DocumentCat.FINANCIAL_STATEMENT_COMPANY: DocumentCutConfiguration(
        valid_included_doc_cats=[],
        min_num_pages=2,
        min_page_frequency_dominance=0.75,
        min_rel_dominance=0.75,
    ),
    # 534
    DocumentCat.LIQUIDITY_PLAN_COMPANY: DocumentCutConfiguration(
        min_rel_dominance=0.55
    ),
    # 541
    DocumentCat.ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.FINANCIAL_STATEMENT_COMPANY],
        min_rel_dominance=0.55,
    ),
    # 603
    DocumentCat.PLAN_ANY: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.PLAN_FLOOR,
            DocumentCat.PLAN_SITUATION,
            DocumentCat.PLAN_CADASTER,
            DocumentCat.PLR_CADASTRE,
        ],
        min_num_pages=2,
    ),
    # Have this in here to handle 2 pages (1 situation, 1 unknown / empty)
    DocumentCat.PLAN_SITUATION: DocumentCutConfiguration(),
    # 607
    # If there are just Situation and Cadaster call the document simply 'Cadastre'
    # (and do not make it a PLAN_ANY because one would expect floor plans there)
    DocumentCat.PLAN_CADASTER: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PLAN_SITUATION]
    ),
    # 608
    DocumentCat.GIS_INFO: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PLAN_CADASTER, DocumentCat.PLAN_SITUATION]
    ),
    # 609
    DocumentCat.PROPERTY_DOCUMENTATION: DocumentCutConfiguration(
        valid_included_doc_cats=NORMAL_PROPERTY_ADDITION_CATS
        + [DocumentCat.PROPERTY_VALUATION, DocumentCat.TENANT_DIRECTORY],
        min_rel_dominance=0.75,
        min_num_pages=3,
        max_num_pages=MIN_NUM_PAGES_UNKNOWN_OVERRIDE,
    ),
    # 610
    DocumentCat.SALES_DOCUMENTATION: DocumentCutConfiguration(
        valid_included_doc_cats=NORMAL_PROPERTY_ADDITION_CATS
        + [
            DocumentCat.PROPERTY_DOCUMENTATION,
            DocumentCat.PROPERTY_VALUATION,
            DocumentCat.TENANT_DIRECTORY,
            DocumentCat.PURCHASE_PRICE_LIST_PROPERTY,
            DocumentCat.CONSTRUCTION_REGULATIONS,
            # This will probably be wrong but regulations are quite clear a document of their own so easy to separate
        ],
        min_num_pages=3,
        max_num_pages=MIN_NUM_PAGES_UNKNOWN_OVERRIDE,
    ),
    # 611
    DocumentCat.EXTRACT_FROM_LAND_REGISTER: DocumentCutConfiguration(
        # Can include the bill for the land register
        # Longer documents might have single pages that are incorrectly classified as FOUNDATION_CERTIFICATE_CONDOMINIUM
        # Include these. But a real FOUNDATION_CERTIFICATE_CONDOMINIUM will not match this because
        # min_page_frequency_dominance is too large (no FOUNDATION_CERTIFICATE_CONDOMINIUM has 60% land register pages)
        valid_included_doc_cats=[
            DocumentCat.BILL_MISC,
            DocumentCat.PROPERTY_INFO,
            DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM,
            DocumentCat.PLAN_CADASTER,
            DocumentCat.PROPERTY_VALUATION_GOV,
            DocumentCat.LAND_REGISTER_MISC,
        ],
        min_page_frequency_dominance=0.5,
        # PROPERTY_INFO can occor (a few pages) but this factor acts as a penalty for too many pages
        page_frequency_weights={DocumentCat.PROPERTY_INFO: 2},
    ),
    # 615
    DocumentCat.PROPERTY_PHOTOS: DocumentCutConfiguration(
        min_page_frequency_dominance=0.75,  # 3 pages out of 4 must be certain to be accepted
        min_rel_dominance=0.8,
    ),
    # 616
    DocumentCat.LIST_OF_RENOVATIONS: DocumentCutConfiguration(
        valid_included_page_cats=[
            PageCat.TAX_DECLARATION_RENOVATIONS
        ],  # TODO: not yet implemented
        min_page_frequency_dominance=0.5,
        min_rel_dominance=0.75,
    ),
    # 617
    DocumentCat.PROPERTY_INSURANCE: DocumentCutConfiguration(
        # Plan situation can also be aerial photo
        # This must match almost 100% because else it will be dominated by DocumentCat.PROPERTY_DOCUMENTATION
        # which contains all these (and more) types
        valid_included_doc_cats=[
            DocumentCat.PLAN_CADASTER,
            DocumentCat.PLAN_SITUATION,
            DocumentCat.PLAN_FLOOR,
            DocumentCat.VOLUME_CALCULATION_SIA,
        ],
        min_page_frequency_dominance=0.5,
        min_rel_dominance=0.75,
    ),
    DocumentCat.GEAK_CERTIFICATE: DocumentCutConfiguration(
        # Normally a 4 page document - if one is different it should still work
        min_page_frequency_dominance=0.75,
        min_rel_dominance=0.75,
    ),
    # 620
    DocumentCat.PROPERTY_VALUATION: DocumentCutConfiguration(
        valid_included_doc_cats=NORMAL_PROPERTY_ADDITION_CATS,
        min_page_frequency_dominance=0.2,  # can be low because of e.g. images but needs a few pages still (different for sales documentation)
        min_num_pages=1,  # Accept one page document, e.g. IAZI estimation
        max_num_pages=MIN_NUM_PAGES_UNKNOWN_OVERRIDE,
    ),
    # 621
    DocumentCat.VOLUME_CALCULATION_SIA: DocumentCutConfiguration(
        min_num_pages=1, min_page_frequency_dominance=0.8
    ),
    # 625
    DocumentCat.PROPERTY_VALUATION_GOV: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PROPERTY_VALUATION],
        min_num_pages=1,
        min_page_frequency_dominance=0.5,
    ),
    # 626
    DocumentCat.PLR_CADASTRE: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.PLAN_FLOOR, DocumentCat.PLAN_CADASTER],
        # This is because of a bug in object detection
        min_rel_dominance=0.65,
        min_page_frequency_dominance=0.6,
        page_frequency_weights={
            # Up to 50% of pages can have a plan cadastre on it and it is still a plr
            DocumentCat.PLAN_CADASTER: 0.1
        },
    ),
    # 628
    DocumentCat.PROPERTY_ACCOUNTS: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.FINANCIAL_STATEMENT_COMPANY,
            DocumentCat.MEETING_MINUTES_CONDOMINIUM,
            # TODO: make Erneuerungsfonds a separate group in this
            DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL,
        ],
        min_rel_dominance=0.75,
        min_page_frequency_dominance=0.4,
    ),
    # 630
    DocumentCat.USER_REGULATIONS_CONDOMINIUM: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM,
            DocumentCat.CONDOMINIUM_MIX,
            DocumentCat.EXTRACT_FROM_LAND_REGISTER,
            DocumentCat.PLAN_FLOOR,
            DocumentCat.REGISTRATION_LAND_REGISTER,  # Anmeldung
        ],
        # At least 40% of all pages must be FOUNDATION_CERTIFICATE_CONDOMINIUM
        # Can have CONDOMINIMU_MIX and PLAN_FLOOR
        min_page_frequency_dominance=0.4,
        min_rel_dominance=0.7,
        min_num_pages=2,  # can be short e.g. for garage
        page_frequency_weights={
            DocumentCat.CONDOMINIUM_MIX: 0.1,
            DocumentCat.USER_REGULATIONS_CONDOMINIUM: 0.2,
        },
    ),
    # 631
    DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.USER_REGULATIONS_CONDOMINIUM,
            DocumentCat.CONDOMINIUM_MIX,
            DocumentCat.EXTRACT_FROM_LAND_REGISTER,
            DocumentCat.PLAN_FLOOR,
            # DocumentCat.BUILDING_DESCRIPTION,  # added 20221006, removed again 20230126
            DocumentCat.REGISTRATION_LAND_REGISTER,  # Anmeldung
            DocumentCat.NOTARY_MISC,  # Needed for last page 'Beglaubigung'
            DocumentCat.POWER_OF_ATTORNEY,  # Happens in French docs
        ],
        # At least 40% of all pages must be FOUNDATION_CERTIFICATE_CONDOMINIUM
        min_page_frequency_dominance=0.4,
        # Can have CONDOMINIMU_MIX and PLAN_FLOOR
        # These typs are to be expected and should not draw down the page freq too much.
        page_frequency_weights={
            DocumentCat.CONDOMINIUM_MIX: 0.1,
            DocumentCat.USER_REGULATIONS_CONDOMINIUM: 0.15,
            DocumentCat.EXTRACT_FROM_LAND_REGISTER: 0.15,
            DocumentCat.PLAN_FLOOR: 0.10,
            DocumentCat.PLAN_ANY: 0.15,
            DocumentCat.PLAN_CADASTER: 0.15,
            DocumentCat.REGISTRATION_LAND_REGISTER: 0.25,
        },
        min_rel_dominance=0.7,
        min_num_pages=1,  # can be short e.g. for garage
    ),
    # 632
    DocumentCat.MEETING_MINUTES_CONDOMINIUM: DocumentCutConfiguration(
        # valid_included_doc_cats=[DocumentCat.PROPERTY_ACCOUNTS],
        # min_page_frequency_dominance=0.25,
        min_rel_dominance=0.75,
        min_num_pages=1,
    ),
    # 638
    DocumentCat.CONDOMINIUM_MIX: DocumentCutConfiguration(
        # This should never be an individual document but only be included in
        # USER_REGULATIONS_CONDOMINIUM or FOUNDATION_CERTIFICATE_CONDOMINIUM
        # Therefore make dominance impossible to achieve
        min_rel_dominance=1.1
    ),
    # 640
    DocumentCat.TENANT_DIRECTORY: DocumentCutConfiguration(
        min_rel_dominance=0.65,  # 2 out of 3 pages must be this
        must_be_long_document=False,  # Often single pages that should be extracted
        min_doc_confidence=0.7,
        min_page_frequency_dominance=0.51,
    ),
    # 641
    DocumentCat.TENANCY_AGREEMENT: DocumentCutConfiguration(
        must_be_long_document=False,  # Often single pages that should be extracted
        min_re_dominance=0.65,  # 2 out of 3 pages must be this
        min_doc_confidence=0.7,
        min_page_frequency_dominance=0.51,
    ),
    # 650
    DocumentCat.BUILDING_RIGHTS_AGREEMENT: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.REGISTRATION_LAND_REGISTER,  # Anmeldung
            DocumentCat.NOTARY_MISC,  # Needed for last page 'Beglaubigung'
        ],
        min_rel_dominance=0.75,
        min_num_pages=3,
    ),
    # 660
    DocumentCat.CONTRACT_OF_SALE: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.EXTRACT_FROM_LAND_REGISTER,
            DocumentCat.AGREEMENT_CHARGE_IMMOVABLE_PROPERTY,
            DocumentCat.CONTRACT_GENERAL_CONTRACTOR,
            DocumentCat.CONTRACT_TOTAL_CONTRACTOR,
            DocumentCat.CONSTRUCTION_CONTRACT,
            DocumentCat.POWER_OF_ATTORNEY,
            DocumentCat.PLAN_FLOOR,
            DocumentCat.NOTARY_MISC,  # Needed for last page 'Beglaubigung'
        ],
        # These typs are to be expected and should not draw down the page freq too much.
        page_frequency_weights={
            DocumentCat.CONDOMINIUM_MIX: 0.1,
            DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 0.1,
            DocumentCat.PLAN_FLOOR: 0.10,
            DocumentCat.PLAN_CADASTER: 0.15,
        },
        # TODO: find better solution for low page frequency with many pages of land register
        min_rel_dominance=0.70,
        # this was 0.5, see if 0.34 is too low. Problem is 40 pages with 25 pages land register
        # 240806 mt: increased from 0.3 to 0.34 but added page_frequency_weights
        min_page_frequency_dominance=0.34,
        min_num_pages=3,
    ),
    DocumentCat.RESERVATION_CONTRACT: DocumentCutConfiguration(
        # 210916 mt: added CONTRACT_OF_SALE and min_page_frequency_dominance=0.5
        valid_included_doc_cats=[
            DocumentCat.CONTRACT_OF_SALE,
            DocumentCat.PLAN_ANY,
            DocumentCat.PLAN_SITUATION,
            DocumentCat.PLAN_CADASTER,
        ],
        min_page_frequency_dominance=0.5,
        min_rel_dominance=0.8,
    ),
    # 670
    DocumentCat.BUILDING_DESCRIPTION: DocumentCutConfiguration(
        # For an individual document we need to be strict, rather make it unknown than wrongly assigned
        valid_included_doc_cats=[
            DocumentCat.PROPERTY_INFO,
            DocumentCat.VOLUME_CALCULATION_SIA,
            DocumentCat.CONSTRUCTION_COMPANY_LIST,
        ],
        min_page_frequency_dominance=0.51,
        min_rel_dominance=0.7,
        min_num_pages=1,  # Kurzbaubeschrieb has only 2 pages, ZKB Summary only 1 page
    ),
    # 671
    DocumentCat.CONSTRUCTION_REQUEST: DocumentCutConfiguration(
        # For an individual document we need to be strict, rather make it unknown than wrongly assigned
        valid_included_doc_cats=[
            DocumentCat.BUILDING_DESCRIPTION,
            DocumentCat.PLAN_CADASTER,
            DocumentCat.PLAN_SITUATION,
            DocumentCat.PLAN_FLOOR,
        ],
        min_page_frequency_dominance=0.51,
        min_rel_dominance=0.7,
        min_num_pages=1,  # Kurzbaubeschrieb has only 2 pages, ZKB Summary only 1 page
    ),
    # 672
    DocumentCat.CONSTRUCTION_PERMIT: DocumentCutConfiguration(min_num_pages=1),
    # 673
    DocumentCat.CONSTRUCTION_REGULATIONS: DocumentCutConfiguration(
        min_rel_dominance=0.7
    ),
    # 675
    DocumentCat.CONTRACT_GENERAL_CONTRACTOR: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.CONSTRUCTION_REGULATIONS],
        min_rel_dominance=0.7,
        min_page_frequency_dominance=0.51,  # can be low (because of many plans) but not too low
        min_num_pages=2,
    ),
    # 677
    DocumentCat.CONSTRUCTION_CONTRACT: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.CONSTRUCTION_REGULATIONS],
        # For an individual document we need to be strict, rather make it unknown than wrongly assigned
        min_rel_dominance=0.7,
        min_page_frequency_dominance=0.3,  # can be low (because of many plans) but not too low
        min_num_pages=2,
    ),
    # 680
    DocumentCat.CONSTRUCTION_COST_ESTIMATE: DocumentCutConfiguration(
        min_rel_dominance=0.7, min_page_frequency_dominance=0.66
    ),
    # 681
    DocumentCat.CONSTRUCTION_QUOTATION: DocumentCutConfiguration(
        min_rel_dominance=0.7, min_page_frequency_dominance=0.66
    ),
    # 682
    DocumentCat.PROJECT_BUDGET: DocumentCutConfiguration(
        min_rel_dominance=0.7, min_page_frequency_dominance=0.66
    ),
    # 683
    DocumentCat.CONSTRUCTION_COST_SUMMARY: DocumentCutConfiguration(
        min_rel_dominance=0.7, min_page_frequency_dominance=0.66
    ),
    # 687
    DocumentCat.CONSTRUCTION_INSURANCE: DocumentCutConfiguration(
        min_rel_dominance=0.7, min_page_frequency_dominance=0.66
    ),
    # 705
    DocumentCat.MORTGAGE_REQUEST_FORM: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.AUTHORIZATION_FOR_INQUIRIES,
            DocumentCat.FINANCING_CONFIRMATION,
            DocumentCat.FINANCING_CHECKLIST_DOCUMENTS,
            DocumentCat.FINANCING_FEES_LIST,
            DocumentCat.US_PERSON_FORM,
            DocumentCat.BROKER_MANDATE,
            DocumentCat.BROKER_AUTHORIZATION,
        ],
        min_rel_dominance=0.75,
    ),
    # 705
    DocumentCat.US_PERSON_FORM: DocumentCutConfiguration(
        # There could be an almost empty page inside (wrong print margins)
        min_rel_dominance=0.75,
        min_page_frequency_dominance=0.6,
    ),
    # 720
    DocumentCat.MORTGAGE_CONTRACT: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION,
            DocumentCat.TRANSFER_OF_SECURITY,
            DocumentCat.TERMS_AND_CONDITIONS,
        ],
        min_num_pages=1,  # E.g. Migrosbank has 1 page contract and rest in product document
    ),
    # 721
    DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.MORTGAGE_CONTRACT,
            DocumentCat.TERMS_AND_CONDITIONS,
        ],
        min_rel_dominance=0.8,
        min_page_frequency_dominance=0.51,
    ),
    # 722
    DocumentCat.MORTGAGE_FRAMEWORK_CONTRACT: DocumentCutConfiguration(
        # We have framework contracts only by rule. So if a rule matches the whole document should be a framework contract
        valid_included_doc_cats=[DocumentCat.MORTGAGE_CONTRACT],
        min_num_pages=3,
    ),
    DocumentCat.FINANCING_CONFIRMATION: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.FINANCING_OFFER], max_num_pages=2
    ),
    # 725
    DocumentCat.FINANCING_OFFER: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.FINANCING_CONFIRMATION,
            DocumentCat.FINANCING_CHECKLIST_DOCUMENTS,
            DocumentCat.FINANCING_FEES_LIST,
            DocumentCat.US_PERSON_FORM,
        ],
        min_rel_dominance=0.75,
    ),
    # 742
    DocumentCat.TRANSFER_OF_SECURITY: DocumentCutConfiguration(min_rel_dominance=0.65),
    # 748
    DocumentCat.DEBT_CERTIFICATE: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.EXTRACT_FROM_LAND_REGISTER,
            DocumentCat.TRANSFER_OF_SECURITY,
            DocumentCat.BUILDING_RIGHTS_AGREEMENT,
        ],
        min_rel_dominance=0.75,
    ),
    # 767
    DocumentCat.BROKER_MANDATE: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.TERMS_AND_CONDITIONS,
            DocumentCat.FINANCING_MISC,
        ],
        min_rel_dominance=0.75,
        min_page_frequency_dominance=0.6,
    ),
    # 772
    DocumentCat.PLATFORM_AGREEMENT: DocumentCutConfiguration(
        valid_included_doc_cats=[DocumentCat.US_PERSON_FORM],
        min_rel_dominance=0.75,
        min_page_frequency_dominance=0.6,
    ),
    # 785
    DocumentCat.AGREEMENT_CHARGE_IMMOVABLE_PROPERTY: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.POWER_OF_ATTORNEY,
            DocumentCat.LAND_REGISTER_MISC,
        ],
        min_page_frequency_dominance=0.5,  # can have half empty page and 'power of attorney'
        min_rel_dominance=0.7,
    ),
    # 799
    DocumentCat.FINANCING_MISC: DocumentCutConfiguration(min_rel_dominance=0.8),
    DocumentCat.MB_SOKO: DocumentCutConfiguration(min_rel_dominance=0.5),
    # If majority of document is in another language, make the whole document unknown
    DocumentCat.UNKNOWN_FR: DocumentCutConfiguration(),
    DocumentCat.UNKNOWN_EN: DocumentCutConfiguration(),
    DocumentCat.UNKNOWN_IT: DocumentCutConfiguration(),
    # DocumentCat.BEKB_GRUNDSATZ: DocumentCutConfiguration(
    #     min_rel_dominance=0.1
    # ),
    DocumentCat.BEKB_EKD108: DocumentCutConfiguration(
        min_page_frequency_dominance=0.5,
        # Full file classification will be wrong (e.g. Liegenschaftsschatzung), therefore rel dominance really low
        min_rel_dominance=0.55,
    ),
    DocumentCat.BEKB_FIPLA_RESULT: DocumentCutConfiguration(
        min_page_frequency_dominance=0.75
    ),
    DocumentCat.BEKB_EKD142: DocumentCutConfiguration(
        min_page_frequency_dominance=0.8, min_rel_dominance=0.8
    ),
    DocumentCat.BEKB_FIPLA_FORM: DocumentCutConfiguration(
        valid_included_doc_cats=[
            DocumentCat.PROPERTY_PHOTOS  # First page detected incorrectly
        ],
        min_page_frequency_dominance=0.55,  # use page frequency instead of rel_dominance because first page is PROPERTY_PHOTOS
        min_num_pages=4,
    ),
    DocumentCat.ZKB_18401_LEASINGANTRAG_FIRMENANGABE: DocumentCutConfiguration(
        min_rel_dominance=0.55,
        # Do not rely on page_frequency because the file classifier is UNKNOWN
        # and that is handled like a page in the page frequency calculation
        # (so 1 correct and 1 unknown page yield page_frequency_dominance
        # of only 0.333)
        min_page_frequency_dominance=0.3,
    ),
    # Unternehmensprofil
    DocumentCat.ZKB_19747_UNTERNEHMENSPROFIL: DocumentCutConfiguration(
        min_rel_dominance=0.66, min_page_frequency_dominance=0.5
    ),
}
