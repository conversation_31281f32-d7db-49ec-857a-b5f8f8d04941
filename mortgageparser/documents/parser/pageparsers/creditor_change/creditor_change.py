from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementArea,
    SearchElementConstrainedArea,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
    TemplateGenericLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)


@dataclass
class TemplateCreditorChangePageParser(TemplateGenericLetterPageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.CREDITOR_CHANGE


def get_parsers_creditor_change():
    parsers = [
        TemplateCreditorChangePageParser(
            desc="Generic Creditor Change Letter DE 2021",
            required_tokens=[
                "<PERSON><PERSON><PERSON>ubigerwe<PERSON>el",
                "Schuldbrief",
                "<PERSON>rundbu<PERSON>",
                "<PERSON><PERSON> geehrte",
                "Beiliegend erhalten Sie den/die Gläubigerwechsel",
            ],
            se=GenericLetterSearchElements(
                document_date=SearchElementConstrainedArea(
                    None, None, text_bottom="Sehr geehrte"
                )
            ),
            debug_breakpoint=False,
        ),
        TemplateCreditorChangePageParser(
            desc="Generic Creditor Change Form DE 2021",
            ranked_titles_all=[RankedTitle("Gläubigerwechsel")],
            required_tokens=["Die Kosten für diesen Gläubigerwechsel"],
            se=GenericLetterSearchElements(
                # Take date from lower half... best effort as the top half can contain the creation date of
                # the debt certificate
                document_date=SearchElementArea(
                    None, None, y_range=PercentageRange(0.5, 1)
                )
            ),
        ),
    ]
    return parsers
