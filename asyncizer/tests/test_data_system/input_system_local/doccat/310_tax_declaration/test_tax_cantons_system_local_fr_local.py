from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, util_test_batch, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat


@pytest.mark.asyncio
async def test_ju_single_2014(
    override_existing=True, show_page=-1, show_filename=None, webbrowser=True
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/310_tax/cantons_fr/mix/"
        ),
        dest_folder_prefix="310_tax_cantons_",
        source_file_filter=["310_ju_single_2014.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    # 201101 Only personal data available
                    "canton_short": "JU",
                    "year": "2014",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "2800",
                    "city": "DELEMONT",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": "756.9114.8096.00",
                    "p1_date_of_birth": "03.11.1976",
                    "p1_profession": "Expert-Comptable",
                    "p1_employer": "Ernst & Young",
                    "phone_primary": "0792492414",
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
    )


@pytest.mark.asyncio
async def test_vd_double_2016_good_scan(
    override_existing=True, show_page=-2, show_filename=None, webbrowser=True
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/310_tax/cantons_fr/mix/"
        ),
        dest_folder_prefix="310_tax_cantons_",
        source_file_filter=["310_vd_double_2016_good_scan.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    # 201101 Only personal data available
                    "canton_short": "VD"
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
    )
