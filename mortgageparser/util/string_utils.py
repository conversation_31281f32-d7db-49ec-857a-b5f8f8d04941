import logging
import os
import re
import time
from typing import List

import <PERSON><PERSON>htein
import unidecode
from fuzzysearch import find_near_matches, Match

from hypodossier.util.basis_string_util import remove_whitespace

import structlog

logger = structlog.getLogger(__name__)

DEFAULT_HAMMING_DISTANCE = -1
DEFAULT_FUZZY_RATIO = 95

# Global dictionary to store fuzzy match timings
fuzzy_match_timings = []


def reset_string_utils_log():
    global string_utils_log
    string_utils_log.clear()


string_utils_log = []


def get_hamming_dist_threshold(needle: str, p_hamming_dist: int):
    """

    :param needle:
    :param p_hamming_dist: If this is an int it is the valid hamming distance. If this is a float < 1 then this is the percentage of chars that can be wrong
    :return:
    """
    len_needle = len(needle)
    if p_hamming_dist == DEFAULT_HAMMING_DISTANCE:
        threshold = round(len_needle / 8)
        threshold = min(threshold, 4)
        return threshold
    elif not isinstance(p_hamming_dist, int) and p_hamming_dist < 1:
        threshold = round(len_needle * p_hamming_dist)
        return threshold
    else:
        return p_hamming_dist


# Apply substring_after repeatedly for each element of needles.
# Only if all needles appear in haystack in the correct order the result will be non-empty
def substring_after_list(
    haystack: str,
    needles: List[str],
    include_needle=False,
    p_hamming_dist=DEFAULT_HAMMING_DISTANCE,
    ignore_whitespace_for_hamming=True,
    return_haystack_if_not_found=False,
):
    s = haystack
    for needle in needles:
        s = substring_after(
            s,
            needle,
            include_needle,
            p_hamming_dist,
            ignore_whitespace_for_hamming,
            return_haystack_if_not_found=return_haystack_if_not_found,
        )
        if not s:
            return ""
    return s


# Check if needle is in haystack
# p_hamming_dist Number of character differences that is allowed
# ignore_whitespace_for_hamming If this is true then whitespace will not be counted as a difference in the hamming calc
def substring_after(
    haystack: str,
    needle: str,
    include_needle=False,
    p_hamming_dist=DEFAULT_HAMMING_DISTANCE,
    ignore_whitespace_for_hamming=True,
    return_haystack_if_not_found=True,
):
    if not needle or not haystack:
        return haystack

    chunks = haystack.split(needle, maxsplit=1)
    if len(chunks) > 1:
        if include_needle:
            return needle + chunks[1]
        else:
            return chunks[1]
    else:
        hamming_dist = get_hamming_dist_threshold(needle, p_hamming_dist)
        if hamming_dist > 0 or ignore_whitespace_for_hamming:
            # try again with approximation:
            # hamm_distance, sub, index = calc_min_dist(haystack, needle, ignore_whitespace=ignore_whitespace_for_hamming)
            hamm_distance, sub, index = calc_min_dist_fuzzysearch(
                haystack,
                needle,
                hamming_dist,
                ignore_whitespace=ignore_whitespace_for_hamming,
            )

            if hamm_distance <= hamming_dist:
                s = haystack[index:]
                if not include_needle:
                    s = s[
                        len(needle) :
                    ]  # Do not use sub here but needle as it might contain whitespace
                return s

    if return_haystack_if_not_found:
        return haystack


def substring_after_newline(haystack: str):
    return substring_after(haystack, "\n", include_needle=False, p_hamming_dist=0)


# return full text if needle not found
def substring_before(
    haystack: str,
    needle: str,
    include_needle=False,
    p_hamming_dist=DEFAULT_HAMMING_DISTANCE,
    ignore_whitespace_for_hamming=True,
    return_haystack_if_not_found=True,
):
    if not needle or not haystack:
        return haystack

    chunks = haystack.split(needle, maxsplit=1)
    if len(chunks) > 1:
        if include_needle:
            return chunks[0] + needle
        else:
            return chunks[0]
    elif p_hamming_dist != 0:
        hamming_dist = get_hamming_dist_threshold(needle, p_hamming_dist)
        if hamming_dist > 0 or ignore_whitespace_for_hamming:
            # try again with approximation:
            # distance, sub, index = calc_min_dist(haystack, needle, ignore_whitespace=ignore_whitespace_for_hamming)
            distance, sub, index = calc_min_dist_fuzzysearch(
                haystack,
                needle,
                hamming_dist,
                ignore_whitespace=ignore_whitespace_for_hamming,
            )

            if distance <= hamming_dist:
                s = haystack[:index]
                if not include_needle:
                    s = s[
                        : len(needle)
                    ]  # Do not use sub here but needle as it might contain whitespace
                return s

    if return_haystack_if_not_found:
        return haystack


def contains_string(
    haystack,
    needle,
    ignore_case=True,
    ignore_whitespace=True,
    p_hamming_dist=DEFAULT_HAMMING_DISTANCE,
    simplify_string=True,
) -> bool:
    global string_utils_log

    if not haystack:
        string_utils_log.append(
            f"calc_hamming_dist(needle={needle}, ignore_case={ignore_case}, ignore_whitespace={ignore_whitespace}, p_hamming_dist={p_hamming_dist}, simplify_string={simplify_string}) -> haystack was empty -> found=False"
        )
        return False
    if not needle:
        string_utils_log.append(
            f"calc_hamming_dist(needle={needle}, ignore_case={ignore_case}, ignore_whitespace={ignore_whitespace}, p_hamming_dist={p_hamming_dist}, simplify_string={simplify_string}) -> needle was empty -> found=False"
        )
        return False

    hamming_dist = get_hamming_dist_threshold(needle, p_hamming_dist)

    h = haystack
    n = needle
    if ignore_case:
        h = haystack.lower()
        n = needle.lower()
    if ignore_whitespace:
        h = remove_whitespace(h)
        n = remove_whitespace(n)

    if not h:
        string_utils_log.append(
            f"calc_hamming_dist(needle={needle}, ignore_case={ignore_case}, ignore_whitespace={ignore_whitespace}, p_hamming_dist={p_hamming_dist}, simplify_string={simplify_string}) -> h was empty -> found=False"
        )
        return False
    if not n:
        string_utils_log.append(
            f"calc_hamming_dist(needle={needle}, ignore_case={ignore_case}, ignore_whitespace={ignore_whitespace}, p_hamming_dist={p_hamming_dist}, simplify_string={simplify_string}) -> n was empty -> found=False"
        )
        return False

    found = n in h
    if not found and hamming_dist > 0:
        if simplify_string:
            h = remove_accents_and_simplify(h)
            n = remove_accents_and_simplify(n)

        distance, sub, index = calc_min_dist_fuzzysearch(
            h, n, hamming_dist, ignore_whitespace=ignore_whitespace
        )
        if 0 <= distance <= hamming_dist:
            # distance == 0 can happen if searched string has an accent
            # and was therefore no exact hit in the simple search
            if logging.getLogger().isEnabledFor(logging.DEBUG):
                logger.debug(
                    f'Found substring with hamming distance {distance}: needle="{needle}", sub="{sub}", index="{index}", haystack="{haystack}"\n\n'
                )
            found = distance <= hamming_dist
        if found:
            string_utils_log.append(
                f"calc_hamming_dist(needle={needle}, dist={p_hamming_dist}) -> fuzzy(dist={distance}, sub={sub}, index={index}) -> hamming_dist={hamming_dist}, found={found}"
            )
        else:
            string_utils_log.append(
                f"calc_hamming_dist(needle={needle}, ignore_case={ignore_case}, ignore_whitespace={ignore_whitespace}, p_hamming_dist={p_hamming_dist}, simplify_string={simplify_string}) -> fuzzy(dist={distance}, sub={sub}, index={index}) -> hamming_dist={hamming_dist}, found={found}"
            )

    else:
        if found:
            string_utils_log.append(
                f"calc_hamming_dist(needle={needle}) -> exact string match. hamming_dist={hamming_dist}, found={found}"
            )
        else:
            string_utils_log.append(
                f"calc_hamming_dist(needle={needle}, ignore_case={ignore_case}, ignore_whitespace={ignore_whitespace}, p_hamming_dist={p_hamming_dist}, simplify_string={simplify_string}) -> exact string match. hamming_dist={hamming_dist}, found={found}"
            )

    return found


def count_strings(
    haystack,
    needles,
    ignore_case=True,
    ignore_whitespace=True,
    dist=DEFAULT_HAMMING_DISTANCE,
) -> bool:
    count = 0
    for needle in needles:
        if contains_string(
            haystack,
            needle,
            ignore_case=ignore_case,
            ignore_whitespace=ignore_whitespace,
            p_hamming_dist=dist,
        ):
            count += 1

    return count


def contains_all_strings(
    haystack: str,
    needles: List[str],
    ignore_case=True,
    ignore_whitespace=True,
    hamming_dist=DEFAULT_HAMMING_DISTANCE,
) -> bool:
    if not haystack or not needles:
        return False
    if not isinstance(haystack, str):
        raise Exception(f"Invalid haystack={haystack} for needles={needles}")
    if not isinstance(needles, List):
        raise Exception(f"Invalid needles={needles} for haystack={haystack}")
    for needle in needles:
        if not contains_string(
            haystack, needle, ignore_case, ignore_whitespace, hamming_dist
        ):
            return False
    return True


def contains_at_least_one_string(
    haystack: str,
    needles: List[str],
    ignore_case=True,
    ignore_whitespace=True,
    hamming_dist=DEFAULT_HAMMING_DISTANCE,
) -> bool:
    if not haystack or not needles:
        return False
    if not isinstance(needles, List):
        raise ValueError(
            f"Invalid needles. Must be list but is '{needles}. Haystack={haystack}"
        )
    if not isinstance(ignore_case, bool):
        raise ValueError(
            f"Invalid needles or ignore_case. Must be list but is '{needles}. ignore_case={ignore_case}, Haystack={haystack}"
        )
    for needle in needles:
        if contains_string(
            haystack, needle, ignore_case, ignore_whitespace, hamming_dist
        ):
            return True
    return False


# return tuple (index of the closest substring, hamming distance of closest substring, closest substring itself
# impl from here: https://stackoverflow.com/questions/55271961/finding-the-closest-sub-string-by-hamming-distance
def calc_min_dist(haystack, needle, ignore_whitespace=False):
    n = remove_whitespace(needle) if ignore_whitespace else needle
    h = remove_whitespace(haystack) if ignore_whitespace else haystack

    if len(h) < len(n):
        raise ValueError(
            f"haystack({len(h)}) must not be smaller than needle ({len(n)})"
        )

    length_n = len(n)
    substrings = [h[i : i + length_n] for i in range(0, len(h) - length_n + 1)]

    result = min(
        (Levenshtein.distance(n, sub), sub, i) for i, sub in enumerate(substrings)
    )
    return result


# Use fuzzysearch and just transform the result to match old usage pattern
def calc_min_dist_fuzzysearch(
    haystack,
    needle,
    max_distance,
    ignore_whitespace=False,
    # No timing for production as this creates a memory leak!
    threshold_seconds_for_slow_timings=-1,
):
    n = remove_whitespace(needle) if ignore_whitespace else needle
    h = remove_whitespace(haystack) if ignore_whitespace else haystack

    max_del = int(len(needle) / 3)

    start_time = time.time()

    x = find_near_matches(
        n, h, max_distance, max_l_dist=max_distance, max_deletions=max_del
    )
    end_time = time.time()
    duration = end_time - start_time
    if 0 < threshold_seconds_for_slow_timings < duration:
        fuzzy_match_timings.append(
            (needle, len(haystack), len(h), max_distance, duration)
        )

    if len(x) > 0:
        m: Match
        m = x[0]
        return m.dist, m.matched, m.start

    return 99999999, "", 0


# Remove ugly chars like strange apostroph and replace by regular characters
# Text will be fully readable, no changes are made that are gramatically not valid
# TODO: add this stuff https://www.cl.cam.ac.uk/~mgk25/ucs/quotes.html
def clean_text(text: str):
    if text:
        s = text
        s = s.replace("–", "-")  # longer than normal hyphen
        s = s.replace("\*********", "-")
        s = s.replace("""’""", "'").replace("´", "'")
        s = s.replace("^", "'").strip()
        return s
    return None


# '35'051.95'


# Do all kinds of simplifications that are not too aggressive
# - remove accents
# - fix apostrophs
#
def remove_accents_and_simplify(text: str):
    if text:
        s = clean_text(text)
        s = unidecode.unidecode(s)
        return s


def remove_empty_lines(text: str, remove_lines_whitespace=True):
    if text:
        if remove_lines_whitespace:
            t = os.linesep.join([s for s in text.splitlines() if s.strip()])
        else:
            t = os.linesep.join([s for s in text.splitlines() if s])
        return t


def contains_any_month_name_de(text):
    return contains_at_least_one_string(
        text,
        [
            "Januar",
            "Februar",
            "März",
            "April",
            "Mai",
            "Juni",
            "Juli",
            "August",
            "September",
            "Oktober",
            "November",
            "Dezember",
        ],
    )


# Format page numbers consistently by removing whitespace to 'ABC Seite 77/118 XYZ'
def format_page_numbers(text: str):
    if text:
        pattern = re.compile(
            r"""
            (.*)        # any Text
            (Seite|Page|Pagina)     # 'Seite'
            \s*         # Whitespace
            (\d+)       # Current page number
            \s*.\s*    # Separator '/'
            (\d+)       # Max page number
            (.*)        # any Text
            """,
            re.VERBOSE,
        )
        newtext = re.sub(pattern, "\\1\\2 \\3/\\4\\5", text, re.VERBOSE)
        return newtext
    return text


def extract_word_after(text, keyword, hammming_distance=0):
    if keyword in text:
        t = substring_after(
            text,
            keyword,
            ignore_whitespace_for_hamming=False,
            p_hamming_dist=hammming_distance,
        )
        if t:
            t = t.split(" ", maxsplit=2)
            return t[1]
