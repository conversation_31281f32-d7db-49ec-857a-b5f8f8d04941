from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/630_user_reg_condominium"
)
dest_folder_prefix = "input_system_local_doccat_630_user_reg_condomiumn"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_user_reg_aenderung_stwe():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["630_aenderung_stwe.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.USER_REGULATIONS_CONDOMINIUM: 1},
            # Found 1 items for doc_cat_frequency:
            # UNKNOWN_DE
            # Found 19 items for doc_cat_frequency_per_page:
            # CONTRACT_OF_SALE
            # EXTRACT_FROM_LAND_REGISTER
            # EXTRACT_FROM_LAND_REGISTER
            # EXTRACT_FROM_LAND_REGISTER
            # EXTRACT_FROM_LAND_REGISTER
            # EXTRACT_FROM_LAND_REGISTER
            # EXTRACT_FROM_LAND_REGISTER
            # EXTRACT_FROM_LAND_REGISTER
            # BUILDING_RIGHTS_AGREEMENT
            # FOUNDATION_CERTIFICATE_CONDOMINIUM
            # PROPERTY_INFO
            # USER_REGULATIONS_CONDOMINIUM
            # USER_REGULATIONS_CONDOMINIUM
            # USER_REGULATIONS_CONDOMINIUM
            # USER_REGULATIONS_CONDOMINIUM
            # USER_REGULATIONS_CONDOMINIUM
            # FOUNDATION_CERTIFICATE_CONDOMINIUM
            # CONTRACT_OF_SALE
            # NOTARY_MISC
            # --> 5 right (USER_REGULATIONS_CONDOMINIUM)
            # --> 9 included (FOUNDATION_CERTIFICATE_CONDOMINIUM, EXTRACT_FROM_LAND_REGISTER)
            # --> 0 unknown
            # --> 4 wrong
            # --> min_page_frequency_dominance = 5 / (5+9+0+5) = 0.26 ??
            # --> min_rel_dominance = 5 / (5+5) = 0.5
        ),
    ).run()
