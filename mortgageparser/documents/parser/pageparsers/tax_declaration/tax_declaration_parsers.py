from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.ag_tax_declaration_parsers import (
    get_parsers_tax_declaration_ag,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ai.ai_tax_declaration_parsers import (
    get_parsers_tax_declaration_ai,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ar.ar_tax_declaration_parsers import (
    get_parsers_tax_declaration_ar,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.be_tax_declaration_parsers import (
    get_parsers_tax_declaration_be,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bl.bl_tax_declaration_parsers import (
    get_parsers_tax_declaration_bl,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bs.bs_tax_declaration_parsers import (
    get_parsers_tax_declaration_bs,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ch.ch_tax_declaration_parsers import (
    get_parsers_tax_declaration_ch,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.fr.fr_tax_declaration_parsers import (
    get_parsers_tax_declaration_fr,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ge.ge_tax_declaration_parsers import (
    get_parsers_tax_declaration_ge,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.gl.gl_tax_declaration_parsers import (
    get_parsers_tax_declaration_gl,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.gr.gr_tax_declaration_parsers import (
    get_parsers_tax_declaration_gr,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ju.ju_tax_declaration_parsers import (
    get_parsers_tax_declaration_ju,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ne.ne_tax_declaration_parsers import (
    get_parsers_tax_declaration_ne,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.nw.nw_tax_declaration_parsers import (
    get_parsers_tax_declaration_nw,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ow.ow_tax_declaration_parsers import (
    get_parsers_tax_declaration_ow,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.sg_tax_declaration_parsers import (
    get_parsers_tax_declaration_sg,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sh.sh_tax_declaration_parsers import (
    get_parsers_tax_declaration_sh,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.so.so_tax_declaration_parsers import (
    get_parsers_tax_declaration_so,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sz.sz_tax_declaration_parsers import (
    get_parsers_tax_declaration_sz,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tg.tg_tax_declaration_parsers import (
    get_parsers_tax_declaration_tg,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ti.ti_tax_declaration_parsers import (
    get_parsers_tax_declaration_ti,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ur.ur_tax_declaration_parsers import (
    get_parsers_tax_declaration_ur,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.vd.vd_tax_declaration_parsers import (
    get_parsers_tax_declaration_vd,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.vs.vs_tax_declaration_parsers import (
    get_parsers_tax_declaration_vs,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zg.zg_tax_declaration_parsers import (
    get_parsers_tax_declaration_zg,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.zh_tax_declaration_parsers import (
    get_parsers_tax_declaration_zh,
)


def get_tax_declaration_pageparsers():
    pageparsers = (
        get_parsers_tax_declaration_ag()
        + get_parsers_tax_declaration_ai()
        + get_parsers_tax_declaration_ar()
        + get_parsers_tax_declaration_be()
        + get_parsers_tax_declaration_bl()
        + get_parsers_tax_declaration_bs()
        + get_parsers_tax_declaration_fr()
        + get_parsers_tax_declaration_ge()
        + get_parsers_tax_declaration_gl()
        + get_parsers_tax_declaration_gr()
        + get_parsers_tax_declaration_ju()
        + get_parsers_tax_declaration_ne()
        + get_parsers_tax_declaration_nw()
        + get_parsers_tax_declaration_ow()
        + get_parsers_tax_declaration_sg()
        + get_parsers_tax_declaration_so()
        + get_parsers_tax_declaration_sh()
        + get_parsers_tax_declaration_sz()
        + get_parsers_tax_declaration_tg()
        + get_parsers_tax_declaration_ti()
        + get_parsers_tax_declaration_ur()
        + get_parsers_tax_declaration_vd()
        + get_parsers_tax_declaration_vs()
        + get_parsers_tax_declaration_zg()
        + get_parsers_tax_declaration_zh()
        + get_parsers_tax_declaration_ch()
    )
    return pageparsers
