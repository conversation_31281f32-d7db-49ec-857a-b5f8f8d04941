from abbyyplumber.converter.ValueConverter import CleanNameConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.pension3a_insurance.CommonPension3aInsuranceParsers import (
    CommonPension3aInsuranceRedemptionLetterParser,
)
from mortgageparser.documents.parser.pageparsers.pension3a_insurance.GenericPension3aInsurancePageParsers import (
    GenericPension3aInsurance,
)


def get_parsers_pension3a_insurance():
    parsers = [
        SmartLetterPageParser(
            doc_cat=DocumentCat.PENSION3A_INSURANCE_STATEMENT,
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            company="AXA",
            titles=["Police"],
            se_fullname=SearchElementConstrainedArea(
                None,
                None,
                text_top="Police Nr.",
                text_left="Versicherungsnehmer",
                text_bottom="geb.",
                converter=CleanNameConverter(max_num_lines=1),
            ),
            # No doc date on first page
            se_date_of_birth=SearchElementConstrainedArea(
                None,
                None,
                text_top="Versicherungsnehmer",
                text_bottom="Versicherte Person",
                text_left="geb.",
            ),
            required_tokens=[
                "AXA Leben AG",
                "Dynamix 3a",
                "gebundene Vorsorgepolice",
                "Prämienbefreiung bei Erwerbsunfähigkeit",
            ],
        ),
        SmartLetterPageParser(
            doc_cat=DocumentCat.PENSION3A_INSURANCE_STATEMENT,
            page_cat=PageCat.GENERIC_NON_FIRST_PAGE,
            company="AXA",
            titles=["Police"],
            se_document_date=SearchElementConstrainedArea(
                None, None, text_top="Police", text_bottom="Überprüfen Sie bitte"
            ),
            required_tokens=[
                "AXA Leben AG",
                "Dynamix 3a",
                "gebundene Vorsorgepolice",
                "Überprüfen Sie bitte, ob der Inhalt dieser Police",
            ],
        ),
        SmartLetterPageParser(
            doc_cat=DocumentCat.PENSION3A_INSURANCE_STATEMENT,
            page_cat=PageCat.GENERIC_SINGLE_PAGE,
            company="SwissLife",
            titles=["Leistungsblatt für die fondsgebundene Versicherung"],
            se_fullname=SearchElementConstrainedArea(
                None,
                None,
                text_top="Versicherungsjahr",
                text_left="Versicherungsnehmer",
                text_bottom="Versicherte Person",
                converter=CleanNameConverter(),
            ),
            se_document_date=SearchElementConstrainedArea(
                None,
                None,
                text_left="Wert Ihrer Fondsanteile",
                text_top="Vertragsdauer",
                text_bottom="Wertanteil",
            ),
            se_date_of_birth=SearchElementConstrainedArea(
                None,
                None,
                text_top="Versicherungsnehmer",
                text_bottom="Versicherungsbeginn",
                text_left="geboren",
            ),
            required_tokens=[
                "Swiss Life",
                "Leistungsblatt für die fondsgebundene Versicherung",
                "Versicherungsbeginn",
                "Ihre Überschüsse",
            ],
        ),
        GenericPension3aInsurance(
            "Skandia",
            ["Wertübersicht per"],
            None,
            ["Skandia Leben AG", "Wert des Sparkapitals", "Rückkaufswert"],
        ),
        CommonPension3aInsuranceRedemptionLetterParser(),
    ]
    return parsers
