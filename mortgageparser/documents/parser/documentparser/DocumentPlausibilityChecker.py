from typing import List

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.Extractions import Extractions
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticDocument import SemanticDocument

invalid_single_page_documents = [
    DocumentCat.TAX_DECLARATION,
    DocumentCat.SALES_DOCUMENTATION,
    DocumentCat.CONTRACT_OF_SALE,
    DocumentCat.PROPERTY_VALUATION,
]


class DocumentPlausibilityChecker:
    def check_document_plausibility(self, docs: List[SemanticDocument]):
        """Make sure that the proposed documents make sense. When in doubt, just make all pages in the doc unknown"""
        result = []
        unknown_sem_pages = []
        for doc in docs:
            # Check if there is a single page tax document. If yes, make it unknown
            if len(doc.pages) == 1:
                first_page = doc.pages[0]
                if first_page.doc_cat in invalid_single_page_documents:
                    # Found a single page document that must be a longer doc -> make it unknown
                    first_page.doc_cat = DocumentCat.UNKNOWN
                    first_page.page_cat = PageCat.UNKNOWN_XX
                    first_page.extractions = Extractions()

                    unknown_sem_pages.append(first_page)
                else:
                    result.append(doc)
            else:
                result.append(doc)

        return result, unknown_sem_pages
