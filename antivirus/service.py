import asyncio
import json
import uuid
from typing import Optional, Dict

import structlog

import global_settings
from antivirus.exceptions import VirusDetected, AntiVirusScanTimeoutException
from antivirus.schemas import VirusScanRequest, AntiVirusType, VirusScanResponse
from asyncizer.publisher import DossierEventPublisher
from asyncizer.rpc_pika import get_rpc_client
from asyncizer.schemas import (
    FileExtractedExceptionV1Schema,
    OriginalFileProcessingErrorSchema,
)
from global_settings import HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY

logger = structlog.getLogger(__name__)

virus_error_messages: Dict[str, str] = {
    "de": "Ein Virus wurde in der Datei erkannt. Die Verarbeitung der Datei wurde abgebrochen.",
    "en": "A virus was detected in the file. Processing of the file has has been cancelled.",
    "fr": "Un virus a été détecté dans le fichier. Le traitement du fichier a été interrompu.",
    "it": "È stato rilevato un virus nel file. L'elaborazione del file è stata annullata.",
}

EXCEPTION_TYPE_VIRUS_DETECTED = "VIRUS_DETECTED"


async def make_antivirus_request_rpc(
    anti_virus_type: AntiVirusType,
    queue_name,
    file_url: str,
    dossier_uuid=None,
    request_timeout: int = global_settings.HYSCAN_REQUEST_TIMEOUT,
) -> VirusScanResponse:
    virus_scan_request = VirusScanRequest(
        correlation_uuid=str(uuid.uuid4()),
        dossier_uuid=dossier_uuid,
        file_url=file_url,
        anti_virus_type=anti_virus_type,
    )

    logger.info("Making antivirus request", virus_scan_request=virus_scan_request)

    rpc_client = await get_rpc_client()

    if request_timeout:
        try:
            t = asyncio.create_task(
                rpc_client.call(queue_name, virus_scan_request.json())
            )
            response = VirusScanResponse.parse_raw(
                await asyncio.wait_for(t, timeout=request_timeout)
            )
        except asyncio.TimeoutError:
            raise AntiVirusScanTimeoutException(
                anti_virus_type=anti_virus_type,
                queue_name=queue_name,
                file_url=file_url,
                dossier_uuid=dossier_uuid,
                request_timeout=request_timeout,
            )
    else:

        response = VirusScanResponse.parse_raw(
            await rpc_client.call(queue_name, virus_scan_request.json())
        )

    logger.info("Received antivirus response", virus_scan_response=response)

    return response


async def make_antivirus_clamav_request(
    file_url: str, dossier_uuid=None
) -> VirusScanResponse:
    return await make_antivirus_request_rpc(
        anti_virus_type=AntiVirusType.CLAMAV,
        queue_name=HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY,
        file_url=file_url,
        dossier_uuid=dossier_uuid,
    )


async def make_antivirus_microsoft_defender_request(
    file_url: str, dossier_uuid=None
) -> VirusScanResponse:
    return await make_antivirus_request_rpc(
        anti_virus_type=AntiVirusType.MICROSOFTDEFENDER,
        queue_name=HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY,
        file_url=file_url,
        dossier_uuid=dossier_uuid,
    )


async def publish_virus_detected_event_file_extracted_exception(
    dossier_event_publisher: DossierEventPublisher,
    dossier_uuid: str,
    extracted_file_uuid: str,
    details: str,
):
    data = FileExtractedExceptionV1Schema(
        dossier_uuid=dossier_uuid,
        extracted_file_uuid=extracted_file_uuid,
        type="Extracted",
        exception_type=EXCEPTION_TYPE_VIRUS_DETECTED,
        details=details,
        de=virus_error_messages["de"],
        en=virus_error_messages["en"],
        fr=virus_error_messages["fr"],
        it=virus_error_messages["it"],
    )
    await dossier_event_publisher.publish(
        "DossierEvent.FileExtractedExceptionV1", data.json().encode()
    )


async def publish_virus_detected_event_original_file_exception(
    dossier_event_publisher: DossierEventPublisher,
    original_file_uuid: str,
    details: str,
):
    data = OriginalFileProcessingErrorSchema(
        original_file_uuid=original_file_uuid,
        exception_type=EXCEPTION_TYPE_VIRUS_DETECTED,
        exception_de=virus_error_messages["de"],
        exception_en=virus_error_messages["en"],
        exception_fr=virus_error_messages["fr"],
        exception_it=virus_error_messages["it"],
        exception_details=details,
    )
    await dossier_event_publisher.publish(
        "DossierEvent.OriginalFileProcessingError", data.json().encode()
    )


async def handle_antivirus_requests(
    file_url: str,
    dossier_uuid=None,
    dossier_event_publisher: DossierEventPublisher = None,
    message_type: str = None,
    file_uuid: str = None,
    raise_exception: bool = False,
    context: str = None,
) -> Optional[VirusScanResponse]:
    """
    Currently only ClamAV is supported

    @param file_url:
    @param dossier_uuid:
    @param dossier_event_publisher: Publisher used to send the "virus detected" message
    @param message_type: Type of the message that is published if a virus was detected
    @param file_uuid:
    @param raise_exception:
    @return:
    """
    if global_settings.ENABLE_ANTIVIRUS_CLAMAV:
        # when Microsoft Defender is supported, we will add asyncio.gather here
        # Todo: Add timeout?
        try:
            clamav_response: VirusScanResponse = await make_antivirus_clamav_request(
                file_url, dossier_uuid
            )
            clamav_response.context = context

            # fix the mising dossier_uuid which is missing from the response
            if not clamav_response.dossier_uuid:
                clamav_response.dossier_uuid = dossier_uuid

            if clamav_response.detection_result is False:
                logger.info("ClamAV PASSED", clamav_response=clamav_response)
                return clamav_response
            elif clamav_response.detection_result is True:
                logger.exception(
                    "ClamAV detected a virus", clamav_response=clamav_response
                )
                virus_info = {
                    "details": clamav_response.details,
                    "anti_virus_type": str(clamav_response.anti_virus_type),
                    "scan_timestamp": str(clamav_response.scan_timestamp),
                    "anti_virus_version": clamav_response.anti_virus_version,
                    "anti_virus_signature_version": clamav_response.anti_virus_signature_version,
                    "anti_virus_all_info": clamav_response.anti_virus_all_info,
                }
                if dossier_event_publisher and message_type:
                    if message_type == "DossierEvent.FileExtractedExceptionV1":
                        await publish_virus_detected_event_file_extracted_exception(
                            dossier_event_publisher,
                            dossier_uuid,
                            file_uuid,
                            json.dumps(virus_info),
                        )
                    elif message_type == "DossierEvent.OriginalFileProcessingError":
                        await publish_virus_detected_event_original_file_exception(
                            dossier_event_publisher,
                            file_uuid,
                            details=json.dumps(virus_info),
                        )
                raise VirusDetected(file_url, clamav_response)

            else:
                logger.exception(
                    "ClamAV encountered an error", clamav_response=clamav_response
                )
                return clamav_response

        except VirusDetected as vd:
            logger.exception("Virus detected", e=vd, exc_info=True)

            if raise_exception:
                raise vd

            return clamav_response

        except AntiVirusScanTimeoutException as avste:
            logger.exception("ClamAV scan timeout", e=avste, exc_info=True)

        except Exception as e:
            logger.exception("Error with ClamAV", e=e, exc_info=True)

    return None


async def main():
    print("Running main")
    dossier_uuid = str(uuid.uuid4())
    clamav_task = make_antivirus_clamav_request(
        file_url="https://secure.eicar.org/eicar.com.txt", dossier_uuid=dossier_uuid
    )

    result = await clamav_task

    print(f"ClamAV Response: {result}")


if __name__ == "__main__":
    asyncio.run(main())
