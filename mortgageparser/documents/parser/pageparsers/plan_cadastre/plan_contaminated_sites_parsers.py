from dataclasses import dataclass

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
    FromBottomTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplatePlanContaminatedSitesPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.PLR_CADASTRE
    # document_title: str = "belastete Standorte"


PARSER_PLAN_CONTAMINATED_SITES_VD_1 = TemplatePlanContaminatedSitesPageParser(
    supported_languages=ALL_LANGUAGES,
    desc="Polluted Sites for canton VD 1 2024",
    required_text_conditions=[
        FromStartTextCond("Guichet cartographique cantonal", num_lines=4),
        FromBottomTextCond(
            ["Plan des eaux et sites pollués", "Sites pollués", "1:500", "1:1000"],
            num_lines=4,
        ),
    ],
)

PARSER_PLAN_CONTAMINATED_SITES_VD_2 = TemplatePlanContaminatedSitesPageParser(
    supported_languages=ALL_LANGUAGES,
    desc="Polluted Sites for canton VD 2 2024",
    required_text_conditions=[
        FromStartTextCond("Sites pollués parcelle", num_lines=4),
        FromBottomTextCond(["1:1000"], num_lines=4),
    ],
)

PARSER_PLAN_CONTAMINATED_SITES_VD_3 = TemplatePlanContaminatedSitesPageParser(
    supported_languages=ALL_LANGUAGES,
    desc="Polluted Sites for canton VD 3 2024",
    required_text_conditions=[
        FromBottomTextCond(
            [
                "Informations dépourvues de foi publique - Géodonnées Etat de Vaud, Office fédéral de topographie"
            ],
            num_lines=4,
        )
    ],
    max_num_chars=500,
)


PARSER_PLAN_CONTAMINATED_SITES_VD_4 = TemplatePlanContaminatedSitesPageParser(
    supported_languages=ALL_LANGUAGES,
    desc="Polluted Sites for canton VD 4 2024",
    required_text_conditions=[
        FromStartTextCond(["Sites pollués"], num_lines=3),
        FromBottomTextCond(["1:1000", "1:500"], num_lines=4),
    ],
    max_num_chars=400,
)

PARSER_PLAN_CONTAMINATED_SITES_VS_1 = TemplatePlanContaminatedSitesPageParser(
    supported_languages=ALL_LANGUAGES,
    desc="Legende FR Wallis almost empty 2 2024",
    required_text_conditions=[
        FromStartTextCond("Légende", num_lines=3),
        FromStartTextCond("Bien-fonds", hamming_dist=4, num_lines=6),
        FromStartTextCond("Communes", num_lines=6),
    ],
    max_num_chars_alpha=100,
)


def get_plan_contaminated_sites_page_parsers():
    parsers_de = [
        TemplatePlanContaminatedSitesPageParser(
            desc="KbS BE",
            required_text_conditions=[
                FromBottomTextCond(
                    "Kataster der belasteten Standorte des Kantons Bern", num_lines=10
                ),
                FromBottomTextCond("Erstellt für Massstab", num_lines=10),
            ],
            max_num_chars_alpha=700,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="KbS Generic (e.g. Kanton Luzern Map)",
            required_text_conditions=[
                FromStartTextCond("Kataster der belasteten Standorte KbS", num_lines=2)
            ],
            max_num_chars_alpha=700,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="KbS SZ, ZH",
            required_text_conditions=[
                FromStartTextCond(
                    [
                        "Kataster der belasteten Standorte Kt SZ",
                        "Kataster der belasteten Standorte (KbS)",  # ZH
                    ],
                    num_lines=4,
                ),
                FromBottomTextCond("Massstab", num_lines=10),
            ],
            # min_num_chars_alpha=700,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="KbS Legende misc cantons (SZ, SO)",
            required_text_conditions=[
                FromStartTextCond("Legende", num_lines=2),
                FromStartTextCond(["Kataster der belasteten Standorte"], num_lines=2),
                FromBottomTextCond(
                    [
                        "belastet, sanierungsbedürftig",
                        "belastet, überwachungsbedürftig",
                    ],
                    num_lines=10,
                ),
            ],
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="KbS Legende misc cantons 2 (BS)",
            required_text_conditions=[
                FromStartTextCond("Geoinformationssystem", num_lines=10),
                FromStartTextCond(["Kataster belasteter Standorte"], num_lines=15),
                FromBottomTextCond(
                    [
                        "belastet, sanierungsbedürftig",
                        "belastet, überwachungsbedürftig",
                    ],
                    num_lines=10,
                ),
            ],
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="KbS Legende canton GR",
            required_text_conditions=[
                FromStartTextCond("Geoportal der kantonalen Verwaltung", num_lines=5),
                FromStartTextCond(["Altlastenkatasterplan"], num_lines=5),
                FromStartTextCond(["Legende"], num_lines=10),
            ],
        ),
    ]

    parsers_fr = [
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="Legende FR Wallis almost empty 1 2024",
            required_text_conditions=[
                FromStartTextCond("Légende", num_lines=3),
                FromStartTextCond("Site de stockage", num_lines=10),
                FromStartTextCond("Pollué, ne nécessite ni surveillance", num_lines=10),
            ],
            max_num_chars_alpha=200,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="Polluted Sites for canton FR, lang is detected as German 2024",
            required_text_conditions=[
                FromStartTextCond("ETAT DE FRIBOURG", num_lines=10),
                FromStartTextCond("Gedruckt am", num_lines=10),
                FromStartTextCond("Sites pollués", num_lines=4),
                FromBottomTextCond("Bundesamt für Landestopographie", num_lines=4),
            ],
            max_num_chars_alpha=200,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="Polluted Sites for canton VD, Legende 2024",
            required_text_conditions=[
                FromStartTextCond("Informations complémentaires", num_lines=5),
                FromStartTextCond("Légende", num_lines=15),
                FromStartTextCond("Pollué, investigation nécessaire ", num_lines=30),
                FromStartTextCond("Pollué, nécessite une surveillance", num_lines=30),
            ],
            max_num_chars_alpha=1200,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="Polluted Sites confirmation as text for canton NE 2024",
            required_text_conditions=[
                FromStartTextCond("DEPARTEMENT DU DEVELOPPEMENT", num_lines=5),
                FromStartTextCond(
                    "Cadastre neuchâtelois des sites pollés", num_lines=15
                ),
                FromStartTextCond("statut de bien-fonds", num_lines=15),
                FromBottomTextCond("www.ne.ch", num_lines=3),
            ],
            max_num_chars_alpha=1200,
        ),
        TemplatePlanContaminatedSitesPageParser(
            supported_languages=ALL_LANGUAGES,
            desc="Polluted Sites confirmation as text and plan for canton GE 2024",
            required_text_conditions=[
                FromStartTextCond("Département de l'environnement", num_lines=5),
                FromStartTextCond(
                    "Extrait du cadastre des sites pollués", num_lines=10
                ),
                FromBottomTextCond("Légende", num_lines=5),
            ],
        ),
        PARSER_PLAN_CONTAMINATED_SITES_VD_1,
        PARSER_PLAN_CONTAMINATED_SITES_VD_2,
        PARSER_PLAN_CONTAMINATED_SITES_VD_3,
        PARSER_PLAN_CONTAMINATED_SITES_VD_4,
        PARSER_PLAN_CONTAMINATED_SITES_VS_1,
    ]
    return parsers_de + parsers_fr
