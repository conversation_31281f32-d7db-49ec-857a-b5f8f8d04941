from hypodossier.core.domain.PageLocation import PageLocation
from mortgageparser.documents.parser.ParserManager import ParserManager
from mortgageparser.documents.parser.pageparsers.payslip.payslip_parsers import (
    get_parsers_payslip,
)
from mortgageparser.documents.parser.pageparsers.salarycertificate.salary_certificate_parsers import (
    get_parsers_salary_certificate,
)


class SalaryCertificateParserManager(ParserManager):
    def __init__(self, lang: str, page_source: PageLocation):
        super().__init__(lang, page_source)

        self.add_parsers(get_parsers_salary_certificate())
        self.add_parsers(get_parsers_payslip())
