from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "210_passport_ch"
path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}")
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by <PERSON>")
async def test_one_doc_mt():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["Pass Manuel Thiemann.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PASSPORT_CH: 1},
            expected_page_objects={
                0: {
                    "document_date": "10.04.2015",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "X0668306",
                    "date_of_birth": "04.09.1977",
                    "sex": "M",
                    "document_validity_end_date": "09.04.2025",
                    "hometown": "Ringgenberg",
                    # "person_height": "180.0",      # seems to work as jpg but not as pdf
                    "mrz": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_doc_aw():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["pass_aw.jpg"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PASSPORT_CH: 1},
            expected_page_objects={
                0: {
                    "document_date": "07.07.2006",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "F2711226",
                    "sex": "M",
                    "document_validity_end_date": "06.07.2016",
                    "person_height": "181.0",
                    "mrz": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_one_doc_passport_ch23():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["passport_ch23.png"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PASSPORT_CH: 1},
            expected_page_objects={
                0: {
                    "document_date": "13.04.2023",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "X0H55Q49",
                    # TODO THIS IS WRONG "date_of_birth": "01.12.2004",
                    "document_validity_end_date": "12.04.2028",
                    "hometown": "Ringgenberg BE",
                    # TODO THIS IS WRONG "person_height": "2013.0",
                    "mrz": PO_EXISTS,
                }
            },
        ),
    ).run()
