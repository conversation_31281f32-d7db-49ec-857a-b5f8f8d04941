S3_HOST=minio.hypo.duckdns.org
S3_HOST_URL=https://minio.hypo.duckdns.org
S3_ACCESS_KEY=S3_ACCESS_KEY
S3_SECRET_KEY=S3_SECRET_KEY
S3_REGION=ch-dk-2
S3_SECURE=true

RABBIT_URL=amqp://admin:<EMAIL>:5672/

MODEL_S3_HOST=https://minio.hypo.duckdns.org
MODEL_S3_ACCESS_KEY=S3_ACCESS_KEY
MODEL_S3_SECRET_KEY=S3_SECRET_KEY

S3_CACHE_BUCKET=dp-cache
ORIGINAL_FILE_PROCESSOR_BUCKET=dp-processing

# This must be the path with the '/build' at the end
DOCUMENT_BROWSER_BUILD_PATH=/home/<USER>/repos/hyextract/artefact/build

# Abs path to project that contains the sample data for the dossier browser
PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER=/home/<USER>/repos/hypodossier-data-dossier

# Abs path to project that contains the internal data for the system tests
PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM=/home/<USER>/repos/hypodossier-data-system

ENABLE_DETECTRON2_OBJECT_DETECTION=True

# Duration that we wait for a single OCR job in seconds
# Default is 3600 = 1 hour
OCR_TIMEOUT_SECONDS=180

#FRE_LICENSE=SWAD-1201-1007-0006-8103-3364
FRE_LICENSE=SWAR-1201-1007-0105-0766-2529

REPLACE_LARGE_OCR_RESULTS_WITH_ORIGINAL=True

SEARCHABLE_PAGE_CACHE_ENABLED=true

# no spacy models in .env so that they get loaded from global_settings.py

# Open webbrowser with relevant offline dossier after running a DossierTest yes/no
ENABLE_PYTEST_SHOW_WEBBROWSER=False

# Set this to true if logging for the "display_search_results" function should be enabled
DISPLAY_SEARCH_RESULT=False

# Running single page pytest evaluation with storing result in constants.OUTPUT_DOSSIER_PREVIEW
ENABLE_PYTEST_SHOW_PAGE=False

# If true then raise exceptions if parser throws an exception.
# For production this must be set to false (default)
# For development this must be set to true
RAISE_EXCEPTIONS_IN_PARSER=False

# Default for threshold is 0.8, set lower to see more uncertain page objects
# PAGE_OBJECT_MIN_VISIBLE_CONFIDENCE=0.001

ENABLE_IMAGEPREDICT_PROCESSING=True
IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD=0.5
USE_IMAGEPREDICT_DEBUG_PREFIX=False

ENABLE_FINHURDLE_HANDLING=True
FINHURDLE_MIN_VISIBLE_CONFIDENCE_THRESHOLD=0.5
# Make all finhurdles visible
ENABLE_FINHURDLE_VISIBILITY=True

# If this is True then layoutlm extraction will be performed on sales documentation pages.
# Else this is skipped.
ENABLE_SALES_DOCUMENT_EXTRACTION=True

# If this is True the filename property for semantic documents will be computed. This is only necessary for the
# offline version and should not be used for staging / prod
GENERATE_OFFLINE_FILENAME=False

# This flag is used during migration to new concept of document cat + title_suffix
# Should be set to False once the Frontend is capable of handling the new title suffix feature.
GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS=False
MARK_TITLE_AS_LEGACY=False

SENTRY_ENVIRONMENT=dev-${USERNAME}

PRETTY_PRINT_LOG=True

# Maximum time per individual conversion process after which we give up
# With retries several ones can be chained so total time can be higher
# Ghostscript can take e.g. 50 seconds for 500 pages so we make this longer.
TIMEOUT_PDF_CONVERSION_SECONDS=75

# Maximum number of tries to call FREP to process a single PDF page
# As long as there is no DLX it makes sense to retry. Afterwards this can be set to 1
MAX_TRIES_FREP_FOR_PDF=2

# Every filename longer than this will be shortened accordingly
MAX_FILENAME_LENGTH_FOR_PROCESSING=175

# Filename suffix that will be applied if a filename is longer than
# MAX_FILENAME_LENGTH_FOR_PROCESSING. If empty, no suffix is applied
# Suffix will be added before extension, e.g. "somename_longfilename.pdf"
SUFFIX_FOR_SHORTENED_FILENAME=_cutfilename

# Enable antivirus scanning, Clamav
ENABLE_ANTIVIRUS_CLAMAV=True

# Enable antivirus scanning, Microsoft Defender
ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER=False

# If there's a failure in the AV scan processing, raise an exception and let file_process_worker.py fail
# If False no exception is raised, processing continues normally
ENABLE_ANTIVIRUS_RAISE_EXCEPTION=False

HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY=Hyscan.Clamav.Request.RoutingKey

HYSCAN_MICROSOFTDEFENDER_SCAN_REQUEST_ROUTING_KEY=Hyscan.MicrosoftDefender.Request.RoutingKey

# If there's a failure in the AV scan processing, raise an exception and let file_process_worker.py fail
# If False no exception is raised, processing continues normally
ENABLE_ANTIVIRUS_RAISE_EXCEPTION=False

# If this is True, the small_files_filter is applied and the flag SKIP_SMALL_FILES
# is used. Else small files are just handled normally (not recommended for production)
APPLY_SMALL_FILES_FILTER=True

# If this is True then small files are skipped without producing an exception,
# else an exception which will be visible to the user is created
# E.g. needs to be turned off to test the eicar virus test file as this is small
SKIP_SMALL_FILES=True

# Timeout for request to hyscan service in seconds
HYSCAN_REQUEST_TIMEOUT=5

# If True disable tests that call function DossierTest and therefore need OCR enabled
DISABLE_TEST_BATCH=False

# If set to True, and an sha256 is not fetched by get_sha256sum as META_DATA_SHA256_SUM in the metadata provided
# by minio, than an md5 will be used instead
FREP_FAILBACK_CACHE_HASH=True

# If True, use the cached results from the cache, else ignore cache element
# In both cases the fresh result will be written to cache (potentially overriding
# the previous cache element
TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED=True
TEST_CACHE_CLASSIFY_PAGE_OBJECTS_ENABLED=True
TEST_CACHE_IMAGEPREDICT_ENABLED=True

# We use caching to speed up the tests - especially the ones that need things from other microservices
# that are not code based off hyextract. Additionally things like the hymsread
# use abby finereader - which costs use per page used. So we want to cache the results

# Ok, we have two types of flag for test caching
# e.g. TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED and TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED_FORCE_OVERWRITE
# The first one is the main flag to enable caching, the second one is to force overwrite of cache files
# example usage: we update page classifier and want to force overwrite of cache files
# so we run all unit tests, then we set TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED_FORCE_OVERWRITE to True
# and rerun the tests that failed before and afterwards unset the FORCE_OVERWRITE  flag again

# GODS
TEST_CACHE_DETECT_PAGE_OBJECTS_FORCE_OVERWRITE=False

# GOCS
TEST_CACHE_CLASSIFY_PAGE_OBJECTS_FORCE_OVERWRITE=False

# hylayoutlm_predictor_image_request
TEST_CACHE_IMAGEPREDICT_FORCE_OVERWRITE=False

# Frep
TEST_FORCE_OVERWRITE_OCR_CACHE=False

# If a document has more than this number of pages we change the document category
# to UNKNOWN. This applies only to certain doc cats where we have a wild mix
# of included document category, e.g. SALES_DOCUMENTATION, PRPERTY_ESTIMATION,
# PROPERTY_INFO. Other categories can have very long documents which are correct
# (e.g. PROPERTY_DESCRIPTION) so this setting should not affect these documents
MIN_NUM_PAGES_UNKNOWN_OVERRIDE=40

# Whether the validate_pdf_in_minio function should open the pdf and check if it is valid (True)
# or just check the file mine type (False)
VALIDATE_PDF_CONTENT_VIA_PYPDF=True

# Whether to enable the PDF repair functionality using Cairo if an OCR failure occurs
TRY_PDF_REPAIR_CAIRO=True

# Maximum number of pages that are accepted in a single document.
# If longer documents are received processing will abort.
MAX_NUM_PAGES_ALLOWED_IN_PDF=500

# Whether extract_attachments and unpack_msg should convert the email content to PDF
# Otherwise just the email attachments are converted to PDF
CONVERT_EMAIL_CONTENT_TO_PDF=True

# When converting the body of an email into pdf, max number of pages in new pdf
MAX_NUM_PAGES_EMAIL_CONTENT_TO_PDF=50

# For some broken msg files the extraction of the html is stuck indefinitely.
# Abort after some seconds to continue without HTML body (or without email)
EMAIL_CONTENT_TO_PDF_PROCESSING_TIMEOUT_SECONDS=5