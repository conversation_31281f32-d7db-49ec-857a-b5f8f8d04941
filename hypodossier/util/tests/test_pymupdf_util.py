import pytest
import warnings
from pathlib import Path
import fitz  # PyMuPDF

from constants import OUTPUT_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.util.pymupdf_util import pymupdf_pdf_fix
from hypodossier.util.ghostscript_util import gs_check_valid_pdf_file

# Suppress SWIG deprecation warning
warnings.filterwarnings(
    "ignore",
    category=DeprecationWarning,
    message="builtin type swigvarlink has no __module__ attribute",
)


# Test data paths - these would be actual PDF files in the test data directory
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_components/pdf_fix"
)

VALID_PDF = path_root_folder / "valid_pdf/valid_pdf.pdf"
GS_FIXABLE_PDF = path_root_folder / "gs_fixable_pdf/gs_fixable_pdf.pdf"
PDFTOCAIRO_FIXABLE_PDF = (
    path_root_folder / "pdftocairo_fixable_pdf/pdftocairo_fixable_pdf.pdf"
)
OUTPUT_PATH_PYMUPDF = Path(OUTPUT_DIR) / "test_pdf_fix_pymupdf"


@pytest.fixture(autouse=True)
def setup_teardown():
    """Setup and teardown for each test."""
    # Create output directory if it doesn't exist
    OUTPUT_PATH_PYMUPDF.mkdir(parents=True, exist_ok=True)
    yield
    # Cleanup could be added here if needed


def test_pymupdf_pdf_fix_valid_pdf():
    """Test fixing a valid PDF that doesn't need fixing."""
    valid_pdf = gs_check_valid_pdf_file(VALID_PDF, timeout=10)
    assert valid_pdf

    # There are still some annotations that pymupdf wants to fix
    result, changed = pymupdf_pdf_fix(
        VALID_PDF, OUTPUT_PATH_PYMUPDF, remove_orphan_annotations=False
    )
    assert result
    assert changed

    # Doing it again should not change anything
    result2, changed2 = pymupdf_pdf_fix(
        result, OUTPUT_PATH_PYMUPDF, remove_orphan_annotations=False
    )
    assert result2 is None
    assert not changed2, "No changes should be made to a valid PDF"

    # Nothing fixed but nothing should be broken
    valid_pdf_2 = gs_check_valid_pdf_file(VALID_PDF, timeout=10)
    assert valid_pdf_2

    # Additional assertions could verify the content is unchanged


def test_pymupdf_pdf_fix_gs_fixable_pdf():
    """Test fixing a PDF that can be repaired by PyMuPDF."""

    assert GS_FIXABLE_PDF.exists()

    # Test that this file needs fixing
    valid_pdf = gs_check_valid_pdf_file(GS_FIXABLE_PDF, timeout=10)
    assert not valid_pdf

    result, changed = pymupdf_pdf_fix(GS_FIXABLE_PDF, OUTPUT_PATH_PYMUPDF)

    assert result is not None
    assert result.exists()
    assert result.name == f"{GS_FIXABLE_PDF.stem}_repairedmu.pdf"
    assert changed, "Changes should be made to fix the PDF"

    result_path = OUTPUT_PATH_PYMUPDF / result.name
    valid_mupdf_fixed_pdf = gs_check_valid_pdf_file(result_path, timeout=10)
    assert valid_mupdf_fixed_pdf


@pytest.mark.skip(
    "test does not work because Ghostscript 10.x sees this as a valid file. Still abbyy cannot process it and pdftocairo fixes that. So assert for gs_check_valid_pdf_file fails."
)
def test_pymupdf_pdf_fix_broken_pdf():
    """Test fixing a PDF that is too broken to be repaired."""

    assert PDFTOCAIRO_FIXABLE_PDF.exists()

    # Test that this file needs fixing
    valid_pdf = gs_check_valid_pdf_file(PDFTOCAIRO_FIXABLE_PDF, timeout=10)
    assert not valid_pdf

    result, changed = pymupdf_pdf_fix(
        PDFTOCAIRO_FIXABLE_PDF, OUTPUT_PATH_PYMUPDF, remove_orphan_annotations=False
    )

    assert result is None
    assert not changed, "No changes should be made if the operation failed"
    # Verify no output file was created
    assert not (OUTPUT_PATH_PYMUPDF / "pdftocairo_fixable_pdf.pdf").exists()


def test_pymupdf_pdf_fix_custom_suffix():
    """Test fixing a PDF with a custom suffix."""
    custom_suffix = "_custom_fix"

    result_2, changed_2 = pymupdf_pdf_fix(
        GS_FIXABLE_PDF, OUTPUT_PATH_PYMUPDF, suffix=custom_suffix
    )

    assert changed_2
    assert result_2 is not None
    assert result_2.exists()
    assert result_2.name == f"{GS_FIXABLE_PDF.stem}{custom_suffix}.pdf"


def test_pymupdf_version():
    """Test that PyMuPDF version is 1.26.x"""
    version = fitz.__version__
    assert version.startswith("1.26."), f"PyMuPDF version {version} is not 1.26.x"
