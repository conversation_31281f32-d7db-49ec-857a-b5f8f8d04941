from abbyyplumber.converter.ValueConverter import MostRecentDateConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
    FromStartTextCond,
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)
from mortgageparser.documents.parser.pageparsers.payslip.PayslipPageParser import (
    PayslipPageParser,
)

parsers = [
    PayslipPageParser(),
    SmartLetterPageParser(
        company="SwissLife",
        product="BVG",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        titles=["Rentenbestätigung für das Jahr"],
        required_tokens=[
            "Swiss Life AG",
            "BVG-Sammelstiftung",
            "Anspruchs-",
            "berechtigte",
            "Person",
            "Rentenart",
        ],
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Rentenbestätigung für das Jahr",
            converter=MostRecentDateConverter(),
        ),
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Rentenbestätigung für das Jahr",
            x_range=PercentageRange(0, 0.5),
        ),
        use_ahv_new=True,
    ),
    SmartLetterPageParser(
        company="SwissLife",
        product="Zusatz",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        titles=["Rentenbestätigung für das Jahr"],
        required_tokens=[
            "Swiss Life AG",
            "Sammelstiftung Zusatzvorsorge",
            "Anspruchs-",
            "berechtigte",
            "Person",
            "Rentenart",
        ],
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Rentenbestätigung für das Jahr",
            converter=MostRecentDateConverter(),
        ),
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Rentenbestätigung für das Jahr",
            x_range=PercentageRange(0, 0.5),
        ),
        use_ahv_new=True,
    ),
    SmartLetterPageParser(
        company="SwissLife",
        product="3b",
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        titles=["Rentenbescheinigung"],
        required_tokens=[
            "Swiss Life AG",
            "Lebensrenten der Säule 3b",
            "Säule 3b",
            "Rentenbezüger",
            "Total der Rente aus dieser Police",
        ],
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Rentenbescheinigung",
            converter=MostRecentDateConverter(),
        ),
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_bottom="Rentenbescheinigung",
            x_range=PercentageRange(0, 0.5),
        ),
    ),
    TemplatePageParser(
        doc_cat=DocumentCat.PAYSLIP,
        page_cat=PageCat.GENERIC_PAGE,
        desc="Gehaltsabrechnung generisch 2022",
        ranked_titles_all=[
            RankedTitle("Gehaltsabrechnung", rank=1, min_length_title=10)
        ],
        required_text_conditions=[
            FromStartTextCond("Gehaltsabrechnung", num_lines=2),
        ],
        required_tokens=["Lohn"],
        min_num_chars_alpha=500,
        max_num_chars_alpha=1500,
    ),
]


def get_parsers_payslip():
    return parsers
