from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_YEAR
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.so.year2016_unused.so_tax_declaration_fields_map_2016 import (
    tax_declaration_so_fields_map_page_02,
)

from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    COLUMN_CODE,
    create_search_elements_by_tax_code,
)
from mortgageparser.util.search_element_util import create_search_element_recent_year


class TaxDeclarationSOIncomePageParser2016(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_INCOME
        self.canton_short = "SO"
        # self.required_search_results = [COLUMN_CODE]

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page

        ce = ContentExtractor()
        elements = [
            SearchElementConstrainedArea(
                "target_main",
                page.main,
                text_top="Ausland",
                text_top_offset_lines=-1,
                text_bottom="Zwischentotal der Einkünfte",
                text_bottom_offset_lines=1,
                extract=False,
            ),
            SearchElementConstrainedArea(
                FIELD_YEAR.sr_inside,
                page.main,
                target_name="target_main",
                text_left="CHF ohne",
                text_bottom="CHF ohne",
                extract=False,
            ),
            create_search_element_recent_year(page),
            SearchElementConstrainedArea(
                COLUMN_CODE,
                page.main,
                target_name="target_main",
                texts_left=[
                    "Lohnausweis",
                    "Angaben eigene Kinder",
                    "Wertschriftenverzeichnis",
                ],
                text_left_offset_chars=1,
                text_right="CHF ohne",
                text_right_offset_chars=-5,
                extract=False,
            ),
            # SearchElementConstrainedArea("target_total", page.main,
            #                              text_top="Zwischentotal der Einkünfte",
            #                              extract=False),
            #
            # SearchElementConstrainedArea("column_code_total", page.main, target_name="target_total",
            #                              text_left="Geschäftsfahrzeugs",
            #                              text_right="Geschäftsfahrzeugs",
            #                              text_right_boundary=ReferenceBoundaryHorizontal.RIGHT,
            #                              text_right_offset_chars=8, extract=False),
            #
            # SearchElementConstrainedArea("column_value_total", page.main, target_name="target_total",
            #                              text_left="Geschäftsfahrzeugs",
            #                              text_left_offset_chars=10,
            #                              text_right="Geschäftsfahrzeugs",
            #                              text_right_boundary=ReferenceBoundaryHorizontal.RIGHT,
            #                              text_right_offset_chars=28, extract=False),
        ]

        elements += create_search_elements_by_tax_code(
            self.page, fields_map=tax_declaration_so_fields_map_page_02
        )
        # elements += create_search_elements_by_tax_code(self.page,
        #                                                field_vertical_line_scale=4,
        #                                                column_code="column_code_total",
        #                                                column_value="column_value_total",
        #                                                fields_map=tax_declaration_so_fields_map_page_02_total)
        ce.search_elements += elements
        ce.search_elements += self.create_standard_search_elements(page)

        return ce
