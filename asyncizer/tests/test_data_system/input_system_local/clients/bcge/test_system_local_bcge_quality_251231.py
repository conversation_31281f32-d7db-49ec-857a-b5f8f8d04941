from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS

FOLDER_TYPE = "bcge"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS}/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_local_bcge"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_250527_pdf_fix():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "250527_pdf_fix",
        # source_file_filter=[""],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(expected_num_processing_exceptions=1),
    ).run()


@pytest.mark.asyncio
async def test_250604_pdf_fix():
    await DossierTest(
        use_ocr_cache=False,
        override_existing=True,
        source_folder=path_root_folder / "250604_pdf_fix",
        # source_file_filter=[""],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(expected_num_processing_exceptions=0),
    ).run()
