from hypodossier.core.documents.bank_account.BankAccountFileCreator import (
    BankAccountFileCreator,
)
from hypodossier.core.documents.correspondence.EmailCorrespondenceFileCreator import (
    EmailCorrespondenceFileCreator,
)
from hypodossier.core.documents.criminalrecords.criminal_records import (
    CriminalRecordsFileCreator,
)
from hypodossier.core.documents.debt_certificate.DebtCertificateFileCreator import (
    DebtCertificateFileCreator,
)
from hypodossier.core.documents.debtcollectioninformation.DebtCollectionInformationFileCreator import (
    DebtCollectionInformationFileCreator,
)
from hypodossier.core.documents.financial_statement_company.FinancialStatementCompanyFileCreator import (
    FinancialStatementCompanyFileCreator,
)
from hypodossier.core.documents.generic_letter.GenericLetterFileCreator import (
    GenericLetterFileCreator,
)
from hypodossier.core.documents.mortgage.MortgageFileCreator import MortgageFileCreator
from hypodossier.core.documents.id_ch.IdChFileCreator import IdChFileCreator
from hypodossier.core.documents.land_register.LandRegisterFileCreator import (
    LandRegisterFileCreator,
)
from hypodossier.core.documents.hra.HraFileCreator import HraFileCreator
from hypodossier.core.documents.payslip.PayslipFileCreator import PayslipFileCreator
from hypodossier.core.documents.pension3_insurance.Pension3InsuranceFileCreator import (
    Pension3InsuranceFileCreator,
)
from hypodossier.core.documents.pension_contribution_confirmation.PensionContributionFileCreator import (
    PensionContributionFileCreator,
)
from hypodossier.core.documents.pension_payment_all.PensionPaymentAllFileCreator import (
    PensionPaymentAllFileCreator,
)
from hypodossier.core.documents.pensioncertificate.PensionCertificateFileCreator import (
    PensionCertificateFileCreator,
)
from hypodossier.core.documents.property_insurance.PropertyInsuranceFileCreator import (
    PropertyInsuranceFileCreator,
)
from hypodossier.core.documents.property_valuation.PropertyValuationFileCreator import (
    PropertyValuationFileCreator,
)
from hypodossier.core.documents.salarycertificate.SalaryCertificateFileCreator import (
    SalaryCertificateFileCreator,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationFileCreator import (
    TaxDeclarationFileCreator,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.filename.FileCreator import FileCreator
from hypodossier.core.filename.FilenamePrefixFileCreator import (
    FilenamePrefixFileCreator,
)
from hypodossier.core.filename.PersonFileCreator import (
    PersonFileCreator,
    CompanyProductFileCreator,
    CantonAddressFileCreator,
)
from hypodossier.core.filename.UnknownFileCreator import UnknownFileCreator

file_creator_dict = {}


def create_file_creator_dict():
    return {
        DocumentCat.FS24_FACTSHEET: PersonFileCreator(),
        DocumentCat.FS24_AUTHORIZATION: PersonFileCreator(),
        DocumentCat.FS24_ZINSANNAHME: PersonFileCreator(),
        DocumentCat.HYPOGUIDE_ZINSANNAHME: PersonFileCreator(),
        DocumentCat.PASSPORT_CH: IdChFileCreator(),
        DocumentCat.PASSPORT_DE: IdChFileCreator(),
        DocumentCat.PASSPORT_FR: IdChFileCreator(),
        DocumentCat.PASSPORT_IT: IdChFileCreator(),
        DocumentCat.ID: IdChFileCreator(),
        DocumentCat.RESIDENCE_PERMIT: IdChFileCreator(),
        DocumentCat.DEBT_COLLECTION_INFORMATION: DebtCollectionInformationFileCreator(),
        DocumentCat.CRIMINAL_RECORDS: CriminalRecordsFileCreator(),
        DocumentCat.ZEK_CHECK: PersonFileCreator(require_firstname=False),
        DocumentCat.IKO_CHECK: PersonFileCreator(require_firstname=False),
        DocumentCat.WORLD_CHECK: GenericLetterFileCreator(),
        DocumentCat.CRIF_TELEDATA: CompanyProductFileCreator(),
        DocumentCat.CRIF_DATA_INFO: CompanyProductFileCreator(),
        DocumentCat.TAX_DECLARATION: TaxDeclarationFileCreator(),
        DocumentCat.TAX_CALCULATION: TaxDeclarationFileCreator(),
        DocumentCat.TAX_LIST_FINANCIAL_ASSETS: TaxDeclarationFileCreator(),
        DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.BANK_DOCUMENT: PersonFileCreator(require_firstname=False),
        DocumentCat.SALARY_CERTIFICATE: SalaryCertificateFileCreator(),
        DocumentCat.SALARY_CONFIRMATION: PersonFileCreator(),
        DocumentCat.EMPLOYMENT_CONFIRMATION: PersonFileCreator(),
        DocumentCat.PAYSLIP: PayslipFileCreator(),
        DocumentCat.SALARY_CONFIRMATION_FORM: PersonFileCreator(),
        DocumentCat.TAX_AT_SOURCE_CONFIRMATION: PersonFileCreator(),
        # These 3 pension types use all the same imagepredict model
        DocumentCat.PENSION_PAYMENT_AHV: PensionPaymentAllFileCreator(),
        DocumentCat.PENSION_PAYMENT_BVG: PensionPaymentAllFileCreator(),
        # DocumentCat.PENSION_PAYMENT_AHV: PersonFileCreator(require_firstname=False, use_company=False, use_product=False),
        # DocumentCat.PENSION_PAYMENT_BVG: PersonFileCreator(require_firstname=False, use_company=False, use_product=False),
        DocumentCat.UNEMPLOYMENT_SALARY_CERTIFICATE: PersonFileCreator(
            require_firstname=False, use_company=False, use_product=False
        ),
        DocumentCat.CONSUMER_LOAN: PersonFileCreator(),
        DocumentCat.CREDIT_CARD_BILL: PersonFileCreator(),
        DocumentCat.LOAN_AGREEMENT: PersonFileCreator(),
        DocumentCat.PENSION_CERTIFICATE_AHV: PersonFileCreator(),
        # 405
        DocumentCat.PENSION_SIMULATION1: PensionPaymentAllFileCreator(),
        DocumentCat.PENSION_CERTIFICATE: PensionCertificateFileCreator(
            require_firstname=False
        ),
        DocumentCat.PENSION_REGULATIONS: PersonFileCreator(),
        DocumentCat.PENSION_WITHDRAWL: PersonFileCreator(),
        DocumentCat.PENSION_CERTIFICATE_SIM_ALL: PersonFileCreator(),
        DocumentCat.PENSION_CERTIFICATE_LETTER: PensionCertificateFileCreator(),
        DocumentCat.PENSION_CERTIFICATE_INFO: PensionCertificateFileCreator(),
        DocumentCat.PENSION_CERTIFICATE_CREDIT_NOTE: PersonFileCreator(),
        DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT: PersonFileCreator(),
        DocumentCat.VESTED_BENEFITS_ACCOUNT: PersonFileCreator(require_firstname=False),
        DocumentCat.VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT: PersonFileCreator(),
        DocumentCat.PENSION3A_ACCOUNT: BankAccountFileCreator(require_firstname=False),
        DocumentCat.PENSION3A_CREDIT_NOTE: PersonFileCreator(),
        DocumentCat.PENSION3A_INSURANCE_CONTRACT: Pension3InsuranceFileCreator(),
        DocumentCat.PENSION3A_INSURANCE_LETTER_REDEMPTION: Pension3InsuranceFileCreator(),
        DocumentCat.PENSION3A_INSURANCE_STATEMENT: PersonFileCreator(),
        DocumentCat.PENSION3_INSURANCE_APPLICATION: GenericLetterFileCreator(),
        DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION: PensionContributionFileCreator(),
        DocumentCat.PENSION_WITHDRAWL_PURPOSE_CONFIRMATION: PensionContributionFileCreator(),
        DocumentCat.RISK_LIFE_INSURANCE: PersonFileCreator(),
        DocumentCat.RETIREMENT_ANALYSIS: GenericLetterFileCreator(),
        DocumentCat.PENSION_MISC: PersonFileCreator(),
        DocumentCat.HRA: HraFileCreator(),
        DocumentCat.FINANCIAL_STATEMENT_COMPANY: FinancialStatementCompanyFileCreator(),
        DocumentCat.SALES_DOCUMENTATION: CantonAddressFileCreator(),
        DocumentCat.EXTRACT_FROM_LAND_REGISTER: LandRegisterFileCreator(),
        DocumentCat.PROPERTY_INSURANCE: PropertyInsuranceFileCreator(),
        DocumentCat.MINERGIE_CERTIFICATE: CompanyProductFileCreator(),
        DocumentCat.PROPERTY_VALUATION: PropertyValuationFileCreator(),
        DocumentCat.PROPERTY_VALUATION_GOV: PropertyInsuranceFileCreator(),
        DocumentCat.RESERVATION_CONTRACT: PersonFileCreator(),
        DocumentCat.BUILDING_DESCRIPTION: PersonFileCreator(),
        DocumentCat.MORTGAGE_REQUEST_FORM: PersonFileCreator(require_firstname=False),
        DocumentCat.AUTHORIZATION_FOR_INQUIRIES: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.AUTHORIZATION_EMAIL: PersonFileCreator(require_firstname=False),
        DocumentCat.US_PERSON_FORM: PersonFileCreator(require_firstname=False),
        DocumentCat.IRREVOCABLE_PROMISES_TO_PAY: PersonFileCreator(),
        DocumentCat.MORTGAGE_CONTRACT: MortgageFileCreator(),
        DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION: MortgageFileCreator(),
        DocumentCat.MORTGAGE_FRAMEWORK_CONTRACT: MortgageFileCreator(),
        DocumentCat.FINANCING_CONFIRMATION: PersonFileCreator(),
        DocumentCat.FINANCING_OFFER: GenericLetterFileCreator(),
        DocumentCat.ACCEPTANCE_OF_MORTAGE_OFFER: PersonFileCreator(),
        # 727
        DocumentCat.MORTGAGE_CONTRACT_CONFIRMATION: PersonFileCreator(),
        DocumentCat.AFFORDABILITY_CALCULATION: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.CREDITOR_CHANGE: PersonFileCreator(),
        # 742
        DocumentCat.TRANSFER_OF_SECURITY: PersonFileCreator(),
        # 743
        DocumentCat.TRANSFER_AGREEMENT: PersonFileCreator(),
        # 747
        DocumentCat.FILE_NOTE_FINANCING: GenericLetterFileCreator(),
        DocumentCat.BASE_CONTRACT: PersonFileCreator(),
        DocumentCat.DEBT_CERTIFICATE: DebtCertificateFileCreator(),
        DocumentCat.PENSION_PLEDGE: PersonFileCreator(require_firstname=False),
        DocumentCat.PLEDGE_NOTICE: PersonFileCreator(require_firstname=False),
        DocumentCat.FINANCING_CHECKLIST_DOCUMENTS: CompanyProductFileCreator(),
        DocumentCat.FINANCING_FEES_LIST: CompanyProductFileCreator(),
        DocumentCat.TERMS_AND_CONDITIONS: CompanyProductFileCreator(),
        DocumentCat.GENERAL_INFO: CompanyProductFileCreator(),
        DocumentCat.BROKER_MANDATE: PersonFileCreator(),
        DocumentCat.BROKER_AUTHORIZATION_BANK_SECRECY: PersonFileCreator(),
        DocumentCat.BROKER_AUTHORIZATION: PersonFileCreator(),
        DocumentCat.BROKER_MISC: GenericLetterFileCreator(),
        DocumentCat.ASSUMPTION_DEBT_NOTICE: PersonFileCreator(),
        DocumentCat.MB_TOTAL_ENGAGEMENT: PersonFileCreator(require_firstname=False),
        DocumentCat.MB_TOTAL_ENGAGEMENT_PROTOCOL: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.MB_KINFO: PersonFileCreator(require_firstname=False),
        DocumentCat.MB_EKB: PersonFileCreator(require_firstname=False),
        DocumentCat.MB_SOKO: PersonFileCreator(require_firstname=False),
        DocumentCat.MB_CREDIT_APPROVAL_REQUEST: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.MB_INTERNET_BANKING_MESSAGE: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.HBL_PRICING: PersonFileCreator(require_firstname=False),
        DocumentCat.HBL_DARLEHENSZUSICHERUNG: PersonFileCreator(
            require_firstname=False
        ),
        DocumentCat.HBL_DIREKTAUFTRAG: PersonFileCreator(require_firstname=False),
        DocumentCat.HBL_MORTGAGE_RENEWAL: PersonFileCreator(require_firstname=False),
        # DocumentCat.ZKB_GVZ_VIEWER: PropertyInsuranceFileCreator(),
        # DocumentCat.ZKB_RENT_CALCULATOR: PersonFileCreator(require_firstname=False),
        # DocumentCat.ZKB_DOSSIER_SUMMARY: PersonFileCreator(require_firstname=False),
        DocumentCat.ZKB_VBV: PersonFileCreator(
            require_firstname=False, use_company=False
        ),
        # DocumentCat.BEKB_GRUNDSATZ: PersonFileCreator(require_firstname=False),
        DocumentCat.FINANCING_MISC: GenericLetterFileCreator(),
        DocumentCat.IMMUTABLE_XLS: UnknownFileCreator(),
        DocumentCat.MISC_CAT: UnknownFileCreator(),
        DocumentCat.CORRESPONDENCE_EMAIL: EmailCorrespondenceFileCreator(),
        DocumentCat.UNKNOWN_DE: UnknownFileCreator(),
        DocumentCat.UNKNOWN_EN: UnknownFileCreator(),
        DocumentCat.UNKNOWN_FR: UnknownFileCreator(),
        DocumentCat.UNKNOWN_IT: UnknownFileCreator(),
        DocumentCat.UNKNOWN: UnknownFileCreator(),
        DocumentCat.WHITE_PAGES: FilenamePrefixFileCreator(),
        DocumentCat.CREDITWORTHINESS_MISC: GenericLetterFileCreator(),
    }


def get_file_creator_from_doc_cat(dc: DocumentCat):
    global file_creator_dict
    if not file_creator_dict:
        file_creator_dict = create_file_creator_dict()
    if dc in file_creator_dict:
        return file_creator_dict[dc]
    else:
        return FileCreator()


if __name__ == "__main__":
    print(f"fc={get_file_creator_from_doc_cat(DocumentCat.TAX_DECLARATION)}")
    print(f"fc={get_file_creator_from_doc_cat(DocumentCat.TAX_DECLARATION)}")
