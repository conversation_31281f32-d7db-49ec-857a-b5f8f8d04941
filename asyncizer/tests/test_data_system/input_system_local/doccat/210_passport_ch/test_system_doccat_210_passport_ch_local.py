from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "210_passport_ch"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_one_doc():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "one_doc",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PASSPORT_CH: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_two_single_page_docs():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "two_single_page_docs",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={},
            expected_doc_cat_frequency={
                DocumentCat.PASSPORT_CH: 1,
                DocumentCat.IDENTITY_MISC: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_pass_and_id():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "pass_and_id",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={},
            expected_doc_cat_frequency={DocumentCat.ID: 1, DocumentCat.PASSPORT_CH: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_2023_one():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "2023_one",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PASSPORT_CH: 1},
            expected_page_objects={
                0: {
                    "document_date": "07.12.2022",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "X0G03A59",
                    "date_of_birth": "01.03.2003",
                    "sex": "M",
                    "document_validity_end_date": "06.12.2032",
                    "hometown": "Kleinlützel SO",
                    "person_height": "190.0",
                    "mrz": PO_EXISTS,
                }
            },
        ),
    ).run()
