from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.hra.hra_parsers import (
    GENERIC_HRA_PARSER,
)
from mortgageparser.documents.parser.pageparsers.payslip.PayslipPageParser import (
    PayslipPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.CommonParsersDE import (
    ClassifierPensionCertificatePageTwoPageParser,
    ClassifierPensionCertificatePageThreePageParser,
    CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.GenericLegacyPropertyInsurancePageParser import (
    GenericLegacyPropertyInsurancePageParser,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_util import (
    PageCfg,
    C_TAX,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm1PersonalDataPageParser import (
    TaxDeclarationBEForm1PersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm2IncomePageParser import (
    TaxDeclarationBEForm2IncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm3AssetsPageParser import (
    TaxDeclarationBEForm3AssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm4DebtPageParser import (
    TaxDeclarationBEForm4DebtPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm5ChildSupportPageParser import (
    TaxDeclarationBEForm5ChildSupportPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm6DeductionsPageParser import (
    TaxDeclarationBEForm6DeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBETaxMePageParser import (
    TaxDeclarationBETaxMePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.fr.fr_tax_declaration_parsers import (
    TAX_DECLARATION_FR_PERSONAL_FR_2020,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ge.TaxDeclarationGEAssetsPageParser import (
    TaxDeclarationGEAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ge.TaxDeclarationGEDeductionsPageParser import (
    TaxDeclarationGEDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ge.TaxDeclarationGEIncomeDetailsPageParser import (
    TaxDeclarationGEIncomeDetailsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ge.TaxDeclarationGEIncomePageParser import (
    TaxDeclarationGEIncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ge.ge_tax_declaration_parsers import (
    TAX_DECLARATION_GE_PERSONAL,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ju.ju_tax_declaration_parsers import (
    TAX_DECLARATION_JU_PERSONAL_2021,
    TAX_DECLARATION_JU_INCOME_2021,
    TAX_DECLARATION_JU_DEDUCTIONS_2021,
    TAX_DECLARATION_JU_ASSETS_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ne.ne_tax_declaration_parsers import (
    TAX_DECLARATION_NE_PERSONAL_2021,
    TAX_DECLARATION_NE_INCOME_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.vd.vd_tax_declaration_parsers import (
    TAX_DECLARATION_VD_INCOME_2020,
    TaxDeclarationVDPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.vs.vs_tax_declaration_parsers import (
    TAX_DECLARATION_VS_PERSONAL_FR,
    TAX_DECLARATION_VS_DEDUCTIONS_2020,
    TAX_DECLARATION_VS_ASSETS_2020,
    TAX_DECLARATION_VS_INCOME_2020,
)

page_configurations_fr = [
    # Put this before Formular 1 because of prefix
    PageCfg(
        prefix="FR/310/BE/Formular 11",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_MISC,
    ),
    PageCfg(
        prefix="FR/310/BE/Formular 1",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm1PersonalDataPageParser(),
    ),
    PageCfg(
        prefix="FR/310/BE/Formular 2",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm2IncomePageParser(),
    ),
    PageCfg(
        prefix="FR/310/BE/Formular 3",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm3AssetsPageParser(),
    ),
    PageCfg(
        prefix="FR/310/BE/Formular 4",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm4DebtPageParser(),
    ),
    PageCfg(
        prefix="FR/310/BE/Formular 5",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm5ChildSupportPageParser(),
    ),
    PageCfg(
        prefix="FR/310/BE/Formular 6",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm6DeductionsPageParser(),
    ),
    # Some of these pages might not have the correct content for extraction but are still 'Récapitulatif TaxMe'
    PageCfg(
        prefix="FR/310/BE/Zusammenzug TaxMe",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBETaxMePageParser(),
    ),
    PageCfg(
        prefix="FR/310/VS/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_PERSONAL_FR,
    ),
    PageCfg(
        prefix="FR/310/VS/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_INCOME_2020,
    ),
    PageCfg(
        prefix="FR/310/VS/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_DEDUCTIONS_2020,
    ),
    PageCfg(
        prefix="FR/310/VS/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_ASSETS_2020,
    ),
    PageCfg(
        prefix="FR/310/NE/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NE_PERSONAL_2021,
    ),
    PageCfg(
        prefix="FR/310/NE/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NE_INCOME_2021,
    ),
    PageCfg(
        prefix="FR/310/FR/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_FR_PERSONAL_FR_2020,
    ),
    PageCfg(
        prefix="FR/310/BE/Zusammenzug TaxMe",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GE_PERSONAL,
    ),
    PageCfg(
        prefix="FR/310/GE/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GE_PERSONAL,
    ),
    PageCfg(
        prefix="FR/310/GE/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationGEIncomePageParser(),
    ),
    PageCfg(
        prefix="FR/310/GE/Abzüge",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationGEDeductionsPageParser(),
    ),
    PageCfg(
        prefix="FR/310/GE/Vermögen",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationGEAssetsPageParser(),
    ),
    PageCfg(
        prefix="FR/310/GE/Zusatz Einkünfte unselbständig",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationGEIncomeDetailsPageParser(),
    ),
    # Legacy, can be deleted after Spacy May 2022 is in prod
    PageCfg(
        prefix="FR/310/JU/Déclaration des personnes physiques",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_JU_PERSONAL_2021,
    ),
    PageCfg(
        prefix="FR/310/JU/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_JU_PERSONAL_2021,
    ),
    PageCfg(
        prefix="FR/310/JU/Revenu",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_JU_INCOME_2021,
    ),
    PageCfg(
        prefix="FR/310/JU/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_JU_DEDUCTIONS_2021,
    ),
    PageCfg(
        prefix="FR/310/JU/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_JU_ASSETS_2021,
    ),
    PageCfg(
        prefix="FR/310/VD/Personalien",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationVDPersonalDataPageParser(),
    ),
    # Revenu actually contains more assets...but the word is on the page
    PageCfg(
        prefix="FR/310/VD/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VD_INCOME_2020,
    ),
    # PageCfg(prefix='FR/310/VD/Recapitulation', confidence_threshold=C_TAX, parser=TaxDeclarationVDIncomePageParser()),
    PageCfg(
        prefix="FR/311/CH/Steuerberechnung (Provisorisch)",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_CALCULATION,
    ),
    PageCfg(
        prefix="FR/340/Salärabrechnung",
        confidence_threshold=0.8,
        parser=PayslipPageParser(),
    ),
    # strict because quality very good
    # 350 is legacy, new number is 345
    PageCfg(
        prefix="FR/345/Bonus",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALARY_BONUS,
    ),
    PageCfg(
        prefix="FR/350/Bonus",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALARY_BONUS,
    ),  # legacy_doc_cat_id
    PageCfg(
        prefix="FR/410/Pensionskassenausweis/Erste Seite",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=500,
        min_num_lines=10,
        parser=CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER,
    ),
    PageCfg(
        prefix="FR/410/Pensionskassenausweis/Zweite Seite",
        confidence_threshold=0.75,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
        parser=ClassifierPensionCertificatePageTwoPageParser(),
    ),
    PageCfg(
        prefix="FR/410/Pensionskassenausweis/Dritte Seite",
        confidence_threshold=0.75,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
        parser=ClassifierPensionCertificatePageThreePageParser(None, None),
    ),
    PageCfg(
        prefix="FR/410/Pensionskassenausweis/Brief",
        confidence_threshold=0.7,
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
    ),
    PageCfg(
        prefix="FR/410/Pensionskassenausweis/Info-Seite",
        confidence_threshold=0.7,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
    ),
    PageCfg(
        prefix="FR/424/Freizügigkeitskonto",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.VESTED_BENEFITS_ACCOUNT,
    ),
    PageCfg(
        prefix="FR/513/Handelsregisterauszug",
        confidence_threshold=0.75,
        parser=GENERIC_HRA_PARSER,
    ),
    PageCfg(
        prefix="FR/530/Jahresrechnung Firma Inhalt",
        doc_cat=DocumentCat.FINANCIAL_STATEMENT_COMPANY,
        page_cat=PageCat.FINANCIAL_STATEMENT_COMPANY_CONTENT,
    ),
    # Hack until 628 in classifier
    PageCfg(
        prefix="FR/607/Katasterplan",
        confidence_threshold=0.6,
        doc_cat=DocumentCat.PLR_CADASTRE,
    ),
    PageCfg(prefix="FR/611/Grundbuchauszug/Auszug", confidence_threshold=0.6),
    PageCfg(
        prefix="FR/617",
        confidence_threshold=0.5,
        parser=GenericLegacyPropertyInsurancePageParser(),
    ),
    PageCfg(
        prefix="FR/626/ÖREB-Kataster",
        confidence_threshold=0.4,
        doc_cat=DocumentCat.PLR_CADASTRE,
    ),
    PageCfg(prefix="FR/675/Generalunternehmervertrag", confidence_threshold=0.6),
    PageCfg(prefix="FR/798/Notariat Diverses", confidence_threshold=0.75),
    PageCfg(
        prefix="FR/920/Rechnung",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.BILL_MISC,
    ),
    # Legacy, can be deleted after spacy model trained in August 2024
    PageCfg(
        prefix="FR/920/Rechnung Liegenschaft",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.PROPERTY_BILL,
    ),
    PageCfg(
        prefix="FR/951/Versicherungsauszug Krankenkasse",
        confidence_threshold=0.7,
        doc_cat=DocumentCat.HEALTH_INSURANCE,
    ),
]
