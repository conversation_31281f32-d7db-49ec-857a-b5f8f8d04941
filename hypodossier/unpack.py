import base64
import hashlib
import json
import os
import re
import shutil
import traceback
import zipfile
from pathlib import Path
from shutil import copyfile
from tempfile import TemporaryDirectory
from typing import List, Dict, Optional
from uuid import uuid4

import extract_msg
import pikepdf
import rarfile
import structlog
from eml_parser import Eml<PERSON>arser
from extract_msg.attachments import EmbeddedMsgAttachment
from py7zr import unpack_7zarchive
from pydantic import BaseModel

import global_settings
from asyncizer.exception_handling import (
    ExceptionDetails,
    HypoDossierException,
    create_exception_details_not_readable,
)
from asyncizer.small_files_filter import (
    DEFAULT_SMALL_FILES_FILTER,
    MIN_SIZE_EMAIL_ATTACHMENT_UNKOWN_TYPE_FILTER_LIMIT,
)
from asyncizer.supported_file_extensions import ALL_SUPPORTED_FILE_SUFFIX
from constants import EXTRACTED_EMAIL_FILENAME_PREFIX
from hypodossier.email_processor import (
    extract_msg_content,
    create_email_body_filename,
    convert_email_to_pdf,
    extract_eml_content,
    sanitize_filename,
)
from hypodossier.excel_util import excel_unprotect
from hypodossier.smime_p7m import decode_smime_email_with_signature
from hypodossier.util.excel.excel_xlsm_to_xlsx import xlsm_to_xlsx
from hypodossier.util.file_util import ensure_path_has_forward_slashes
from hypodossier.util.image_conversion_util import convert_any_image_to_jpeg
from mortgageparser.util.file_extension import (
    optionally_fix_fileextension,
    optionally_shorten_filename,
)

logger = structlog.getLogger(__name__)


filename_endings_to_ignore_in_unpack = [".xlsm#"]  # Lock file for .xlsm

UNKNOWN_EXCEPTION_DETAILS = ExceptionDetails(
    type=HypoDossierException.UNKNOWN_EXCEPTION,
    en="Unknown error during the extraction occurred.",
    de="Ein unbekannter Fehler ist aufgetreten.",
    fr="Une erreur inconnue s'est produite pendant le traitement.",
    it="Si è verificato un errore sconosciuto durante l'elaborazione.",
)

NOT_READABLE_EXCEPTION_DETAILS = create_exception_details_not_readable()

PASSWORD_PROTECTED_EXCEPTION_DETAILS = ExceptionDetails(
    type=HypoDossierException.PASSWORD_PROTECTED,
    en="The file is password-protected.",
    de="Die Datei ist passwortgeschützt.",
    fr="Le fichier est protégé par un mot de passe.",
    it="Il file è protetto da password.",
)

XLSM_FILE_CANNOT_BE_PROCESSED = ExceptionDetails(
    type=HypoDossierException.XLSM_FILE_CANNOT_BE_CONVERTED,
    en="The file contains Excel macros which could not be removed.",
    de="Die Excel-Datei enthält Macros, die nicht automatisch entfernt werden konnten.",
    fr="Le fichier contient des macros Excel qui n'ont pas pu être supprimées.",
    it="Il file contiene macro di Excel che non è stato possibile rimuovere.",
)


class FileExtraction(BaseModel):
    original_file: str
    extracted_files: List[str]
    exceptions: Dict[str, ExceptionDetails]


def unpack_msg(archive: str, dest: str):
    """
    :param str archive: path to msg file that should be unpacked as a string
    :param str dest: path to directory into whunpich the attachments should be saved as a string
    """

    msg = extract_msg.openMsg(archive)
    try:
        path_target_folder = Path(dest)

        try:
            # Generate PDF from email content if enabled
            if global_settings.CONVERT_EMAIL_CONTENT_TO_PDF:
                email_content = extract_msg_content(archive)

                # Create a deterministic representation of the email content
                html_str = None
                if email_content["is_html"]:
                    html_str = email_content["html"][0:2000]
                content_for_hash = {
                    "subject": email_content["metadata"]["subject"],
                    "sender": email_content["metadata"]["sender"],
                    "body": email_content["body"],
                    "html": html_str,
                }
                content_str = json.dumps(content_for_hash, sort_keys=True)
                content_hash = hashlib.sha256(content_str.encode("utf-8")).hexdigest()

                # Check if we've already processed an email with this content hash and deduplication is enabled
                existing_pdf_path = None
                if global_settings.ENABLE_EMAIL_CONTENT_DEDUPLICATION:
                    existing_pdf_path = find_pdf_with_content_hash(
                        path_target_folder, content_hash
                    )

                if existing_pdf_path:
                    logger.info(
                        "Duplicate email content detected in MSG file",
                        msg_file=str(archive),
                        content_hash=content_hash,
                        existing_pdf_path=str(existing_pdf_path),
                    )
                else:
                    # This is a new email content, create a new PDF with the hash in the filename
                    pdf_filename = create_email_body_filename(content_hash)
                    pdf_path = path_target_folder / pdf_filename
                    convert_email_to_pdf(email_content, pdf_path)

                    logger.info(
                        "Processing MSG email content",
                        msg_file=str(archive),
                        content_hash=content_hash,
                        pdf_path=str(pdf_path),
                    )
        except Exception as e:
            logger.exception(
                f"Could not convert msg email text into pdf. e={e}", exc_info=True
            )

        try:
            for attachment in msg.attachments:
                if isinstance(attachment, EmbeddedMsgAttachment):
                    nested_filename = (
                        attachment.name
                        or attachment.longFilename
                        or attachment.shortFilename
                        or f"embedded_{uuid4().hex[:8]}.msg"
                    )
                    nested_filename = Path(nested_filename)
                    if nested_filename.suffix.lower() != ".msg":
                        nested_filename = nested_filename.with_suffix(".msg")

                    # The extractEmbedded is very important here. Without it
                    # The nested inner email will be saved as "message.txt"
                    # plus all attachments. This is not what we want. We need
                    # an .msg file. The unpack() will take care of recursively
                    # unpacking it.
                    attachment.save(
                        customPath=dest,
                        customFilename=sanitize_filename(str(nested_filename)),
                        extractEmbedded=True,
                    )
                    continue

                # Check file size before saving
                min_size_bytes = MIN_SIZE_EMAIL_ATTACHMENT_UNKOWN_TYPE_FILTER_LIMIT
                filename = None
                ext = None
                if attachment.longFilename:
                    filename = Path(attachment.longFilename)
                    ext = filename.suffix.lower()
                    if ext.endswith("\x00"):
                        # This is an encoding problem with msg files that sometimes they have \x00 added to the filename
                        # for attachments. We try to handle it gracefully and ignore the \x00
                        ext = ext.rstrip("\x00")
                        filename = filename.with_suffix(ext)
                elif attachment.shortFilename:
                    filename = Path(attachment.shortFilename)
                    ext = filename.suffix.lower()
                    if ext.endswith("\x00"):
                        # This is an encoding problem with msg files that sometimes they have \x00 added to the filename
                        # for attachments. We try to handle it gracefully and ignore the \x00
                        ext = ext.rstrip("\x00")
                        filename = filename.with_suffix(ext)

                if filename and ext:
                    if ext not in ALL_SUPPORTED_FILE_SUFFIX:
                        # filter out e.g. .htm, .p7s
                        continue
                    if ext in [".txt"] and filename.stem.lower().startswith("body-"):
                        # this is e.g. body-22.txt which is the plain text body of the email again as an attachment
                        # there is also then bodyAlternate-22.htm but that is excluded by ALL_SUPPORTED_FILE_SUFFIX
                        continue

                    if ext in DEFAULT_SMALL_FILES_FILTER.small_files_map:
                        min_size_bytes = DEFAULT_SMALL_FILES_FILTER.small_files_map[ext]
                    size_attachment_bytes = (
                        len(attachment.data) if attachment.data else 0
                    )
                    if size_attachment_bytes <= min_size_bytes:
                        logger.warning(
                            "Skipping small attachment msg",
                            filename=attachment.longFilename,
                            actual_size=size_attachment_bytes,
                            min_size=min_size_bytes,
                            parent_msg=archive,
                            ext=ext,
                        )
                        continue
                    filename_str = sanitize_filename(str(filename))
                    attachment.save(customPath=dest, customFilename=filename_str)

        except Exception as e:
            logger.exception("Could not read msg attachment", exc_info=True, e=str(e))
    finally:
        msg.close()


shutil.register_unpack_format("msg", [".msg"], unpack_msg)


def unpack_rar(archive, dest):
    try:
        with rarfile.RarFile(archive) as rf:
            rf.extractall(dest)
    except Exception:
        logger.exception(f"could not unpack rar file {archive}")


shutil.register_unpack_format("rar", [".rar"], unpack_rar)


# Helper function to find existing PDF files with a specific content hash
def find_pdf_with_content_hash(output_path: Path, content_hash: str) -> Optional[Path]:
    """Find an existing PDF file with the given content hash in the output directory"""
    if not content_hash:
        return None

    # Find all PDF files that start with the email prefix
    email_pdfs = list(output_path.glob(f"{EXTRACTED_EMAIL_FILENAME_PREFIX}*.pdf"))

    # Filter for files that contain the full hash
    matching_files = [
        f
        for f in email_pdfs
        if f.stem.endswith(f"_{content_hash}") or f"_{content_hash}_" in f.stem
    ]

    if matching_files:
        return matching_files[0]
    return None


# Helper function to check if a PDF file is an email content PDF with a hash in its name
def is_email_content_pdf_with_hash(file_path: Path) -> bool:
    """Check if a file is an email content PDF with a hash in its name"""
    if not file_path.is_file() or file_path.suffix.lower() != ".pdf":
        return False

    # Check if it's an email content PDF (starts with the email prefix)
    if not file_path.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX):
        return False

    # Check if it has a hash pattern in the filename (64 hex characters after an underscore)
    # This regex matches a pattern like _[64 hex chars] at the end of the stem or followed by another underscore
    stem = file_path.stem
    return bool(re.search(r"_[0-9a-f]{64}($|_)", stem))


# Helper function to extract the content hash from an email content PDF filename
def extract_content_hash_from_filename(file_path: Path) -> Optional[str]:
    """Extract the content hash from an email content PDF filename"""
    if not is_email_content_pdf_with_hash(file_path):
        return None

    # Extract the hash part from the filename using regex
    # Format: PREFIX_timestamp_[64 hex chars].pdf or PREFIX_timestamp_[64 hex chars]_something.pdf
    stem = file_path.stem
    match = re.search(r"_([0-9a-f]{64})($|_)", stem)
    if not match:
        return None

    # Return the full hash (64 characters)
    return match.group(1)


def unpack_eml(eml_file: Path, output_path: Path) -> List[Path]:
    assert eml_file.exists()
    assert output_path.exists()

    result = []

    try:
        # Generate PDF from email content if enabled
        if global_settings.CONVERT_EMAIL_CONTENT_TO_PDF:

            email_content = extract_eml_content(str(eml_file))
            # Create a deterministic representation of the email content
            html_str = None
            if email_content["is_html"]:
                html_str = email_content["html"][0:2000]

            content_for_hash = {
                "subject": email_content["metadata"]["subject"],
                "sender": email_content["metadata"]["sender"],
                "body": email_content["body"],
                "html": html_str,
            }
            content_str = json.dumps(content_for_hash, sort_keys=True)[0:2000]
            content_hash = hashlib.sha256(content_str.encode("utf-8")).hexdigest()

            # Check if we've already processed an email with this content hash and deduplication is enabled
            existing_pdf_path = None
            if global_settings.ENABLE_EMAIL_CONTENT_DEDUPLICATION:
                existing_pdf_path = find_pdf_with_content_hash(
                    output_path, content_hash
                )

            if existing_pdf_path:
                logger.info(
                    "Duplicate email content detected",
                    eml_file=str(eml_file),
                    content_hash=content_hash,
                    existing_pdf_path=str(existing_pdf_path),
                )

            else:
                # This is a new email content, create a new PDF with the hash in the filename
                pdf_filename = create_email_body_filename(content_hash)
                pdf_path = output_path / pdf_filename
                created = convert_email_to_pdf(email_content, pdf_path)
                if created:
                    result.append(pdf_path)

                    logger.info(
                        "Processing email content",
                        eml_file=str(eml_file),
                        content_hash=content_hash,
                        content_for_hash=content_for_hash,
                        pdf_path=str(pdf_path),
                    )
    except Exception:
        logger.exception("Could not convert msg email text into pdf", exc_info=True)

    # include_attachment_data is needed, otherwise attachments are not included
    ep = EmlParser(include_attachment_data=True)
    parsed_sub_email = ep.decode_email_bytes(eml_file.read_bytes())
    attachments = parsed_sub_email.get("attachment", [])

    # EmlParser pulls out attachments sub attachments from nested emails.
    # Hence if there is an attachment in a nexted email, it gets added twice.
    # Handle this behaviour by deduplicating the attachments by their hash
    # in a parent function

    for attachment in attachments:
        filename = attachment.get("filename")

        if not filename:
            continue  # Skip if no filename found

        # Dict with all header fields for this attachment
        content_headers = attachment.get("content_header", {})

        # This is e.g. 'image/jpeg; name="617 gvb max muster.jpg"'
        content_type = content_headers.get("content-type", [""])[0].lower()

        # This is e.g. 'attachment' or 'inline' but can be empty string for normal jpg attachments
        content_disposition = content_headers.get("content-disposition", [""])[
            0
        ].lower()

        # Skip HTML and plain text parts (likely email body)
        if content_type.startswith("text/plain") or content_type.startswith(
            "text/html"
        ):
            continue

        ext = Path(filename).suffix
        if ext not in ALL_SUPPORTED_FILE_SUFFIX:
            # filter out e.g. .htm, .p7s
            continue

        # Keep only real attachments and embedded images
        if content_disposition and (
            "attachment" not in content_disposition
            and "inline" not in content_disposition
        ):
            continue

        if "inline" in content_disposition and not content_type.startswith("image/"):
            continue  # Skip non-image inlines (e.g. inline HTML)

        # Normalize and fix filename
        if filename.startswith("part-") and "." not in filename:
            # This is probably a nested email inside the eml we try to unpack
            # Could be improved by checking the mimetype of this part if it
            # is really an eml file.
            filename = filename + ".eml"

        output_file = output_path / filename

        if output_file not in result:
            data = attachment.get("raw")
            if data is None:
                continue  # Skip if there's no data

            decoded_data = base64.b64decode(data)

            # Check file size before saving
            min_size_bytes = MIN_SIZE_EMAIL_ATTACHMENT_UNKOWN_TYPE_FILTER_LIMIT
            ext = Path(filename).suffix.lower()
            if ext in DEFAULT_SMALL_FILES_FILTER.small_files_map:
                min_size_bytes = DEFAULT_SMALL_FILES_FILTER.small_files_map[ext]
            size_attachment_bytes = len(decoded_data)
            if size_attachment_bytes <= min_size_bytes:
                logger.warning(
                    "Skipping small attachment eml",
                    filename=filename,
                    actual_size_bytes=size_attachment_bytes,
                    min_size_bytes=min_size_bytes,
                    parent_eml=str(eml_file),
                    ext=ext,
                )
                continue

            output_file.write_bytes(decoded_data)
            result.append(output_file)

    return result


def unpack_eml_wrapper(eml_file: str, dest: str):
    """
    :param str eml_file: path to msg file that should be unpacked as a string
    :param str dest: path to directory into which the attachments should be saved as a string
    :return: List of Paths inside the dest directory of all files that have been extracted
    """
    return unpack_eml(Path(eml_file), Path(dest))


shutil.register_unpack_format("eml", [".eml"], unpack_eml_wrapper)


def unpack_signed_message_content_p7m(archive_p7m: str, dest: str):
    """
    :param str archive_p7m: path to file that is inside a msg and contains the signed message and its attachments
    :param str dest: path to directory into which the attachments should be saved as a string
    :return: Replace p7m file by a decoded eml file. We assume that no validation of the signature with public key
    is necessary, just decoding
    """
    p_in = Path(archive_p7m)
    p_out = Path(dest) / f"{p_in.stem}.eml"
    decode_smime_email_with_signature(p_in, p_out)


shutil.register_unpack_format("p7m", [".p7m"], unpack_signed_message_content_p7m)


def unpack_any_image(img_in_str: str, dest: str):
    p_in = Path(img_in_str)
    p_dir_out = Path(dest)
    convert_any_image_to_jpeg(p_in, p_dir_out)


shutil.register_unpack_format("avif", [".AVIF", ".avif"], unpack_any_image)
shutil.register_unpack_format("heic", [".HEIC", ".heic"], unpack_any_image)
shutil.register_unpack_format("heif", [".HEIF", ".heif"], unpack_any_image)

# Tiff is supported by ABBYY but in case it has syntax errors most of them could be fixed by loading and
# saving it in pillow so we convert all of them to jpeg
shutil.register_unpack_format(
    "tiff", [".TIFF", ".tiff", ".TIF", ".tif"], unpack_any_image
)

# Rely on py7zr to unpack 7z: https://pypi.org/project/py7zr/0.3.2/#description
shutil.register_unpack_format("7zip", [".7z"], unpack_7zarchive)


def unzip(f, dest: Path, encoding, v):
    with zipfile.ZipFile(f) as z:
        for i in z.namelist():
            n = dest / Path(i.encode("cp437").decode(encoding))
            if v:
                print(n)
            if i[-1] == "/":
                if not n.exists():
                    n.mkdir()
            else:
                with n.open("wb") as w:
                    w.write(z.read(i))


def _ensure_directory(path):
    """This has been copied from shutil because it is needed by _unpack_zipfile_from_shutil"""
    """Ensure that the parent directory of `path` exists"""
    dirname = os.path.dirname(path)
    if not os.path.isdir(dirname):
        os.makedirs(dirname)


def is_att_file(name: str):
    if name:
        s = name.lower()

        r = r"att[\d]{3,8}.(htm|html|txt)"

        if s:
            ret = re.match(r, s)
            if ret:
                return True
    return False


def is_mac_os_zip_trash(name: str, filename: str):
    if name and filename:
        if filename.endswith(".zip"):
            if name == "__MACOSX" or name.startswith("__MACOSX/"):
                return True
    return False


def _unpack_zipfile_from_shutil(filename, extract_dir):
    """Unpack zip `filename` to `extract_dir`"""
    import zipfile  # late import for breaking circular dependency

    if not zipfile.is_zipfile(filename):
        raise ZipReadError("%s is not a zip file" % filename)

    zip = zipfile.ZipFile(filename)
    try:
        for info in zip.infolist():
            name = info.filename

            # don't extract absolute paths or ones with .. in them
            # THIS LINE HAS BEEN CHANGED BY MT 210610. REST OF FUNCTION IS THE SAME
            if name.startswith("/") or "../" in name or "..\\" in name:
                continue

            if is_att_file(name):
                continue

            if is_mac_os_zip_trash(name, filename):
                continue

            target = os.path.join(extract_dir, *name.split("/"))
            if "╠" in target or "├" in target:
                target = target.encode("cp437").decode("utf-8")

            if not target:
                continue

            _ensure_directory(target)
            if not name.endswith("/"):
                # file
                data = zip.read(info.filename)
                f = open(target, "wb")
                try:
                    f.write(data)
                finally:
                    f.close()
                    del data
    finally:
        zip.close()


shutil.unregister_unpack_format("zip")
shutil.register_unpack_format("zip", [".zip"], _unpack_zipfile_from_shutil, [])


def is_mac_os_ds_store(filename: str):
    # "DS_Store" would be an invalid name (it should be ".DS_Store") but
    # happens in real live, therefore exclude it as well
    return filename.lower() == ".ds_store" or filename == "DS_Store"


def is_ignored_file_in_unpack(rel_path: str, file_path: str):
    filename = rel_path

    filenames_to_ignore_in_unpack = [
        # Secure/Multipurpose Internet Mail Extensions (S/MIME), contains no interesting data for us
        # Caution: p7m is something different, this is an encoded (signed) email content which needs to be decoded
        "smime.p7s",
        "SMIME.p7s",
    ]
    if filename in filenames_to_ignore_in_unpack:
        # All these filenames are explicitly ignored and trigger no error message
        return True

    if is_att_file(filename):
        # Silently ignore this email inline attachment file:
        # http://kb.mit.edu/confluence/pages/viewpage.action?pageId=4981187
        return True
    if is_mac_os_ds_store(filename):
        # Silently ignore this Mac OS file: https://en.wikipedia.org/wiki/.DS_Store
        return True

    return False


def has_ignored_ending(current_file: Path) -> bool:
    """
    This will be used for filtering out extensions mostly
    """
    for ending in filename_endings_to_ignore_in_unpack:
        if current_file.name.endswith(ending):
            return True
    return False


class ZipReadError(Exception):
    pass


def unpack_path(file: Path) -> FileExtraction:
    """
    :param Path file: path to file which will be unpacked inplace. If the file can be unpacked it is replaced
    by a folder containing its unpacked tree of children (on potentially multiple levels). Else a FileExtraction
    with a single entry (the file unchanged) will be returned
    """

    assert file.is_file()

    exceptions = {}
    extracted_files = []

    # Dictionary to track email content hashes during this unpacking operation
    # Key: content hash (8 chars), Value: relative path to the PDF file
    email_content_hashes = {}

    files_to_extract = [file]
    while len(files_to_extract) > 0:
        current_file = files_to_extract.pop()
        if current_file.is_dir():
            continue

        rel_path = str(current_file.relative_to(file.parent))
        rel_path = ensure_path_has_forward_slashes(rel_path)

        ext = current_file.suffix.lower()
        if ext == ".pdf":
            try:
                remove_access_restrictions_pdf(current_file)
            except:
                logger.info(
                    "optionally remove access restrictions failed",
                    current_file=current_file.name,
                )
        if ext in [".xlsm"]:
            # Try to convert xlsm to xlsx and then push the new xlsx file back to the files_to_extract
            try:
                converted_current_file = convert_xlsm_to_xlsx(current_file)
                files_to_extract.append(converted_current_file)
                continue
            except Exception:
                logger.error(
                    f"Found excel xlsm file {current_file} and tried to convert it to xlsx but an error occurred. Further processing this file is not supported"
                )
                exceptions[rel_path] = XLSM_FILE_CANNOT_BE_PROCESSED
                extracted_files.append(rel_path)
                continue

        elif ext in [".xlsx"]:
            try:
                remove_access_restrictions_excel(current_file)
            except:
                logger.info(
                    f"Found excel file {current_file} and tried to remove access restrictions (unprotect sheets, convert xlsm to xlsx, set printing dimensions for all sheets) but an error occurred."
                    "Removing the access restrictions is optional, so the error is just ignored"
                )

        elif has_ignored_ending(current_file):
            # just skip these files without any warning
            continue
        elif is_ignored_file_in_unpack(current_file.name, str(current_file)):
            # just skip these files without any warning
            continue

        # unpack with shutil.unpack_archive
        if any(
            str(current_file).endswith(extension)
            for extension in shutil_supported_suffixes()
        ):
            # try to unpack file, if there is an exception, add the current file and reraise

            try:
                with TemporaryDirectory() as temp_dir:
                    shutil.unpack_archive(current_file, temp_dir)

                    current_file.unlink()
                    shutil.move(temp_dir, current_file)
                    files = list(current_file.glob("**/*"))

                    # try to fix encoding if the file is from a mac (maybe unreliable)
                    try:
                        for f in files:
                            if "╠" in f.name or "├" in f.name:
                                f_new = f.with_name(
                                    f.name.encode("cp437").decode("utf-8")
                                )
                                shutil.move(f, f_new)
                    except Exception:
                        logger.error("could not fix filename of zipfile")

                    # try to fix extensions and limit filename length
                    # files are renamed in the filesystem in this loop
                    # fresh list of files from the filesystem is used afterwards
                    try:
                        for f in files:
                            p = Path(f)

                            if p.is_file():
                                filename = p.name
                                (
                                    filename,
                                    oldfilename,
                                    changed,
                                ) = optionally_shorten_filename(
                                    filename,
                                    global_settings.MAX_FILENAME_LENGTH_FOR_PROCESSING,
                                    global_settings.SUFFIX_FOR_SHORTENED_FILENAME,
                                )
                                if changed:
                                    logger.warning(
                                        "Filename of extractedfile was longer than allowed and therefore shorted.",
                                        filename=filename,
                                        oldfilename=oldfilename,
                                    )
                                    p_corrected = Path(f).parent / filename
                                    shutil.move(f, p_corrected)

                                ret = optionally_fix_fileextension(
                                    f, ALL_SUPPORTED_FILE_SUFFIX
                                )
                                if ret.changed:
                                    logger.warning(
                                        f"Wrong extension found in unpack for f={f}: {ret}"
                                    )
                    except Exception:
                        logger.error(
                            f"could not fix extensions inside files={files}",
                            exc_info=True,
                        )

                    files = list(current_file.glob("**/*"))
                    files_to_extract.extend(files)
            except ZipReadError:
                logger.exception("Could not read zip file", exc_info=True)
                exceptions[rel_path] = NOT_READABLE_EXCEPTION_DETAILS
                extracted_files.append(rel_path)
            except OSError as e:
                if e.errno == 22:
                    exceptions[rel_path] = NOT_READABLE_EXCEPTION_DETAILS.copy(
                        update={"details": str(e)}
                    )
                else:
                    exceptions[rel_path] = UNKNOWN_EXCEPTION_DETAILS.copy(
                        update={"details": str(e)}
                    )
                    logger.exception("Could not read the file.", exc_info=True)
                extracted_files.append(rel_path)
            except RuntimeError as e:
                if "is encrypted, password required for extraction" in str(e):
                    exceptions[rel_path] = PASSWORD_PROTECTED_EXCEPTION_DETAILS
                else:
                    exceptions[rel_path] = UNKNOWN_EXCEPTION_DETAILS
                    logger.exception("Could not extract the file.", exc_info=True)
                extracted_files.append(rel_path)
            except Exception as e:
                stacktrace = "".join(
                    traceback.TracebackException.from_exception(e).format()
                )
                exceptions[rel_path] = UNKNOWN_EXCEPTION_DETAILS.copy(
                    update={"details": str(e), "stacktrace": stacktrace}
                )
                logger.exception("Could not extract file.", exc_info=True)
                logger.error(
                    f"stacktrace on unzip exception of file {file}: {stacktrace}"
                )
                extracted_files.append(rel_path)
        else:
            # Check if this is an email content PDF with a hash in its name
            if (
                global_settings.ENABLE_EMAIL_CONTENT_DEDUPLICATION
                and is_email_content_pdf_with_hash(current_file)
            ):
                # Extract the content hash from the filename
                content_hash = extract_content_hash_from_filename(current_file)
                if content_hash:
                    # Check if we've already seen this content hash
                    if content_hash in email_content_hashes:
                        existing_pdf_path = email_content_hashes[content_hash]
                        logger.info(
                            "Duplicate email content detected during unpacking",
                            current_file=str(current_file),
                            content_hash=content_hash,
                            existing_pdf_path=existing_pdf_path,
                        )
                        # Skip this file as it's a duplicate
                        continue
                    else:
                        # Store this content hash and its path
                        email_content_hashes[content_hash] = rel_path

            # Add the file to the extracted files list
            extracted_files.append(rel_path)

    return FileExtraction(
        original_file=file.name,
        extracted_files=sorted(extracted_files),
        exceptions=exceptions,
    )


def remove_access_restrictions_pdf(current_file):
    logger.info("rm access restrictions on pdf", current_file=current_file)
    if current_file.suffix.lower() in [".pdf"]:
        with TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            temp_file = temp_path / current_file.name
            copyfile(current_file, temp_file)
            with pikepdf.open(temp_file) as input_pdf:
                if not input_pdf._allow_extract:
                    input_pdf.save(str(current_file))


def remove_access_restrictions_excel(current_file):
    logger.info("removing access restrictions on excel", current_file=current_file)
    if current_file.suffix.lower() in [".xlsx", ".xlsm"]:
        excel_unprotect(current_file, current_file)


def convert_xlsm_to_xlsx(current_file: Path) -> Path:
    """
    We still want to see that the xlsx came from a xlsm file. Therefore the path should be nested myfile.xlsm/myfile.xlsx.
    We cannot create directly a myfile.xlsm directory as a file with the same name exists. So we use a temp directory name generated as a uuid

    So move file /tmp/xyz/myfile.xlsm to /tmp/xyz/myfile.xlsm/myfile.xlsm and then convert to
    /tmp/xyz/myfile.xlsm/myfile.xlsx
    """
    if current_file.suffix.lower() in [".xlsm"]:
        rand_id = str(uuid4())

        # E.g. /tmp/xyz/785346a7-0680-4e1c-87df-fdd0a7128bb5/myfile.xlsm
        temp_current_file = current_file.parent / rand_id / current_file.name
        temp_current_file.parent.mkdir(parents=True, exist_ok=True)
        shutil.move(current_file, temp_current_file)

        # E.g. /tmp/xyz/myfile.xlsm/myfile.xlsm
        nested_current_file = (
            current_file.parent / current_file.name / current_file.name
        )

        os.rename(str(temp_current_file.parent), nested_current_file.parent)

        # Successful conversion yields same filename with xlsx instead of xlsm
        p_out = xlsm_to_xlsx(nested_current_file)
        return p_out
    return current_file


def shutil_supported_suffixes():
    formats = set()
    for k in shutil.get_unpack_formats():
        formats = formats.union(set(k[1]))
    return formats
