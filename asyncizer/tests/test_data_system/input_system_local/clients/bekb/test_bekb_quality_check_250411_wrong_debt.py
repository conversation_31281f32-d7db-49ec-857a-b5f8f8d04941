from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250408_wrong_debt"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_bekb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_wrong_debt_single_page():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "single_page",
        show_page=-1,
        source_file_filter=["03.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Would be land register but was incorrectly identified as
            # DEBT_INFORMATION with 92% confidence
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN_DE: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_wrong_debt_full_doc():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "full_doc",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 1,
                DocumentCat.WHITE_PAGES: 1,
            },
            expected_num_processing_exceptions=0,
        ),
    ).run()
