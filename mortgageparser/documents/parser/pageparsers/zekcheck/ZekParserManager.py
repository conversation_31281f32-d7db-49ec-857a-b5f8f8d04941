from hypodossier.core.domain.PageLocation import PageLocation
from mortgageparser.documents.parser.ParserManager import ParserManager

from mortgageparser.documents.parser.pageparsers.zekcheck.get_zekcheck_parsers import (
    get_parsers_zekcheck,
)


class ZekParserManager(ParserManager):
    def __init__(self, lang: str, page_source: PageLocation):
        super().__init__(lang, page_source)

        self.add_parsers(get_parsers_zekcheck())
