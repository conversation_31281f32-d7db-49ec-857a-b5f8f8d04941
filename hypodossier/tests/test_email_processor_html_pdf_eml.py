import shutil
from tempfile import TemporaryDirectory

import pytest
from pathlib import Path
from email.message import EmailMessage

from asyncizer.tests.util_tests import DossierT<PERSON>, DossierExpectations
from constants import EXTRACTED_EMAIL_FILENAME_PREFIX
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.email_processor import (
    extract_eml_content,
    convert_email_to_pdf,
    html_to_pdf,
)
import pdfplumber

from hypodossier.unpack import unpack_eml, unpack_path


@pytest.fixture
def demo_email_path():
    """Path to demo email files"""
    return Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS) / "email_processor"


def create_test_eml_with_long_content(tmp_path, content_type="text/plain"):
    """Helper to create test .eml file with long content"""
    file_path = tmp_path / "long_content.eml"

    msg = EmailMessage()
    msg["Subject"] = "Test Long Email"
    msg["From"] = "<EMAIL>"
    msg["To"] = "<EMAIL>"
    msg["Date"] = "Wed, 1 Jan 2023 10:00:00 +0000"

    # Create a long email body with multiple paragraphs
    long_content = "\n\n".join(
        [
            "This is paragraph 1 with some content.",
            "This is paragraph 2 with different content.",
            "This is paragraph 3 with more text.",
            "This is paragraph 4 with additional information.",
            "This is the final paragraph with concluding text.",
        ]
    )

    if content_type == "text/plain":
        msg.set_content(long_content)
    else:  # text/html
        content = "<br><br>".join(long_content.split("\n\n"))
        html_content = f"<html><body>{content}</body></html>"
        msg.add_alternative(html_content, subtype="html")

    with open(file_path, "wb") as f:
        f.write(msg.as_bytes())

    return file_path


def extract_text_from_pdf(pdf_path):
    """Helper to extract text from PDF"""
    with pdfplumber.open(pdf_path) as pdf:
        return "\n".join(page.extract_text() for page in pdf.pages)


@pytest.mark.parametrize("content_type", ["text/plain", "text/html"])
def test_email_content_preservation(tmp_path, content_type):
    """Test that full email content is preserved in PDF conversion"""
    # Create test email
    eml_file = create_test_eml_with_long_content(tmp_path, content_type)

    # Extract content
    email_content = extract_eml_content(eml_file)

    # Convert to PDF
    pdf_path = tmp_path / "output.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Extract text from PDF
    pdf_text = extract_text_from_pdf(pdf_path)

    # Verify metadata is present
    assert "From: <EMAIL>" in pdf_text
    assert "To: <EMAIL>" in pdf_text
    assert "Subject: Test Long Email" in pdf_text

    # Verify all paragraphs are present
    expected_content = [
        "This is paragraph 1",
        "This is paragraph 2",
        "This is paragraph 3",
        "This is paragraph 4",
        "This is the final paragraph",
    ]

    for content in expected_content:
        assert content in pdf_text, f"Missing content: {content}"


def test_very_long_email_content(tmp_path: Path):
    """Test handling of very long email content"""
    # Create email with very long content
    file_path = tmp_path / "very_long.eml"

    msg = EmailMessage()
    msg["Subject"] = "Very Long Email"
    msg["From"] = "<EMAIL>"
    msg["To"] = "<EMAIL>"

    # Create long content with 100 paragraphs
    long_content = "\n\n".join(
        [
            f"This is paragraph {i} with some repeated content to make it longer." * 3
            for i in range(1, 101)
        ]
    )

    msg.set_content(long_content)

    with open(file_path, "wb") as f:
        f.write(msg.as_bytes())

    # Process email
    email_content = extract_eml_content(str(file_path))
    pdf_path = tmp_path / "long_output.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Verify PDF content
    pdf_text = extract_text_from_pdf(pdf_path)

    # Check for presence of first, middle and last paragraphs
    assert "This is paragraph 1" in pdf_text
    assert "This is paragraph 50" in pdf_text
    assert "This is paragraph 100" in pdf_text


def test_email_with_special_characters(tmp_path):
    """Test handling of emails with special characters"""
    file_path = tmp_path / "special_chars.eml"

    msg = EmailMessage()
    msg["Subject"] = "Special Characters Test äöüß"
    msg["From"] = "sender@例子.com"
    msg["To"] = "<EMAIL>"

    #     Chinese: 你好世界
    #     Japanese: こんにちは世界
    #     Emoji: 👋 🌍 🎉

    content = """
    Hello with special characters:
    German: äöüß
    French: éèêë
    """

    msg.set_content(content)

    with open(file_path, "wb") as f:
        f.write(msg.as_bytes())

    # Process email
    email_content = extract_eml_content(str(file_path))
    pdf_path = tmp_path / "special_chars.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Verify PDF content
    pdf_text = extract_text_from_pdf(pdf_path)

    # We currently don't support non latin characters in extractor

    # assert "特殊字符测试" in pdf_text
    # assert "你好世界" in pdf_text
    # assert "こんにちは世界" in pdf_text
    # Check for presence of special characters
    assert "äöüß" in pdf_text
    assert "éèêë" in pdf_text


@pytest.mark.parametrize(
    "eml_file",
    [
        "emails_without_attachment/html_test_email_eml/html_test_email.eml",
        "emails_without_attachment/plain_text_email_eml/plain_text_email.eml",
    ],
)
# @pytest.mark.skip("Weasyprint doesn't render email addresses in PDFs")
def test_extract_eml_demo_emails(demo_email_path, eml_file, tmp_path):
    """Test extracting content from demo .eml files"""
    file_path = demo_email_path / eml_file
    assert file_path.exists(), f"Demo file not found: {file_path}"

    email_content = extract_eml_content(file_path)

    # Basic validation that content was extracted
    assert email_content["metadata"]["subject"] is not None
    assert email_content["body"] is not None

    pdf_path = tmp_path / "eml_demo_email.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Verify PDF content
    pdf_text = extract_text_from_pdf(pdf_path)

    # weasyprint's HTML hides the email address, so we can't check for it, which is why this fails
    # could also be due to css styling, but I can't see anything in the css that would do it
    assert "Manuel Thiemann <<EMAIL>>" in pdf_text
    # Check that special characters are preserved
    assert "Zürich" in pdf_text


def test_html_to_pdf_offline():
    """
    Test HTML to PDF conversion works without internet access.
    This causes an error log from weasyprint because dummy http url is not processed,
    but the PDF is still created.
    """
    email_content = {
        "metadata": {
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "subject": "Test Email",
            "date": "2024-03-07 13:00:00",
        },
        "html": """
        <html>
        <body>
            <h1>Test Email</h1>
            <p>This is a test paragraph.</p>
            <!-- Remote image that should fail gracefully -->
            <img src="https://68b3f571-edef-47d1-b732-229811f9acc9/image.jpg" alt="Remote Image">
            <!-- Local content that should work -->
            <div style="width:100px;height:100px;background-color:blue;">
                Local colored div
            </div>
        </body>
        </html>
        """,
    }

    with TemporaryDirectory() as temp_dir:
        output_path = Path(temp_dir) / "output.pdf"

        # Convert HTML to PDF
        created = html_to_pdf(email_content, output_path)
        assert created

        # Verify the PDF was created
        assert output_path.exists()

        # Extract and verify PDF content
        with pdfplumber.open(output_path) as pdf:
            text = pdf.pages[0].extract_text()

            # Check metadata is present
            assert "From: <EMAIL>" in text
            assert "To: <EMAIL>" in text
            assert "Subject: Test Email" in text

            # Check content is present
            assert "Test Email" in text
            assert "This is a test paragraph" in text


def test_extract_nested_eml_emails(demo_email_path, tmp_path):

    file_path = (
        demo_email_path / "email_messages_nested/eml_within_eml_with_attachments.eml"
    )
    assert file_path.exists(), f"Demo file not found: {file_path}"

    email_content = extract_eml_content(file_path)

    # Basic validation that content was extracted
    assert email_content["metadata"]["subject"] is not None
    assert email_content["body"] is not None

    pdf_path = tmp_path / "eml_demo_email.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Verify PDF content
    pdf_text = extract_text_from_pdf(pdf_path)

    assert "Piotr Gryko <<EMAIL>>" in pdf_text


def test_extract_weird_plain_text_formating(demo_email_path, tmp_path):
    """Test that font renders correctly for plain text emails with weird formatting
    in this case Polish characters
    """

    file_path = (
        demo_email_path / "email_formatting/GRYCZANKA_test_polish_characters.eml"
    )
    assert file_path.exists(), f"Demo file not found: {file_path}"

    email_content = extract_eml_content(file_path)

    # Basic validation that content was extracted
    assert email_content["metadata"]["subject"] is not None
    assert email_content["body"] is not None

    pdf_path = tmp_path / "eml_demo_email.pdf"
    convert_email_to_pdf(email_content, pdf_path)

    # Verify PDF content
    pdf_text = extract_text_from_pdf(pdf_path)

    # <AUTHOR> <EMAIL>" in pdf_text
    assert "Subject: Fwd: Twoje zamówienie od GRYCZANKA jest już spakowane!" in pdf_text
    assert "linku będą dla Ciebie niepełne, lub masz dowolnie inną sprawę"


@pytest.mark.asyncio
async def test_extract_weird_plain_text_formating_as_dossier_test(demo_email_path):

    # mt 250328: currently failing because this is duplicate extraction:
    # eml_within_eml_with_attachments.eml/Elektroniczny dokument sprzedaży do zamówienia nr ***********.eml/dokument_sprzedazy.pdf
    # eml_within_eml_with_attachments.eml/dokument_sprzedazy.pdf

    target_folder = demo_email_path / "email_formatting"
    dest_folder_prefix = "input_system_components_email_"

    assert target_folder.exists()
    # Two emails, one nested within the other, with attachments in both

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["GRYCZANKA_test_polish_characters.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_DE: 1,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_extract_attachments_nested_eml_emails(demo_email_path):
    # https://pypi.org/project/mail-parser/
    """Test extracting attachments from demo emails
    This test takes about 30 sec to run, probably reportlab is slow

    ['multipart/mixed',
    'text/plain',
    'application/pdf',
    'message/rfc822',
    'multipart/mixed',
    'text/html',
    'application/pdf']

    """
    with TemporaryDirectory() as temp_dir:

        # Test .eml file with attachments
        eml_file = (
            demo_email_path
            / "email_messages_nested/eml_within_eml_with_attachments.eml"
        )
        assert eml_file.exists()

        attachments = unpack_eml(eml_file, Path(temp_dir))
        assert len(attachments) > 0

        # Verify email body PDF was created
        pdf_files = [
            att
            for att in attachments
            if att.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
        ]
        assert len(pdf_files) == 1


@pytest.mark.asyncio
def test_unpack_path_eml(demo_email_path):

    test_eml = demo_email_path / "email_messages_nested/"

    with TemporaryDirectory() as temp_dir:
        # Copy test_eml to temp_dir
        shutil.copy2(test_eml / "eml_within_eml_with_attachments.eml", temp_dir)

        unpack_path(file=Path(temp_dir) / "eml_within_eml_with_attachments.eml")

        # Method 1: Using Path.glob() for all files
        files_list = list(
            Path(Path(temp_dir) / "eml_within_eml_with_attachments.eml").glob("*")
        )

        # Original verification code
        assert len(files_list) > 0

        pdf_files = [
            f for f in files_list if f.name.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
        ]
        assert len(pdf_files) == 1

        assert "Karta zamówienia.pdf" in str(files_list)
