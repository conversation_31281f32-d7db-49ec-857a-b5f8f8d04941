from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON><PERSON><PERSON><PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL}/email/misc_emails_with_attachments"
)
dest_folder_prefix = "input_system_components_local_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_outer_email_with_attachment_misc_01():
    target_folder = path_root_folder / "misc_01"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 1},
            expected_page_objects={
                0: {
                    "email_from": "<EMAIL>",
                    "email_to": "<EMAIL>",
                    "email_date": "11.03.2025",
                    "email_subject": "12577542 Sieber Urs + Liselotte: VSW",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_outer_email_with_attachment_misc_02():
    target_folder = path_root_folder / "misc_02"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(  # Email is detected correcly, rest is not perfect
            expected_doc_cat_frequency={
                DocumentCat.BUILDING_DESCRIPTION: 1,
                DocumentCat.CONSTRUCTION_QUOTATION: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PLAN_FLOOR: 1,
                DocumentCat.UNKNOWN_DE: 4,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_outer_email_with_attachment_misc_03():
    target_folder = path_root_folder / "misc_03"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PENSION_CERTIFICATE: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PENSION_CERTIFICATE: 3,
                DocumentCat.PENSION_CERTIFICATE_LETTER: 1,
            },
            expected_page_objects={
                0: {
                    "document_date": "15.01.2025",
                    "document_validity_start_date": "01.01.2025",
                    "marital_status": "ledig",
                    "degree_employment": "60%",
                    "address_block": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 60'000",
                    "applicable_annual_salary_insured": "CHF 44'124",
                    "current_assets": "CHF 66'829.25",
                    "url_email_company": "www.servisa.ch",
                    "firstname": PO_EXISTS,
                    "fullname": PO_EXISTS,
                    "date_of_birth": "31.07.1984",
                    "sex": "m",
                }
            },
        ),
    ).run()
