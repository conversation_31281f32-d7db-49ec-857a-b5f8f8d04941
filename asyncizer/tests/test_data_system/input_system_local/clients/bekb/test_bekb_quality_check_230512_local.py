from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierE<PERSON>pect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from mortgageparser.util.file_extension import (
    get_file_extension,
    is_wrong_processable_file_extension,
    get_file_extension_by_mimetype,
)

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "230512_unknown_extension"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_bekb_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


def test_unknown_extensions_files_locally_with_fix():
    for p in path_root_folder.iterdir():
        if not p.is_file():
            continue

        ext = p.suffix
        ret = is_wrong_processable_file_extension(str(p))
        logger.info(f"{p} -> {ext} / {ret}")

        if (
            str(p.name)
            == 'Rechnung Gebäudeversicherungsausweis 2023.jpg"; \tfilename*1="'
        ):
            assert ret.changed
            assert ret.actual_extension == '.jpg"; 	filename*1="'
            assert ret.corrected_extension == ".jpeg"
            corrected_filename = Path(ret.corrected_filename).name
            assert (
                corrected_filename
                == 'Rechnung Gebäudeversicherungsausweis 2023.jpg"; 	filename*1="_fixext.jpeg'
            )
        elif str(p.name) == "broken_pdf.pdf":
            assert not ret.changed
            assert ret.corrected_filename is None
            assert ret.corrected_extension is None
        elif str(p.name) == "Rechnung Gebäudeversicherungsausweis 2023 (copy).jpg":
            # do NOT correct .jpg to .jpeg, just leave it!
            assert not ret.changed
            assert ret.corrected_extension is None
            assert ret.true_extension == ".jpeg"
        elif str(p.name) == "msg_with_unknown_fileext.msg":
            assert not ret.changed
            assert ret.corrected_extension is None
        else:
            raise Exception(f"not check for {p.name}")


def test_unknown_extensions_files_locally():
    for p in (path_root_folder / "broken").iterdir():
        ext = p.suffix
        extension = get_file_extension(str(p))
        assert extension is None
        print(f"{p} -> {ext} / {extension}")


def test_unknown_extensions_files_locally_for_mimetype():
    for p in (path_root_folder / "broken").iterdir():
        ext = p.suffix
        extension = get_file_extension_by_mimetype(str(p))
        assert extension is None
        print(f"{p} -> {ext} / {extension}")


@pytest.mark.asyncio
async def test_unknown_extensions_dossier():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "broken",
        source_file_filter=["broken_pdf.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={}, expected_num_processing_exceptions=1
        ),
    ).run()
