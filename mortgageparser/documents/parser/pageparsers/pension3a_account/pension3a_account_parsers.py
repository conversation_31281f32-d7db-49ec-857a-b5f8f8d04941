from typing import List

from abbyyplumber.converter.ValueConverter import MostRecentDateConverter
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementArea,
    create_labeled_field,
    SearchElementLabeledField,
    create_labeled_field_vertical,
)
from abbyyplumber.plumberstudio.SearchRelation import SearchRelationRightOf
from abbyyplumber.util.plumberstudio_util import (
    PercentageRange,
    RANGE_LEFT,
    RANGE_RIGHT,
    FieldPosition,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from hypodossier.util.constants import LANG_FR
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardContainsAllPageParser,
)
from mortgageparser.documents.parser.pageparsers.pension3a_account.CommonPension3aAccountParsers import (
    SmartPension3aAccountParser,
)
from mortgageparser.documents.parser.pageparsers.pension3a_account.GenericPension3aAccountDE import (
    GenericPension3aAccountDE,
    Pension3aAccountCreditNotePageParser,
)
from mortgageparser.documents.parser.pageparsers.pension3a_account.pension_3a_util import (
    TemplatePension3aAccountPageParser,
    Pension3aSearchElements,
)

# Page 2 or later for account statement
from mortgageparser.util.search_element_util import create_address_region


def createPension3aAccountAdditionalPageParser(
    contains_all: List[str],
    max_l_dist: int = 4,
    company=None,
    desc="3a Account Statement additional page",
):
    return StandardContainsAllPageParser(
        desc=desc,
        page_cat=PageCat.PENSION3A_ACCOUNT_STATEMENT_ADDITIONAL_PAGE,
        contains_all=contains_all,
        max_l_dist=max_l_dist,
        company=company,
    )


def get_parsers_pension3a_account():
    parsers_de = [
        TemplatePension3aAccountPageParser(
            company="AGKB",
            desc="3a Account AGKB Vorsorgeportfolio 2016",
            ranked_titles_all=[RankedTitle("Gesamttotal")],
            required_tokens=[
                "Aargauischen Kantonalbank",
                "Vorsorgestiftung Sparen 3",
                "Vermögensausweis per",
                "Gesamttotal",
            ],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="Für Sie zuständig",
                #                              text_bottom="Vermögensausweis per",
                #                              x_range=PercentageRange(0.5, 1)),
                # SearchElementConstrainedArea("region_total", None, text_top="Total Vorsorgeguthaben",
                #                              text_bottom="Total Vorsorgeguthaben").include_vertical(),
                SearchElementConstrainedArea(
                    "region_date", None, text_top="Depot-Nr", text_bottom="Depot-Nr"
                ).include_bottom(3)
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None, None, text_bottom="Kunden-Nr.", text_right="Kunden-Nr."
                ).include_right(20),
                account_no=create_labeled_field("Depot-Nr."),
                # iban=SearchElementConstrainedArea(None, None, text_top="Inhaber", text_left="Konto:",
                #                                   text_bottom="Vorsorgeplan", text_right=" / CHF"),
                document_date=create_labeled_field(
                    "Vermögensausweis per", "region_date"
                ),
                total_amount=SearchElementConstrainedArea(
                    None,
                    None,
                    text_top="Gesamttotal",
                    text_left="Marktwert",
                    text_right="Marktwert",
                    text_bottom="Gesamttotal",
                ).include_all(-1, -3, 3, 1.5),
            ),
        ),
        TemplatePension3aAccountPageParser(
            supported_languages=["en"],
            company="AGKB",
            desc="3a Account AGKB Konto EN 1/x 2016",
            ranked_titles_all=[
                RankedTitle("Account statement"),
                RankedTitle("Vorsorgestiftung Sparen 3"),
            ],
            required_tokens=[
                "Aargauischen Kantonalbank",
                "Savings 3 CHF",
                "IBAN",
                "Balance",
            ],
            use_document_date=True,
            regions=[
                create_address_region(RANGE_RIGHT, ["Account Statement"]),
                SearchElementConstrainedArea(
                    "region_total", None, text_top="Volume of transactions"
                ),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None, None, texts_bottom=["IBAN"], texts_left=["IBAN"]
                ).include_left(),
                total_amount=create_labeled_field("Balance", "region_total"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            page_cat=PageCat.PENSION3A_ACCOUNT_STATEMENT_ADDITIONAL_PAGE,
            supported_languages=["en"],
            company="AGKB",
            desc="3a Account AGKB Konto EN 2/2 2016",
            ranked_titles_all=[RankedTitle("Vorsorgestiftung Sparen 3")],
            required_tokens=[
                "Aargauischen Kantonalbank",
                "Savings 3 CHF",
                "IBAN",
            ],  # 'Balance' is missing here
            use_document_date=True,
            regions=[
                create_address_region(RANGE_RIGHT, ["Account Statement"]),
                SearchElementConstrainedArea(
                    "region_total", None, text_top="Volume of transactions"
                ),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None, None, texts_bottom=["IBAN"], texts_left=["IBAN"]
                ).include_left(),
                # total_amount=create_labeled_field("Balance", "region_total"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="BLKB",
            desc="3a Account BLKB Vorsorgekonto 2015",
            ranked_titles_all=[RankedTitle("Kontoauszug")],
            required_tokens=[
                "Vorsorgestiftung Sparen 3 der",
                "Basellandschaftlichen Kantonalbank",
                "Sparen 3-Konto",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Kontoauszug in CHF",
                    x_range=RANGE_RIGHT,
                ),
                SearchElementConstrainedArea(
                    "region_total", None, text_top="Umsatztotal"
                ),
                SearchElementConstrainedArea(
                    "region_date", None, text_bottom="Konotoauszug in CHF"
                ).include_bottom(3),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None, None, text_bottom="IBAN", text_left="IBAN"
                ).include_left(-2),
                iban=create_labeled_field("IBAN"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=create_labeled_field("Saldo", target_name="region_total"),
            ),
        ),
        GenericPension3aAccountDE(
            "Bank CIC",
            ["Saldoübersicht Konten"],
            "Saldoübersicht Konten per",
            ["Vorsorgekonto 3a", "Bank CIC (Schweiz) AG", "BANQUE CIC SUISSE"],
        ),
        TemplatePension3aAccountPageParser(
            company="BEKB",
            desc="3A Account BEKB Gutschriftsanzeige 2016",
            ranked_titles_all=[RankedTitle("Gutschriftsanzeige per")],
            required_tokens=[
                "Sparen 3 mit 2. Säule",
                "Berner Kantonalbank",
                "Freundliche Grüsse",
                "Neuer Saldo",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_top="IBAN",
                    text_bottom="Gutschriftsanzeige",
                    x_range=PercentageRange(0.5, 1),
                ).include_top(-2),
                SearchElementConstrainedArea(
                    "region_iban",
                    None,
                    text_top="IBAN",
                    text_bottom="IBAN",
                    x_range=PercentageRange(0, 0.5),
                ).include_vertical(),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Neuer Saldo",
                    text_bottom="Freundliche Grüsse",
                ).include_top(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Gutschriftsanzeige per",
                    text_bottom="Gutschriftsanzeige per",
                ).include_vertical(),
            ],
            se=Pension3aSearchElements(
                iban=create_labeled_field("IBAN", "region_iban"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementConstrainedArea(
                    None,
                    None,
                    target_name="region_table",
                    text_top="Neuer Saldo",
                    text_bottom="Neuer Saldo",
                    text_left="CHF",
                )
                .include_vertical()
                .include_bottom(3),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="BEKB",
            desc="3A Account BEKB Kontoauszug 2016",
            ranked_titles_all=[RankedTitle("Kontoauszug per")],
            required_tokens=[
                "Sparen 3",
                "Vorsorgestiftung Sparen 3 der Berner Kantonalbank",
                "Freundliche Grüsse",
                "Saldo",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_top="IBAN",
                    text_bottom="Kontoauszug",
                    x_range=PercentageRange(0.5, 1),
                ).include_top(-2),
                SearchElementConstrainedArea(
                    "region_iban",
                    None,
                    text_top="IBAN",
                    text_bottom="IBAN",
                    x_range=PercentageRange(0, 0.5),
                ).include_vertical(),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Kontoauszug per",
                    text_bottom="Vorbezüge",
                ),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Kontoauszug per",
                    text_bottom="Kontoauszug per",
                ).include_vertical(),
            ],
            se=Pension3aSearchElements(
                iban=create_labeled_field("IBAN", "region_iban"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementConstrainedArea(
                    None,
                    None,
                    target_name="region_table",
                    text_top="Total Umsatz",
                    text_bottom="Total Umsatz",
                    text_left="Saldo",
                )
                .include_vertical()
                .include_left(-6),
            ),
        ),
        GenericPension3aAccountDE(
            "Corner",
            ["DETAILLIERTER AUSZUG"],
            None,
            ["Vorsorgestiftung", "Cornèr Dritte Säule", "SCHWEIZER FR"],
            "+41 91 800 51 11",
        ),
        TemplatePension3aAccountPageParser(
            company="CS",
            desc="3A Account CS Kontoauszug 2015",
            ranked_titles_all=[RankedTitle("Kontoauszug per")],
            required_tokens=["CREDIT SUISSE", "3. Säule Vorsorgekonto", "Buchsaldo"],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Kontoauszug per",
                    x_range=RANGE_RIGHT,
                ),
                SearchElementConstrainedArea(
                    "region_table", None, text_top="Buchsaldo per"
                ).include_top(),
                SearchElementConstrainedArea(
                    "region_date", None, text_bottom="Kontoauszug per"
                ),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None,
                    None,
                    text_top="3. Säule Vorsorgekonto",
                    text_bottom="Konto lautend auf",
                    x_range=RANGE_LEFT,
                ),
                iban=create_labeled_field(
                    "IBAN",
                    "region_table_top",
                    field_pos_page_horizontal=PercentageRange(0, 0.6),
                ),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_table",
                    label="Buchsaldo",
                    field_pos_page_horizontal=PercentageRange(0.8, 1),
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="CS",
            desc="3A Account CS Abschlussrechnung 2016",
            ranked_titles_all=[
                RankedTitle("Abschlussrechnung per"),
                RankedTitle("Credit Suisse"),
            ],
            required_tokens=[
                "CREDIT SUISSE (SCHWEIZ) AG",
                "3. Säule Vorsorgekonto",
                "Buchsaldo",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_top="Clearing-Nr",
                    text_bottom="Abschlussrechnung per",
                    x_range=PercentageRange(0.5, 1),
                ),
                SearchElementConstrainedArea(
                    "region_table_top",
                    None,
                    text_top="3. Säule Vorsorgekonto",
                    text_left="3. Säule Vorsorgekonto",
                    text_bottom="Abschlussrechnung per",
                    x_range=PercentageRange(0, 0.5),
                ),
                SearchElementConstrainedArea(
                    "region_table", None, text_top="Abschlussrechnung per"
                ),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="IBAN",
                    text_bottom="Abschlussrechnung per",
                ).include_bottom(),
            ],
            se=Pension3aSearchElements(
                iban=create_labeled_field("IBAN", "region_table_top"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_table",
                    label="Buchsaldo",
                    field_pos_page_horizontal=PercentageRange(0.8, 1),
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            # This is independent of 3a if there is not only Privilegia but also other accounts in this list
            company="CS",
            desc="Bank CS Gesamtübersicht 2017 #1/2",
            ranked_titles_all=[
                RankedTitle("Credit Suisse"),
                RankedTitle("Gesamtübersicht"),
            ],
            required_tokens=[
                "Grafik zu den ausgewählten Anlagen",
                "Total der ausgewählten Anlagen in CHF",
                "CREDIT SUISSE PRIVILEGIA",
            ],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="Clearing-Nr",
                #                              text_bottom="Abschlussrechnung per",
                #                              x_range=PercentageRange(0.5, 1)),
                SearchElementConstrainedArea(
                    "region_total",
                    None,
                    text_top="Auswertungswährung",
                    text_left="Auswertungswährung",
                    text_bottom="Auswertungswährung",
                ).include_vertical(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Wert per Bewertungsdatum",
                    text_left="Wert per Bewertungsdatum",
                    text_right="Auswertungswährung",
                    text_bottom="Wert per Bewertungsdatum",
                ).include_vertical(),
            ],
            se=Pension3aSearchElements(
                # iban=create_labeled_field("IBAN", "region_table_top"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None, None, target_name="region_total", label="CHF"
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            # This is independent of 3a if there is not only Privilegia but also other accounts in this list
            # Just look if there is at least a line with Vorsorgedepot
            # This is an additional page, not the first (normally #2)
            page_cat=PageCat.PENSION3A_ACCOUNT_STATEMENT_ADDITIONAL_PAGE,
            company="CS",
            desc="Bank CS Gesamtübersicht 2017 #2/2",
            # ranked_titles_all=[RankedTitle('Credit Suisse'), RankedTitle("Gesamtübersicht")],
            required_tokens=[
                "Zwischensumme in CHF",
                "Support Privatkunden",
                "Support Firmenkunden",
                "3. Säule Vorsorgetdepot",
                "CREDIT SUISSE PRIVILEGIA",
                "Total in CHF",
            ],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="Clearing-Nr",
                #                              text_bottom="Abschlussrechnung per",
                #                              x_range=PercentageRange(0.5, 1)),
                SearchElementConstrainedArea(
                    "region_name",
                    None,
                    text_top="CREDIT SUISSE PRIVILEGIA",
                    text_left="3. Säule Vorsorgedepot,",
                    text_right="CREDIT SUISSE PRIVILEGIA",
                    text_bottom="CREDIT SUISSE PRIVILEGIA",
                ).include_vertical(-1.5, 1.5),
                SearchElementConstrainedArea(
                    "region_total",
                    None,
                    text_top="Total in CHF",
                    text_left="Total in CHF",
                    text_bottom="Total in CHF",
                    text_left_offset_chars=50,
                ).include_vertical(),
            ],
            se=Pension3aSearchElements(
                # iban=create_labeled_field("IBAN", "region_table_top"),
                # document_date=SearchElementArea(None, None, target_name="region_date"),
                fullname=SearchElementArea(None, None, target_name="region_name"),
                total_amount=SearchElementArea(
                    None,
                    None,
                    target_name="region_total",
                    x_range=PercentageRange(0.75, 1),
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="CS",
            desc="3A Account CS Privilegia #1/2 2015",
            ranked_titles_all=[RankedTitle("Vermögensausweis")],
            required_tokens=[
                "CREDIT SUISSE PRIVILEGIA",
                "Inhaltsverzeichnis",
                "Erstellt am",
                "Depot-Nr.",
                "3. Säule",
            ],
            # regions=[
            #     SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None,
            #                                  text_top="Vermögensausweis", text_bottom="Erstellt am",
            #                                  x_range=RANGE_RIGHT),
            #     SearchElementConstrainedArea("region_table", None, text_top="Vermögensausweis",
            #                                  x_range=RANGE_LEFT,
            #                                  text_bottom="Auswertungswährung").include_bottom(),
            #
            # ],
            #
            # se=Pension3aSearchElements(
            #     account_no=create_labeled_field("Depot-Nr.", "region_table"),
            #     document_date=create_labeled_field("Erstellt am", "region_table"),
            # )
        ),
        TemplatePension3aAccountPageParser(
            company="CS",
            desc="3A Account CS Privilegia #2/2 2013",
            page_cat=PageCat.PENSION3A_ACCOUNT_STATEMENT_ADDITIONAL_PAGE,
            ranked_titles_all=[RankedTitle("1. Übersicht")],
            required_tokens=[
                "CREDIT SUISSE PRIVILEGIA",
                "Anlagevermögen",
                "Vermögensstruktur",
                "Depot-Nr.",
                "Total Anlagevermögen",
            ],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_bottom="Kontoabschluss",
                #                              x_range=PercentageRange(0, 0.5)),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Vermögensstruktur nach Anlagekategorien",
                    text_right="Vermögensstruktur nach Anlagekategorien",
                ).include_right(2),
                SearchElementConstrainedArea(
                    "region_date", None, text_bottom="Depot-Nr."
                ),
            ],
            se=Pension3aSearchElements(
                account_no=create_labeled_field("Deport-Nr."),
                document_date=create_labeled_field(
                    "Vermögensausweis per", "region_date"
                ),
                total_amount=create_labeled_field(
                    "Total Anlagevermögen", "region_table"
                ),
            ),
        ),
        SmartPension3aAccountParser(
            desc="CS Privilegia",
            company="CS",
            titles=["Vermögensausweis per"],
            required_tokens=[
                "CREDIT SUISSE PRIVILEGIA",
                "Anlagevermögen",
                "Vermögensstruktur",
            ],
        ),
        TemplatePension3aAccountPageParser(
            desc="CS Positionen 3a",
            page_cat=PageCat.PENSION3A_ACCOUNT_STATEMENT,
            company="CS",
            ranked_titles_all=[RankedTitle("Positionen")],
            required_tokens=[
                "CREDIT SUISSE PRIVILEGIA",
                "Veränderung",
                "Positionen per Bewertungsdatum",
            ],
            regions=[
                SearchElementConstrainedArea(
                    "region_total", None, text_top="Gesamtwert in CHF"
                ).include_top(),
                SearchElementLabeledField(
                    "region_account_no", None, label="Portfolio/Depot"
                ),
            ],
            se=Pension3aSearchElements(
                account_no=SearchElementConstrainedArea(
                    None, None, target_name="region_account_no", text_right="Depot"
                ),
                fullname=create_labeled_field(
                    "3. Säule Vorsorgedepot,",
                    field_pos_page_horizontal=PercentageRange(0, 0.6),
                ),
                document_date=create_labeled_field(
                    "Positionen per Bewertungsdatum",
                    field_pos_page_horizontal=RANGE_LEFT,
                ),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_total",
                    label="Gesamtwert in CHF",
                    field_vertical_line_scale=2.5,
                    field_pos_page_horizontal=PercentageRange(0.4, 0.7),
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="LUKB",
            desc="3A Account LUKB Kontoauszug 2016",
            ranked_titles_all=[RankedTitle("Luzerner")],
            required_tokens=[
                "Luzerner Kantonalbank AG",
                "6002 Luzern",
                "Vorsorgekonto Sparen 3",
                "Kontoauszug in CHF",
                "BIC/SWIFT",
            ],
            # 250510 mt: This was previously set to 0.5 which caused the full request to hang for 5min.
            # hamming_dist=0.2,
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Kontoauszug",
                    x_range=PercentageRange(0, 0.5),
                ),
                SearchElementConstrainedArea(
                    "region_table", None, text_top="Kontoauszug", text_bottom="Umsatz"
                ).include_bottom(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Kontoauszug",
                    text_left="Kontoauszug",
                    text_bottom="Kontoauszug",
                ).include_bottom(3),
            ],
            se=Pension3aSearchElements(
                fullname=create_labeled_field("Inhaber"),
                iban=create_labeled_field("IBAN"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_table",
                    label="Saldo per",
                    field_pos_page_horizontal=PercentageRange(0.7, 1),
                ),
            ),
            debug_breakpoint=False,
        ),
        createPension3aAccountAdditionalPageParser(
            [
                "Vorsorge-Depot 3",
                "Vermögensauszug in CHF",
                "Vorsorgestiftung Sparen 3",
                "Luzerner",
            ],
            company="LUKB",
        ),
        TemplatePension3aAccountPageParser(
            company="Migrosbank",
            desc="3A Account Migrosbank Kontoabschluss 2019",
            ranked_titles_all=[RankedTitle("Kontoabschluss")],
            required_tokens=[
                "Vorsorge-Portfolio",
                "Migros Bank AG",
                "+41 ***********",
                "Vorsorgekonoto",
                "Portfolio",
            ],
            #     regions=[
            #         SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_bottom="Kontoabschluss",
            #                                      x_range=PercentageRange(0, 0.5)),
            #         SearchElementConstrainedArea("region_table", None, text_top="Kontoabschluss"),
            #         SearchElementConstrainedArea("region_date", None, text_top="Kontoabschluss",
            #                                      text_bottom="Kontoabschluss").include_top(-15),
            #
            #     ],
            #
            #     se=Pension3aSearchElements(
            #         iban=create_labeled_field("IBAN"),
            #         document_date=SearchElementArea(None, None, target_name="region_date"),
            #         total_amount=create_labeled_field("Schlusssaldo", "region_table"),
            #     )
        ),
        TemplatePension3aAccountPageParser(
            company="Migrosbank",
            desc="3a Account Migrosbank Vorsorgeportfolio 2016",
            ranked_titles_all=[RankedTitle("MIGROSBANK"), RankedTitle("Anlagen")],
            required_tokens=["Vermögensübersicht der Vorsorgestiftung", "Gesamttotal"],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="Für Sie zuständig",
                #                              text_bottom="Vermögensausweis per",
                #                              x_range=PercentageRange(0.5, 1)),
                # SearchElementConstrainedArea("region_total", None, text_top="Total Vorsorgeguthaben",
                #                              text_bottom="Total Vorsorgeguthaben").include_vertical(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_bottom="Vermögensübersicht der Vorsorgestiftung",
                ).include_vertical()
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None,
                    None,
                    text_bottom="Vermögensübersicht der Anlagestiftung",
                    text_right="|",
                ),
                # iban=SearchElementConstrainedArea(None, None, text_top="Inhaber", text_left="Konto:",
                #                                   text_bottom="Vorsorgeplan", text_right=" / CHF"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    label="Gesamttotal",
                    field_pos_page_horizontal=PercentageRange(0.7, 1),
                ),
            ),
        ),
        SmartPension3aAccountParser(
            company="Migros Bank",
            titles=["Dienstleistungsübersicht per"],
            se_address_inside=SearchElementConstrainedArea(
                None,
                None,
                text_top="Dienstleistungsübersicht",
                y_range=PercentageRange(0, 0.3),
                x_range=PercentageRange(0, 0.5),
            ),
            document_date_label="Dienstleistungsübersicht per",
            required_tokens=[
                "Migros Bank AG",
                "Vorsorgesparen",
                "Vorsorgekonto",
                "Total Vorsorgesparen",
            ],
        ),
        Pension3aAccountCreditNotePageParser(
            "Migros Bank",
            ["Gutschriftsanzeige"],
            "Valuta",
            ["Vorsorge 3", "Migros Bank AG", "Wir haben Ihrem Konto gutgeschrieben"],
        ),
        # Name is not matched because not on page. Instead 'Creditsuisse' is matched as FULLNAME... ignore that
        GenericPension3aAccountDE(
            "Privilegia",
            ["Vermögensausweis per"],
            None,
            [
                "CREDIT SUISSE PRIVILEGIA",
                "ANSPRUECHE CREDIT SUISSE ANLAGESTIFTUNG",
                "Total Anlagevermögen",
            ],
        ),
        TemplatePension3aAccountPageParser(
            company="Postfinance",
            desc="3A Account Postfinance Kontoauszug DE 2014",
            ranked_titles_all=[RankedTitle("Vorsorgekonto 3a")],
            required_tokens=[
                "Vorsorgekonto 3a",
                "Kontonummer",
                "Vorsorgeguthaben inkl. Zins in CHF",
                "Freundliche Grüsse",
                "Postfinance Vorsorgestiftung 3a",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Kontonummer",
                    x_range=PercentageRange(0, 0.5),
                ),
                SearchElementConstrainedArea(
                    "region_table", None, text_top="Kontobewegung"
                ),
                SearchElementConstrainedArea(
                    "region_bottom", None, text_top="Vorsorgeguthaben inkl. Zins"
                ).include_top(),
                # Search for date on the full page as this changed and in 2017 they just write 'Januar 2017'... so find any other date.
                # SearchElementConstrainedArea("region_date", None, text_top="www.postfinance.ch", text_bottom="Kontonummer"),
                SearchElementConstrainedArea("region_date", None),
                SearchElementConstrainedArea(
                    "region_col_right", None, text_left="Saldo in CHF"
                ).include_left(),
            ],
            se=Pension3aSearchElements(
                account_no=create_labeled_field("Kontonummer"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_col_right",
                    target_name_label="region_bottom",
                    label="Vorsorgeguthaben inkl. Zins",
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            supported_languages=LANG_FR,
            company="Postfinance",
            desc="3A Account Postfinance Kontoauszug FR 2017",
            ranked_titles_all=[RankedTitle("Compte prévoyance 3a")],
            required_tokens=[
                "Avoir de prévoyance en CHF",
                "Numéro de compte",
                "Avec nos salutations",
                "Fondation de prévoyance 3a PostFinance",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Numéro de compte",
                    x_range=PercentageRange(0, 0.5),
                ),
                SearchElementConstrainedArea(
                    "region_table", None, text_top="Mouvements de"
                ),
                SearchElementConstrainedArea(
                    "region_bottom", None, text_top="Avoir de prévoyance"
                ).include_top(),
                # Search for date on the full page as this changed and in 2017 they just write 'Januar 2017'... so find any other date.
                # SearchElementConstrainedArea("region_date", None, text_top="www.postfinance.ch", text_bottom="Kontonummer"),
                SearchElementConstrainedArea("region_date", None),
                SearchElementConstrainedArea(
                    "region_col_right", None, text_left="Solde en CHF"
                ).include_left(),
            ],
            se=Pension3aSearchElements(
                account_no=create_labeled_field("Numéro de compte"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_col_right",
                    target_name_label="region_bottom",
                    label="Avoir de prévoyance",
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            # Various versions exists. This is the 2021 version of a printout from e-finance
            company="Postfinance",
            desc="3A Account Postfinance E-Finance Vorsorgeguthaben Printout 2021",
            ranked_titles_all=[RankedTitle("Vorsorgeguthaben")],
            required_tokens_any=[
                ["Postfinance"],
                ["Direktausdruck aus E-Finance"],
                ["Total Vorsorgeguthaben", "Betrag der Einzahlungen", "Vermögen"],
            ],
            regions=[
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Total Vorsorgeguthaben",
                    text_right="Total Vorsorgeguthaben",
                    text_bottom="Total Vorsorgeguthaben",
                )
                .include_right(5)
                .include_bottom(4.2),
                SearchElementConstrainedArea(
                    "region_name",
                    None,
                    text_top="Vorsorgekonto",
                    text_bottom="Total Vorsorgeguthaben",
                    text_right=" 5",
                ).include_right(-1.5),
                SearchElementConstrainedArea("region_date", None),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None, None, target_name="region_name"
                ),
                account_no=SearchElementConstrainedArea(
                    "region_account_no",
                    None,
                    text_top="Vorsorgekonto",
                    text_bottom="Total Vorsorgeguthaben",
                    text_left=" 5",
                    x_range=PercentageRange(0, 0.2),
                    relations=[SearchRelationRightOf("fullname")],
                ).include_left(0.2),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementConstrainedArea(
                    None, None, target_name="region_table"
                ),  # can be with and without CHF
            ),
        ),
        TemplatePension3aAccountPageParser(
            # There are 2 (tested) examples with different layout. Therefore name and account_no cannot be read reliably. Check with newer versions
            company="Postfinance",
            desc="3A Account Postfinance E-Finance Vorsorgeguthaben 2017",
            ranked_titles_all=[RankedTitle("Vorsorgeguthaben")],
            required_tokens_any=[
                ["Postfinance"],
                ["E-Finance"],
                ["Total Guthaben Vorsorgefonds", "Total Kontosaldo inkl. Zinsen"],
            ],
            regions=[
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Total Vorsorgeguthaben",
                    text_bottom="Betrag der Einzahlungen",
                ),
                SearchElementConstrainedArea("region_date", None),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None,
                    None,
                    text_top="Vorsorgekonto",
                    text_left="Vorsorgegutha",
                    text_bottom="Vorsorgekonto",
                    x_range=PercentageRange(0, 0.2),
                )
                .include_top(-1.5)
                .include_bottom(0),
                # Too unreliable because there are several versions...
                # account_no=SearchElementConstrainedArea(None, None, text_top="Vorsorgekonto",
                #         text_left="Vorsorgegutha",
                #                                         text_bottom="Vorsorgekonto").include_top(-1).include_bottom(2),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementConstrainedArea(
                    None, None, target_name="region_table"
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            # There are 2 (tested) examples with different layout. Therefore name and account_no cannot be read reliably. Check with newer versions
            page_cat=PageCat.PENSION3A_ACCOUNT_STATEMENT_ADDITIONAL_PAGE,
            company="Postfinance",
            desc="3A Account Postfinance E-Finance Lebensversicherung 2021",
            ranked_titles_all=[RankedTitle("Lebensversicherung")],
            required_tokens=[
                "E-Finance",
                "Versicherte Leistungen",
                "Zahlungsmodus",
                "Prämie",
                "Gebundene Vorsorge 3a",
            ],
            regions=[
                # SearchElementConstrainedArea("region_table", None, text_top="Total Vorsorgeguthaben",
                #                              text_bottom="Betrag der Einzahlungen"),
                SearchElementConstrainedArea("region_date", None)
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None,
                    None,
                    text_top="Lebensversicherung",
                    text_bottom="Lebensversicherung",
                    x_range=PercentageRange(0, 0.3),
                ).include_bottom(3.5),
                # Too unreliable because there are several versions...
                # account_no=SearchElementConstrainedArea(None, None, text_top="Vorsorgekonto",
                #         text_left="Vorsorgegutha",
                #                                         text_bottom="Vorsorgekonto").include_top(-1).include_bottom(2),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                # total_amount=SearchElementConstrainedArea(None, None, target_name="region_table"),
            ),
        ),
        SmartPension3aAccountParser(
            company="Privor",
            titles=["Kontoauszug"],
            se_address_inside=SearchElementConstrainedArea(
                None,
                None,
                text_top="www.privor.ch",
                text_bottom="Kontoauszug",
                x_range=PercentageRange(0, 0.5),
            ),
            se_document_date=SearchElementConstrainedArea(
                None,
                None,
                text_bottom="Kontoauszug vom",
                converter=MostRecentDateConverter(),
            ),
            required_tokens=[
                "PRIVOR | STIFTUNG 3. SÄULE",
                "Valutadatum",
                "Kontosaldo per",
            ],
        ),
        GenericPension3aAccountDE(
            "Postfinance",
            ["Vorsorgekonto 3a"],
            "bis",
            ["Vorsorgekonto 3a", "Postfinance Vorsorgestiftung 3a", "Vorsorgeguthaben"],
        ),
        TemplatePension3aAccountPageParser(
            company="Raiffeisen",
            desc="3a Account Raiffeisen Kontoauszug 2017",
            ranked_titles_all=[
                RankedTitle("Raiffeisen"),
                RankedTitle("Raiffeisenbank"),
            ],
            required_tokens=[
                # If there is little text on the page, Kontoauszug is not large enough to be a title
                "Kontoauszug",
                "Vorsorgeplan 3",
                # 'Vertrag',  This is optional
                "Inhaber",
                "Kontostand",
            ],
            regions=[
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    texts_top=["Aktueller Kontostand", "Kontostand per"],
                    texts_bottom=["Aktueller Kontostand", "Kontostand per"],
                ).include_vertical()
            ],
            se=Pension3aSearchElements(
                fullname=create_labeled_field("Inhaber:"),  # can also be Kontoinhaber
                iban=SearchElementConstrainedArea(
                    None,
                    None,
                    text_top="Inhaber",
                    text_left="Konto:",
                    text_bottom="Vorsorgeplan",
                    text_right=" / CHF",
                ),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    label="Kontostand",
                    field_pos_page_horizontal=PercentageRange(0.75, 1),
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            supported_languages=["de", "fr"],
            company="Raiffeisen",
            desc="3a Account Raiffeisen Vermögensausweis DE/FR 2016,2017",
            ranked_titles_any=[
                RankedTitle("Raiffeisen Vorsorgestiftung"),
                RankedTitle("Vermögensausweis per"),
                RankedTitle("Fondation de prévoyance"),
                RankedTitle("Etat de fortune au"),
            ],
            ranked_titles_any_min=2,
            required_tokens_any=[
                ["Raiffeisen"],
                ["Vorsorgeplan 3", "Plan de prévoyance 3"],
                ["Total Vorsorgeguthaben", "Total avoir de prévoyance"],
                ["Kontoinhaber", "Titulaire compte"],
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    texts_top=["Für Sie zuständig", "A votre disposition"],
                    texts_bottom=["Vermögensausweis per", "Etat de fortune au"],
                    x_range=PercentageRange(0.5, 1),
                ),
                SearchElementConstrainedArea(
                    "region_total",
                    None,
                    texts_top=["Total Vorsorgeguthaben", "Total avoir de prévoyance"],
                    texts_bottom=[
                        "Total Vorsorgeguthaben",
                        "Total avoir de prévoyance",
                    ],
                ).include_vertical(-2, 2),
                # SearchElementConstrainedArea("region_date", None, text_top="Datum:", text_left="Datum:",
                #                              texts_bottom=["Datum:", 'Date:']).include_vertical(),
            ],
            se=Pension3aSearchElements(
                document_date=create_labeled_field(["Datum:", "Date:"]),
                fullname=create_labeled_field(["Kontoinhaber:", "Titulaire compte"]),
                iban=create_labeled_field({"IBAN/Konto-Nr.:": 3, "IBAN/N0 compte:": 3}),
                # document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=create_labeled_field(
                    ["Total Vorsorgeguthaben", "Total avoir de prévoyance"],
                    "region_total",
                ),
            ),
        ),
        GenericPension3aAccountDE(
            "Raiffeisen",
            ["Tagesauszug per:"],
            None,
            [
                "Raiffeisen Vorsorgestiftung",
                "Vorsorgeplan 3 / CHF",
                "Saldo zu Ihren Gunsten",
            ],
        ),
        GenericPension3aAccountDE(
            "Rendita",
            ["Auszug Vorsorgeguthaben"],
            " bis ",
            ["Rendita Vorsorgestiftung 3a", "Vorsorgeguthaben"],
        ),
        # No saldo here
        GenericPension3aAccountDE(
            "Rendita",
            ["Buchungsanzeige"],
            " lautend auf ",
            ["Rendita Vorsorgestiftung 3a", "Wertschriften"],
        ),
        TemplatePension3aAccountPageParser(
            company="SGKB",
            desc="3A Account SGKB Kontoauszug 2019",
            ranked_titles_all=[RankedTitle("Kontoauszug")],
            required_tokens=[
                "Vorsorgestiftung Sparen 3",
                "St.Galler Kantonalbank AG",
                "St. Leonhardstrasse 25",
                "Sparen 3-Konto CHF",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Kontoauszug",
                    x_range=PercentageRange(0.5, 1),
                ),
                SearchElementConstrainedArea(
                    "region_table", None, text_top="Kontoauszug"
                ),
                SearchElementConstrainedArea(
                    "region_total", None, text_top="Umsatztotal"
                ),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Kontoauszug",
                    text_bottom="Kontoauszug",
                ).include_top(-10),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(None, None)
                .with_all_texts("Sparen 3-Konto CHF")
                .include_top(-2)
                .include_left(-2)
                .include_right(30),
                iban=SearchElementConstrainedArea(None, None, text_left="IBAN")
                .with_all_texts("Sparen 3-Konto CHF")
                .include_bottom(3)
                .include_right(20),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=create_labeled_field("Saldo", "region_total"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            supported_languages=["de", "en"],
            company="SHKB",
            desc="3a Account SHKB Kontoauszug DE/EN 2017",
            ranked_titles_any=[
                RankedTitle("Schaffhauser"),
                RankedTitle("Kantonalbank"),
                RankedTitle("Kontoauszug"),
                RankedTitle("Account statement"),
            ],
            ranked_titles_any_min=3,
            required_tokens_any=[
                ["Vorsorgekonto 3a"],
                ["Saldo", "Balance"],
                ["Inhaber", "Holder"],
            ],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="IBAN",
                #                              text_bottom="Kontoauszug",
                #                              x_range=PercentageRange(0.5, 1)),
                SearchElementConstrainedArea(
                    "region_total",
                    None,
                    texts_top=["Kontoauszug", "Account statement"],
                    texts_bottom=["Saldo", "Balance"],
                ).include_bottom(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    texts_top=["Datum/Zeit", "Date/time"],
                    texts_bottom=["Datum/Zeit", "Date/time"],
                ).include_vertical(),
            ],
            se=Pension3aSearchElements(
                fullname=create_labeled_field(["Inhaber/in", "Holder"]),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=create_labeled_field("CHF", "region_total"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="WIR",
            desc="3A Account WIR 2016",
            required_text_conditions=[
                FromStartTextCond("WIR Bank Genossenschaft", num_lines=2),
                FromStartTextCond("www.wir.ch", num_lines=10),
                FromStartTextCond("Terzo-Konto", num_lines=15),
            ],
        ),
        TemplatePension3aAccountPageParser(
            company="ZKB",
            desc="3a Account ZKB Swisscanto Printout 2016",
            ranked_titles_all=[
                RankedTitle("Ansprueche Swisscanto BVG 3"),
                RankedTitle("CHF"),
                RankedTitle("Stück"),
            ],
            required_tokens=[
                "Die Angaben erfolgen ohne Gewähr",
                "Zürcher Kantonalbank",
            ],
            regions=[
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="IBAN",
                #                              text_bottom="Kontoauszug",
                #                              x_range=PercentageRange(0.5, 1)),
                SearchElementConstrainedArea(
                    "region_table_left",
                    None,
                    text_top="Ansprueche",
                    text_right="Valor",
                    text_bottom="Rendite",
                ).include_bottom(),
                SearchElementConstrainedArea(
                    "region_date", None, text_top="Datum/Zeit", text_bottom="Datum/Zeit"
                ).include_vertical(),
            ],
            se=Pension3aSearchElements(
                fullname=create_labeled_field("Inhaber/in"),
                iban=create_labeled_field("IBAN"),
                document_date=create_labeled_field("Bewertungsdatum"),
                total_amount=create_labeled_field("Marktwert", "region_table_left"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="TKB",
            desc="3a Account TKB Kontoauszug 2017",
            ranked_titles_all=[
                RankedTitle("Thurgauer"),
                RankedTitle("Kantonalbank"),
                RankedTitle("Kontoauszug"),
            ],
            required_tokens=[
                "VORSORGESTIFTUNG SPAREN 3",
                "Saldo",
                "Freundliche Grüsse",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_top="IBAN",
                    text_bottom="Kontoauszug",
                    x_range=PercentageRange(0.5, 1),
                ),
                SearchElementConstrainedArea(
                    "region_total", None, text_top="Saldovortrag"
                ).include_bottom(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Kontoauszug",
                    text_bottom="Kontoauszug",
                ).include_top(-4),
            ],
            se=Pension3aSearchElements(
                fullname=SearchElementConstrainedArea(
                    None, None, text_bottom="Sparen 3 CHF", text_left="Sparen 3 CHF"
                ).include_left(-3),
                iban=create_labeled_field("IBAN"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=create_labeled_field("Saldo", "region_total"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            supported_languages=["de", "fr"],
            company="UBS",
            desc="3a Account UBS Kontobewegungen DE/FR 2016,2017",
            ranked_titles_any=[
                RankedTitle("Kontobewegungen"),
                RankedTitle("Mouvements de compte"),
            ],
            required_text_conditions=[
                FromStartTextCond("Fisca", num_lines=10),
                FromStartTextCond("UBS", num_lines=5),
            ],
            required_tokens_any=[
                ["UBS"],
                ["Bewegungen", "mouvements"],
                ["Gutschrift", "Virement"],
                ["Bewertet in CHF", "Evalution en CHF"],
                # ['Anfangssaldo', 'Schlusssaldo', 'Solde final'],
                ["UBS Fiscakonto CHF", "Compte Fisca UBS CHF"],
                ["Vorsorge", "PREVOYANCE", "PENSION"],
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    texts_top=["UBS Fiscakonto", "Compte Fisca"],
                    texts_bottom=["Kontobewegungen", "Mouvements de compte"],
                    x_range=PercentageRange(0, 0.5),
                ).include_top(),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    texts_top=["Kontobewegungen", "Mouvements de compte"],
                ),
            ],
            use_se=True,
            se=Pension3aSearchElements(
                document_date=create_labeled_field(
                    {"Kontobewegungen": 3, "Mouvements de compte": 5}
                ),
                # fullname=create_labeled_field("Lautend auf"),
                iban=create_labeled_field("IBAN"),
                total_amount=create_labeled_field(
                    {"Schlusssaldo": 2, "Solde final": 2}, "region_table"
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="UBS",
            desc="3a Account UBS Kontoauszug 2016",
            ranked_titles_all=[
                RankedTitle("Kontoauszug"),
                # RankedTitle('UBS') not always detected
            ],
            required_tokens=[
                "UBS Switzerland AG",
                "Ihr Konto auf einen Blick",
                "Schlusssaldo",
                "UBS Fiscakonto CHF",
                # 'GEBUNDENE' remove this because it could be in English
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_top="UBS Fiscakonto",
                    text_bottom="Kontoauszug",
                    x_range=PercentageRange(0, 0.5),
                ).include_top(),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Ihr Konto auf einen Blick",
                    text_bottom="Schlussaldo",
                ).include_vertical(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Kontoauszug",
                    text_bottom="Ihr Konto auf einen Blick",
                ),
            ],
            se=Pension3aSearchElements(
                # fullname=create_labeled_field("Lautend auf"),
                iban=create_labeled_field("IBAN"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=create_labeled_field("Schlusssaldo", "region_table"),
            ),
        ),
        # Disabled 220311 because this could also be non pillar 3a
        # TemplatePension3aAccountPageParser(
        #     company="UBS",
        #     desc="3a UBS Vermögensausweis 2016 #1 / 2",
        #     ranked_titles_all=[RankedTitle('Vermögensausweis per')],
        #     required_tokens=['UBS Switzerland AG', 'Gebundene Vorsorge 3a', 'Ihre Anlagebedürfnisse'],
        #
        #     regions=[
        #         SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, text_top="Kundeninformation",
        #                                      text_bottom="Vermögensausweis per",
        #                                      x_range=PercentageRange(0, 0.4)).include_top(),
        #         SearchElementConstrainedArea("region_table", None,
        #                                      text_top="Vermögensübersicht nach Anlagekategorien",
        #                                       text_bottom="Detailpositionen nach Anlagekategorien").include_top(),
        #         SearchElementConstrainedArea("region_date", None, text_top="Vermögensausweis per",
        #                                      text_bottom="Vermögensausweis per").include_top(-5),
        #     ],
        #
        #     se=Pension3aSearchElements(
        #         fullname=create_labeled_field("Inhaber"),
        #         account_no=create_labeled_field("Portfolio-Nr,"),
        #         document_date=SearchElementArea(None, None, target_name="region_date"),
        #         total_amount=SearchElementConstrainedArea(None, None, target_name="region_table", text_top="Gebundende Vorsorge 3a Konten", text_left="Bewertet in CHF", text_bottom="Gebundene Vorsorge 3a Konten").include_top(-1.5).include_left(-1).include_bottom(),
        #     )
        # ),
        #
        # createPension3aAccountAdditionalPageParser(
        #     # Page 2 of Vermögensausweis (with 3a)
        #     ['Detailpositionen nach Anlagekategorien', 'Pro Memoria - Gebundene Vorsorge 3a', 'Vertriebsentschädigungen und nicht-monetäre'], desc="UBS Vermögensübersicht DE 2/2"),
        createPension3aAccountAdditionalPageParser(
            [
                "UBS Fiscakonto CHF",
                "gelten folgende Maximalbeträge für die Säule 3a",
                "Seite 2/2",
            ]
        ),
        TemplatePension3aAccountPageParser(
            company="UBS",
            desc="3A Account UBS Positionsdetails 2017",
            ranked_titles_all=[RankedTitle("Positionsdetails")],
            required_tokens=[
                "UBS Fiscakonto",
                "www.ubs.com",
                "Kontoinformationen",
                "GEBUNDENE VORSORGE",
            ],
            document_date_label="Per:",
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_bottom="Positionsdetails",
                    x_range=RANGE_LEFT,
                ),
                SearchElementLabeledField(
                    "region_name",
                    None,
                    label="Konto-Nr.",
                    field_position=FieldPosition.LEFT_OF_LABEL,
                ),
            ],
            se=Pension3aSearchElements(
                iban=create_labeled_field("Konto-Nr.:"),
                fullname=SearchElementArea(None, None, target_name="region_name"),
                total_amount=create_labeled_field_vertical("Anzahl/Betrag"),
            ),
        ),
        TemplatePension3aAccountPageParser(
            # Simple page with text
            company="UBS",
            desc="3a Account UBS last page 2025",
            required_text_conditions=[
                FromStartTextCond("UBS Fiscakonto CHF", num_lines=3)
            ],
            required_tokens=[
                "Die Reform AHV 21 ist per",
                "ubs.com/pn-ch",
                "Formular ohne Unterschrift",
            ],
        ),
        TemplatePension3aAccountPageParser(
            company="ZKB",
            desc="3A Account ZKB Stand Vorsorgevermögen 2016",
            ranked_titles_all=[
                RankedTitle("Stand Vorsorgevermögen"),
                RankedTitle("Sparen 3"),
            ],
            required_tokens=[
                "Vorsorgestiftung Sparen 3",
                "Zürcher Kantonalbank",
                "ZKB Sparen 3 Konto",
            ],
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    text_top="E-Mail",
                    text_bottom="Stand Vorsorge",
                    x_range=PercentageRange(0, 0.5),
                ),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    text_top="Währung",
                    text_bottom="Total Vorsorge",
                ).include_bottom(),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    text_top="Stand Vorsorgevermögen",
                    text_bottom="Stand Vorsorgevermögen",
                ).include_top(-6),
            ],
            se=Pension3aSearchElements(
                fullname=create_labeled_field("Lautend auf"),
                iban=create_labeled_field("IBAN"),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_table",
                    label="Total Vorsorgevermögen per",
                    field_pos_page_horizontal=PercentageRange(0.8, 1),
                ),
            ),
        ),
        TemplatePension3aAccountPageParser(
            company="ZKB",
            desc="3A Account ZKB Vermögensauszug 2015,2021",
            ranked_titles_all=[
                RankedTitle("Vorsorgestiftung"),
                RankedTitle("Sparen 3"),
            ],
            required_tokens=[
                "Stand Vorsorgevermögen",
                "Vorsorgestiftung Sparen 3",
                "Zürcher Kantonalbank",
                "Total Vorsorgevermögen per",
            ],
            # Title was first "Vermögensauszug" and then "Stand Vorsorgevermögen"
            regions=[
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    None,
                    texts_bottom=["Vermögensauszug", "Stand Vorsorgevermögen"],
                    x_range=PercentageRange(0, 0.5),
                ).include_bottom(-5),
                SearchElementConstrainedArea(
                    "region_table",
                    None,
                    texts_top=["Vermögensauszug", "Stand Vorsorgevermögen"],
                    text_right="Anteil",
                    text_bottom="Total Vorsorgevermögen per",
                )
                .include_bottom()
                .include_right(-10),
                SearchElementConstrainedArea(
                    "region_date",
                    None,
                    texts_top=["Vermögensauszug", "Stand Vorsorgevermögen"],
                    texts_bottom=["Vermögensauszug", "Stand Vorsorgevermögen"],
                ).include_top(-6),
            ],
            se=Pension3aSearchElements(
                # fullname=create_labeled_field("Lautend auf"),
                account_no=create_labeled_field("Vermögensauszug Konto"),
                iban=SearchElementConstrainedArea(
                    None, None, text_top="Vermögensauszug", text_bottom="per"
                ),
                document_date=SearchElementArea(None, None, target_name="region_date"),
                total_amount=SearchElementLabeledField(
                    None,
                    None,
                    target_name="region_table",
                    label="Total Vorsorgevermögen per",
                    field_pos_page_horizontal=PercentageRange(0.6, 1),
                ),
            ),
        ),
        GenericPension3aAccountDE(
            "Zurich Invest",
            ["Vermögensübersicht"],
            "per",
            [
                "Zurich Invest AG",
                "Vorsorgestiftungen",
                "Performance",
                "Wert CHF",
                "Stiftungsreglements",
            ],
            "www.zurichinvest.ch",
        ),
    ]

    parsers_en = [
        TemplatePension3aAccountPageParser(
            supported_languages=["en"],
            company="Viac",
            desc="3A Account Viac EN 2021",
            required_text_conditions=[
                FromStartTextCond("Client", num_lines=3),
                FromStartTextCond("Reporting date", num_lines=7),
                FromStartTextCond("Contract", num_lines=7),
            ],
            required_tokens=[
                "Terzo Vorsorgestiftung",
                "www.viac.ch",
                "<EMAIL>",
                "0800 80 40 40",
            ],
        )
    ]

    return parsers_de + parsers_en
