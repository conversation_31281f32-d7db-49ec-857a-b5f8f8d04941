from abbyyplumber.converter.ValueConverter import CleanNameConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)

parsers_generic_de = [
    SmartLetterPageParser(
        desc="HBL Zahlungsversprechen #1",
        doc_cat=DocumentCat.IRREVOCABLE_PROMISES_TO_PAY,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        titles=["Unwiderrufliches Zahlungsversprechen"],
        required_tokens=[
            "Hypothekarbank Lenzburg AG",
            "www.hbl.ch",
            "mit der öffentlichen Beurkundung des Kaufvertrags beauftragt",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Zahlungsversprechen #1 custom",
        doc_cat=DocumentCat.IRREVOCABLE_PROMISES_TO_PAY,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        titles=["Unwiderrufliches Zahlungsversprechen"],
        required_tokens=[
            "Hypothekarbank Lenzburg AG",
            "www.hbl.ch",
            "schuldbrief",
            "Unter der Bedingung, dass",
            "beauftragt",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Zahlungsversprechen #2",
        doc_cat=DocumentCat.IRREVOCABLE_PROMISES_TO_PAY,
        page_cat=PageCat.GENERIC_PAGE,
        titles=["Seite 2 von 3"],
        required_tokens=[
            "Unter den folgenden Bedingungen verpflichten wir uns unwiderruflich",
            "Hypothekarbank Lenzburg AG",
            "Dieses Zahlungsversprechen ist gültig bis",
            "Freundliche Grüsse",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Zahlungsversprechen #3",
        doc_cat=DocumentCat.IRREVOCABLE_PROMISES_TO_PAY,
        page_cat=PageCat.GENERIC_PAGE,
        titles=["Seite 3 von 3"],
        required_tokens=[
            "Auftragsbestätigung für unwiderrufliches Zahlungsversprechen",
            "Hypothekarbank Lenzburg AG",
            "Allgemeinen Geschäftsbedingungen der Hypothekarbank Lenzburg AG",
            "Für alle finanziellen Folgen, die Ihnen aus der Übernahme",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Finanzierungsbestätigung",
        doc_cat=DocumentCat.FINANCING_CONFIRMATION,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="HBL",
        titles=["Hypothek Nr."],
        required_tokens=[
            "Sehr geehrte",
            "Wir freuen uns, Ihnen aufgrund",
            "zur Verfügung gestellten Unterlagen",
            "Finanzierung durch unsere Bank bestätigen",
            "Hypothekarbank Lenzburg",
            "Kontaktperson",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Sicherungsübereignung #1",
        doc_cat=DocumentCat.TRANSFER_OF_SECURITY,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="HBL",
        titles=["Sicherungsvereinbarung"],
        required_tokens=[
            "Vertragsparteien",
            "Hypothekarbank Lenzburg AG, Aktiengesellschaft",
            "die Errichtung einer Sicherungsvereinbarung",
            "Sicherungsgegenstand",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Sicherungsübereignung #2",
        doc_cat=DocumentCat.TRANSFER_OF_SECURITY,
        page_cat=PageCat.GENERIC_PAGE,
        company="HBL",
        titles=["Veräusserung von Grundstücken"],
        required_tokens=[
            "Fälligkeit und Verwertung",
            "Wohnsitz/Firmensitz) ist Lenzburg",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Sicherungsübereignung #3a",
        doc_cat=DocumentCat.TRANSFER_OF_SECURITY,
        page_cat=PageCat.GENERIC_LAST_PAGE,
        company="HBL",
        titles=["Verzeichnis der Schuldbriefe"],
        required_tokens=[
            "Nominalwert",
            "Schriftlichkeit",
            "Dieser Vertrag enthält seitens der Bank keine",
            "(Unterschrift/en Grundeigentümer)",
            "Sicherungsvereinbarung V",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Sicherungsübereignung #3b",
        doc_cat=DocumentCat.TRANSFER_OF_SECURITY,
        page_cat=PageCat.GENERIC_LAST_PAGE,
        company="HBL",
        titles=["Verzeichnis der Schuldbriefe"],
        required_tokens=[
            "Nominalwert",
            "Beschrieb der Schuldbriefe und Grundstücke",
            "Seite 3/3",
            "INTERNA",
            "Sicherungsvereinbarung V",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Pfandvertrag #1",
        doc_cat=DocumentCat.PENSION_PLEDGE,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="HBL",
        titles=["Vertrag über die Verpfändung des Vorsorgekapitals"],
        required_tokens=[
            "aus gebundener Selbstvorsorge (Säule 3a)",
            "Vertragsparteien",
            "Hypothekarbank Lenzburg AG, Aktiengesellschaft",
            "in dem nach Gesetz maximal zulässigen Umfange (Art. 30b",
            "Pflicht zur Nachdeckung",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Pfandvertrag #2",
        doc_cat=DocumentCat.PENSION_PLEDGE,
        page_cat=PageCat.GENERIC_PAGE,
        company="HBL",
        titles=["Anzeigen"],
        required_tokens=[
            "Zustimmung",
            "Pfandverwertung",
            "Pfanderlös",
            "Bundesgericht, ist Lenzburg",
            "Mit der Unterzeichnung dieses Pfandvertrages",
            "Vertrag nach Art. 331d Abs. 5 OR",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Pfandvertrag #3",
        doc_cat=DocumentCat.PENSION_PLEDGE,
        page_cat=PageCat.GENERIC_PAGE,
        company="HBL",
        titles=["Schriftlichkeit"],
        required_tokens=[
            "Dieser Vertrag enthält seitens der Bank keine",
            "(Unterschrift/en Kunde)",
            "Pfandvertrag Säule 3a",
            "INTERNA",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Pfandvertrag #4",
        doc_cat=DocumentCat.PENSION_PLEDGE,
        page_cat=PageCat.GENERIC_PAGE,
        company="HBL",
        titles=["Verpfändungsanzeige Vorsorge"],
        required_tokens=[
            "Sehr geehrte",
            "Wir teilen Ihnen hiermit gemäss Art. 331 Abs.3",
            "Vorsorge-Kapital",
            "Hypothekarbank Lenzburg AG",
            "Vorsorge-Ausweises",
        ],
    ),
]

parsers_custom_de = [
    SmartLetterPageParser(
        desc="HBL Darlehenszusicherung #1/2",
        doc_cat=DocumentCat.HBL_DARLEHENSZUSICHERUNG,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        titles=["Darlehenszusicherung - Grundpfandbestellung"],
        required_tokens=[
            "Hypothekarbank Lenzburg AG",
            "Sehr geehrte",
            "Grundeigentümer und Titelschuldner",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Darlehenszusicherung #2/2",
        doc_cat=DocumentCat.HBL_DARLEHENSZUSICHERUNG,
        page_cat=PageCat.GENERIC_LAST_PAGE,
        titles=["Gültigkeit der Darlehenszusicherung"],
        required_tokens=[
            "Hypothekarbank Lenzburg AG",
            "Besonderes",
            "Für Ihre Bemühungen danken",
            "Frendlche Grüsse",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Einreichung Dokumente - Auftrag an Verarbeitung Finanzieren",
        doc_cat=DocumentCat.HBL_DIREKTAUFTRAG,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        titles=["Einreichung Vorlage", "Einreichung Dokumente"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Einreichung",
            text_left="Kreditnehmer",
            text_bottom="Telefon",
            x_range=PercentageRange(0, 0.5),
        ),
        use_document_date=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Bearbeitungsgebühr",
            text_bottom="Datum",
            x_range=PercentageRange(0, 0.3),
        ),
        required_tokens=[
            "Hypothekarbank",
            "Lenzburg",
            "AUFTRAG AN VERARBEITUNG FINANZIEREN",
            "Kreditnehmer",
            "Kundenberater HBL",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Auftrag an Verarbeitung Finanzieren Verlängerung",
        doc_cat=DocumentCat.HBL_MORTGAGE_RENEWAL,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        titles=["VERLÄNGERUNG"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="VERLÄNGERUNG",
            text_left="Kreditnehmer",
            text_bottom="Telefon",
            x_range=PercentageRange(0, 0.5),
            converter=CleanNameConverter(max_num_lines=1),
        ),
        use_document_date=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Kompetenzen gemäss",
            text_bottom="Datum",
            x_range=PercentageRange(0, 0.3),
        ),
        required_tokens=[
            "Hypothekarbank",
            "Lenzburg",
            "AUFTRAG AN VERARBEITUNG FINANZIEREN",
            "Kreditnehmer",
            "ohne Kreditvorlage",
            "PRÜFUNG KUNDENDATEN",
            "Kompetenzen gemäss",
            "Kundenberater HBL",
        ],
    ),
    SmartLetterPageParser(
        desc="HBL Pricingblatt",
        doc_cat=DocumentCat.HBL_PRICING,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        # this is ID of client
        se_product=SearchElementConstrainedArea(
            None,
            None,
            text_top="Kundendaten",
            text_left="Partner-Nr.",
            text_bottom="Hypothek-Nr.",
        ),
        # This is mortgage amount
        # se_product=SearchElementConstrainedArea(None, None, text_top="Liegenschaft", text_left="Verkehrswert", text_bottom="Gesamtfinanzierung"),
        use_document_date=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            text_top="Kompetenzen gemäss",
            text_bottom="Datum",
            x_range=PercentageRange(0, 0.3),
        ),
        titles=["Pricingblatt"],
        # Do not check 'Hypothekarbank', 'Lenzburg' as OCR can be bad
        required_tokens=[
            "Sachbearbeiter",
            "Rang",
            "Kundendaten",
            "Partner-Nr.",
            "Hypothek-Nr.",
            "ETP-Charakter",
            "Unterschrift Erfasser",
        ],
    ),
]

parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_hbl():
    return parsers_generic_de + parsers_custom_de + parsers_en + parsers_fr + parsers_it
