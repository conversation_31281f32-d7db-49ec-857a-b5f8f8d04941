from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

USE_OCR_CACHE = True
FOLDER_TYPE = "706_authorization_for_inquiries"

path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}")
dest_folder_prefix = f"input_system_doccat_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_706_feyn_de():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "feyn",
        source_file_filter=["feyn_Auskunftsermächtigung DE.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 2,
            },
            expected_page_objects={
                0: {
                    "company": "feyn",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_706_feyn_en():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "feyn",
        source_file_filter=["feyn_Auskunftsermächtigung EN.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 2,
            },
            expected_page_objects={
                0: {
                    "company": "feyn",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_706_feyn_fr():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "feyn",
        source_file_filter=["feyn_Auskunftsermächtigung FR.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 2,
            },
            expected_page_objects={
                0: {
                    "company": "feyn",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_706_feyn_it():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "feyn",
        source_file_filter=["feyn_Auskunftsermächtigung IT.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 2,
            },
            expected_page_objects={
                0: {
                    "company": "feyn",
                }
            },
        ),
    ).run()
