from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250523_msg"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekbfipla/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_local_clients_bekbfipla_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_msg():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder,
        show_page=-1,
        # source_file_filter=["03.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Some are unwanted images, must be removed later
            expected_doc_cat_frequency={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PENSION_CERTIFICATE: 1,
                DocumentCat.UNKNOWN_DE: 5,
                DocumentCat.WHITE_PAGES: 2,
            }
        ),
    ).run()
