from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog


logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250227_schweizer"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/clientis/dcb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_dcb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_schweizer():
    """
    This has error files inside a zip. Should not cause errors during unpacking
    @return:
    """

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder,
        # source_file_filter=["340 Salärabrechnung Sinan_Lohn_Juni.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
