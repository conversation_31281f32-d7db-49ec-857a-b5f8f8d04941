from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, PO_EXISTS, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "310_tax_declaration"
source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}/cantons_de"
)
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_ai_double_2019():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "AI" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ai_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_bl_double_2017():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_bl_double_2017.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={
                0: {
                    "canton_short": "BL",
                    "p1_profession": "Rentner",
                    "p2_profession": "Rentnerin",
                    # WRONG Extraction, because position of code 825 at wrong position on page "assets_other": "CHF 825",
                    "assets_real_estate_total_gross": "CHF 167'300",
                    "assets_gross_total": "CHF 779'832",
                    "assets_net_total": "CHF 479'832",
                    "assets_taxable_local": "CHF 329'832",
                    "debt_private": "CHF 300'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_bl_double_2018():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_bl_double_2018.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={
                0: {
                    "canton_short": "BL",
                    "p1_income_employed_main": "CHF 175'002",
                    # WRONG OCR "p2_income_employed_main": "CHF 7'251",
                    "income_other": "CHF 11'471",
                    "income_gross_total": "CHF 372'375",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p1_expense_employment": "CHF 500",
                    "p2_expense_employment": "CHF 500",
                    "interest_paid_on_debt": "CHF 55'024",
                    "deductions_total": "CHF 93'183",
                    "income_taxable_local": "CHF 279'192",
                    "assets_portfolio": "CHF 1'057'628",
                    "assets_real_estate_total_gross": "CHF 451'800",
                    "assets_gross_total": "CHF 3'217'484",
                    # "assets_taxable_local": "CHF -1'728'745",
                    "debt_private": "CHF 4'321'500",
                    "property_imputed_rental_value": "CHF 17'491",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_bl_double_straight_2018():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "BL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_bl_double_straight_2018.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={
                # BAD OCR
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_gl_double_2017():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "GL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_gl_double_2017.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_gl_double_2019():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "GL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_gl_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={
                0: {
                    "canton_short": "GL",
                    "year": "2019",
                    "phone_primary": PO_EXISTS,
                    # WRONG "p1_profession": "- und Familienverhältnisse am 31.",
                    # WRONG "p1_employer": "Arb",
                    "p1_income_employed_main": "CHF 154'159",
                    "p2_income_employed_main": "CHF 0",
                    "p1_income_employed_side": "CHF 1'404",
                    "p2_income_employed_side": "CHF 0",
                    "income_portfolio": "CHF 1'467",
                    "income_real_estate_gross": "CHF 17'900",
                    "income_real_estate_gross_other": "CHF 500",
                    "income_real_estate_net": "CHF 16'060",
                    "income_gross_total": "CHF 173'090",
                    "expense_children_daycare": "CHF 4'336",
                    "p1_contribution_pillar_3a": "CHF 6'826",
                    "p2_contribution_pillar_3a": "CHF 0",
                    "p1_expense_employment": "CHF 10'960",
                    "deductions_education": "CHF 3'747",
                    "deductions_illness": "CHF 14'052",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'200",
                    "interest_paid_on_debt": "CHF 8'010",
                    "deductions_wealth_management": "CHF 335",
                    "deductions_donations": "CHF 945",
                    "property_maintenance_cost": "CHF 1'740",
                    "deductions_total": "CHF 48'944",
                    "income_net_total": "CHF 124'146",
                    "income_taxable_local": "CHF 88'149",
                    "assets_portfolio": "CHF 167'297",
                    "assets_cars": "CHF 14'763",
                    "assets_real_estate_total_net": "CHF 825'000",
                    "assets_gross_total": "CHF 1'007'060",
                    "assets_net_total": "CHF 282'685",
                    "assets_taxable_local": "CHF 57'685",
                    "debt_private": "CHF 724'375",
                    "property_imputed_rental_value": "CHF 17'400",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_spacy  # page 18 should be TAX_MISC, not second document
@pytest.mark.broken_legacy_test
@pytest.mark.skip("Needs more spacy training for all pages of GL tax")
async def test_gl_double_2020():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        source_folder=source_folder / "GL" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_gl_double_2020.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1,  # This is wrong
            },
            expected_page_objects={
                0: {
                    "canton_short": "GL",
                    "year": "2020",
                    # WRONG because blacked "p1_profession": "- und Familienverhältnisse am 31.",
                    "p1_income_employed_main": "CHF 123'800",
                    "p2_income_employed_main": "CHF 10'700",
                    "p2_income_employed_side": "CHF 8'000",
                    "income_child_benefits": "CHF 1'919",
                    "income_portfolio": "CHF 25'681",
                    "income_real_estate_net": "CHF 35'702",
                    "income_real_estate_gross": "CHF 42'089",
                    "income_other": "CHF 647",
                    "p1_contribution_pillar_3a": "CHF 6'682",
                    "p2_contribution_pillar_3a": "CHF 2'751",
                    "p1_expense_employment": "CHF 7'561",
                    "p2_expense_employment": "CHF 3'860",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 8'000",
                    "interest_paid_on_debt": "CHF 16'359",
                    "deductions_wealth_management": "CHF 181",
                    "deductions_donations": "CHF 230",
                    "property_maintenance_cost": "CHF 2'031",
                    "deductions_total": "CHF 56'593",
                    "income_net_total": "CHF 149'856",
                    "income_taxable_local": "CHF 135'626",
                    "assets_portfolio": "CHF 146'327",
                    "assets_life_insurance": "CHF 26'564",
                    "assets_cars": "CHF 1'251",
                    "assets_other": "CHF 23'000",
                    "assets_real_estate_total_net": "CHF 1'289'500",
                    "assets_gross_total": "CHF 1'486'642",
                    "assets_net_total": "CHF 192'342",
                    "assets_taxable_local": "CHF 0",
                    "debt_private": "CHF 1'294'300",
                    # "debt_detail_lines": PO_EXISTS,
                    "property_imputed_rental_value": "CHF 21'780",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_so_blacked_1():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "SO" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_so_1.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_so_blacked_2():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "SO" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_so_2.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_so_blacked_3():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "SO" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_so_3.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.parallelfails
@pytest.mark.xdist_group(name="blacked_misc")
async def test_zg_blacked_2018():
    # This one extracts badly because text is not full page so the frame of reference is missing
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ZG" / "blacked_misc",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_zg_single_2018.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()
