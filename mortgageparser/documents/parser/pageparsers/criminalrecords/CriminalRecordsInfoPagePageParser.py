from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings


class CriminalRecordsInfoPagePageParser(AbstractPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ALL_LANGUAGES

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        # Info page comes in 2 version landscape or portrait

        success = contains_all_strings(
            text,
            [
                "Auszug aus dem Schweizerischen Strafregister",
                "Rechtsgrundlage / Base légale / Base legale / Legal basis",
            ],
        )

        if success:
            success = contains_all_strings(
                text,
                [
                    "Dieses elektronische Dokument ist nur in elektronischer Form gültig und überprüfbar.",
                ],
            ) or contains_all_strings(
                text,
                [
                    "Amtliches Dokument / Document officiel",
                    "Dieses elektronische Dokument ist in elektronischer Form als auch in Papierform gültig.",
                ],
            )

        return success

    def parse(self) -> SemanticPage:
        return create_semantic_page(
            self, DocumentCat.CRIMINAL_RECORDS, PageCat.CRIMINAL_RECORDS_INFO_PAGE
        )
