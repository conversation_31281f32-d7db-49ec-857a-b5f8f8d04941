import hashlib
from pathlib import Path
from typing import Dict

import structlog

from constants import BASE_DIR

logger = structlog.getLogger(__name__)

known_ignored_files_dict = None


def create_unique_file_identifier_from_path(path: Path):
    with path.open("rb") as f:
        bytes = f.read()  # read entire file as bytes
        return create_unique_file_identifier_from_bytes(bytes)


def create_unique_file_identifier_from_bytes(bytes):
    sha256sum = hashlib.sha256(bytes).hexdigest()
    return sha256sum


def load_known_ignored_files(path_root: Path) -> Dict[str, str]:
    ids: Dict[str, str] = {}
    assert path_root.exists(), f"Could not find path for ignored files: {path_root}"
    files = path_root.rglob("*")
    for f in files:
        path_rel = f.relative_to(path_root)
        if f.is_file():
            with f.open("rb") as f_read:
                byte_data = f_read.read()  # read entire file as bytes
                # sha256sum = hashlib.sha256(bytes).hexdigest()
                file_id = create_unique_file_identifier_from_bytes(byte_data)
                ids[file_id] = str(path_rel)
    return ids


def get_known_ignored_files_dict():
    global known_ignored_files_dict

    if known_ignored_files_dict is None:
        known_ignored_files_dict = load_known_ignored_files(
            path_root=Path(f"{BASE_DIR}/data_production/known_ignored_files")
        )

        logger.info(
            "Loaded known ignored files",
            count=len(known_ignored_files_dict),
            known_ignored_files=known_ignored_files_dict.values(),
        )

    return known_ignored_files_dict


def is_known_ignored_file_by_path(path: Path):
    id = create_unique_file_identifier_from_path(path)
    return is_known_ignored_file_by_id(id)


def is_known_ignored_file_by_bytes(bytes):
    id = create_unique_file_identifier_from_bytes(bytes)
    return is_known_ignored_file_by_id(id)


def is_known_ignored_file_by_id(id):
    d = get_known_ignored_files_dict()
    if id in d:
        return d[id]
