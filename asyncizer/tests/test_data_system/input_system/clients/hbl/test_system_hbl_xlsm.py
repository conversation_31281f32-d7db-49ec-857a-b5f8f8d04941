from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "xlsm"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS}/hbl/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_hbl_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_affordability():
    # Note: if you open the xlsm locally in linux. It then creates a .~lock.Bonität-Mustermann.xlsm# lock file which
    # should be ignored. Lock file is check into gitlab intentionally so in testing it should be ignored.
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "affordability",
        # source_file_filter=['Finanzierungspotenzial_FK.xlsx'],
        # show_page=5,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Document is not yet properly detected.
        ),
    ).run()


@pytest.mark.skip(
    "returns sometimes UNKNOWN_DE and sometimes PROPERTY_VALUATION - needs to be fixed/checked by Manuel"
)
@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_amortisation():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "amortisation",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Document not properly detected as custom xlsm file
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.skip(
    "Tries to print a large xlsm with many sheets, takes >2min on frep for excel. retest with new office module"
)
async def test_vorlage():
    # This does not fully work yet. Only some 6 pages are printed but printing in libre office yields >25 pages
    assert path_root_folder.exists()
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "vorlage",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1}
        ),
    ).run()
