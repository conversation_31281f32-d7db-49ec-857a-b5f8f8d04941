FROM python:3.11.9


RUN apt-get update

# This installs poppler 22.12.0
#RUN echo "deb http://ftp.de.debian.org/debian bookworm main non-free" > /etc/apt/sources.list.d/debian-nonfree.list \
#    && apt -y update \
#    && apt install -y libgl1-mesa-glx libmagickwand-dev ghostscript poppler-utils openssh-server unrar iputils-ping openssl qpdf

# Install poppler version 25.05.0
RUN echo "deb http://ftp.de.debian.org/debian bookworm main non-free" > /etc/apt/sources.list.d/debian-nonfree.list \
    && apt-get update && apt-get install -y \
    libgl1-mesa-glx libmagickwand-dev ghostscript openssh-server unrar iputils-ping openssl \
    cmake g++ git pkg-config \
    libfontconfig1-dev libfreetype6-dev libjpeg-dev libpng-dev libtiff-dev libopenjp2-7-dev \
    libnss3-dev libglib2.0-dev liblcms2-dev curl \
    && git clone --depth=1 --branch poppler-25.05.0 https://gitlab.freedesktop.org/poppler/poppler.git \
    && mkdir -p poppler/build && cd poppler/build \
    && cmake .. -DCMAKE_INSTALL_PREFIX=/usr -DENABLE_UTILS=ON -DENABLE_CPP=ON -DENABLE_GPGME=OFF -DENABLE_BOOST=OFF -DENABLE_QT5=OFF -DENABLE_QT6=OFF -DENABLE_LIBCURL=OFF -DENABLE_LIBTIFF=ON \
    && make -j"$(nproc)" && make install \
    && cd ../.. && rm -rf poppler


# Install Ghostscript 10.02.0 from source (replaces older apt version)
RUN apt-get update && apt-get install -y \
    build-essential \
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    zlib1g-dev \
    curl \
    && curl -L -o gs.tar.gz https://github.com/ArtifexSoftware/ghostpdl-downloads/releases/download/gs10020/ghostscript-10.02.0.tar.gz \
    && tar -xzf gs.tar.gz && cd ghostscript-10.02.0 \
    && ./configure && make -j$(nproc) && make install \
    && cd .. && rm -rf ghostscript-10.02.0 gs.tar.gz \
    && apt-get remove -y build-essential libjpeg-dev libpng-dev libtiff-dev zlib1g-dev \
    && apt-get autoremove -y && apt-get clean

# Install QPDF 12.x from source
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    libjpeg-dev \
    zlib1g-dev \
    libpng-dev \
    curl \
 && curl -LO https://github.com/qpdf/qpdf/releases/download/v12.2.0/qpdf-12.2.0.tar.gz \
 && tar -xzf qpdf-12.2.0.tar.gz \
 && cd qpdf-12.2.0 \
 && mkdir build \
 && cd build \
 && cmake .. \
 && make -j"$(nproc)" \
 && make install \
 && cd ../.. \
 && rm -rf qpdf-12.2.0 qpdf-12.2.0.tar.gz \
 && apt-get clean \
 && rm -rf /var/lib/apt/lists/*
RUN ldconfig

# see also https://izziswift.com/integrating-python-poetry-with-docker/
ENV YOUR_ENV=${YOUR_ENV} \
  PYTHONFAULTHANDLER=1 \
  PYTHONUNBUFFERED=1 \
  PYTHONHASHSEED=random \
  PIP_NO_CACHE_DIR=off \
  PIP_DISABLE_PIP_VERSION_CHECK=on \
  PIP_DEFAULT_TIMEOUT=100 \
  POETRY_VERSION=1.8.3 \
  POETRY_CACHE_DIR='/var/cache/pypoetry'

RUN python3 -m ensurepip --upgrade && pip install --upgrade pip setuptools wheel


RUN pip install "poetry==$POETRY_VERSION"

RUN mkdir /app
WORKDIR /app
COPY poetry.lock pyproject.toml /app/


# Project initialization:
COPY artefact/de_core_news_sm-2.3.0.tar.gz artefact/de_core_news_sm-2.3.0.tar.gz
RUN --mount=type=secret,id=auth_toml,required,dst=/root/.config/pypoetry/auth.toml poetry config virtualenvs.create false \
  && poetry install $(test "$YOUR_ENV" == production && echo "--no-dev") --no-interaction --no-ansi && rm -rf $POETRY_CACHE_DIR

RUN python -m spacy download de_core_news_sm
RUN sed -i 's/policy domain="coder" rights="none" pattern="PDF"/policy domain="coder" rights="read | write" pattern="PDF"/g' /etc/ImageMagick-6/policy.xml


ENV DISPLAY_SEARCH_RESULT=0





COPY . /app

# automatically unpacks build.tar.gz
ADD artefact/build.tar.gz /app/
ENV DOCUMENT_BROWSER_BUILD_PATH=/app/build

ENV PYTHONPATH=/app:$PYTHONPATH

CMD ["python", "hypodossier/consumer.py"]

