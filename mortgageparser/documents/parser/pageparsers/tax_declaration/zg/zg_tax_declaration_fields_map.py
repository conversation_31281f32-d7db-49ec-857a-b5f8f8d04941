from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    INCOME_PORTFOLIO,
    INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_CHILDREN,
    INCOME_GROSS_TOTAL,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    DEDUCTIONS_TOTAL,
    INCOME_NET_TOTAL,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_CASH_GOLD,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_OTHER,
    ASSETS_REAL_ESTATE_MAIN_PROPERTY,
    ASSETS_REAL_ESTATE_CURRENT_VALUE,
    ASSETS_GROSS_TOTAL,
    DEBT_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    INCOME_OTHER,
    P1_INCOME_CHILD_BENEFITS,
    P2_INCOME_CHILD_BENEFITS,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    EXPENSE_CHILDREN_DAYCARE,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_TAXABLE_GLOBAL,
    ASSETS_NET_TOTAL,
    DEDUCTIONS_ILLNESS,
    DEDUCTIONS_DONATIONS,
    INCOME_LUMP_SUM,
    INCOME_REAL_ESTATE_NET_PRIMARY,
    INCOME_REAL_ESTATE_NET_OTHER,
    DEBT_PRIVATE,
    DEBT_BUSINESS,
)

tax_declaration_zg_fields_map_page_02 = {
    100: P1_INCOME_EMPLOYED_MAIN,
    101: P2_INCOME_EMPLOYED_MAIN,
    105: P1_INCOME_EMPLOYED_SIDE,
    106: P2_INCOME_EMPLOYED_SIDE,
    # 110, 111, 112, 113 missing
    115: P1_INCOME_SELF_MAIN,
    116: P2_INCOME_SELF_MAIN,
    125: P1_INCOME_SELF_SIDE,
    126: P2_INCOME_SELF_SIDE,
    130: P1_INCOME_PENSION_AHV,
    131: P2_INCOME_PENSION_AHV,
    135: P1_INCOME_PENSION,
    136: P2_INCOME_PENSION,
    # 140, 141 missing
    145: P1_INCOME_EO,
    146: P2_INCOME_EO,
    150: P1_INCOME_SOCIAL_SECURITY,
    151: P2_INCOME_SOCIAL_SECURITY,
    155: P1_INCOME_CHILD_BENEFITS,
    156: P2_INCOME_CHILD_BENEFITS,
    160: INCOME_PORTFOLIO,
    170: INCOME_ALIMONY_PARTNER,
    171: INCOME_ALIMONY_CHILDREN,
    173: INCOME_OTHER,
    174: INCOME_LUMP_SUM,
    181: INCOME_REAL_ESTATE_NET_PRIMARY,
    182: INCOME_REAL_ESTATE_NET_OTHER,
    190: INCOME_GROSS_TOTAL,
}

tax_declaration_zg_fields_map_page_03 = {
    201: P1_EXPENSE_EMPLOYMENT,
    202: P2_EXPENSE_EMPLOYMENT,
    205: INTEREST_PAID_ON_DEBT,
    210: EXPENSE_ALIMONY_PARTNER,
    211: EXPENSE_ALIMONY_CHILDREN,
    # 256: EXPENSE_ANNUITY_CONTRIBUTIONS,
    220: P1_CONTRIBUTION_PILLAR_3A,
    221: P2_CONTRIBUTION_PILLAR_3A,
    230: INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    253: EXPENSE_CHILDREN_DAYCARE,
    280: DEDUCTIONS_TOTAL,
    285: INCOME_GROSS_TOTAL,
    295: DEDUCTIONS_ILLNESS,
    296: DEDUCTIONS_DONATIONS,
    299: INCOME_NET_TOTAL,
    490: INCOME_TAXABLE_GLOBAL,
    "500-1": INCOME_TAXABLE_LOCAL,
}

tax_declaration_zg_fields_map_page_04 = {
    600: ASSETS_PORTFOLIO_ACCOUNTS,
    601: ASSETS_CASH_GOLD,
    603: ASSETS_LIFE_INSURANCE,
    604: ASSETS_CARS,
    606: ASSETS_OTHER,
    610: ASSETS_REAL_ESTATE_MAIN_PROPERTY,  # Einfamilienhaus oder Stockwerkeigentum
    611: ASSETS_REAL_ESTATE_CURRENT_VALUE,  # Verkehrswert, but here it is 'in canton Zug'
    # 422: ASSETS_REAL_ESTATE_CAPITALIZED,  # Ertragswert
    630: ASSETS_GROSS_TOTAL,
    640: DEBT_PRIVATE,
    643: DEBT_BUSINESS,
    650: DEBT_TOTAL,
    660: ASSETS_NET_TOTAL,
    700: ASSETS_TAXABLE_LOCAL,  # only in canton (not other cantons / countries)
}
