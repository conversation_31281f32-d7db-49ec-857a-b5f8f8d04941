from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/610_sales_documentation"
)
dest_folder_prefix = "input_system_doccat_sales_doc"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_sales_doc_default_behavior_check():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "sales_doc/normal_11",
        source_file_filter=["sales_dok_3.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.SALES_DOCUMENTATION: 1,
                DocumentCat.WHITE_PAGES: 1,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_sales_doc_unknown_check():
    """
    This is properly categorized as sales doc but it is too long. Make an
    unknown doc because this could be a sausage. Triggered by
    global_settings.MIN_NUM_PAGES_UNKNOWN_OVERRIDE
    """
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "sales_doc/long_55",
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.UNKNOWN_FR: 1,
                DocumentCat.WHITE_PAGES: 1,
            }
        ),
    ).run()
