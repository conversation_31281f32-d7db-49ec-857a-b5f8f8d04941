from dataclasses import dataclass
from typing import List

from hypodossier.core.domain.ClassificationValueObject import ClassificationValueObject


@dataclass
class MatchPageResult:
    confidence: float = 1
    classification: ClassificationValueObject = None
    parsername: str = None

    # Textual explanation of what happened during evaluation
    eval_context: List[str] = None

    def matched(self) -> bool:
        return self.confidence > 0.0

    @property
    def formatted_confidence(self):
        p = str(int(self.confidence * 100))
        return f"{p.rjust(3)}%"
