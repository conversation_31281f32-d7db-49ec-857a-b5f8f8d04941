from enum import Enum
from typing import Optional, List, Any

from pydantic import BaseModel


class SemanticDocumentSplittingStyle(str, Enum):
    DEFAULT = "Default"

    # No splitting of extracted files
    NO_SPLITTING = "NoSplitting"


class OriginalFileProcessingConfig(BaseModel):
    # If each extracted file that is uploaded should be classified as a
    # specific document category the key of the respective document category
    # can be forced here. No splitting will be applied to the extracted file
    force_document_category_key: Optional[str] = None

    # List of elements that are appended to title, joined by ' '.
    # Joined list equals the title suffix
    # None means do nothing
    # Empty list means do not add suffix
    # Deprecated, replaced by force_title_suffix
    force_title_elements: Optional[List[str]] = None

    # Optional title suffix to be used
    force_title_suffix: Optional[str] = None

    # Behaviour of document splitting. Default behaviour is to split best
    # effort or skip if too dangerous.
    semantic_document_splitting_style: Optional[SemanticDocumentSplittingStyle] = (
        SemanticDocumentSplittingStyle.DEFAULT
    )

    # If True every input file (original files and extracted files) is scanned for viruses in processing.
    # If a virus is found, the processing of this file is aborted.
    enable_virus_scan: Optional[bool] = None

    # Overwrites MAX_NUM_PAGES_ALLOWED_IN_PDF from global_settings
    max_num_pages_allowed_input: Optional[int] = None

    # Custom filter can be set here. If none is set, DEFAULT_SMALL_FILES_FILTER
    # will be used
    small_files_filter: Optional[Any] = None

    # This is a string that can be set by the client to help to identify the
    # account, dossier, original file that is being worked on.
    # This must not be parsed or used for any other purpose - information only.
    context: Optional[str] = None

    # Number of retries for OCR. If none or <0 then default MAX_TRIES_FREP_FOR_PDF
    # from global_settings is used
    ocr_max_num_tries: Optional[int] = None
