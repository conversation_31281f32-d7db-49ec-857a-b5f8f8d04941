from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "220_id"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_2023_one_front():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "2023_one_front",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ID: 1},
            expected_page_objects={
                0: {
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "KOR45H58",
                    "date_of_birth": "18.11.1968",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_2023_one_back():

    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "2023_one_back",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ID: 1},
            expected_page_objects={
                0: {
                    "document_date": "18.05.2023",
                    # "person_id": "KOR45H58", # not reliable on the back side
                    "hometown": "Küsnacht ZH",
                    "person_height": "186.0",
                    "mrz": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_2023_many_front():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "2023_many_front",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={DocumentCat.ID: 1}
        ),
    ).run()
