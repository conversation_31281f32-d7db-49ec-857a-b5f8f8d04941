from array import array
from io import BytesIO

import PIL
from PIL.Image import Image
from PIL.ImageDraw import ImageDraw
from PIL.ImageFont import ImageFont


def print_utf8(image, text, color):
    fontName = "FreeSerif.ttf"
    font = ImageFont.truetype(fontName, 18)
    img_pil = Image.fromarray(image)
    draw = ImageDraw.Draw(img_pil)
    draw.text(
        (0, image.shape[0] - 30),
        text,
        font=font,
        fill=(color[0], color[1], color[2], 0),
    )
    image = array(img_pil)
    return image


def pil_gif_info(file_path):
    with PIL.Image.open(file_path) as img:
        is_animated = getattr(img, "is_animated", False)
        frame_count = getattr(img, "n_frames", 1)
        return is_animated, frame_count


def pil_gif_is_animated(file_path):
    is_animated, _ = pil_gif_info(file_path)
    return is_animated


def pil_gif_info_from_bytes(data):
    with PIL.Image.open(BytesIO(data)) as img:
        is_animated = getattr(img, "is_animated", False)
        frame_count = getattr(img, "n_frames", 1)
        return is_animated, frame_count
