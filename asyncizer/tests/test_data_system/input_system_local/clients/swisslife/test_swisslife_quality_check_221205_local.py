from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/swisslife/221205"
)
dest_folder_prefix = "input_system_clients_swisslife_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_sl_finhurdle():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "finhurdle",
        source_file_filter=["finhurdle.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_plan():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "plan",
        source_file_filter=["plan.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_SITUATION: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_finanzanalyse():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "finanzanalyse",
        source_file_filter=["finanzanalyse.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.RETIREMENT_ANALYSIS: 12},
            expected_page_objects={
                0: {
                    "document_date": "09.02.2018",
                    "company": "Swiss Life",
                    "fullname": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_finanzanalyse_2():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "finanzanalyse",
        source_file_filter=["finanzanalyse_2.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.RETIREMENT_ANALYSIS: 8},
            expected_page_objects={
                0: {
                    "document_date": "04.05.2018",
                    "company": "Swiss Life",
                    "fullname": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_finanzanalyse_all():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "finanzanalyse",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.RETIREMENT_ANALYSIS: 2}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_salary():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "salary",
        source_file_filter=["salary.pdf"],
        show_page=1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PAYSLIP: 2}
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_dutch():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "dutch",
        # source_file_filter=['dutch_1.pdf'],
        # show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN: 23},
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 3},
        ),
    ).run()
