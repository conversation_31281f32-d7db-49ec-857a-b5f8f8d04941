from abbyyplumber.converter.ValueConverter import (
    ParagraphConverter,
    MostRecentDateConverter,
    YearConverter,
    ValidNumberConverter,
    DateConverter,
    CompositeConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElementConstrainedArea,
    SearchElementLabeledField,
)
from abbyyplumber.plumberstudio.SearchRelation import ReferenceBoundaryHorizontal
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    PI_FIELD_INSURANCE_AMOUNT,
    FIELD_PROP_OWNER_ADDRESS,
    FIELD_PROP_ADDRESS,
    PI_FIELD_YEAR_CONSTRUCTION,
    FIELD_PROP_CUBATURE,
    FIELD_PROP_ESTIMATION_DATE,
    FIELD_PROP_PROPERTY_DESC,
    FIELD_PROP_CADASTER_NO,
    FIELD_PROP_POLICE_NO,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_CANTON_SHORT,
    FIELD_FIRSTNAME,
    FIELD_DOCUMENT_DATE,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.CommonPropertyInsurancePageParsers import (
    StandardPropertyInsurancePageParser,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.property_insurance_util import (
    PI_CONVERTER_INSURANCE_AMOUNT,
    PI_CONVERTER_CUBATURE,
)
from mortgageparser.util.search_element_util import create_search_elements_address
from mortgageparser.util.string_utils import contains_all_strings


class PropertyInsuranceSOConfirmation2015PageParser(
    StandardPropertyInsurancePageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Solothurnische Gebäudeversicherung",
                "<EMAIL>",
                "Versicherungsnachweis",
                "Grundbuchnummer",
                "Neuwert des Gebäudes",
                "Vers.Wert",
            ],
        )

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementConstant(FIELD_CANTON_SHORT.name, "SO"),
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.main,
                    x_range=PercentageRange(0.5, 1),
                    text_top="E-Mail",
                    text_bottom="Grundbuchnummer",
                ),
                SearchElementConstrainedArea(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.main,
                    x_range=PercentageRange(0.5, 1),
                    text_bottom="E-Mail",
                    converter=MostRecentDateConverter(),
                ),
                SearchElementConstrainedArea(
                    "target_table",
                    self.page.main,
                    text_top="Versicherungsnachweis",
                    text_top_offset_lines=-11,
                    text_right="Vers.Wert",
                    text_bottom="Versicherungsnachweis",
                    extract=False,
                ),
                SearchElementLabeledField(
                    FIELD_PROP_ESTIMATION_DATE.name,
                    self.page.main,
                    label="Einschätzung vom",
                    field_pos_page_horizontal=PercentageRange(0, 0.5),
                    converter=DateConverter(),
                ),
                SearchElementLabeledField(
                    FIELD_PROP_POLICE_NO.name,
                    self.page.main,
                    target_name="target_table",
                    label="Versicherung Nr.",
                    converter=ParagraphConverter(max_num_spaces_per_line=0),
                ),
                SearchElementLabeledField(
                    FIELD_PROP_CADASTER_NO.name,
                    self.page.main,
                    target_name="target_table",
                    label="Grundbuchnummer",
                    converter=ValidNumberConverter(max=9999),
                ),
                SearchElementLabeledField(
                    FIELD_PROP_ADDRESS.name,
                    self.page.main,
                    target_name="target_table",
                    label="Ortslage",
                    converter=ParagraphConverter(max_num_lines_valid=1),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_OWNER_ADDRESS.name,
                    self.page.main,
                    target_name="target_table",
                    text_top="Ortslage",
                    text_left="Eigentümer",
                    # Can be 2 lines
                    converter=ParagraphConverter(max_num_lines_valid=2),
                ),
                SearchElementConstrainedArea(
                    "target_prop",
                    self.page.main,
                    text_top="Versicherungsnachweis",
                    # Section with legal stuff does not always exist
                    text_bottom="Neuwert des Gebäudes 20",
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    PI_FIELD_YEAR_CONSTRUCTION.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Baujahr",
                    text_left="Baujahr",
                    text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                    text_right="Länge",
                    text_bottom="Baujahr",
                    text_bottom_offset_lines=3.5,
                    converter=CompositeConverter(
                        converters=[
                            ParagraphConverter(max_num_lines=1),
                            YearConverter(),
                        ]
                    ),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_CUBATURE.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Baujahr",
                    text_left="Höhe",
                    text_right="Vers.Wert",
                    text_bottom="Baujahr",
                    text_bottom_offset_lines=3.5,
                    converter=CompositeConverter(
                        converters=[
                            ParagraphConverter(max_num_lines=1),
                            PI_CONVERTER_CUBATURE,
                        ]
                    ),
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_PROPERTY_DESC.name,
                    self.page.main,
                    target_name="target_prop",
                    text_top="Gebäudedetail",
                    text_right="Baujahr",
                    text_bottom="Gebäudedetail",
                    text_bottom_offset_lines=3.5,
                    converter=ParagraphConverter(max_num_lines=1),
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_AMOUNT.name,
                    self.page.main,
                    label="Neuwert des Gebäudes",
                    field_pos_page_horizontal=PercentageRange(0.7, 1),
                    converter=PI_CONVERTER_INSURANCE_AMOUNT,
                ),
                # SearchElementLabeledField(PI_FIELD_INSURANCE_YEARLY_BILL_AMOUNT.name,
                #                           self.page.main,
                #                           label="Prämie pro Jahr",
                #                           field_pos_page_horizontal=PercentageRange(0.7, 1),
                #                           converter=PI_CONVERTER_YEARLY_FEE)
            ]
            + create_search_elements_address(self.page.main, self.page)
        )
