from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "221114_auth"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_bekb_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_221114_auth():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        # source_file_filter=['email.msg'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1
            }
        ),
    ).run()
