from abbyyplumber.api import Page
from abbyyplumber.converter.ValueConverter import CurrencyConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementLabeledField,
    SearchElementStaticText,
    SearchElementSetChooseFirst,
    SearchElementArea,
    SearchElementConstrainedArea,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    SearchRelationAbove,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    DEBT_TOTAL,
    INTEREST_PAID_ON_DEBT,
    DEBT_DETAIL_LINES,
    DEBT_DETAIL_LINES_2,
)
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.ag_tax_declaration_fields_map import (
    create_search_elements_standard_ag,
)
from mortgageparser.util.string_utils import contains_all_strings


# Page 'Personen-, Vermögens- und Abschlusssituation ( P V A )'
# Can have 2 pages. If that is the case, debt info is on second page (which maybe has no header like
# 'Personen-, Vermögen- und Abschlusssituation (P V A )'
class TaxDeclarationAGDebtPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_DEBT_INVENTORY
        self.canton_short = "AG"

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        # This matcher is needed if the debt table comes on the second page without any title, header whatsoever.
        # Then just match by "Legende"
        success = contains_all_strings(
            text,
            [
                "Steuererklärung",
                "Kanton Aargau",
                "Adress-Nr.",
                "Seite",
                "Total",
                "Legende: P/G/E: P: Privat, G: Geschäft, E: Erbschaft",
            ],
        )
        return success

    def create_search_elements(self, page: Page) -> ContentExtractor:
        text = page.get_text()
        has_header = contains_all_strings(text, ["P/G/E", "Schuldbetrag"])
        has_footer = contains_all_strings(
            text, ["Legende: P/G/E: P: Privat, G: Geschäft, E: Erbschaft"]
        )

        elements = []

        if has_footer:
            elements += [
                SearchElementStaticText(
                    "region_title_debt",
                    page.fullpage,
                    label="Kredite/Schulden/Hypotheken",
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    "region_debt", page.fullpage, dist=4, extract=False
                )
                .with_all_texts("Legende: P/G/E: P: Privat, G: Geschäft, E: Erbschaft")
                .include_all(-20, -2, 65, 1)
                .below("region_title_debt"),
                SearchElementConstrainedArea(
                    "region_debt_total",
                    page.fullpage,
                    target_name="region_debt",
                    text_top="Legende: P/G/E: P: Privat, G: Geschäft, E: Erbschaft",
                    dist=4,
                    extract=False,
                ).include_top(-3),
                SearchElementConstrainedArea(
                    "region_col_debt",
                    page.fullpage,
                    target_name="region_debt",
                    text_left="Schuldbetrag",
                    text_right="Schuldbetrag",
                    extract=False,
                )
                .include_left(-1)
                .include_right(1),
                SearchElementConstrainedArea(
                    "region_col_interest",
                    page.fullpage,
                    target_name="region_debt",
                    text_left="Schuldbetrag",
                    text_left_offset_chars=3,
                    extract=False,
                ),
                SearchElementLabeledField(
                    DEBT_TOTAL.name,
                    page.fullpage,
                    target_name="region_col_debt",
                    target_name_label="region_debt_total",
                    label="Total",
                    converter=CurrencyConverter(),
                ),
                SearchElementLabeledField(
                    INTEREST_PAID_ON_DEBT.name,
                    page.fullpage,
                    target_name="region_col_interest",
                    target_name_label="region_debt_total",
                    label="Total",
                    converter=CurrencyConverter(),
                ),
            ]

        name_lines = DEBT_DETAIL_LINES.name
        if not has_header:
            name_lines = DEBT_DETAIL_LINES_2.name

        elements += [
            SearchElementSetChooseFirst(
                [
                    SearchElementStaticText(
                        "debt_top", page.fullpage, label="Schuldbetrag", max_l_dist=0
                    ),
                    SearchElementStaticText(
                        "debt_top",
                        page.fullpage,
                        label="Kredite/Schulden/Hypotheken",
                        max_l_dist=5,
                    ),
                    SearchElementStaticText(
                        "debt_top", page.fullpage, label="Kanton Aargau", max_l_dist=1
                    ),
                ]
            ),
            SearchElementStaticText(
                "debt_bottom",
                page.fullpage,
                label="Legende:",
                max_l_dist=0,
                relations=[
                    SearchRelationBelow("page_top"),
                    SearchRelationBelow("debt_title"),
                    SearchRelationBelow("pge"),
                    SearchRelationAbove(DEBT_TOTAL.name),
                ],
            ),
            SearchElementArea(
                name_lines,
                page.fullpage,
                relations=[
                    SearchRelationBelow("debt_top"),
                    SearchRelationAbove(DEBT_TOTAL.name),
                    SearchRelationAbove("debt_bottom"),
                ],
            ),
        ]
        elements += create_search_elements_standard_ag(page)

        return elements
