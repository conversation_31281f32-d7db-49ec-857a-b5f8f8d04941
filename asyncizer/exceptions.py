from typing import List


class FileProcessingException(Exception):
    """Exception raised if there is an error during the file processing"""

    def __init__(
        self, extracted_file: str, base_exception: Exception, message, details: str
    ) -> None:
        self.extracted_file = extracted_file
        self.base_exception = base_exception
        self.message = message
        self.details = details
        super().__init__(self.message)


class PdfSplitException(Exception):
    pass


class TooManyPagesError(Exception):
    def __init__(
        self, num_pages: int, max_num_pages_allowed_in_pdf: int, details
    ) -> None:
        self.details = details
        self.num_pages = num_pages
        self.max_num_pages_allowed_in_pdf = max_num_pages_allowed_in_pdf
        super().__init__(self.details)


class UnsupportedFileType(Exception):
    """Exception raised if the filetype is currently not supported"""

    def __init__(self, filetype, supported_filetypes) -> None:
        self.filetype = filetype
        self.supported_filetypes = supported_filetypes
        self.message = f"File type '{filetype}' not supported. Currently supported file types: {supported_filetypes}"
        super().__init__(self.message)


class OcrImageFileCorruptedException(Exception):
    """
    Exception if the file processed by OCR is considered corrupted by ABBYY OCR. Could still be possible to
    repair the file with other tools.
    """

    pass


class OcrAbbyyException(Exception):
    """Raised if ABBYY OCR fails with an error message"""

    def __init__(self, error: str) -> None:
        self.error = error
        self.message = f"ABBYY OCR failed with error: {error}"
        super().__init__(self.message)


class OcrFiletypeProcessingException(Exception):
    """
    Exception raised if the file passed our file type filter but fails in ABBYY OCR.
    This can e.g. happen if an (unsupported) .avif has a .jpeg extension.
    """

    def __init__(self, filetype: str, supported_filetypes=List[str]) -> None:
        self.filetype = filetype
        self.supported_filetypes = supported_filetypes
        self.message = f"File corrupt or incorrect file type assigned and real file type not supported. Currently supported file types: {supported_filetypes}"
        super().__init__(self.message)


class OcrTooSlowException(Exception):
    """Raised if ocr timeout occurs. This can happen because OCR service not available or file too large or corrupt."""

    def __init__(self, bucket, object_name, timeout: int) -> None:
        self.bucket = bucket
        self.object_name = object_name
        self.message = f"OCR on the file {self.object_name} in bucket {self.bucket} took too long. Reasons could be: OCR service not available / file too large / file corrupt. Timeout {timeout} seconds."
        super().__init__(self.message)
