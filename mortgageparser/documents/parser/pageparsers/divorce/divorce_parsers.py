from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


def get_parsers_divorce():
    parsers = [
        TemplatePageParser(
            desc="Unterhaltsbeiträge generisch 2021",
            doc_cat=DocumentCat.DIVORCE_DOCUMENT,
            page_cat=PageCat.GENERIC_PAGE,
            ranked_titles_all=[
                RankedTitle("Berechnungstabelle für Unterhaltsbeiträge", 1, 10)
            ],
        ),
    ]
    return parsers
