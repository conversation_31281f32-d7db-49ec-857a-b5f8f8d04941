import os
from pathlib import Path
from typing import Union, Tuple, Optional
import fitz  # PyMuPDF
import re
import structlog

logger = structlog.getLogger(__name__)


def resolve_page_xref(
    page_ref: Union[str, Tuple], context: Optional[str] = None
) -> Optional[int]:
    """
    Resolves a page reference object to a page xref number.

    Args:
        page_ref: The /P reference from an annotation object, typically a string like '12 0 R' or a tuple.
        context: Optional logging context.

    Returns:
        The integer xref number of the page, or None if it could not be resolved.
    """
    try:
        if isinstance(page_ref, tuple):
            return int(page_ref[0])
        elif isinstance(page_ref, str):
            return int(page_ref.split()[0])
    except (ValueError, IndexError, TypeError) as e:
        logger.debug(
            "Failed to resolve page xref",
            page_ref=page_ref,
            error=str(e),
            context=context,
        )
    return None


def xref_to_page_number(doc: fitz.Document, page_xref: int) -> int:
    """
    Returns the page number (0-based) for a given page object xref.
    If the xref is not found, returns -1.
    """
    for page_number in range(len(doc)):
        if doc[page_number].xref == page_xref:
            return page_number
    return -1


def register_orphan_annotations(
    doc: fitz.Document, context: Optional[str] = None
) -> bool:
    """
    Register orphaned annotations to their referenced pages via /P.
    If no page reference is found or is invalid, remove the annotation and log it.
    """
    changed = False
    referenced_annots = set()

    # Step 1: Collect all annotation xrefs already attached to pages
    for page in doc:
        annots = page.annots()
        if annots:
            for annot in annots:
                referenced_annots.add(annot.xref)

    # Step 2: Scan all objects to find annotations
    empty_page_ref = ("null", "null")
    num_xref = doc.xref_length()
    for xref in range(1, num_xref):
        try:
            # type_key = doc.xref_get_key(xref)
            try:
                obj = doc.xref_object(xref, compressed=False)
            except Exception as e:
                # This xref is referenced in the PDF's cross reference table
                # but it does not exist or is malformed.
                # We ignore this as we mainly want to check for annotations
                logger.info("Could not find xref in doc", xref=xref, error=str(e))
                continue

            if "/Type /Annot" not in obj:
                # if type_key != "/Annot":
                continue  # not an annotation

            if xref in referenced_annots:
                continue  # already registered

            # Try to resolve the /P (page) reference
            page_ref = doc.xref_get_key(xref, "P")
            if (
                not page_ref
                or page_ref == empty_page_ref
                or not isinstance(page_ref, Tuple)
            ):
                doc.update_object(xref, "<<>>")
                logger.info(
                    "Removed orphan annotation: no /P reference found",
                    xref=xref,
                    context=context,
                )
                changed = True
                continue

            page_xref = resolve_page_xref(page_ref, context)
            if page_xref is None:
                doc.update_object(xref, "<<>>")
                logger.info(
                    "Removed orphan annotation: unreadable /P reference",
                    xref=xref,
                    context=context,
                )
                changed = True
                continue

            page_number = xref_to_page_number(doc, page_xref)
            if page_number == -1:
                doc.update_object(xref, "<<>>")
                logger.info(
                    "Removed orphan annotation: invalid /P page reference",
                    xref=xref,
                    context=context,
                )
                changed = True
                continue

            # Register annotation on the correct page
            page = doc.load_page(page_number)
            annots = page.get_annot_xrefs() or []

            if xref not in annots:
                annots.append(xref)
                page.set_annot_xrefs(annots)
                logger.info(
                    "Registered orphan annotation",
                    xref=xref,
                    page_number=page_number,
                    context=context,
                )
                changed = True

        except Exception as e:
            logger.error(
                f"Error processing xref={xref}: {e}, context={context}. Nothing changes because of this - processing continues."
            )

    return changed


def clean_orphan_annotations(doc: fitz.Document, context: Optional[str] = None) -> bool:
    """
    Remove only orphan annotations (not referenced by any page).

    NOT SURE IF THIS REALLY WORKS, SO DISABLED FOR NOW.
    page.annots() only sees standard annotations attached via /Annots.
    It does not see non referenced annotations which is not strictly correct
    but exists in many pdfs.
    """
    changed = False
    active_annot_xrefs = set()

    # Collect xrefs of annotations used on pages
    for page in doc:
        annots = page.annots()
        if not annots:
            continue
        for annot in annots:
            active_annot_xrefs.add(annot.xref)

    # Remove any /Type /Annot xref not in active set
    for xref in range(1, doc.xref_length()):  # skip xref 0 as that is not a valid ref
        obj = None
        try:
            obj = doc.xref_object(xref, compressed=False)
            if "/Type /Annot" in obj and xref not in active_annot_xrefs:
                logger.info("Removing orphan annotation", xref=xref, context=context)
                doc.update_object(xref, "<<>>")
                changed = True
        except Exception as e:
            logger.warning(
                "Problem while trying to remove orphaned annotations",
                doc_name=getattr(doc, "name", context),
                obj=obj,
                xref=xref,
                error=str(e),
                context=context,
            )
    return changed


def clean_bbox_size_zero(doc: fitz.Document, context: Optional[str] = None) -> bool:
    """
    Remove all bboxes with width or height of zero.

    """
    changed = False

    for xref in range(1, doc.xref_length()):  # skip xref 0 as that is not a valid ref
        try:
            obj = doc.xref_object(xref, compressed=False)
            if "/Subtype /Form" in obj and "/BBox" in obj:
                match = re.search(r"/BBox\s*\[\s*([^\]]+)\]", obj)
                if match:
                    vals = list(map(float, match.group(1).split()))
                    if len(vals) == 4:
                        width = vals[2] - vals[0]
                        height = vals[3] - vals[1]
                        if width == 0 or height == 0:
                            logger.info(
                                "Removing Form XObject with 0-size BBox",
                                xref=xref,
                                context=context,
                            )
                            doc.update_object(xref, "<<>>")
                            changed = True
        except Exception as e:
            logger.warning("xref parse error", xref=xref, error=str(e), context=context)
    return changed


def pymupdf_pdf_fix(
    input_path: Path,
    output_path_dir: Path,
    suffix: str = "_repairedmu",
    timeout: float = -1,
    remove_orphan_annotations: bool = True,
    remove_size_zero_bboxes: bool = True,
    context: Optional[str] = None,
) -> Union[Tuple[Path, bool], Tuple[None, bool]]:
    """
    Fully cleans invalid orphaned annotations and appearance objects using PyMuPDF.
    Safe for use with Ghostscript 10+ strict validation.

    Args:
        :param input_path: Input PDF
        :param output_path_dir: Output directory
        :param suffix: Suffix to append to filename
        :param timeout: Unused
        :param remove_size_zero_bboxes:
        :param remove_orphan_annotations:
        :param context: String that will be logged and can help with handling exceptions

    Returns:
        Tuple of (output_path, True) if changes have been made or (None, False) if no changes have been made (or error in processing).

    """
    dest_path = output_path_dir / f"{input_path.stem}{suffix}{input_path.suffix}"

    changed_annotation = None
    changed_bbox = None
    changed = None
    try:
        doc = fitz.open(str(input_path))

        # changed_register_annotation = register_orphan_annotations(doc, context)
        #
        # # Clean orphan annotations and stray Form XObjects
        # if remove_orphan_annotations:
        #     changed_annotation = clean_orphan_annotations(doc, context)
        # else:
        #     changed_annotation = False
        #
        # # Remove stray Form XObjects with invalid BBox
        # if remove_size_zero_bboxes:
        #     changed_bbox = clean_bbox_size_zero(doc, context)
        # else:
        #     changed_bbox = False
        #
        # changed = changed_register_annotation or changed_annotation or changed_bbox
        #
        # if changed:
        doc.save(str(dest_path), garbage=4, deflate=True)
        doc.close()
        changed = True

        file_size_input = os.path.getsize(input_path)  # in bytes
        file_size_output = os.path.getsize(dest_path)

        logger.info(
            "pymupdf_pdf_fix",
            input_path=str(input_path),
            output_path=str(dest_path),
            changed_annotation=changed_annotation,
            changed_bbox=changed_bbox,
            changed=changed,
            context=context,
            file_size_input=file_size_input,
            file_size_output=file_size_output,
        )
        if changed:
            return dest_path, True

    except Exception as e:
        mu_warnings = fitz.TOOLS.mupdf_warnings()

        logger.error(
            "pymupdf_pdf_fix: Exception while fixing PDF with PyMuPDF",
            input_path=str(input_path),
            output_path=str(dest_path),
            changed_annotation=changed_annotation,
            changed_bbox=changed_bbox,
            changed=changed,
            error=str(e),
            context=context,
            mu_warnings=mu_warnings,
        )
    return None, False


def pymupdf_get_page_count(filepath: Union[str, Path]) -> int:
    doc = fitz.open(filepath)
    return doc.page_count
