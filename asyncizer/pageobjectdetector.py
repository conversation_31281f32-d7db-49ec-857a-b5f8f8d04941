import os
import uuid
from typing import List, Optional

import structlog
from pydantic import BaseModel

from asyncizer.rpc_pika import get_rpc_client

logger = structlog.getLogger(__name__)


class PageObjectDetectionRequestV1(BaseModel):
    # model_config = ConfigDict(protected_namespaces=())
    uuid: str
    bucket: str
    prefix_images: List[str]
    model_name: Optional[str] = None


class BBox(BaseModel):
    x1: int
    y1: int
    x2: int
    y2: int


class PageObjectDetection(BaseModel):
    classification: str
    confidence: float
    image_width: int
    image_height: int
    bbox: BBox


class PageObjectDetectionsPerFile(BaseModel):
    bucket: str
    prefix_image: str
    pod: PageObjectDetection


class PageObjectDetectionResponseV1(BaseModel):
    # model_config = ConfigDict(protected_namespaces=())
    detections: List[PageObjectDetectionsPerFile]
    model_name: Optional[str] = None


ASYNC_OBJECT_DETECTOR_WORKER_ROUTING_KEY = os.getenv(
    "ASYNC_OBJECT_DETECTOR_WORKER_ROUTING_KEY",
    default="ObjectDetector.PageObjectDetectionRequestV1",
)


async def call_detect_page_objects_remote(
    bucket: str, prefix_images: List[str], model_name="model_v15s"
) -> PageObjectDetectionResponseV1:
    rpc_client = await get_rpc_client()
    result = await rpc_client.call(
        ASYNC_OBJECT_DETECTOR_WORKER_ROUTING_KEY,
        PageObjectDetectionRequestV1(
            uuid=str(uuid.uuid4()),
            bucket=bucket,
            prefix_images=prefix_images,
            model_name=model_name,
        ).json(),
    )
    ret = PageObjectDetectionResponseV1.parse_raw(result)
    return ret
