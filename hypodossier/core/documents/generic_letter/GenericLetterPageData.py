from dataclasses import dataclass

# There is a specification here: https://www.elohnausweis-ssk.ch/de/assets/documents/Benutzerhandbuch_eLA.pdf
# https://www.elohnausweis-ssk.ch/de/assets/documents/SalaryDeclarationElohnOnline.xsd
from typing import Dict

from hypodossier.core.domain.PageData import StandardMultipagePageData
from hypodossier.core.domain.SemanticField import (
    SemanticField,
    fields_as_dict,
    FIELD_ADDRESS_BLOCK,
    FIELD_FULLNAME,
    FIELD_FIRSTNAME,
    FIELD_STREET,
    FIELD_CITY,
    FIELD_ZIP,
    FIELD_DOCUMENT_DATE,
    FIELD_LASTNAME,
    FIELD_DATE_OF_BIRTH,
    FIELD_COMPANY,
    FIELD_PRODUCT,
    FIELD_DOCUMENT_TITLE,
)

FIELDS_GENERIC_LETTER = fields_as_dict(
    [
        FIELD_DOCUMENT_DATE,
        FIELD_COMPANY,
        FIELD_PRODUCT,
        FIELD_DOCUMENT_TITLE,
        FIELD_ADDRESS_BLOCK,
        FIELD_FIRSTNAME,
        FIELD_LASTNAME,
        FIELD_FULLNAME,
        FIELD_STREET,
        FIELD_ZIP,
        FIELD_CITY,
        FIELD_DATE_OF_BIRTH,
    ]
)


@dataclass
class GenericLetterPageData(StandardMultipagePageData):
    document_date: str = None
    company: str = None
    product: str = None

    document_title: str = None

    address_block: str = None

    firstname: str = None
    lastname: str = None

    # Name of person as derived from address_block
    fullname: str = None

    street: str = None
    zip: str = None
    city: str = None

    date_of_birth: str = None

    def get_invalidity_reason(self):
        msg = None
        if not self.firstname:
            msg = "firstname is missing"
        return msg

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        d = super().get_semantic_fields()
        return {**d, **FIELDS_GENERIC_LETTER}
