from abbyyplumber.plumberstudio.SearchElement import (
    Search<PERSON><PERSON><PERSON><PERSON>,
    SearchElement<PERSON><PERSON><PERSON><PERSON><PERSON>,
    SearchElementConstrainedArea,
    SearchElementMultiStaticText,
    create_labeled_field_vertical,
    create_labeled_field,
)
from abbyyplumber.util.plumberstudio_util import RANGE_RIGHT, RANGE_LEFT
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FULL<PERSON>ME,
    DEBT_PRIVATE,
    ASSETS_UNDISTRIBUTED_INHERITANCES,
    INCOME_CHILD_BENEFITS,
    P1_DEDUCTIONS_EDUCATION,
    P2_DEDUCTIONS_EDUCATION,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_CHILDREN,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    DEDUCTIONS_TOTAL,
    INCOME_NET_TOTAL,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_CASH_GOLD,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_OTHER,
    ASSETS_REAL_ESTATE_CURRENT_VALUE,
    ASSETS_GROSS_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    INCOME_OTHER,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    EXPENSE_CHILDREN_DAYCARE,
    DEDUCTIONS_DONATIONS,
    ASSETS_TAXABLE_GLOBAL,
    DEDUCTIONS_ILLNESS,
    INCOME_TAXABLE_GLOBAL,
    DEDUCTIONS_OTHER,
    INCOME_LUMP_SUM,
    DEBT_BUSINESS,
    ASSETS_NET_TOTAL,
    P1_CONTRIBUTION_PILLAR_1,
    P1_CONTRIBUTION_PILLAR_2,
    P2_CONTRIBUTION_PILLAR_1,
    P2_CONTRIBUTION_PILLAR_2,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    COLUMN_CODE,
    COLUMN_VALUE,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.template_tax_declaration_util import (
    TemplateTaxDeclarationPageParser,
    TaxDeclarationSearchElements,
)

TAX_DECLARATION_NW_PERSONAL = TemplateTaxDeclarationPageParser(
    desc="Tax NW Personal 2018",
    page_cat=PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA,
    canton="NW",
    ranked_titles_all=[RankedTitle("Steuererklärung"), RankedTitle("Kanton Nidwalden")],
    required_tokens=[
        "steuern-nw.ch",
        "Personalien, Berufs- und",
        "stehende Kinder, deren",
    ],
    page_main=SearchElementConstrainedArea(
        None,
        None,
        text_top="Kanton Nidwalden",
        text_left="Kanton Nidwalden",
        text_bottom="Hauptformular HF",
        text_right="Hauptformular HF",
    ).include_all(-15, -1, 10, 1),
    regions=[
        SearchElementConstrainedArea(
            "region_header", None, text_bottom="Kanton Nidwalden"
        ).include_bottom(3),
        SearchElementConstrainedArea(
            FIELD_FIRSTNAME.sr_inside,
            None,
            text_top="Kanton Nidwalden",
            text_left="Personalien, Berufs- und",
            texts_bottom=["Personalien, Berufs- und"],
        ).include_all(2, -1, 1, -8),
        SearchElementConstrainedArea(
            "region_table",
            None,
            text_top="Personalien, Berufs- und",
            text_left="Personalien, Berufs- und",
            text_bottom="Minderjährige (20",
        ).include_left(),
        SearchElementArea(
            "region_table_p1", None, target_name="region_table", x_range=RANGE_LEFT
        ),
        SearchElementArea(
            "region_table_p2", None, target_name="region_table", x_range=RANGE_RIGHT
        ),
        SearchElementConstrainedArea(
            "region_children",
            None,
            text_top="stehende Kinder, deren",
            text_bottom="Erwerbsunfähige oder",
        ),
    ],
    se=TaxDeclarationSearchElements(
        year=SearchElementLabeledField("region_header", None, label="Steuererklärung"),
        p1_fullname=SearchElementConstrainedArea(
            P1_FULLNAME.name, None, target_name=FIELD_FIRSTNAME.sr_inside
        ),
        p1_date_of_birth=create_labeled_field(
            "Geburtsdatum", "region_table_p1", field_vertical_line_scale=2.5
        ),
        p2_date_of_birth=create_labeled_field(
            "Geburtsdatum", "region_table_p2", field_vertical_line_scale=2.5
        ),
        p1_ahv_new=SearchElementArea(None, None, target_name="region_header"),
        p1_profession=create_labeled_field(
            "Beruf", "region_table_p1", field_vertical_line_scale=2.5
        ),
        p2_profession=create_labeled_field(
            "Beruf", "region_table_p2", field_vertical_line_scale=2.5
        ),
        p1_employer_location=create_labeled_field(
            "Arbeitsort", "region_table_p1", field_vertical_line_scale=2.5
        ),
        p2_employer_location=create_labeled_field(
            "Arbeitsort", "region_table_p2", field_vertical_line_scale=2.5
        ),
        p1_employer=create_labeled_field(
            "Arbeitgeber/in", "region_table_p1", field_vertical_line_scale=2.5
        ),
        p2_employer=create_labeled_field(
            "Arbeitgeber/in", "region_table_p2", field_vertical_line_scale=2.5
        ),
        section_children=SearchElementConstrainedArea(
            None,
            None,
            target_name="region_children",
            texts_top=["Schule oder", "Lehrfirma", "Haushalt"],
            texts_left=["Vorname", "Schule", "Konkubinats"],
            texts_right=["Geburtsjahr", "Ausbildungsende"],
            text_bottom="Konkubinats",
        ).include_left(-0.2),
    ),
)

# No classification, just extraction. Therefore not part of the return statement at the bottom.
TAX_DECLARATION_NW_INCOME = TemplateTaxDeclarationPageParser(
    page_cat=PageCat.TAX_DECLARATION_PAGE_INCOME,
    desc="Tax NW Income 2018",
    canton="NW",
    # ranked_titles_all=[
    #     RankedTitle('EINKÜNFTE IM IN- UND AUSLAND', rank=2, min_length_title=10),
    #     RankedTitle('Hauptformular HF Seite 2', min_length_title=10),
    #     RankedTitle('IMPÔT', min_length_title=4)
    # ],
    # required_tokens=["Ersatzeinkünfte", "ohne Erwerbseinkommen dieser Kinder"],
    regions=[
        SearchElementConstrainedArea(
            "region_total",
            None,
            text_top="Übertrag Ziffer 159",
            text_bottom="Hauptformular HF",
        ).include_top(-5),
        SearchElementConstrainedArea(
            "region_total_value",
            None,
            target_name="region_total",
            text_top="Kanton",
            text_left="Kanton",
            text_right="Bund",
            text_right_offset_chars=-10,
        ).include_left(-4),
        SearchElementMultiStaticText(
            "token_code_left", None, labels={"Lohnausweis": 4}
        ),
        create_labeled_field_vertical(
            "CHF ohne Rappen",
            name=COLUMN_CODE,
            offset_left=-17,
            offset_right=-21,
            offset_bottom=1000,
        )
        .rightof("token_code_left")
        .above("region_total"),
        create_labeled_field_vertical(
            "CHF ohne Rappen",
            name=COLUMN_VALUE,
            offset_left=0,
            offset_right=5,
            offset_bottom=1000,
        )
        .rightof(COLUMN_CODE)
        .above("region_total"),
    ],
    se=TaxDeclarationSearchElements(
        income_portfolio=create_labeled_field(
            "Guthaben,Wertschriften",
            "region_total_value",
            target_name_label="region_total",
        ),
        income_real_estate_net=create_labeled_field(
            "Liegenschaften", "region_total_value", target_name_label="region_total"
        ),
        income_undistributed_inheritances=create_labeled_field(
            "unverteilte Erbschaften",
            "region_total_value",
            target_name_label="region_total",
        ),
        income_gross_total=create_labeled_field(
            "Total der Einkünfte",
            "region_total_value",
            target_name_label="region_total",
        ),
    ),
    tax_code_map={
        100: P1_INCOME_EMPLOYED_MAIN,
        101: P2_INCOME_EMPLOYED_MAIN,
        104: P1_INCOME_EMPLOYED_SIDE,
        105: P2_INCOME_EMPLOYED_SIDE,
        # 095 missing
        110: P1_INCOME_SELF_MAIN,
        111: P2_INCOME_SELF_MAIN,
        114: P1_INCOME_SELF_SIDE,
        115: P2_INCOME_SELF_SIDE,
        # 118, 119 missing
        130: P1_INCOME_PENSION_AHV,
        131: P2_INCOME_PENSION_AHV,
        132: P1_INCOME_PENSION,
        133: P2_INCOME_PENSION,
        # 134...137 missing (2nd pension)
        140: P1_INCOME_SOCIAL_SECURITY,
        141: P2_INCOME_SOCIAL_SECURITY,
        # 142, 143 missing
        144: INCOME_CHILD_BENEFITS,
        150: INCOME_ALIMONY_PARTNER,
        151: INCOME_ALIMONY_CHILDREN,
        155: INCOME_OTHER,
        156: INCOME_LUMP_SUM,
    },
)

# No classification, just extraction. Therefore not part of the return statement at the bottom.
TAX_DECLARATION_NW_DEDUCTIONS = TemplateTaxDeclarationPageParser(
    page_cat=PageCat.TAX_DECLARATION_PAGE_DEDUCTIONS,
    desc="Tax NW Deductions 2018",
    canton="NW",
    # ranked_titles_all=[
    #     RankedTitle('ABZÜGE', min_length_title=10),
    #     RankedTitle('EINKOMMENSBERECHNUNG', min_length_title=10),
    #     RankedTitle('Hauptformular HF Seite 3', min_length_title=10),
    #     RankedTitle('IMPÔT', min_length_title=4)
    # ],
    # required_tokens=[
    #     "Minuszeichen eintragen, wenn negativ",
    #     "(Ziffer 18 abzüglich Ziffern 19.1",
    #     "22. STEUERBARES EINKOMMEN (Ziffer 20 abzüglich Ziffer 21.1 bis 21.9)"
    # ],
    page_main=SearchElementConstrainedArea(
        None,
        None,
        text_top="Abzüge",
        text_left="Abzüge",
        text_bottom="Hauptformular HF",
        text_right="Hauptformular HF",
    ).include_all(-2, -1, 10, 1),
    regions=[
        SearchElementMultiStaticText(
            "token_code_left",
            None,
            labels={"Kinderbetreuungskosten": 6, "Schuldenverzeichnis": 4},
        ),
        create_labeled_field_vertical(
            "CHF ohne Rappen",
            name=COLUMN_CODE,
            offset_left=-9,
            offset_right=-15,
            offset_bottom=1000,
        ).rightof("token_code_left"),
        create_labeled_field_vertical(
            "CHF ohne Rappen",
            name=COLUMN_VALUE,
            offset_left=0,
            offset_right=5,
            offset_bottom=1000,
        ).rightof(COLUMN_CODE),
    ],
    se=TaxDeclarationSearchElements(),
    tax_code_map={
        200: P1_EXPENSE_EMPLOYMENT,
        220: P2_EXPENSE_EMPLOYMENT,
        250: INTEREST_PAID_ON_DEBT,
        254: EXPENSE_ALIMONY_PARTNER,
        255: EXPENSE_ALIMONY_CHILDREN,
        256: EXPENSE_ANNUITY_CONTRIBUTIONS,
        258: P1_CONTRIBUTION_PILLAR_1,
        259: P2_CONTRIBUTION_PILLAR_1,
        260: P1_CONTRIBUTION_PILLAR_2,
        261: P2_CONTRIBUTION_PILLAR_2,
        262: P1_CONTRIBUTION_PILLAR_3A,
        263: P2_CONTRIBUTION_PILLAR_3A,
        270: INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
        # 280 missing
        282: P1_DEDUCTIONS_EDUCATION,
        283: P2_DEDUCTIONS_EDUCATION,
        285: DEDUCTIONS_OTHER,
        287: EXPENSE_CHILDREN_DAYCARE,
        299: DEDUCTIONS_TOTAL,
        320: DEDUCTIONS_ILLNESS,
        325: DEDUCTIONS_DONATIONS,
        330: INCOME_NET_TOTAL,
        380: INCOME_TAXABLE_GLOBAL,
        "380": INCOME_TAXABLE_LOCAL,
    },
)

# No classification, just extraction. Therefore, not part of the return statement at the bottom.
TAX_DECLARATION_NW_ASSETS = TemplateTaxDeclarationPageParser(
    desc="Tax NW Assets 2018",
    canton="NW",
    page_cat=PageCat.TAX_DECLARATION_PAGE_ASSETS,
    # ranked_titles_all=[
    #     RankedTitle('VERMÖGEN IM IN- UND AUSLAND', min_length_title=10),
    #     RankedTitle('Hauptformular HF Seite 4', min_length_title=10),
    # ],
    # required_tokens=[
    #     "Minuszeichen eintragen, wenn negativ",
    #     "einschliesslich Nutzniessungsvermögen",
    #     "abzüglich Ziffer 28.1 bis 28.3)"
    # ],
    regions=[
        SearchElementMultiStaticText(
            "token_code_left",
            None,
            labels={"Wertschriftenverzeichnis": 6, "Erbengemeinschaften": 6},
        ),
        create_labeled_field_vertical(
            "CHF ohne Rappen",
            name=COLUMN_CODE,
            offset_left=-12,
            offset_right=-20,
            offset_bottom=1000,
        ).rightof("token_code_left"),
        create_labeled_field_vertical(
            "CHF ohne Rappen", name=COLUMN_VALUE, offset_bottom=1000
        )
        .include_horizontal(0, 4)
        .rightof(COLUMN_CODE),
    ],
    tax_code_map={
        400: ASSETS_PORTFOLIO_ACCOUNTS,
        404: ASSETS_CASH_GOLD,
        410: ASSETS_LIFE_INSURANCE,
        412: ASSETS_CARS,
        414: ASSETS_UNDISTRIBUTED_INHERITANCES,
        416: ASSETS_OTHER,
        420: ASSETS_REAL_ESTATE_CURRENT_VALUE,  # Verkehrswert
        # business stuff missing 430 - 443
        450: ASSETS_GROSS_TOTAL,
        460: DEBT_PRIVATE,
        461: DEBT_BUSINESS,  # unprecise: is debt of P1
        470: ASSETS_NET_TOTAL,
        480: ASSETS_TAXABLE_GLOBAL,
        "480": ASSETS_TAXABLE_LOCAL,
    },
)

# No classification, just extraction. Therefore, not part of the return statement at the bottom.
TAX_DECLARATION_NW_DEBT = TemplateTaxDeclarationPageParser(
    desc="Tax NW Debts 2018",
    canton="NW",
    page_cat=PageCat.TAX_DECLARATION_DEBT_INVENTORY,
    # ranked_titles_all=[RankedTitle('Schuldenverzeichnis S', min_length_title=10)],
    # required_tokens=["Kanton Nidwalden", "Seite 3, Ziffer 13.1", "Seite 4, Ziffer 26.1"],
    regions=[
        create_labeled_field_vertical(
            "bzw. am Ende der Steuerpflicht",
            name="column_value_debt",
            offset_right=3,
            offset_bottom=1000,
        ),
        create_labeled_field_vertical(
            "Schuldzinsen 202",
            name="column_value_interest",
            offset_bottom=1000,
            offset_right=6,
        ),
    ],
    se=TaxDeclarationSearchElements(
        debt_private=create_labeled_field_vertical(
            "Seite 4, Ziffer 460",
            target_name="column_value_debt",
            offset_top=-6,
            offset_bottom=-2,
            offset_right=16,
        ),
        interest_paid_on_debt=create_labeled_field_vertical(
            "Seite 3, Ziffer 250",
            target_name="column_value_interest",
            offset_top=-6,
            offset_bottom=-2,
            offset_right=16,
        ),
        debt_detail_lines=create_labeled_field_vertical(
            "des Gläubigers / der Gläubigerin",
            offset_left=-5,
            offset_right=100,
            offset_bottom=20,
        ),
    ),
)

TAX_DECLARATION_NW_OVERVIEW = TemplateTaxDeclarationPageParser(
    desc="Tax NW Aufstellung 2018",
    canton="NW",
    ranked_titles_all=[RankedTitle("Aufstellung zu")],
    required_text_conditions=[
        FromStartTextCond("Kanton Nidwalden", num_lines=5),
        FromStartTextCond("Versichteren-Nr.", num_lines=8),
        FromStartTextCond("PID-Nr.", num_lines=8),
    ],
)

TAX_DECLARATION_NW_OCC_EXP = TemplateTaxDeclarationPageParser(
    desc="Tax NW Berufskosten 2018",
    canton="NW",
    ranked_titles_all=[
        RankedTitle("Berufskosten"),
        RankedTitle("Berufskosten BK-"),
    ],
    required_text_conditions=[
        FromStartTextCond("Kanton Nidwalden", num_lines=5),
        FromStartTextCond("Vorname", num_lines=5),
        FromStartTextCond("PID-Nr.", num_lines=5),
    ],
)


def get_parsers_tax_declaration_nw():
    return [
        TAX_DECLARATION_NW_PERSONAL,
        TAX_DECLARATION_NW_OVERVIEW,
        TAX_DECLARATION_NW_OCC_EXP,
    ]
