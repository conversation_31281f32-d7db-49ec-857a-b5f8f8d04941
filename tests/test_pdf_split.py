import subprocess
from pathlib import Path
from tempfile import TemporaryDirectory

import pikepdf
import structlog

from constants import BASE_PATH
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS

logger = structlog.getLogger(__name__)


def test_qpdf_version():
    qpdf_version = pikepdf.__libqpdf_version__
    logger.info("QPDF Version in pikepdef", qpdf_version=qpdf_version)

    # 240309 mt: Should be 11.9.1 or higher
    # 250523 mt: Should be 12.2.0 or higher (also added in the dockerfile)
    assert qpdf_version.startswith(
        "12."
    ), f"found unexpected qpdf_version: {qpdf_version}"


def test_split_simple():
    file = BASE_PATH / "data_demo/sales_pitch_mix_dir/Betreibungen Manuel Angelica.pdf"
    split_and_assert_paths(file, 2)


def test_split_large_plan_pdf():
    file = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS)
        / "large_plans/large_plan_as_pdf/2282_Verkaufsdok-plan_El_Haus_C_EG.pdf"
    )
    split_and_assert_paths(file, 1)


def split_and_assert_paths(file: Path, num_pages_expected: int):
    filesize = file.stat().st_size
    logger.info("input", filesize=filesize)
    with TemporaryDirectory() as temp_dir:
        assert file.exists()
        res = subprocess.run(
            [
                "qpdf",
                "--verbose",
                "--split-pages",
                "--report-memory-usage",
                str(file),
                f"{temp_dir}/%d.pdf",
            ],
            capture_output=True,
            text=True,
        )
        assert res.returncode == 0

        if res.stderr:
            logger.info("Memory consumption is reported to stderr:")
            logger.info(res.stderr)

        print(res.stdout)
        files = list(sorted(Path(temp_dir).glob("*.pdf")))

        assert len(files) == num_pages_expected

        chars = len(str(len(files)))
        for idx, file in enumerate(files):
            assert str(idx + 1).zfill(chars) + ".pdf" == file.name
            f = Path(file)
            filesize_page = f.stat().st_size
            logger.info("Filesize output", idx=idx, filesize_page=filesize_page)
