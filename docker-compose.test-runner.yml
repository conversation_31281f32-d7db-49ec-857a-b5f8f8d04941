version: '3.8'
# Goal of this docker compose test runner is to run tests locally
# You will need to ensure that PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER and
#  PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM are set to where you installed
# hypodossier-data-dossier and hypodossier-data-system
# which is by default in the parent directory of this project
services:
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.dev
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
      - auth_toml
    networks:
      core-services:
      caddy:
    command: tail -f /dev/null
    volumes:
      - ./:/app
      - poetry-cache:/var/cache/pypoetry
      - ../hypodossier-data-dossier:/app/hypodossier-data-dossier
      - ../hypodossier-data-system:/app/hypodossier-data-system
    environment:
      DOCUMENT_BROWSER_BUILD_PATH: /app/artefact/build
      PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER: /app/hypodossier-data-dossier
      PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM: /app/hypodossier-data-system


volumes:
  poetry-cache:

secrets:
  ENV_CONF_V5:
    file: .env
  auth_toml:
    file: ./auth.toml

networks:
  core-services:
    external: true
  caddy:
    external: true

