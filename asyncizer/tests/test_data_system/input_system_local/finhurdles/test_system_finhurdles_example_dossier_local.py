from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import (
    PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_FINHURDLES,
    PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_FINHURDLES,
)

source_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_FINHURDLES}")
dest_folder_prefix = "input_system_local_finhurdles_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_example_finhurdle_dossier():
    FOLDER_TYPE = "example_dossier_with_finhurdles"
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / FOLDER_TYPE,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["finhurdle_leasing.pdf"],
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={
            #     DocumentCat.TAX_LIST_FINANCIAL_ASSETS: 1
            # },
            # expected_num_docs=1,
            expected_num_extracted_file_exceptions=0
        ),
    ).run()


@pytest.mark.asyncio
async def test_finhurdles_single_page():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=0,
        source_folder=Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_FINHURDLES)
        / "finhurdles_leasing_single_page",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["finhurdle_leasing_single_page.pdf"],
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={
            #     DocumentCat.TAX_LIST_FINANCIAL_ASSETS: 1
            # },
            # expected_num_docs=1,
            expected_num_extracted_file_exceptions=0
        ),
    ).run()
