# Instructions for Testing Dossier Event Consumer

## Setup

1. Start Docker Compose setups in both `du` and `dp`:
   ```
   docker compose up
   ```

# Testing Dossier Event Consumer
1. In core services Caddy, ensure the following is enabled for proxying connection:
   ```
   reverse_proxy /* host.docker.internal:8000
   ```

2. Stop the following workers:
   ```
   docker stop du-dms-1
   docker stop du-dec-1
   docker stop du-diew-1
   docker stop du-worker-1
   ```

3. In `dossier-backend`, open 4 different windows, load poetry shell, and run:
   ```
   python -m uvicorn --lifespan auto projectconfig.asgi:application --host 0.0.0.0 --reload
   python manage.py dossier_event_consumer_v2
   python manage.py worker
   python manage.py image_exporter_worker
   ```

# Testing hyextract workers

1. Stop the following workers:
   ```
   docker compose stop hyextract
   docker compose stop pdf2img
   docker compose stop spacy-classifier
   docker compose stop original_file_processor
   ```

2. In `hyextract`, open 4 different windows, load poetry shell, and run:
   ```
   export PYTHONPATH=$(pwd):$PYTHONPATH
   
   python asyncizer/pdf2jpg_worker.py
   
   python classifier/spacy_worker.py
   
   python asyncizer/file_process_worker.py
   
   python hypodossier/consumer.py
   ```


## Enable Photo Download

1. Navigate to https://dms.hypo.duckdns.org/admin/
2. Go to the account section
3. In "Photo album docx template", set:
   ```
   photo-album-docx-template-default-v01.docx
   ```
   This enables photo download via the UI for dossiers containing documents classified as photos.

## Testing

Navigate to https://service.hypo.duckdns.org/

> Note: Use the FQDN (not localhost) for websockets to work correctly.

1. Upload a document to verify processing
2. Upload a photo to verify processing
3. Check if you can download a dossier zip (requires FQDN https://service.hypo.duckdns.org/)
4. Check if you can download a photo zip (requires documents classified as 615)
```

Is there anything else you'd like me to clarify or expand upon in these instructions?