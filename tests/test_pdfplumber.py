from pathlib import Path

import pdfplumber

from constants import BASE_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.PageLayoutInfo import PageLayoutInfo
from mortgageparser.util.pdf_plumber_util import get_page_layout_info_from_page


def test_page_layout_info():
    ti = PageLayoutInfo(
        titles=["some title", "some top title"], top_titles=["some top title"]
    )

    ti2 = PageLayoutInfo(
        titles=["some title", "some top title"],
        top_titles=["some top title"],
        titles_with_size=[("hoho", 7.77)],
    )

    print(ti)
    print(ti2)

    assert ti
    assert ti2


def test_load_pdfplumber_file():
    path_testfile = Path(
        f"{BASE_DIR}/data_demo/sales_pitch_mix_dir/330 mt vr 10 searchable.pdf"
    )

    with pdfplumber.open(path_testfile) as pdf:
        num_pages = len(pdf.pages)

        assert num_pages == 1


def test_extract_titles():
    path_testfile = Path(
        f"{BASE_DIR}/data_demo/sales_pitch_mix_dir/330 mt vr 10 searchable.pdf"
    )
    with pdfplumber.open(path_testfile) as pdf:
        assert len(pdf.pages) == 1
        plumber_page = pdf.pages[0]

        (
            titles,
            top_titles,
            titles_with_size,
            top_titles_with_size,
        ) = get_page_layout_info_from_page(plumber_page)

        ti = PageLayoutInfo(
            titles=titles,
            top_titles=top_titles,
            titles_with_size=titles_with_size,
            top_titles_with_size=top_titles_with_size,
        )
        assert ti

        ti_without_top = PageLayoutInfo(
            titles=titles, titles_with_size=titles_with_size
        )
        assert ti_without_top

        ti_without_all_titles = PageLayoutInfo()
        assert ti_without_all_titles

        pass


def test_extract_titles_pk():
    path_testfile = Path(
        f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/410_pension_certificate/410_pk_angelica_thiemann.pdf"
    )
    assert path_testfile.exists()
    with pdfplumber.open(path_testfile) as pdf:
        assert len(pdf.pages) == 2
        plumber_page = pdf.pages[0]

        (
            titles,
            top_titles,
            titles_with_size,
            top_titles_with_size,
        ) = get_page_layout_info_from_page(plumber_page)

        ti = PageLayoutInfo(
            titles=titles,
            top_titles=top_titles,
            titles_with_size=titles_with_size,
            top_titles_with_size=top_titles_with_size,
        )

        titles_expected = [
            "Swiss Life Collective BVG Foundation",
            "SwissLife",
            "Personal/confidential",
            "Angelica Thiemann",
            "Segantinistrasse 195",
            "8049 Zürich",
            "Personal certificate  valid as of 06.01.2014",
            "for Ms Angelica Thiemann ",
        ]

        assert ti
        assert ti.titles == titles_expected


def test_extract_titles_sorted():
    titles = [
        "Swiss Life Collective BVG Foundation",
        "SunGard (Switzerland) SA,  Zurich, Genève  SwissLife",
        "15",
        "Personal/confidential",
        "Ms",
        "Angelica Thiemann",
        "Segantinistrasse 195",
        "8049 Zürich",
        "Personal certificate  valid as of 06.01.2014",
        "for Ms Angelica Thiemann ",
    ]

    titles_with_size = [
        ("Swiss Life Collective BVG Foundation", 1.1039566255166384),
        ("SunGard (Switzerland) SA,  Zurich, Genève  SwissLife", 1.3705096541552741),
        ("15", 1.1035875027085777),
        ("Personal/confidential", 1.1039186783120714),
        ("Ms", 1.1033667189729153),
        ("Angelica Thiemann", 1.1039186783120714),
        ("Segantinistrasse 195", 1.1038838177222299),
        ("8049 Zürich", 1.1037420513235414),
        ("Personal certificate  valid as of 06.01.2014", 1.326891852686471),
        ("for Ms Angelica Thiemann ", 1.3258641569645186),
    ]

    top_titles = ["SwissLife"]

    top_titles_with_size = [("SwissLife", 2.3175668732483623)]

    pi = PageLayoutInfo(
        titles=titles,
        top_titles=top_titles,
        titles_with_size=titles_with_size,
        top_titles_with_size=top_titles_with_size,
    )

    assert pi

    # sorted_titles_with_size = pi.get_titles_with_size_by_size()

    sorted_titles = pi.get_titles_by_size()

    assert sorted_titles
