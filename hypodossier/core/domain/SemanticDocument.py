import statistics
from copy import copy
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional

from global_settings import (
    GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS,
    MARK_TITLE_AS_LEGACY,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.Extractions import (
    Extractions,
    extract_formatted_extractions,
)
from hypodossier.core.domain.PageData import PageData
from hypodossier.core.domain.PageDataMapping import get_pagedata_by_doc_cat
from hypodossier.core.domain.SemanticField import SemanticException
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.core.filename.FileCreatorFactory import get_file_creator_from_doc_cat

import structlog

logger = structlog.getLogger(__name__)


@dataclass
class SemanticDocumentStats:
    custom_confidence: float = None

    confidences: List[float] = field(default_factory=list)

    def add_page_confidence(self, prob: float):
        self.confidences.append(prob)

    def min_confidence(self):
        if self.confidences:
            return min(self.confidences)
        return 0

    def avg_confidence(self):
        return statistics.fmean(self.confidences)

    def confidence(self):
        if self.custom_confidence:
            return self.custom_confidence
        else:
            return self.avg_confidence()

    def formatted_confidence(self):
        return f"{int(self.confidence() * 100)}%"

    def formatted_custom_confidence(self):
        if self.custom_confidence:
            return f"{int(self.custom_confidence * 100)}%"
        else:
            return ""

    def formatted_avg_confidence(self):
        return f"{int(self.avg_confidence() * 100)}%"

    def formatted_min_confidence(self):
        return f"{int(self.min_confidence() * 100)}%"

    def formatted_confidence_details(self):
        str_prob = self.formatted_custom_confidence()
        if str_prob:
            str_prob = f"{str_prob} (avg {self.formatted_avg_confidence()})"
        else:
            str_prob = self.formatted_avg_confidence()
        return str_prob


# Just a wrapper for a stack of pages that are in the same original file
@dataclass
class SemanticFile:
    pages: List[SemanticPage] = field(default_factory=list)


# A logical / semantic document which contains pages that belong together
@dataclass
class SemanticDocument:
    pages: List[SemanticPage] = field(default_factory=list)

    # Language that will be used to create deliverables, e.g. filenames and titles of extracted data
    client_lang: str = "de"

    # language of document (use language of first page). This will be extracted from the pages
    lang: str = "de"
    doc_cat: DocumentCat = None
    extractions: Extractions = None

    stats: SemanticDocumentStats = field(default_factory=SemanticDocumentStats)

    # Pretty title of document, e.g. "310 Steuererklärung Manuel Thiemann ZH 2019'
    # or '340 Salärabrechnung Manuel Thiemann 2019-03'
    _title: str = None

    _title_elements: List[str] = None

    # 220422 mt: New attribute to determine the title from document cat + title_suffix
    _title_suffix: Optional[str] = None

    # Filename transformed from title to make it compliant with filesystem
    _filename: str = None

    def initialize(self, override=False):
        if not self._title or override:
            if self.pages and len(self.pages) > 0:
                p = self.pages[0]
                if override or not self.lang:
                    self.lang = p.lang
                if override or not self.doc_cat:
                    self.doc_cat = p.doc_cat

                extractions_for_this_doc_cat: Extractions = Extractions()

                for p in self.pages:
                    if self.extractions:
                        if p.extractions:
                            for key, val in p.extractions.items():
                                if key not in self.extractions:
                                    self.extractions[key] = val
                                if (
                                    key not in extractions_for_this_doc_cat
                                    and self.doc_cat == p.doc_cat
                                ):
                                    extractions_for_this_doc_cat[key] = val
                    else:
                        self.extractions = copy(p.extractions)
                        if self.doc_cat == p.doc_cat:
                            extractions_for_this_doc_cat = copy(p.extractions)
                        else:
                            extractions_for_this_doc_cat = Extractions()

                    confidence = 0
                    if p.match_page_result:
                        if self.doc_cat == p.doc_cat:
                            confidence = p.match_page_result.confidence
                    self.stats.add_page_confidence(confidence)

                fc = get_file_creator_from_doc_cat(self.doc_cat)
                self._title_suffix, self._title_elements = fc.compose_document_title(
                    self.doc_cat,
                    extractions_for_this_doc_cat,
                    self.page_source.filename,
                    self.page_source.filename,
                    self.client_lang,
                    self.pages,
                    add_document_category=False,
                )

                if GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS:
                    self._title, _ = fc.compose_document_title(
                        self.doc_cat,
                        extractions_for_this_doc_cat,
                        self.page_source.filename,
                        self.page_source.filename,
                        self.client_lang,
                        self.pages,
                        add_document_category=True,
                    )
                    self._filename, self._title_elements = fc.compose_document_filename(
                        self.doc_cat,
                        extractions_for_this_doc_cat,
                        self.page_source.filename,
                        self.page_source.filename,
                        self.client_lang,
                        self.pages,
                    )
                    if MARK_TITLE_AS_LEGACY:
                        self._filename = f"LEGACY_{self._filename}"

    @property
    def title(self):
        self.initialize()
        return self._title

    @title.setter
    def title(self, value):
        self._title = value

    @property
    def filename(self):
        self.initialize()
        return self._filename

    @filename.setter
    def filename(self, value):
        self._filename = value

    @property
    def page_source(self):
        # We assume that all pages come from the same page source. Throw an error if that is not the case.
        # So merging pages from different files into one semantic document is not supported.
        ps = None
        idx_ps = -1
        for idx, page in enumerate(self.pages):
            if ps is None:
                ps = page.page_source
                idx_ps = idx
            else:
                if not ps.path == page.page_source.path:
                    raise SemanticException(
                        f"Found different pagesources in semantic document! idx_ps={idx_ps}, idx={idx}, ps={ps}, current page source={page.page_source}"
                    )
        return ps

    def get_page_data(self) -> PageData:
        if self.doc_cat:
            page_data: PageData = get_pagedata_by_doc_cat(self.doc_cat)
            if page_data:
                return page_data.create_from_extractions(self.extractions)

    def get_formatted_extractions(
        self,
        client_lang="de",
        show_empty_fields=True,
        allow_multiline=False,
        empty_value="",
    ) -> Extractions:
        """These extractions are formatted and filtered by the related list of semantic fields. It may contain
        empty fields (depending on the parameter)."""
        extractions = self.extractions
        page_data = self.get_page_data()

        return extract_formatted_extractions(
            extractions,
            page_data,
            client_lang,
            allow_multiline,
            empty_value,
            show_empty_fields,
        )

    def get_page_categories(self):
        ret = []
        for page in self.pages:
            ret.append(page.page_cat.name)
        return ret

    def create_file(self, path_output_dir: Path) -> Path:
        if self.pages:
            fc = get_file_creator_from_doc_cat(self.doc_cat)
            sources = []
            for p in self.pages:
                sources.append(p.page_source)
            return fc.create_file(path_output_dir, self.filename, sources)

    def to_string_line(self):
        return f"Document({self.title}, {len(self.pages)} page(s))"

    def __str__(self):
        ex = {}
        if self.extractions:
            for key, val in self.extractions.items():
                ex[key] = str(val.value)[0:15]
        return f"SemDoc[{self.lang}, {self.doc_cat}, {self.filename}, {self.stats.formatted_confidence_details()} confidence, {self.stats.formatted_min_confidence()} min confidence, {len(self.pages)} pages]"
