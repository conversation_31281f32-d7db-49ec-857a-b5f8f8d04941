S3_HOST=hyminio
S3_HOST_URL=http://hyminio
S3_ACCESS_KEY=S3_ACCESS_KEY
S3_SECRET_KEY=S3_SECRET_KEY
S3_REGION=ch-dk-2
S3_SECURE=true

RABBIT_URL=amqp://admin:admin@hyrabbit:5672/

MODEL_S3_HOST=http://hyminio
MODEL_S3_ACCESS_KEY=S3_ACCESS_KEY
MODEL_S3_SECRET_KEY=S3_SECRET_KEY

S3_CACHE_BUCKET=dp-cache
ORIGINAL_FILE_PROCESSOR_BUCKET=dp-processing

# This must be the path with the '/build' at the end
DOCUMENT_BROWSER_BUILD_PATH=/home/<USER>/code/hypodossier/hyextract/artefact/build

# Abs path to project that contains the sample data for the dossier browser
PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER=/home/<USER>/code/hypodossier/hypodossier-data-dossier

# Abs path to project that contains the internal data for the system tests
PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM=/home/<USER>/code/hypodossier/hypodossier-data-system

ENABLE_DETECTRON2_OBJECT_DETECTION=True

#FRE_LICENSE=SWAD-1201-1007-0006-8103-3364
FRE_LICENSE=SWAR-1201-1007-0105-0766-2529

REPLACE_LARGE_OCR_RESULTS_WITH_ORIGINAL=True

SEARCHABLE_PAGE_CACHE_ENABLED=true

# no spacy models in .env so that they get loaded from global_settings.py

# Open webbrowser with relevant offline dossier after running a DossierTest yes/no
ENABLE_PYTEST_SHOW_WEBBROWSER=True

# Set this to true if logging for the "display_search_results" function should be enabled
DISPLAY_SEARCH_RESULT=False

# Running single page pytest evaluation with storing result in constants.OUTPUT_DOSSIER_PREVIEW
ENABLE_PYTEST_SHOW_PAGE=False

# If true then raise exceptions if parser throws an exception.
# For production this must be set to false (default)
# For development this must be set to true
RAISE_EXCEPTIONS_IN_PARSER=True

# Default for threshold is 0.8, set lower to see more uncertain page objects
# PAGE_OBJECT_MIN_VISIBLE_CONFIDENCE=0.001

ENABLE_IMAGEPREDICT_PROCESSING=True
IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD=0.5
USE_IMAGEPREDICT_DEBUG_PREFIX=False

ENABLE_FINHURDLE_HANDLING=True
FINHURDLE_MIN_VISIBLE_CONFIDENCE_THRESHOLD=0.5
# Make all finhurdles visible
ENABLE_FINHURDLE_VISIBILITY=True

# If this is True the filename property for semantic documents will be computed. This is only necessary for the
# offline version and should not be used for staging / prod
GENERATE_OFFLINE_FILENAME=True

# This flag is used during migration to new concept of document cat + title_suffix
# Should be set to False once the Frontend is capable of handling the new title suffix feature.
GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS=True
MARK_TITLE_AS_LEGACY=False

SENTRY_ENVIRONMENT=dev-${USERNAME}

# Maximum time per individual conversion process after which we give up
# With retries several ones can be chained so total time can be higher
# Ghostscript can take e.g. 50 seconds for 500 pages so we make this longer.
TIMEOUT_PDF_CONVERSION_SECONDS = 75

# Maximum number of tries to call FREP to process a single PDF page
# As long as there is no DLX it makes sense to retry. Afterwards this can be set to 1
MAX_TRIES_FREP_FOR_PDF = 3

# Enable antivirus scanning, Clamav
ENABLE_ANTIVIRUS_CLAMAV=True

# Enable antivirus scanning, Microsoft Defender
ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER=False

HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY=Hyscan.Clamav.Request.RoutingKey

HYSCAN_MICROSOFTDEFENDER_SCAN_REQUEST_ROUTING_KEY=Hyscan.MicrosoftDefender.Request.RoutingKey

# If set to True, the antivirus will raise an exception if a virus is found
ENABLE_ANTIVIRUS_RAISE_EXCEPTION=False

# Timeout for request to hyscan service in seconds
HYSCAN_REQUEST_TIMOUT = 5