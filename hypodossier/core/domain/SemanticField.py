from copy import deepcopy
from dataclasses import dataclass
from enum import Enum
from typing import List, Dict

from abbyyplumber.util.currency_util import format_currency
from hypodossier.util.date_util import format_timestamp
from hypodossier.util.language_detector import ALL_LANGUAGES


class SemanticException(Exception):
    pass


class ExtractionType(Enum):
    STRING = (1,)  # Single line string, default
    PARAGRAPH = (2,)  # Multi-Line string
    ADDRESS_BLOCK = (
        3,
    )  # An address but with variable number of elements (not fully cleaned)
    DATE = (10,)
    INT = (20,)
    CURRENCY = (30,)  # Format "CHF 1'234'567"
    OBJECT = (80,)  # For custom stuff e.g. debt collection information status
    IMAGE = (
        90,
    )  # Graphical/visual page objects like plans, photos, identity documents

    FINHURDLE = 70  # Textual marker for single word/sentence which is formatted in a special way

    def format_value(
        self, value: object, lang="de", allow_multiline=False, empty_value=""
    ):
        if not value:
            return empty_value

        if self == ExtractionType.ADDRESS_BLOCK or self == ExtractionType.PARAGRAPH:
            if allow_multiline:
                return value
            else:
                return value.replace("\n", ", ")
        elif self == ExtractionType.CURRENCY:
            try:
                amount = float(value)

                # Convert all full CHF amounts to int else this will be formatted as 123'456.0
                if amount == round(amount):
                    amount = int(round(amount))

                ret = format_currency(amount)
                return ret
            except Exception:
                return empty_value
        elif self == ExtractionType.DATE:
            try:
                ret = format_timestamp(value, empty_value=empty_value)
                return ret
            except Exception:
                return value
        elif self == ExtractionType.OBJECT:
            try:
                return value.trans(lang)
            except:
                return value
        else:
            return value


@dataclass
class FieldTitles:
    de: str
    en: str = None
    fr: str = None
    it: str = None

    def get_title(self, lang: str):
        title = None
        if lang in self.__dict__:
            title = self.__dict__[lang]
        if not title:
            title = f"[{self.en}]"
        if not title:
            title = f"[{self.de}]"
        return title

    def get_titles_all_languages(self):
        d = self.__dict__.copy()
        if not self.en:
            d["en"] = f'[{d["de"]}]'
        if not self.fr:
            d["fr"] = f'[{d["en"]}]'
        if not self.it:
            d["it"] = f'[{d["en"]}]'
        return d


class SemanticFieldStatus(Enum):
    ACTIVE = (1,)
    INFO_ONLY = (10,)
    DELETED = -1


@dataclass
class SemanticField:
    name: str
    extraction_type: ExtractionType = ExtractionType.STRING
    titles: FieldTitles = None

    # Description of field to explain content (as a specification) in German
    desc_de: str = None

    status: SemanticFieldStatus = SemanticFieldStatus.ACTIVE

    def get_title(self, lang: str):
        if lang not in ALL_LANGUAGES:
            raise Exception(f"Invalid lang: {lang}")
        if self.titles:
            return self.titles.get_title(lang)

    @property
    def sr_inside(self):
        return f"{self.name}_sr_inside"


def fields_as_dict(
    list_sem_fields: List[SemanticField],
) -> Dict[str, List[SemanticField]]:
    d = {}
    for f in list_sem_fields:
        d[f.name] = f
    return d


def merge_dicts(d1: Dict, d2: Dict):
    """
    deepcopy both dicts and merge the result
    keys are ordered so that keys of d1 come first, then keys of d2
    """

    d11 = deepcopy(d1)
    d22 = deepcopy(d2)
    d22.update(d11)
    return d22


FIELD_CANTON_SHORT = SemanticField(
    "canton_short",
    ExtractionType.STRING,
    FieldTitles("Kanton", "Canton"),
    "Kantonskürzel, 2 Grossbuchstaben, z.B. 'ZH'",
)
FIELD_CANTON_LONG = SemanticField(
    "canton_long",
    ExtractionType.STRING,
    FieldTitles("Kanton", "Canton"),
    "Kantonsname als Wort, z.B. 'Zürich'",
)

FIELD_YEAR = SemanticField(
    "year",
    ExtractionType.INT,
    FieldTitles("Jahr", "Year"),
    "Jahreszahl, auf die sich das Dokument bezieht. I.d.R. im Format YYYY, in Einzelfällen YY, z.B. '2021'",
)

FIELD_FIRSTNAME = SemanticField(
    "firstname",
    ExtractionType.STRING,
    FieldTitles("Vorname", "Firstname"),
    "Vorname des Dokumentenempfängers / der primären Person des Dokuments, z.B. 'Max'",
)
FIELD_LASTNAME = SemanticField(
    "lastname",
    ExtractionType.STRING,
    FieldTitles("Nachname", "Lastname"),
    "Nachname des Dokumentenempfängers / der primären Person des Dokuments, z.B. 'Mustermann'",
)
FIELD_FULLNAME = SemanticField(
    "fullname",
    ExtractionType.STRING,
    FieldTitles("Name", "Name"),
    "Vor- und Nachname des Dokumentenempfängers / der primären Person des Dokuments, z.B. 'Max Mustermann'",
)
FIELD_ZIP_CITY = SemanticField(
    "zip_city",
    ExtractionType.STRING,
    FieldTitles("Postleitzahl Ort", "Zip City"),
    "Kombination von PLZ und Ort der Adresse, z.B. '8001 Zürich'",
)
FIELD_STREET = SemanticField(
    "street",
    ExtractionType.STRING,
    FieldTitles("Strasse", "Street"),
    "Strassenname und Hausnummer der Adresse, z.B. 'Bahnhofstrasse 5-7'",
)
FIELD_ZIP = SemanticField(
    "zip",
    ExtractionType.INT,
    FieldTitles("Postleitzahl", "Zip Code"),
    "Postleitzahl der Adresse, z.B. '8001'",
)
FIELD_CITY = SemanticField(
    "city",
    ExtractionType.STRING,
    FieldTitles("Ort", "City"),
    "Ortschaft der Adresse, z.B. 'Zürich'",
)

FIELD_MARITAL_STATUS = SemanticField(
    "marital_status",
    titles=FieldTitles("Zivilstand", "Civil Status"),
    desc_de="ledig/verheiratet/geschieden/getrennt",
)

FIELD_SEX = SemanticField(
    "sex",
    ExtractionType.STRING,
    FieldTitles("Geschlecht", "Sex", "Sexe", "Sesso"),
    "Geschlecht der Person ('m' = 'männlich', 'f' = 'weiblich', z.B. 'm'",
)

FIELD_SALUTATION = SemanticField(
    "salutation",
    ExtractionType.STRING,
    FieldTitles("Anrede", "Salutation", "Salutation", "Saluto"),
    "Anrede der Person - ohne Dr./Prof./... ('Herr' oder 'Frau'), z.B. 'Herr'",
)

# If there is a "private" phone number, use this one. If there is a mobile phone number, use this one. If there is only one phone number use this one
FIELD_PHONE_PRIMARY = SemanticField(
    "phone_primary",
    ExtractionType.STRING,
    FieldTitles("Telefonnummer", "Phone number"),
    "Primäre Telefonnummer, Mobilnummern und private Nummern werden zuerst verwendet, z.B. '079 123 44 55'",
)

# If there is an additional company phone use this
FIELD_PHONE_SECONDARY = SemanticField(
    "phone_secondary",
    ExtractionType.STRING,
    FieldTitles("Telefonnummer (2. Prio)", "Phone number (2nd Priority)"),
    "Sekundäre Telefonnummer, falls zusätzlich zur Primärnummer vorhanden, z.B. '044 777 88 99'",
)

FIELD_EMAIL = SemanticField(
    "email",
    ExtractionType.STRING,
    FieldTitles("E-Mail", "E-Mail"),
    "E-Mail-Adresse der primären Person des Dokuments, z.B. '<EMAIL>'",
)

FIELD_AHV_NEW = SemanticField(
    "ahv_new",
    ExtractionType.STRING,
    FieldTitles("Neue AHV-Nr.", "New AHV No"),
    desc_de="13-stellige AHV, z.b. '756.1111.2222.33'",
)


FIELD_IBAN = SemanticField(
    "iban",
    ExtractionType.STRING,
    FieldTitles("IBAN", "IBAN"),
    desc_de="IBAN adress in format 'CH00 1234 5678 1234 5678 9'",
)

FIELD_ADDRESS_BLOCK = SemanticField(
    "address_block",
    ExtractionType.ADDRESS_BLOCK,
    FieldTitles("Adresse", "Address"),
    desc_de="Adresse aus Namen/Strasse/PLZ Ort, mehrzeilig",
)

# Field to find stuff like '5. September 2020' or '5.9.2020'
FIELD_DOCUMENT_DATE = SemanticField(
    "document_date",
    ExtractionType.DATE,
    FieldTitles("Datum", "Date"),
    "Erstellungs-/Gültigkeitsdatum des Dokuments, z.B. '31.12.2020'",
)

FIELD_DOCUMENT_TITLE = SemanticField(
    "document_title",
    ExtractionType.STRING,
    FieldTitles("Dokumententitel", "Document Title"),
    "Titel / Bezeichnung / Betreff des Dokuments, z.B. 'Vereinbarung betreffend XYZ'",
)


FIELD_DOCUMENT_VALIDITY_START_DATE = SemanticField(
    "document_validity_start_date",
    ExtractionType.DATE,
    FieldTitles("Gültigkeitsdatum Begin", "Validity Date Start"),
    "Beginn der Gültigkeit des Dokuments, z.B. '31.12.2022'",
)

# Date by which the document expires
FIELD_DOCUMENT_VALIDITY_END_DATE = SemanticField(
    "document_validity_end_date",
    ExtractionType.DATE,
    FieldTitles("Gültigkeitsdatum Ende", "Expiry Date"),
    "Ablauf / Ende der Gültigkeit des Dokuments, z.B. '31.12.2028'",
)

FIELD_DOCUMENT_TYPE = SemanticField(
    "document_type",
    ExtractionType.STRING,
    FieldTitles("Dokumenten-Typ", "Document Type"),
    "Bezeichnung der Art des Dokuments, z.B. 'Vermögensausweis', 'Kontoauszug'",
)

# Separate field to find stuff like 'September 2020' (which will be converted as 1.9.2020)
FIELD_DOCUMENT_MONTH = SemanticField(
    "document_month",
    ExtractionType.DATE,
    FieldTitles("Monat", "Month"),
    "Datum des Dokuments, jeweils gerundet auf den 1. des Monats, z.B. für 'Salärabrechnung Sept. 2020' enthält dies '1.9.2021'",
)


# e.g. Ringgenberg BE
FIELD_NATIVE_PLACE = SemanticField(
    "native_place",
    ExtractionType.STRING,
    FieldTitles("Geburtsort", "Native Place"),
    desc_de="Geburtsort der Person, z.B. 'Ringgenberg, BE'",
)

# e.g. Ringgenberg BE
FIELD_DATE_OF_BIRTH = SemanticField(
    "date_of_birth",
    ExtractionType.DATE,
    FieldTitles("Geburtsdatum", "Date of Birth"),
    desc_de="Geburtsdatum, z.B. '31.12.1999'",
)


# 2 letters, e.g. CH
FIELD_NATIONALITY = SemanticField(
    "nationality",
    ExtractionType.STRING,
    FieldTitles("Nationalität", "Nationality", "Nationalité"),
    desc_de="Nationalität / Staatsangehörigkeit, z.B. 'Deutschland'",
)

FIELD_HOMETOWN = SemanticField(
    "hometown",
    ExtractionType.STRING,
    FieldTitles("Heimatort/Geburtsort", "Home Town/City of Birth"),
    "Heimatort, Kantonskürzel (national CH) oder Geburtsort (international), z.B. 'Ringgenberg, BE' oder 'Roma'",
)

FIELD_PAGE_INDEX_CURRENT = SemanticField(
    "page_index_current",
    ExtractionType.INT,
    FieldTitles("Aktuelle Seite", "Current Page"),
    desc_de="Seitenzahl der aktuellen Seite",
    status=SemanticFieldStatus.DELETED,
)
FIELD_PAGE_INDEX_MAX = SemanticField(
    "page_index_max",
    ExtractionType.INT,
    FieldTitles("Anzahl Seiten", "Number of Pages"),
    desc_de="Gesamtzahl der Seiten im Dokument wie angezeigt (kann von der effektiven Anzahl abweichen)",
    status=SemanticFieldStatus.DELETED,
)

FIELD_PERSON_ID = SemanticField(
    "person_id",
    ExtractionType.STRING,
    FieldTitles("ID Person", "ID Person"),
    desc_de="Eindeutige ID der Person, kann AHV-Nr. oder z.B. Kundennummer sein",
)
FIELD_UNIQUE_DOC_ID = SemanticField(
    "unique_doc_id",
    ExtractionType.STRING,
    FieldTitles("Identifikation Dokument", "ID Document"),
    desc_de="Eindeutige ID des vorliegenden Dokuments, die z.B. auf jeder Seite aufgedruckt ist oder Vertragsnummer",
)

FIELD_COMPANY = SemanticField(
    "company",
    ExtractionType.STRING,
    titles=FieldTitles("Firma", "Company"),
    desc_de="Name der Firma, die das Dokument ausgestellt hat, z.B. 'Swiss Life'",
)


# Can be used e.g. for 'type of pension contract product'
FIELD_PRODUCT = SemanticField(
    "product",
    titles=FieldTitles("Produkt", "Product"),
    desc_de="Produktbezeichnung, meist im Zusammenhang mit dem Feld 'Firma', z.B. für einen PK-Ausweis 'Zusatz GL'",
)


# Only AG, e.g. "Stockwerk"
FIELD_PROPERTY_TYPE = SemanticField(
    "property_type",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Liegenschaft Art", "Property Type"),
    desc_de="Art der Liegenschaft: Stockwerk/EFH/...; kanonsspezifisch, aktuell nur AG",
)

# Year in which this property has been built
FIELD_PROPERTY_YEAR = SemanticField(
    "property_year",
    extraction_type=ExtractionType.INT,
    titles=FieldTitles("Liegenschaft Baujahr", "Property Year of Construction"),
    desc_de="Baujahr der Liegenschaft, z.B. '2004'",
)


# # Wohnfläche
# FIELD_PROP_AREA_NET = SemanticField('property_area_living_net', extraction_type=ExtractionType.INT, titles=FieldTitles('Wohnfläche (netto)', 'Living Area (net)'), desc_de="Netto-Wohnfläche der Immobilien (Haus/Wohnung) in m2, z.B. '164'")

FIELD_PROPERTY_AREA_LIVING_GROSS = SemanticField(
    "property_area_living_gross",
    extraction_type=ExtractionType.INT,
    titles=FieldTitles("Wohnfläche (brutto)", "Living area (gross)"),
    desc_de="Wohnfläche der Liegenschaft brutto (Haus/Wohnung) in m2, z.B. '164'",
)
FIELD_PROPERTY_AREA_LIVING_NET = SemanticField(
    "property_area_living_net",
    extraction_type=ExtractionType.INT,
    titles=FieldTitles("Wohnfläche (netto)", "Living area (net)"),
    desc_de="Wohnfläche der Liegenschaft netto (Haus/Wohnung) in m2, z.B. '164'",
)
FIELD_PROPERTY_AREA_LIVING_DEFAULT = SemanticField(
    "property_area_living_default",
    extraction_type=ExtractionType.INT,
    titles=FieldTitles("Wohnfläche", "Living area"),
    desc_de="Wohnfläche der Liegenschaft (Haus/Wohnung) in m2 - kann brutto oder netto sein, z.B. '164'",
)


# Year in which this property has been purchased by the current owner
FIELD_PROPERTY_PURCHASE_YEAR = SemanticField(
    "property_purchase_year",
    extraction_type=ExtractionType.INT,
    titles=FieldTitles("Liegenschaft Kaufjahr", "Property Year of Purchase"),
    desc_de="Jahr in dem die Liegenschaft gekauft wurde, z.B. '2010'",
)

# If there are several values for the Bund and the Kanton this is the bund. If there is only one value then it is this one
# Translations from Credit Suisse
FIELD_PROPERTY_IMPUTED_RENTAL_VALUE = SemanticField(
    "property_imputed_rental_value",
    extraction_type=ExtractionType.CURRENCY,
    titles=FieldTitles(
        "Liegenschaft Eigenmietwert",
        "Imputed Rental Value",
        "Valeur locative",
        "Valore locativo",
    ),
    desc_de="Eigenmietwert der selbstbewohnten Liegenschaft, kantonsspezifisch anzuwenden auf die Direkte Bundessteuer",
)

FIELD_PROPERTY_IMPUTED_RENTAL_VALUE_CANTON = SemanticField(
    "property_imputed_rental_value_canton",
    extraction_type=ExtractionType.CURRENCY,
    titles=FieldTitles(
        "Liegenschaft Eigenmietwert Kanton",
        "Imputed Rental Value Canton",
        "Valeur locative Canton",
    ),
    desc_de="Eigenmietwert der selbstbewohnten Liegenschaft für die kantonale Steuerberechnung; weicht in einigen Kantonen ab von 'Liegenschaft Eigenmietwert'",
)


FIELD_PROPERTY_MAINTENANCE_COST = SemanticField(
    "property_maintenance_cost",
    extraction_type=ExtractionType.CURRENCY,
    titles=FieldTitles(
        "Liegenschaft Unterhaltskosten",
        "Maintenance Cost",
        "Charges et frais d'entretien d'immeuble",
    ),
    desc_de="Unterhaltskosten (pauschal oder effektiv) der Liegenschaft",
)

# FIELD_PROPERTY_MAINTENANCE_COST_STANDARD = SemanticField('property_maintenance_cost_standard', extraction_type=ExtractionType.CURRENCY, titles=FieldTitles('Unterhaltskosten pauschal', 'Maintenance Cost Standard'))
# FIELD_PROPERTY_MAINTENANCE_COST_ACTUAL = SemanticField('property_maintenance_cost_actual', extraction_type=ExtractionType.CURRENCY, titles=FieldTitles('Unterhaltskosten effektiv', 'Maintenance Cost Actual'))
FIELD_DEGREE_EMPLOYMENT = SemanticField(
    "degree_employment",
    ExtractionType.STRING,
    FieldTitles("Beschäftigungsgrad", "Degree of Employment", "Degré d'occupation"),
    "Prozentsatz zu dem der Arbeitnehmer angestellt ist (100% = Vollzeit)",
)
FIELD_EMPLOYER = SemanticField(
    "employer",
    ExtractionType.STRING,
    FieldTitles("Arbeitgeber", "Employer"),
    "Name des Arbeitgebers",
)

FIELD_MRZ = SemanticField(
    "mrz",
    ExtractionType.STRING,
    FieldTitles("Maschinenlesbarer Bereich", "Machine Readable Zone", "MRZ", "MRZ"),
    "Machinenlesbarer Bereich auf ID-Dokumenten wie Pass oder Identitätskarte",
)

CONTRACT_NUMBER = SemanticField(
    "contract_number",
    ExtractionType.STRING,
    FieldTitles("Vertragsnummer", "Contract number"),
    desc_de="Vertragsnummer des Dokuments. Z.B. 'ABC-123456789'",
)
CONTRACT_START_DATE = SemanticField(
    "contract_start_date",
    ExtractionType.DATE,
    FieldTitles("Starttermin", "Start date"),
    desc_de="Datum des Beginns des Vertrags. Z.B. '28.2.2023'",
)
CONTRACT_END_DATE = SemanticField(
    "contract_end_date",
    ExtractionType.DATE,
    FieldTitles("Endtermin", "End date"),
    desc_de="Datum des Endes des Vertrags. Z.B. '28.2.2033'",
)
