from hypodossier.util.firstname.firstname_util import (
    get_all_firstnames,
    get_first_firstname_in_string,
)


def test_firstname_load():
    names = get_all_firstnames(max=33)
    assert 33 == len(names)


def test_find_firstname_middle():
    text = "<PERSON> aasdlfkjh  askdfj sdaflk<PERSON>sh Peter <PERSON> asdöf\nMaria asdfölk asdöfl Peter asödlf <PERSON>"
    assert "<PERSON>" == get_first_firstname_in_string(text)


def test_find_firstname_middle_before():
    # <PERSON> has trailing alpha
    text = "<PERSON> aasdlfkjh  askdfj sdaflkjassh <PERSON> asdöf\nsMaria asdfölk asdöfl <PERSON> asödl<PERSON>"
    assert "<PERSON>" == get_first_firstname_in_string(text)


def test_find_firstname_middle_after():
    # <PERSON> has trailing alpha
    text = "<PERSON> aasdlfkjh  askdfj sdaflkjassh <PERSON> asdöf\nMariaasdfölk asdöfl <PERSON> asödl<PERSON>"
    assert "<PERSON>" == get_first_firstname_in_string(text)


def test_find_firstname_begin():
    text = "<PERSON> aasdlfkjh  askdfj sdaflkjassh <PERSON> asdöf\nMa asdfölk asdöfl <PERSON> asödlf Anna"
    assert "<PERSON>" == get_first_firstname_in_string(text)


def test_find_firstname_begin_2():
    text = "dddter aasdlfkjh  askdfj sdaflkjassh Petder <PERSON> Andre asdöf\nMa asdfölk asdöfl Peddter asödlf Anna"
    assert "Anna" == get_first_firstname_in_string(text)


def test_find_firstname_recursion():
    text = "dddter aasdlfkjh  askdfj sdaflkjassh  Manuel asdöf\nMa asdfölk asdöfl Peddter asödlf Anna Peter 324"
    assert "Peter" == get_first_firstname_in_string(text, recursion_depth=1)
    assert "Anna" == get_first_firstname_in_string(text, recursion_depth=2)
    assert "Manuel" == get_first_firstname_in_string(text, recursion_depth=3)


def test_find_firstname_ignore_case():
    text = "dddter aasdlfkjh  askdfj sdaflkjassh  PETER 324"
    assert "PETER" == get_first_firstname_in_string(text, ignore_case=True)
    assert get_first_firstname_in_string(text, ignore_case=False) is None


def test_find_firstname_ignore_case_renata():
    text = "dddter aasdlfkjh  askdfj sdaflkjassh RENATA 324"
    assert "RENATA" == get_first_firstname_in_string(text, ignore_case=True)
    assert get_first_firstname_in_string(text, ignore_case=False) is None


def test_find_firstname_clea():
    text = "dddter aasdlfkjh Clea askdfj sdaflkjassh \nManuel asdf"
    assert get_first_firstname_in_string(text, ignore_case=True) == "Manuel"


def test_find_firstname_comma_before():
    text = "dddter,Manuel asdf"
    assert get_first_firstname_in_string(text, ignore_case=True) == "Manuel"


def test_find_firstname_comma_after():
    text = "dddter Manuel == asdf"
    assert get_first_firstname_in_string(text, ignore_case=True) == "Manuel"


def test_find_firstname_urs():
    text = "'Herr\nUrs Sample\nBahnhofstrasse 77\n8832 Wollerau SZ\nSchweiz'"
    assert get_first_firstname_in_string(text, ignore_case=True) is None
    assert get_first_firstname_in_string(text, ignore_case=True, min_length=3) == "Urs"
