from dataclasses import replace
import structlog

from abbyyplumber.converter.ValueConverter import (
    NewAhvConverter,
    C<PERSON><PERSON>cyConverter,
    DateConverter,
    ParagraphConverter,
    YearConverter,
    salary_converter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementLabeledField,
    SearchElementArea,
    SearchElementStaticText,
    SearchElementSetChooseFirst,
    SearchElementMultiLabeledField,
    SearchElementReference,
    SearchElementMultiStaticText,
    SearchElementConstrainedArea,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationAbove,
    SearchRelationBelow,
    ReferenceBoundaryVertical,
    SearchRelationRightOf,
    SearchRelationInside,
    SearchRelationLeftOf,
    ReferenceBoundaryHorizontal,
)
from abbyyplumber.plumberstudio.bounding_box_util import create_bounding_box
from abbyyplumber.util.plumberstudio_util import (
    PercentageRang<PERSON>,
    FieldPosition,
    HorizontalAlignment,
)
from hypodossier.core.documents.salarycertificate.SalaryCertificatePageData import (
    FIELD_ACCOUNTING_PERIOD_FROM,
    FIELD_ACCOUNTING_PERIOD_TO,
    FIELD_COMPANY_CONTACT,
    FIELD_SALARY_BASE,
    FIELD_SALARY_NET,
    FIELD_SALARY_GROSS,
    FIELD_SALARY_IRREGULAR_BENEFITS,
    FIELD_SALARY_BOARD,
    FIELD_SALARY_OTHER,
    FIELD_SALARY_BENEFITS_TRAVEL,
    FIELD_SALARY_BENEFITS_CAR,
    FIELD_SALARY_BENEFITS_OTHER,
    FIELD_SALARY_CAPITAL_BENEFITS,
    FIELD_SALARY_OWNERSHIP_RIGHTS,
    FIELD_SALARY_AHV,
    FIELD_SALARY_PILLAR_2_REGULAR,
    FIELD_SALARY_PILLAR_2_PURCHASE,
    FIELD_WITHHOLDING_TAX_DEDUCTION,
    FIELD_SALARY_EXPENSES_ACTUAL_TRAVEL,
    FIELD_SALARY_EXPENSES_ACTUAL_OTHER,
    FIELD_SALARY_EXPENSES_OVERALL_REPRESENTATION,
    FIELD_SALARY_EXPENSES_CAR,
    FIELD_SALARY_EXPENSES_OTHER,
    FIELD_SALARY_CONTRIBUTIONS_EDUCATION,
    FIELD_SALARY_COMMENTS,
    FIELD_SALARY_IRREGULAR_BENEFITS_DESC,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.PageLayoutInfo import PageLayoutInfo
from hypodossier.core.domain.SemanticField import (
    FIELD_AHV_NEW,
    FIELD_YEAR,
    FIELD_DOCUMENT_DATE,
    FIELD_FIRSTNAME,
)
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
)
from mortgageparser.util.search_element_util import (
    create_search_elements_address,
    EXTRACT_DEBUG,
)
from mortgageparser.util.string_utils import (
    contains_all_strings,
    contains_at_least_one_string,
)

DO_RULE_BASED_NAME_EXTRACTION = False

TEXT_LINE_BELOW_BASE = "text_line_below_base"


UPPER_HALF_OF_PAGE = "upper_half_of_page"

COL_LEFT_NUMBERS = "col_left_numbers"

TEXT_LINE_ABOVE_BASE = "text_line_above_base"

TEXT_FORM_11 = "text_form11"

TEXT_FOOTER_LINE = "text_footer_line"


logger = structlog.getLogger(__name__)


class SalaryCertificatePageParser(AbstractPageParser):
    VALUE_COL_RANGE = PercentageRange(16.1 / 19.7, 1)
    LINE_HEIGHT_FACTOR = 1.2

    def __init__(self):
        super().__init__()
        self.supported_languages = ALL_LANGUAGES

    def match_version_one_page_by_titles(
        self, page_layout_info: PageLayoutInfo, text: str
    ) -> bool:
        """
        :param page_layout_info:
        :return: true if the titles for this page match else false. Default is true because no restriction on title
        @param page_layout_info:
        @param text:
        """

        title1 = "Lohnausweis - Certificat de salaire"
        title2 = (
            "Rentenbescheinigung - Attestation de rentes - Attestazione delle rendite"
        )

        if FromStartTextCond(title1, 5).apply(self.page, text) and FromStartTextCond(
            title2, 5
        ).apply(self.page, text):
            return True

        # Check that this title exists only once
        tt = page_layout_info.get_top_titles_by_size(
            2, min_length_title=len(title1) - 5
        )

        title_text = " xxxxxxxxxxxxx ".join(tt)

        if contains_all_strings(title_text, [title1, title2]):
            return True
        return False

    def match_version_one_with_title(self, text: str) -> bool:
        success = self.match_version_one_page_by_titles(
            self.page.page_layout_info, text
        )
        if success:
            success = not contains_at_least_one_string(
                text, ["Zusatzblatt - Annexe"]
            ) and contains_all_strings(
                text, ["Verwaltungsratsentschädigungen", "Beiträge AHV/IV/EO"]
            )
        return success

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = self.match_version_one_with_title(text)
        return success

    def parse_page_header(self):
        success = self.page.set_header_by_text(
            "Jahr - Année", offset_vertical=0.2 / 29, max_l_dist=3
        )
        if not success:
            success = self.page.set_header_by_text(
                "Jahr   Année", offset_vertical=0.2 / 29, max_l_dist=3
            )
        if not success:
            success = self.page.set_header_by_text(
                "Kantinenverpflegung", offset_vertical=0.8 / 29
            )
        if not success:
            success = self.page.set_header_by_text(
                "Lunch-Checks", offset_vertical=0.6 / 29
            )
        if not success:
            success = self.page.set_header_by_text(
                "Neue AHV - Nr. - Nouveau No AVS", offset_vertical=1 / 29
            )
        if not success:
            success = self.page.set_header_by_text(
                "Neue AHV-Nr. - Nouveau No AVS", offset_vertical=1 / 29
            )
        if not success:
            success = self.page.set_header_by_percentage(4 / 29)
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text(
            "Ort und Datum - Lieu et date", offset_vertical=-0.4 / 28, max_l_dist=4
        )
        if not success:
            success = self.page.set_footer_by_text(
                "Bemerkungen",
                offset_vertical=0.4 / 28,
                max_l_dist=4,
                include_pattern=False,
                multiline=False,
            )
            if not success:
                success = self.page.set_footer_by_text(
                    "Osservazioni",
                    offset_vertical=0.2 / 28,
                    max_l_dist=4,
                    include_pattern=False,
                    multiline=False,
                )
                if not success:
                    success = self.page.set_footer_by_percentage(1 / 29)
        return success

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        elements = [
            SearchElementArea(
                UPPER_HALF_OF_PAGE,
                page.fullpage,
                y_range=PercentageRange(0, 0.5),
                extract=EXTRACT_DEBUG,
            ),
            SearchElementMultiStaticText(
                "text_left",
                page.main,
                labels={
                    "Weitere Gehaltsnebenleistungen": 4,
                    "Beiteiligungsrechte gemäss": 8,
                    "Beiträge AHV": 2,
                },
                extract=False,
            ),
            SearchElementArea(
                "col_left_numbers_area",
                page.fullpage,
                extract=EXTRACT_DEBUG,
                relations=[
                    SearchRelationLeftOf("text_left", offset=page.width_chars(-0.6))
                ],
            ),
            SearchElementMultiStaticText(
                COL_LEFT_NUMBERS,
                page.fullpage,
                labels={"10": 0, "12": 0, "15": 0},
                compress_whitespace=True,
                extract=EXTRACT_DEBUG,
                relations=[SearchRelationInside("col_left_numbers_area")],
            ),
            SearchElementStaticText(
                "col_left_fifteen",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="15",
                extract=False,
            ),
            SearchElementStaticText(
                "col_left_fourteen",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="14",
                extract=False,
                relations=[SearchRelationAbove("col_left_fifteen")],
            ),
            SearchElementStaticText(
                "col_left_thirteen",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="13",
                extract=False,
                relations=[SearchRelationAbove("col_left_fourteen")],
            ),
            SearchElementStaticText(
                "col_left_twelve",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="12",
                extract=False,
                relations=[SearchRelationAbove("col_left_thirteen")],
            ),
            SearchElementStaticText(
                "col_left_eleven",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="11",
                extract=False,
                relations=[SearchRelationAbove("col_left_twelve")],
            ),
            SearchElementStaticText(
                "col_left_ten",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="10",
                extract=False,
                relations=[SearchRelationAbove("col_left_eleven")],
            ),
            SearchElementStaticText(
                "col_left_nine",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="9",
                extract=False,
                relations=[SearchRelationAbove("col_left_ten")],
            ),
            SearchElementStaticText(
                "col_left_eight",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="8",
                extract=False,
                relations=[SearchRelationAbove("col_left_nine")],
            ),
            SearchElementStaticText(
                "col_left_six",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="6",
                extract=False,
                relations=[SearchRelationAbove("col_left_eight")],
            ),
            SearchElementStaticText(
                "col_left_four",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="4",
                extract=False,
                relations=[SearchRelationAbove("col_left_six")],
            ),
            SearchElementStaticText(
                "col_left_three",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="3",
                extract=EXTRACT_DEBUG,
                relations=[SearchRelationAbove(COL_LEFT_NUMBERS)],
            ),
            SearchElementStaticText(
                "col_left_two",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="2",
                extract=EXTRACT_DEBUG,
                relations=[
                    SearchRelationAbove(COL_LEFT_NUMBERS),
                    SearchRelationAbove("col_left_three"),
                ],
            ),
            SearchElementStaticText(
                "col_left_one",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="1",
                extract=EXTRACT_DEBUG,
                relations=[
                    SearchRelationAbove(COL_LEFT_NUMBERS),
                    SearchRelationAbove("col_left_two"),
                ],
            ),
            SearchElementStaticText(
                "col_left_h",
                page.fullpage,
                target_name="col_left_numbers_area",
                label="H",
                extract=EXTRACT_DEBUG,
                relations=[
                    SearchRelationAbove(COL_LEFT_NUMBERS),
                    SearchRelationAbove("col_left_one"),
                    SearchRelationAbove("col_left_two"),
                ],
            ),
            SearchElementSetChooseFirst(
                [
                    SearchElementLabeledField(
                        FIELD_AHV_NEW.name,
                        page.header,
                        label="Neue AHV-Nr. - Nouveau No AVS - ",
                        max_l_dist=0.4,
                        field_position=FieldPosition.ABOVE_LABEL,
                        field_alignment_horizontal=HorizontalAlignment.CENTER,
                        field_vertical_line_scale=5,
                        field_width=PercentageRange(0, 5),
                        line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                        converter=NewAhvConverter(),
                    ),
                    SearchElementLabeledField(
                        FIELD_AHV_NEW.name,
                        page.header,
                        label="Rentenbescheinigung",
                        max_l_dist=0.4,
                        field_position=FieldPosition.BELOW_LABEL,
                        field_alignment_horizontal=HorizontalAlignment.CENTER,
                        field_vertical_line_scale=5,
                        field_width=PercentageRange(0, 5),
                        line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                        converter=NewAhvConverter(),
                        optional=True,
                    ),
                ]
            ),
            SearchElementMultiLabeledField(
                FIELD_YEAR.name,
                page.header,
                labels={"Jahr - Année": 5, "Jahr  Année": 5, "Jahr": 1},
                field_position=FieldPosition.ABOVE_LABEL,
                field_vertical_line_scale=4,
                field_width=PercentageRange(0, 2.5 / 19),
                line_height_factor=self.LINE_HEIGHT_FACTOR * 3,
                compress_whitespace=True,
                converter=YearConverter(),
            ).below(FIELD_AHV_NEW.name),
            SearchElementMultiLabeledField(
                FIELD_ACCOUNTING_PERIOD_FROM.name,
                page.header,
                labels={"von - du - dal": 4, "von": 0},
                field_position=FieldPosition.ABOVE_LABEL,
                field_vertical_line_scale=4,
                field_width=PercentageRange(0, 2.2 / 19),
                line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                converter=DateConverter(),
                compress_whitespace=True,
            ).below(FIELD_AHV_NEW.name),
            SearchElementSetChooseFirst(
                change_name=False,
                elements=[
                    SearchElementMultiLabeledField(
                        FIELD_ACCOUNTING_PERIOD_TO.name,
                        page.header,
                        labels={"bis - au - al": 4, "bis": 0},
                        field_position=FieldPosition.ABOVE_LABEL,
                        field_vertical_line_scale=5,
                        field_width=PercentageRange(0, 2.2 / 19),
                        line_height_factor=self.LINE_HEIGHT_FACTOR * 4,
                        converter=DateConverter(),
                    ),
                    SearchElementConstrainedArea(
                        FIELD_ACCOUNTING_PERIOD_TO.name,
                        page.header,
                        text_left="von",
                        text_right="von",
                        text_right_offset_chars=29,
                        converter=DateConverter(),
                    )
                    .rightof(FIELD_ACCOUNTING_PERIOD_FROM.name)
                    .align_vertical_with(FIELD_ACCOUNTING_PERIOD_FROM.name, -20, 20)
                    .below(FIELD_AHV_NEW.name),
                ],
            ),
            SearchElementSetChooseFirst(
                [
                    SearchElementMultiStaticText(
                        TEXT_LINE_BELOW_BASE,
                        page.main,
                        labels={
                            "Verpflegung, Unterkunft - Pension, logement": 6,
                            "Verpflegung, Unterkunft": 6,
                            "Gehaltsnebenleistungen": 6,
                            "Prestations salariales": 6,
                        },
                        extract=False,
                        relations=[SearchRelationInside(UPPER_HALF_OF_PAGE)],
                    ),
                    SearchElementReference(
                        TEXT_LINE_BELOW_BASE, "col_left_two", extract=EXTRACT_DEBUG
                    ),
                ]
            ),
            SearchElementSetChooseFirst(
                [
                    SearchElementMultiStaticText(
                        TEXT_LINE_ABOVE_BASE,
                        page.main,
                        labels={
                            "CHF": 0,
                            "Unicamente importi interi": 7,
                            "Nur ganze Frankenbeträge": 5,
                            "soweit nicht unter Ziffer 2-7 aufzuführen": 7,
                        },
                        extract=False,
                        relations=[
                            SearchRelationAbove(TEXT_LINE_BELOW_BASE),
                            SearchRelationInside(UPPER_HALF_OF_PAGE),
                        ],
                    ),
                    SearchElementReference(TEXT_LINE_ABOVE_BASE, "col_left_one"),
                ]
            ),
            SearchElementSetChooseFirst(
                [
                    SearchElementArea(
                        FIELD_SALARY_BASE.name,
                        page.main,
                        x_range=self.VALUE_COL_RANGE,
                        converter=salary_converter,
                        relations=[
                            SearchRelationBelow(
                                TEXT_LINE_ABOVE_BASE,
                                ref_boundary=ReferenceBoundaryVertical.CENTER,
                            ),
                            SearchRelationAbove(
                                TEXT_LINE_BELOW_BASE,
                                ref_boundary=ReferenceBoundaryVertical.CENTER,
                            ),
                        ],
                    ),
                    SearchElementLabeledField(
                        FIELD_SALARY_BASE.name,
                        page.main,
                        label="Gehaltsnebenleistungen",
                        max_l_dist=6,
                        field_pos_page_horizontal=self.VALUE_COL_RANGE,
                        field_vertical_line_scale=10,
                        field_pos_vertical_line_offset=-1.4,
                        converter=salary_converter,
                        relations=[SearchRelationBelow(TEXT_LINE_ABOVE_BASE)],
                    ),
                    SearchElementLabeledField(
                        FIELD_SALARY_BASE.name,
                        page.main,
                        label="soweit nicht unter Ziffer 2-7 aufzuführen",
                        field_pos_page_horizontal=self.VALUE_COL_RANGE,
                        field_vertical_line_scale=14,
                        field_pos_vertical_line_offset=0,
                        max_l_dist=8,
                        converter=salary_converter,
                        relations=[SearchRelationAbove(TEXT_LINE_BELOW_BASE)],
                    ),
                ]
            ),
        ]

        if DO_RULE_BASED_NAME_EXTRACTION:
            elements.append(
                SearchElementArea(
                    FIELD_FIRSTNAME.sr_inside,
                    page.main,
                    extract=EXTRACT_DEBUG,
                    compress_whitespace=False,
                    relations=[
                        SearchRelationAbove(FIELD_SALARY_BASE.name),
                        SearchRelationAbove(
                            TEXT_LINE_ABOVE_BASE, offset=page.height_lines(-2)
                        ),
                        SearchRelationRightOf("col_left_h"),
                        SearchRelationRightOf("col_left_one"),
                        SearchRelationRightOf("col_left_two"),
                        # SearchRelationRightOf(COL_LEFT_NUMBERS)    # Leave this out because single letters will be filtered later (kick out 'H')
                    ],
                )
            )

            # col_left_h
            elements += create_search_elements_address(
                page.main, page, search_width_multiplier=2, min_length_firstname=3
            )

        elements += [
            SearchElementMultiStaticText(
                "marker_region_values_chf",
                page.fullpage,
                labels={
                    "automobile di servizio": 4,
                    "Conributi per il riscatto": 6,
                    "secondo allegato": 3,
                },
            ),
            SearchElementConstrainedArea(
                "region_values_chf",
                page.fullpage,
                text_right_boundary=ReferenceBoundaryHorizontal.LEFT,
                extract=False,
            )
            .with_all_texts("Rentenbescheinigung -")
            .include_all(40, 73, 80, 180)
            .above("page_footer_anchor")
            .rightof("marker_region_values_chf", 4),
            SearchElementConstrainedArea(
                "region_col_left_numbers_2",
                page.main,
                text_top="2",
                target_name="col_left_numbers_area",
                extract=False,
            )
            .include_top(-1)
            .below("col_left_one"),
            SearchElementConstrainedArea(
                "region_section_left_2",
                page.fullpage,
                x_range=PercentageRange(0, 0.3),
                extract=False,
                relations=[
                    SearchRelationBelow(
                        "col_left_two", ref_boundary=ReferenceBoundaryVertical.TOP
                    ),
                    SearchRelationAbove("col_left_three"),
                    SearchRelationRightOf("col_left_numbers_area"),
                ],
            ),
            SearchElementConstrainedArea(
                "region_section_left_10",
                page.fullpage,
                x_range=PercentageRange(0, 0.4),
                extract=False,
            )
            .below("col_left_nine")
            .above("col_left_eleven")
            .rightof("col_left_numbers_area"),
            SearchElementConstrainedArea(
                "region_section_left_13",
                page.fullpage,
                x_range=PercentageRange(0.1, 0.3),
                extract=False,
            )
            .below("col_left_twelve")
            .above("col_left_fourteen")
            .rightof("col_left_numbers_area"),
            SearchElementConstrainedArea(
                "region_section_left_13_3",
                page.fullpage,
                x_range=PercentageRange(0, 0.1),
                y_range=PercentageRange(0.6, 1),
                extract=False,
            )
            .below("col_left_twelve")
            .above("col_left_fourteen")
            .rightof("col_left_numbers_area"),
            SearchElementLabeledField(
                FIELD_SALARY_BENEFITS_TRAVEL.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_2",
                label="2.1",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_BENEFITS_CAR.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_2",
                label="2.2",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_BENEFITS_OTHER.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_2",
                label="2.3",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementMultiLabeledField(
                FIELD_SALARY_IRREGULAR_BENEFITS.name,
                page.main,
                labels={
                    "Unregelmässige Leistungen - Prestations non périodiques": 6,
                    "Unregelmässige Leistungen": 4,
                },
                field_pos_page_horizontal=self.VALUE_COL_RANGE,
                field_vertical_line_scale=4,
                field_pos_vertical_line_offset=1,
                line_height_factor=self.LINE_HEIGHT_FACTOR,
                converter=CurrencyConverter(min_amount=100),
            ),
            SearchElementArea(
                FIELD_SALARY_IRREGULAR_BENEFITS_DESC.name,
                page.main,
                converter=ParagraphConverter(
                    max_special_char_total=30, max_num_lines_valid=2
                ),
                relations=[
                    SearchRelationBelow(
                        "col_left_three", ref_boundary=ReferenceBoundaryVertical.CENTER
                    ),
                    SearchRelationAbove("col_left_four"),
                    SearchRelationLeftOf("region_values_chf"),
                ],
            ).rightof("region_marker_comments"),
            SearchElementLabeledField(
                FIELD_SALARY_CAPITAL_BENEFITS.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_col_left_numbers_2",
                label="4",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_OWNERSHIP_RIGHTS.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_col_left_numbers_2",
                label="5",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementMultiLabeledField(
                FIELD_SALARY_BOARD.name,
                page.main,
                labels={
                    "Verwaltungsratsentschädigungen - Indemnités des membres de l'administration": 5,
                    "Verwaltungsratsentschädigungen": 5,
                },
                field_pos_page_horizontal=self.VALUE_COL_RANGE,
                field_vertical_line_scale=4,
                line_height_factor=self.LINE_HEIGHT_FACTOR,
                converter=salary_converter,
            ),
            SearchElementLabeledField(
                FIELD_SALARY_OTHER.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_col_left_numbers_2",
                label="7",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementSetChooseFirst(
                [
                    # Sometimes the field height is too small if char height is set too large then try as a fallback with
                    # higher field_vertical_line_scale
                    # Do not do that from scratch as it could lead to multiple lines being captured in normal files
                    SearchElementMultiLabeledField(
                        FIELD_SALARY_GROSS.name,
                        page.main,
                        labels={"Bruttolohn total / Rente": 5, "Bruttolohn total": 3},
                        field_pos_page_horizontal=self.VALUE_COL_RANGE,
                        field_vertical_line_scale=4,
                        line_height_factor=self.LINE_HEIGHT_FACTOR,
                        converter=salary_converter,
                    ),
                    SearchElementMultiLabeledField(
                        FIELD_SALARY_GROSS.name,
                        page.main,
                        labels={"Bruttolohn total / Rente": 5, "Bruttolohn total": 3},
                        field_pos_page_horizontal=self.VALUE_COL_RANGE,
                        field_vertical_line_scale=5,
                        line_height_factor=self.LINE_HEIGHT_FACTOR,
                        converter=salary_converter,
                    ),
                ]
            ),
            SearchElementLabeledField(
                FIELD_SALARY_AHV.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_col_left_numbers_2",
                label="9",
                field_vertical_line_scale=4,
                converter=salary_converter,
            ),
            SearchElementLabeledField(
                FIELD_SALARY_PILLAR_2_REGULAR.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_10",
                label="10.1",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_PILLAR_2_PURCHASE.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_10",
                label="10.2",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementMultiLabeledField(
                FIELD_SALARY_NET.name,
                page.main,
                labels={
                    "Nettolohn / Rente": 4,
                    "Nettolohn": 1,
                    "Salaire net/Rente": 3,
                    "Salario netto /Rendita": 4,
                },
                field_pos_page_horizontal=self.VALUE_COL_RANGE,
                field_vertical_line_scale=5,
                converter=salary_converter,
                relations=[SearchRelationBelow(FIELD_SALARY_GROSS.name)],
            ),
            SearchElementLabeledField(
                FIELD_WITHHOLDING_TAX_DEDUCTION.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_col_left_numbers_2",
                label="12",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_EXPENSES_ACTUAL_TRAVEL.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_13",
                label="13.1.1",
                max_l_dist=0,
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_EXPENSES_ACTUAL_OTHER.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_13",
                label="13.1.2",
                max_l_dist=0,
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_EXPENSES_OVERALL_REPRESENTATION.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_13",
                label="13.2.1",
                max_l_dist=0,
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_EXPENSES_CAR.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_13",
                label="13.2.2",
                max_l_dist=0,
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_EXPENSES_OTHER.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_13",
                label="13.2.3",
                max_l_dist=0,
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_CONTRIBUTIONS_EDUCATION.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_section_left_13_3",
                label="13.3",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementLabeledField(
                FIELD_SALARY_CONTRIBUTIONS_EDUCATION.name,
                page.main,
                target_name="region_values_chf",
                target_name_label="region_col_left_numbers_2",
                label="13.3",
                field_vertical_line_scale=4,
                converter=CurrencyConverter(),
            ),
            SearchElementArea(
                "region_comments",
                page.main,
                relations=[
                    SearchRelationBelow(
                        "col_left_fifteen",
                        ref_boundary=ReferenceBoundaryVertical.TOP,
                        offset=-30,
                    )
                ],
                extract=False,
            ),
            # Must be wider than expected because sometimes there is some message vertically on the left: "bitte keine Mitteilungen anbringen"
            SearchElementArea(
                "region_label_comments",
                page.main,
                x_range=PercentageRange(0, 0.25),
                relations=[
                    SearchRelationBelow(
                        "col_left_fifteen",
                        ref_boundary=ReferenceBoundaryVertical.TOP,
                        offset=-30,
                    )
                ],
                extract=False,
            ),
            # This is the line above (14.) which sometimes is very close to 15.
            SearchElementMultiStaticText(
                "region_marker_comments_above",
                page.main,
                labels={
                    "Altre prestazioni accessorie al salario": 5,
                    "Further fringe benefits": 4,
                },
                target_name="region_label_comments",
                extract=False,
            ),
            SearchElementMultiStaticText(
                "region_marker_comments",
                page.main,
                labels={
                    "Bemerkungen": 3,
                    "Observations": 3,
                    "Osservazioni": 3,
                    "Comments": 3,
                },
                target_name="region_label_comments",
                extract=False,
            ),
            SearchElementArea(
                FIELD_SALARY_COMMENTS.name,
                page.main,
                target_name="region_comments",
                converter=ParagraphConverter(
                    max_special_char_total=15, max_num_lines_valid=3
                ),
                relations=[
                    SearchRelationBelow(
                        "col_left_fifteen",
                        ref_boundary=ReferenceBoundaryVertical.TOP,
                        offset=page.height_lines(-1),
                    )
                ],
            )
            .rightof("region_marker_comments")
            .below_center("region_marker_comments_above"),
            # Use this to optionally place the company box right of it
            # These are ordered by length of the item descending
            SearchElementSetChooseFirst(
                [
                    SearchElementStaticText(
                        TEXT_FOOTER_LINE,
                        page.footer,
                        label="compresi indirizzo e numéro di telefono esatti del datore di lavoro",
                        max_l_dist=6,
                        extract=False,
                    ),
                    SearchElementStaticText(
                        TEXT_FOOTER_LINE,
                        page.footer,
                        label="datore di lavoro",
                        max_l_dist=6,
                        extract=False,
                    ),
                    SearchElementStaticText(
                        TEXT_FOOTER_LINE,
                        page.footer,
                        label="inkl. genauer Anschrift und Telefonnummer des Arbeitgebers",
                        max_l_dist=5,
                        extract=False,
                    ),
                    SearchElementStaticText(
                        TEXT_FOOTER_LINE,
                        page.footer,
                        label="Die Richtigkeit und Vollständigkeit bestätigt",
                        max_l_dist=5,
                        extract=False,
                    ),
                    SearchElementStaticText(
                        TEXT_FOOTER_LINE,
                        page.footer,
                        label="Arbeitgebers",
                        max_l_dist=5,
                        extract=False,
                    ),
                    SearchElementStaticText(
                        TEXT_FOOTER_LINE,
                        page.footer,
                        label="including exact address and telephone number of employer",
                        extract=False,
                    ),
                ]
            ),
            SearchElementStaticText(
                TEXT_FORM_11,
                page.footer,
                label="Fo rm. 11",
                max_l_dist=3,
                extract=False,
            ),
            SearchElementMultiLabeledField(
                FIELD_DOCUMENT_DATE.name,
                page.footer,
                labels={"Ort und Datum - Lieu et date": 5, "Ort und Datum": 4},
                field_position=FieldPosition.BELOW_LABEL,
                line_height_factor=1,
                field_vertical_line_scale=7,
                field_width=PercentageRange(0, 5 / 21),
                converter=DateConverter(),
            ).above(TEXT_FORM_11),
            SearchElementConstrainedArea(
                FIELD_COMPANY_CONTACT.name,
                page.footer,
                texts_left=["des Arbeitgebers"],
                # text_left_offset_chars=1,
                text_left_required=False,
                texts_bottom=["Seite", "Page", "Pagina"],
                text_bottom_offset_lines=-2,
                text_bottom_required=False,
                # offset in rightof() is in pixels, therefore usage not encouraged
                converter=ParagraphConverter(
                    min_char_per_line=4, max_num_spaces_per_line=5, max_num_lines=6
                ),
            ).rightof(TEXT_FOOTER_LINE, 0),
        ]
        return ContentExtractor(elements)

    def parse(self) -> SemanticPage:
        page = self.page

        ce = self.create_content_extractor()
        ce.find(page)

        bbox = create_bounding_box(
            page.footer.bbox, x_range=PercentageRange(15.5 / 23, 1)
        )

        sr = page.get_search_result(TEXT_FOOTER_LINE)
        if sr:
            bbox.left = sr.bbox.right

        sr = page.get_search_result(TEXT_FORM_11)
        if sr:
            bbox.bottom = sr.bbox.top

        if not page.get_search_result("accounting_period_to"):
            sr = page.get_search_result("accounting_period_from")
            if sr:
                bbox = replace(
                    sr.bbox, left=sr.bbox.right, right=sr.bbox.right + sr.bbox.width * 2
                )
                ce.find_search_element(
                    SearchElementArea(
                        "accounting_period_to",
                        page.header,
                        top=bbox.top,
                        left=bbox.left,
                        right=bbox.right,
                        bottom=bbox.bottom,
                    )
                )

        if not page.get_search_result("location_date"):
            sr_mid_col = page.get_search_result("footer_line_de")
            if sr_mid_col:
                se = SearchElementArea("location_date", page.footer)
                se.top = sr_mid_col.bbox.top - sr_mid_col.bbox.height
                se.right = sr_mid_col.bbox.left
                se.bottom = int(sr_mid_col.bbox.bottom + sr_mid_col.bbox.height * 4)
                ce.find_search_element(se)

        return create_semantic_page(
            self, DocumentCat.SALARY_CERTIFICATE, PageCat.SALARY_CERTIFICATE
        )


"""
    def validate_page(self, dp: SemanticPage, do_raise_exception: bool = True):

        meta: SalaryCertificatePageData = dp.meta

        assert_equals(DocumentCat.SALARY_CERTIFICATE, dp.doc_cat, "doc cat", meta)
        assert_equals(PageCat.SALARY_CERTIFICATE, dp.page_cat, "page cat", meta)
        assert_equals(True, meta.ahv_new is not None)
        assert_equals(16, len(meta.ahv_new), "ahv_new missing", meta)
        assert_equals(True, meta.salary_net and int(meta.salary_net) > 0, meta)
        assert_equals(True, meta.valid(), "valid()", meta)
        return True
"""
