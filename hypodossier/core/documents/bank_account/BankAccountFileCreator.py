from typing import List

from hypodossier.core.domain.Extractions import Extractions
from hypodossier.core.domain.SemanticField import FIELD_YEAR, FIELD_COMPANY
from hypodossier.core.filename.FileCreator import FileCreator


class BankAccountFileCreator(FileCreator):
    def get_document_title_elements(
        self,
        elements: List[str],
        extractions: Extractions,
        filename: str,
        lang: str,
        pages: List,
        long_version: bool,
    ):
        elements.append(self.get_best_effort_name(extractions, self.require_firstname))

        if long_version:
            year = extractions.get_value(FIELD_YEAR.name)
            elements.append(year)

            company = extractions.get_value(FIELD_COMPANY.name)
            elements.append(company)

            # if ENABLE_LONG_FILENAMES:
            #     amount = extractions.get_value(BANK_ACCOUNT_TOTAL_AMOUNT.name)
            #     if amount:
            #         elements.append(I18NElement("Saldo", "Value").trans(lang))
            #         elements.append(amount)

        return elements

    def __repr__(self):
        return self.__class__.__name__
