from typing import Tuple

from hypodossier.core.domain.TextContentStats import TextContentStats
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_de import (
    page_configurations_de,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_deen import (
    page_configurations_deen,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_defrit import (
    page_configurations_defrit,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_en import (
    page_configurations_en,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_fr import (
    page_configurations_fr,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_it import (
    page_configurations_it,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_util import (
    PageCfg,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.partner.bekb.classification_parser_map_bekb_de import (
    page_configurations_bekb_de,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.partner.bekb.classification_parser_map_bekb_fr import (
    page_configurations_bekb_fr,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.partner.bekb.classification_parser_map_bekb_it import (
    page_configurations_bekb_it,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.partner.zkb.classification_parser_map_zkb_de import (
    page_configurations_zkb_de,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.partner.zkb.classification_parser_map_zkb_fr import (
    page_configurations_zkb_fr,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.partner.zkb.classification_parser_map_zkb_it import (
    page_configurations_zkb_it,
)

page_configurations = (
    page_configurations_de
    + page_configurations_en
    + page_configurations_fr
    + page_configurations_it
    + page_configurations_deen
    + page_configurations_defrit
    + page_configurations_bekb_de
    + page_configurations_bekb_fr
    + page_configurations_bekb_it
    + page_configurations_zkb_de
    + page_configurations_zkb_fr
    + page_configurations_zkb_it
    + [
        # Min Threshold overall for FR if not defined less strict above
        PageCfg(prefix="FR/", confidence_threshold=0.8)
    ]
    + [
        # Min Threshold overall for IT if not defined less strict above
        PageCfg(prefix="IT/", confidence_threshold=0.8)
    ]
)


def get_page_configuration_by_prefix(classification_name: str) -> PageCfg:
    for cfg in page_configurations:
        if classification_name.startswith(cfg.prefix):
            return cfg
    return PageCfg()


def check_if_page_cfg_applies(
    page_config: PageCfg, confidence: float, text_content_stats: TextContentStats
) -> Tuple[bool, str]:
    # It is not mandatory to have a page_config. But if there is one, its constraints apply
    individual_threshold_ok = confidence >= page_config.confidence_threshold
    num_alpha_page = text_content_stats.num_chars_alpha
    num_lines = text_content_stats.num_lines
    min_num_alpha_ok = num_alpha_page >= page_config.min_chars_alpha_per_page
    min_num_lines_ok = num_lines >= page_config.min_num_lines
    percentage_alpha_ok = (
        page_config.min_percentage_chars_alpha
        <= text_content_stats.percentage_chars_alpha
        <= page_config.max_percentage_chars_alpha
    )
    percentage_digit_ok = (
        page_config.min_percentage_chars_digit
        <= text_content_stats.percentage_chars_digit
        <= page_config.max_percentage_chars_digit
    )
    percentage_special_ok = (
        page_config.min_percentage_chars_special
        <= text_content_stats.percentage_chars_special
        <= page_config.max_percentage_chars_special
    )
    everything_ok = (
        individual_threshold_ok
        and min_num_alpha_ok
        and min_num_lines_ok
        and percentage_alpha_ok
        and percentage_digit_ok
        and percentage_special_ok
    )

    eval_context = f"everything_ok={everything_ok}: individual_threshold_ok={individual_threshold_ok}, num_alpha_page={num_alpha_page}, num_lines={num_lines}, min_num_alpha_ok={min_num_alpha_ok}, min_num_lines_ok={min_num_lines_ok}, percentage_alpha_ok={percentage_alpha_ok}, percentage_digit_ok={percentage_digit_ok}, percentage_special_ok={percentage_special_ok}"

    if everything_ok:
        pass

    return everything_ok, eval_context
