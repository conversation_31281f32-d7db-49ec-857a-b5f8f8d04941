from typing import List

from asyncizer.pageobjectclassifier import (
    PAGE_CLASSIFICATION_CLASSNAME_RENAMING_DICTIONARY,
)
from hypodossier.semantic_page_object_configuration import page_object_configurations
from imagepredict.client.model_mapping import MAP_GRAPHICAL_PAGE_OBJECTS_FOR_EXTRACTION

import structlog

logger = structlog.getLogger(__name__)


VALID_CLASSES_BEFORE_V16 = {
    "identity_ahv": 0,
    "identity_cover_misc": 1,
    "identity_id_ita": 2,
    "identity_id_other": 3,
    "identity_passport_france": 4,
    "identity_passport_germany": 5,
    "identity_passport_italy": 6,
    "identity_passport_other": 7,
    "identity_swiss_foreigner_id_paper_back": 8,
    "identity_swiss_foreigner_id_paper_details": 9,
    "identity_swiss_foreigner_id_paper_front": 10,
    "identity_swiss_foreigner_id_paper_photo_page": 11,
    "identity_swiss_foreigner_id_plastic_back": 12,
    "identity_swiss_foreigner_id_plastic_front": 13,
    "identity_swiss_id_back": 14,
    "identity_swiss_id_front": 15,
    "identity_swiss_passport_first": 16,
    "identity_swiss_residence_permit_back": 17,
    "identity_swiss_residence_permit_front": 18,
    "misc_signature": 19,
    "partner_mb_zek": 20,
    "partner_zkb_gvz_viewer": 21,
    "partner_zkb_rent_calculator": 22,
    "photo_building_exterior": 23,
    "photo_building_interior": 24,
    "photo_building_visualisation": 25,
    "photo_outdoor": 26,
    "plan_aerial_photo": 27,
    "plan_cadaster": 28,
    "plan_floor": 29,
    "plan_side_view": 30,
    "plan_situation": 31,
    "rest_logos": 32,
    "rest_minergy": 33,
    "rest_misc_trash": 34,
    "rest_people_stock_photos": 35,
    "rest_qr_code": 36,
}

# This can be inserted from the json file here
VALID_CLASSES_FROM_V16 = {
    "identity_ahv": 0,
    "identity_cover_misc": 1,
    "identity_id_ch00_back": 2,
    "identity_id_ch00_front": 3,
    "identity_id_ch23_back": 4,
    "identity_id_ch23_front": 5,
    "identity_id_other": 6,
    "identity_passport_ch00": 7,
    "identity_passport_ch23": 8,
    "identity_passport_france": 9,
    "identity_passport_germany": 10,
    "identity_passport_italy": 11,
    "identity_passport_other": 12,
    "identity_swiss_foreigner_id_paper_back": 13,
    "identity_swiss_foreigner_id_paper_details": 14,
    "identity_swiss_foreigner_id_paper_front": 15,
    "identity_swiss_foreigner_id_paper_photo_page": 16,
    "identity_swiss_foreigner_id_plastic_back": 17,
    "identity_swiss_foreigner_id_plastic_front": 18,
    "identity_swiss_residence_permit_back": 19,
    "identity_swiss_residence_permit_front": 20,
    "misc_signature": 21,
    "partner_mb_zek": 22,
    "partner_zkb_gvz_viewer": 23,
    "partner_zkb_rent_calculator": 24,
    "photo_building_exterior": 25,
    "photo_building_interior": 26,
    "photo_building_visualisation": 27,
    "photo_outdoor": 28,
    "plan_aerial_photo": 29,
    "plan_cadaster": 30,
    "plan_floor": 31,
    "plan_side_view": 32,
    "plan_situation": 33,
    "rest_logos": 34,
    "rest_misc_trash": 35,
    "rest_people_stock_photos": 36,
    "rest_qr_code": 37,
}.keys()


def check_classes(classes_to_be_tested, warn_for_additional_configurations: bool):
    configured_keys = page_object_configurations.keys()

    print(classes_to_be_tested)

    print(configured_keys)

    ok = True

    # Test 1: check if all classnames have a configuration
    for classname in classes_to_be_tested:
        if classname not in configured_keys:
            # Now check if a mapping is defined
            if classname in PAGE_CLASSIFICATION_CLASSNAME_RENAMING_DICTIONARY:
                mapped_classname = PAGE_CLASSIFICATION_CLASSNAME_RENAMING_DICTIONARY[
                    classname
                ]
                if mapped_classname not in configured_keys:
                    logger.error(
                        f"Valid class is not configured in page_object_configurations or in mapped classnames: {classname}"
                    )
                    ok = False

    # Test 2: check if there are any legacy configurations around
    if warn_for_additional_configurations:
        for classname in configured_keys:
            if classname not in classes_to_be_tested:
                logger.warning(
                    f"Found page_object_configuration which is no longer present in the model: {classname}"
                )

    if not ok:
        raise Exception("Not all valid classes are configured")


def check_mapping_for_graphical_page_objects(classes_to_be_tested: List[str]):
    ok = True
    for classname in MAP_GRAPHICAL_PAGE_OBJECTS_FOR_EXTRACTION:
        if classname not in classes_to_be_tested:
            ok = False
            logger.error(
                f"Found legacy mapping for graphical object data extraction. Probably the new mapping is missing: {classname}"
            )

    if not ok:
        raise Exception("Problems in the mapping of graphical page objects")


def test_classes_have_configurations_v16(
    classes_to_be_tested: List[str] = VALID_CLASSES_FROM_V16,
    warn_for_additional_configurations=True,
):
    check_classes(
        classes_to_be_tested,
        warn_for_additional_configurations=warn_for_additional_configurations,
    )

    check_mapping_for_graphical_page_objects(classes_to_be_tested)


def test_classes_have_configurations_before_v16(
    classes_to_be_tested: List[str] = VALID_CLASSES_BEFORE_V16,
    warn_for_additional_configurations=False,
):
    check_classes(
        classes_to_be_tested,
        warn_for_additional_configurations=warn_for_additional_configurations,
    )
