from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.document_category_util import get_document_cat_by_id
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map import (
    get_page_configuration_by_prefix,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_util import (
    PageCfg,
)

import structlog

logger = structlog.getLogger(__name__)


def map_classifier_name(classification_name: str):
    # Map a string like 'DE/610/Verkaufsdokumentation' to corresponding DocumentCat, PageCat
    # Currently only doc_cat is assigned, different page_cat might be added later

    # Do not fix legacy classification names here but instead in spacy_worker -> spacy_classification_map

    lang: str = None
    doc_cat: DocumentCat = None
    page_cat: PageCat = None
    doc_path_elements = None
    elements = classification_name.split("/")
    if elements:
        if elements[0] in [
            "DE",
            "DEEN",
            "DEENFRIT",
            "DEFR",
            "DEFRIT",
            "EN",
            "FR",
            "IT",
            "XX",
        ]:
            all_languages = elements[0].lower()
            lang = all_languages[0:2]

            if lang not in ["de", "fr", "en", "it"]:
                lang = None

            if len(elements) >= 2:
                if elements[1] == "100":
                    # 100 is for partner documents which will be mapped to
                    # custom category later
                    # Must be handled here because needed for top file classifcation
                    # Not needed for single page classification because done later in
                    # Sgdparser
                    cfg: PageCfg = get_page_configuration_by_prefix(classification_name)
                    if cfg and cfg.doc_cat:
                        doc_cat = cfg.doc_cat
                        page_cat = cfg.page_cat
                    else:
                        page_cat = PageCat.UNKNOWN_DE
                        doc_cat = DocumentCat.UNKNOWN_DE
                elif len(elements[1]) == 3:
                    doc_cat_id = elements[1]

                    # Try to set document category by second path element (best effort)
                    doc_cat = get_document_cat_by_id(doc_cat_id)

                    if not doc_cat:
                        logger.error(
                            f"Could not find valid doc_cat_id '{elements[1]}' for classification {classification_name}. If this is a legacy doccat, add mapping in spacy_worker -> spacy_classification_map"
                        )
                        page_cat = PageCat.UNKNOWN_DE
                        doc_cat = DocumentCat.UNKNOWN_DE

                elif elements[1] in ["Rest_DE"]:
                    page_cat = PageCat.UNKNOWN_DE
                    doc_cat = DocumentCat.UNKNOWN_DE
            if len(elements) >= 3:
                doc_path_elements = elements
    return lang, doc_cat, page_cat, doc_path_elements
