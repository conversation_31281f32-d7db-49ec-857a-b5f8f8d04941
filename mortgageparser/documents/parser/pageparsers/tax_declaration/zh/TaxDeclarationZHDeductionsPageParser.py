from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementMultiStaticText,
    SearchElementConstrainedArea,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.PageLayoutInfo import PageLayoutInfo
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    create_search_elements_by_tax_code,
    COLUMN_CODE,
    COLUMN_VALUE,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.zh_tax_declaration_fields_map import (
    fields_map_page_03,
)
from mortgageparser.util.string_utils import contains_all_strings
import structlog

"""
Abzüge 2019
Abzüge
Staatssteuer Bundessteuer
CHF ohne Rappen CHF ohne Rappen
11. Berufsauslagen bei unselbständiger Erwerbstätigkeit
7 901 7 901*
11.1 Ehemann / Einzelperson / P1 Berufsauslagen 220
8 850 8 850*
11.2 Ehefrau / P2 Berufsauslagen 240
*
12. Schuldzinsen (soweit nicht schon unter Ziﬀ . 2 abgezogen) Schuldenverzeichnis 250 11 257 11 257
13. Unterhaltsbeiträge und Rentenleistungen
13.1 Unterhaltsbeiträge an den geschiedenen oder getrennt lebenden Ehegatten / Partn. 
 (mit der Steuererklärung 2019 sind alle Belege einzureichen) 254
13.2 Unterhaltsbeiträge für minderjährige Kinder (bis zum Monat der Volljährigkeit) 
 (mit der Steuererklärung 2019 sind alle Belege einzureichen) 255
13.3 Rentenleistungen CHF 2561 113211 abzugsfähig: 40% 256
 
 
14. Beiträge an anerkannte Formen der geb. Selbstvorsorge (3. Säule a)
14.1 Ehemann / Einzelpers./ P1 eﬀ . CHF 262 13213 3150 Bescheinigung 260 3 350 3 350
14.2 Ehefrau / P2 eﬀ . CHF 263 13216 7100 Bescheinigung 261 6 700 6 700
7 800 4 900*
 15. Versicherungsprämien, Zinsen von Sparkapitalien Versich.prämien 270
16. Weitere Abzüge: Bescheinigung
16.1 Beiträge an die AHV, IV und 2. Säule, sofern nicht unter Ziﬀ . 1 und 2 abgezogen 280
16.2 Berufsorientierte Aus - und Weiterbildungskosten Hilfsblatt 292
16.3 Kosten für die Verwaltung des beweglichen Privatvermögens 283
16.4 Behinderungsbedingte Kosten Hilfsblatt 3160
16.5 Weitere Abzüge (z.B. Beiträge an politische Parteien) Aufstellung 284
*
19 248 19 248
16.6 Abzug für fremdbetreute Kinder (Jahrg. 2005 - 2019) max. 10’100 376 
 
 
17. Sonderabzug bei Erwerbstätigkeit beider Ehegatten/Partn. 
5 900 9 379
 Siehe Wegleitung zur Steuererklärung 290
71 006 71 585
18. Total der Abzüge, zu übertragen in Ziﬀ er 20 299
Einkommensberechnung
188 762 188 762
19. Total der Einkünfte Übertrag von Seite 2, Ziﬀ er 7 199
20. Total der Abzüge Über trag von Ziﬀ er 18 299 – 71 006 – 71 585
117 756 117 177
21. Nettoeinkommen 310
22. Zusätzliche Abzüge 
22.1 Krankheits - und Unfallkosten Hilfsblatt 320 – –
22.2 Gemeinnützige Zuwendungen Aufstellung 324 – –
117 756 117 177
23. Reineinkommen (Ziﬀ er 21 abzüglich Ziﬀ ern 22.1 und 22.2) 350
24. Steuerfreie Beträge (Sozialabzüge) Staatssteuer Bundessteuer 
24.1 Abzug für Kinder in Ihrem Haushalt (gemäss Seite 1/9’000 6‘500 370 – 18 000 – 13 000*
 Abzug für Kinder ausserhalb Ihres Haushaltes (gem. S. 1) 9’000 6‘500 372 – –
24.2 Abzug für unterstützte Personen Bestätigung 2‘700 6‘500 374 – – 
2 600
24.3 Abzug für Ehegatten / Partn. — 2‘600 365 –
99 ***********
25. Steuerbares Einkommen gesamt (Ziﬀ er 23 abz. Ziﬀ . 24.1 bis 24.3) 390
26. Vom steuerbaren Einkommen gemäss Ziﬀ er 25 entfallen:
26.1 Auf steuerbare Einkünfte in anderen Kantonen 394 –
26.2 Auf steuerbare Einkünfte im Ausland 396 – –
27. Steuerbares Einkommen im Kanton Zürich bzw. in der Schweiz 398 99 ***********
3
LASTNAME FIRSTNAME und FIRSTNAME, Zürich
756.4078.9585.31 
${zoli=0106192603761} ZHprivateTax Version 2.0.0 / FK - Version 2019 - 7abd4 - 0d321 Seite 01061926037/1 Seite 3 von 13 20.04.2020 17:11 unverbindlicher Einzelblattausdruck
"""


"""
Abzüge
Staatssteuer
CHF ohne Rappen
220 7 901
240 8 850
250 11 ***********
256
260 3 350
261 6 700
270 7 ***********
283
3160
284
376    19 248
290 5 900
299 71 *********** 762
299   –  71 *********** 756
 
320   – 
324   – 
***********
370  –  18 000
372  – 
374  – 
365   
390 99 756
394  –
396  – 
398 99 756
ich
sion 2019-7abd4-0d321
1 unverbin
"""


logger = structlog.getLogger(__name__)


class TaxDeclarationZHDeductionsPageParser(StandardTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_DEDUCTIONS
        # self.required_search_results = ["text_ref_tax_code"]

    def match_page_by_titles(self, page_layout_info: PageLayoutInfo, test: str) -> bool:
        titles = page_layout_info.get_titles_by_size(3, min_length_title=4)
        if len(titles) >= 2:
            if contains_all_strings(
                " ".join(titles), ["Abzüge", "Einkommensberechnung"], hamming_dist=4
            ):
                return True
        return False

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "Abzüge 20",
                "Staatssteuer",
                "Bundessteuer",
                "Steuerbares Einkommen im Kanton Zürich bzw.",
            ],
        )
        return success

    def parse_page_header(self):
        success = self.page.set_header_by_text(
            "Berufsauslagen bei unselb", include_pattern=False
        )
        if not success:
            success = super().parse_page_header()
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text(
            "Steuerbares Einkommen im Kanton Zürich", include_pattern=False
        )
        if not success:
            success = super().parse_page_footer()
        return success

    def create_content_extractor(self) -> ContentExtractor:
        ce = ContentExtractor(
            [
                SearchElementMultiStaticText(
                    "text_ref_tax_code",
                    self.page.main,
                    labels={"Schuldenverzeichnis": 3, "Versich.prämien": 3},
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    COLUMN_CODE,
                    self.page.main,
                    texts_left=["Schuldenverzeichnis", "Versich.prämien"],
                    texts_right=["Schuldenverzeichnis", "Versich.prämien"],
                    extract=False,
                ).include_right(10),
                SearchElementConstrainedArea(
                    COLUMN_VALUE,
                    self.page.main,
                    text_top="CHF ohne Rappen",
                    text_top_required=False,
                    texts_right=["Schuldenverzeichnis", "Versich.prämien"],
                    extract=False,
                )
                .include_right(33)
                .rightof(COLUMN_CODE),
            ]
        )
        ce.search_elements += self.get_search_elements_footer(self.page)

        # field_vertical_line_scale will break the demo dossier
        search_elements = create_search_elements_by_tax_code(
            self.page,
            fields_map=fields_map_page_03,
            field_vertical_line_scale=3,
            column_value=COLUMN_VALUE,
        )
        ce.search_elements += search_elements

        return ce


"""  def parse(self) -> SemanticPage:
        text = self.page.get_text()

        dp = createTaxDeclarationSemanticPage(self.lang, PageCat.TAX_DECLARATION_PAGE_DEDUCTIONS, text, True)

        # Extract middle column with 'Staatssteuer'
        text_main_col = self.page.extract_text_from_column(11 / 21, 5 / 21, y_tolerance=7)
        code_map = map_code_value_text(text_main_col, min_code=199, max_code=398)
        local_fields = map_code_fields_to_text_currency_fields(code_map, fields_map_page_03)

        dp.meta.update(local_fields)

        return dp

"""
