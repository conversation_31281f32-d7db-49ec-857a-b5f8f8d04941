from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "240207_pages_extension"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_bekb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_240207_pages_extension():
    """
    .pages files look like zip but should not be renamed to zip...
    @return:
    """
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "pages_extension",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(expected_num_processing_exceptions=1),
    ).run()


@pytest.mark.asyncio
async def test_240207_frepkiller_jpg():
    """
    took 17 minutes to process on production - but locally it works fast
    @return:
    """
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "frepkiller_jpg",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_240207_frepkiller_pdf():
    """
    kills the frep process when processing the second page
    (page #0 and page #2 work fine, only page #1 is the problem)
    So this job will not return and add a "dangerous" request to the freq queue
    @return:
    """
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "frepkiller_pdf",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_240207_registre_foncier():
    """
    Processing works but bad classification. Added 8 pages to spacy
    @return:
    """
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "registre_foncier",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # These are wrong, improve spacy FR
            # 250318: better but not good enough
            expected_doc_cat_frequency_per_page={
                DocumentCat.CONTRACT_OF_SALE: 2,
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 3,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_doc_cat_frequency={DocumentCat.EXTRACT_FROM_LAND_REGISTER: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_240207_ppsm():
    """
    PPSM is PPTX with macros. Throw proper exception
    @return:
    """
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "ppsm",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(expected_num_processing_exceptions=1),
    ).run()


@pytest.mark.asyncio
async def test_240207_big_img_pdf():
    """
    2 page PDF with 2.3 MB. This works locally but not in prod.

    Solution: convert to jpeg (not PNG, this is 3-4MB per image) with pdftocairo

    @return:
    """
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "big_img_pdf",
        source_file_filter=["processed.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
