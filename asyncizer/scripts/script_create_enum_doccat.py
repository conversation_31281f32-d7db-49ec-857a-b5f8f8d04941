import json
from pathlib import Path
from typing import Optional

from pydantic import BaseModel

from hypodossier.core.domain.DocumentCatElement import DocumentCatalog


class DoccatScriptConfig(BaseModel):
    name: str  # Used in comment
    path: Path  # Take definitions from here
    document_catalog: Optional[DocumentCatalog] = None
    num_doccat: int  # Expected number of document categories


def generate_doccat_text(dsc: DoccatScriptConfig):
    print(dsc.path)
    assert dsc.path.exists()

    f = open(dsc.path)
    doccats_json_list = json.load(f)

    print(f"Found {len(doccats_json_list)} document categories")

    assert len(doccats_json_list) == dsc.num_doccat

    header = f"    # {dsc.name} doccats - generated with script_create_enum_doccat.py - start here #########################################################\n"
    print(header)
    for line in doccats_json_list:
        suffix_misc = ", misc_type=True" if line["misc_type"] == "1" else ""
        suffix_dc = (
            f", document_catalog=DocumentCatalog.{dsc.document_catalog.name}"
            if dsc.document_catalog
            else ""
        )
        s = f'    {line["name"]} = DocumentCatElement("{line["docid"]}", DocumentTopic.{line["topic"]}, "{line["de"]}", "{line["en"]}", "{line["fr"]}", "{line["it"]}", "{line["desc_de"]}"{suffix_misc}{suffix_dc})'
        print(f"{s}\n")


if __name__ == "__main__":
    # Step 1: Get file export in format json and csv (caution: export DEFAULT account only!)
    # - from this URL: https://classifier.swarm-v2.hypodossier.ch/admin/documentcategories/documentcategory/?account__uuid__exact=057db588-0b2e-4833-898f-8bd336913931&q=
    # - into this folder: doc/internal/export_doccat_classifier/default
    # Step 2: Run this script to generate generic DocumentCat entries (with adjusted filename)
    # Step 3: Replace these defintions in the code of DocumentCat.py
    # Step 4: Run script_create_hypodossier_spec_file_plan.py to update the "nice" xls file plan and txt
    # Step 5: Compare the txt fileplan with the previous version to check what has changed

    # p = Path(__file__).parent.parent.parent / 'doc' / 'internal' / 'export_doccat_classifier' / 'DocumentCategory-2023-04-04 all.json'

    # These are 246
    # p = Path(__file__).parent.parent.parent / 'doc' / 'internal' / 'export_doccat_classifier' / 'DocumentCategory-2023-07-14.json'

    # 230904 mt: These are 247 with new AUDIT_REPORT_COMPANY
    # 240514 mt: total 258 now with new FK stuff
    # 240523 mt: rename grundbuch stuff for ZKB
    # 240912 mt: now 261 doc cats with DEATH_CERTIFICATE and MORTGAGE_SUBORDINATION_AGREEMENT

    dsc_default = DoccatScriptConfig(
        name="Default",
        path=Path(__file__).parent.parent.parent
        / "doc"
        / "internal"
        / "export_doccat_classifier"
        / "default"
        / "DocumentCategory-2025-03-10.json",
        num_doccat=264,
    )

    dsc_bekb = DoccatScriptConfig(
        name="BEKB",
        path=Path(__file__).parent.parent.parent
        / "doc"
        / "internal"
        / "export_doccat_classifier"
        / "bekb"
        / "DocumentCategory-2025-03-10bekb.json",
        document_catalog=DocumentCatalog.BEKB,
        num_doccat=64,
    )

    # 12 is the number with 5xx-xx format without legacy zkb doc cats
    dsc_zkb = DoccatScriptConfig(
        name="ZKB",
        path=Path(__file__).parent.parent.parent
        / "doc"
        / "internal"
        / "export_doccat_classifier"
        / "zkb"
        / "DocumentCategory-2024-05-22zkb.json",
        num_doccat=12,
    )

    # Create the text here, replace that section in DocumentCat.py and check in commit if the change is ok.

    generate_doccat_text(dsc_default)

    generate_doccat_text(dsc_bekb)
    # generate_doccat_text(dsc_zkb)
