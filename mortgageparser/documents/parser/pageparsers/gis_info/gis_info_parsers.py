from dataclasses import dataclass

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.util.language_detector import ALL_LANGUAGES
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class GisInfoSearchElements:
    pass


@dataclass
class TemplateGisInfoPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.GIS_INFO
    company: str = None

    se: GisInfoSearchElements = None

    # def update_search_elements(self):
    #     super().update_search_elements_generic(self.se, FIELDS_PENSION_3A.keys())
    #
    #     if self.company:
    #         self.search_elements.append(SearchElementConstant(FIELD_COMPANY.name, self.company))


def get_parsers_gis_info():
    parsers = [
        TemplateGisInfoPageParser(
            supported_languages=ALL_LANGUAGES,  # can have very little text
            desc="ZH GIS-Browser Infoabfrage (maps.zh.ch) ",
            required_text_conditions=[
                FromStartTextCond("GIS-Browser Infoabfrage", num_lines=3)
            ],
            regions=[],
        ),
        TemplateGisInfoPageParser(
            desc="ZH GIS-Browser Infoabfrage (maps.zh.ch)  Katasterplan",
            required_tokens=[
                "GIS-Browser (https://maps.zh.ch)",
                "Amtliche Vermessung schwarz/weiss",
                "Diese Karte stellt einen Zusammenzug von amtlichen",
            ],
            regions=[],
        ),
    ]
    return parsers
