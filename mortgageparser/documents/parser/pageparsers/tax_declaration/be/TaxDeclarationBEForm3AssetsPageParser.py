from abbyyplumber.api import Page
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import SearchElementMultiStaticText
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    ASSETS_PORTFOLIO_ACCOUNTS,
    DEDUCTIONS_WEALTH_MANAGEMENT,
)
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm1PersonalDataPageParser import (
    create_search_element_be_name,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.tax_declaration_be_fields_map import (
    create_search_elements_standard_be,
    create_search_element_zpv,
    col_right,
)
from mortgageparser.util.search_element_util import add_two_fields


class TaxDeclarationBEForm3AssetsPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS
        self.canton_short = "BE"

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = [
            SearchElementMultiStaticText(
                "target_title",
                page.main,
                extract=False,
                labels={
                    "Wertschriftenverzeichnis und Rückerstattungsantrag": 5,
                    "Etat des titres et demande de": 4,
                },
            ),
            create_search_element_be_name(
                page.main, relation_name_bottom="target_title"
            ),
        ]

        elements += add_two_fields(
            page,
            dict_fields={ASSETS_PORTFOLIO_ACCOUNTS.name: col_right},
            labels={
                "Total Vermögen (Kolonne I)": 3,
                "Total de la fortune (colonne I)": 4,
            },
            vertical_line_scale=3.5,
        )

        elements += add_two_fields(
            page,
            dict_fields={DEDUCTIONS_WEALTH_MANAGEMENT.name: PercentageRange(0.6, 1)},
            labels={
                "Nachweisbare Kosten für Wertschriften": 5,
                "Frais prouvés d'administration de titres": 7,
            },
            vertical_line_scale=3.5,
        )

        elements += create_search_elements_standard_be(page)
        elements.append(create_search_element_zpv(page))
        return elements
