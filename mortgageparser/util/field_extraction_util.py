import logging

from abbyyplumber.util.currency_util import clean_number_text

# Return valid new ahv or None
from constants import DEF_SEPARATOR
from mortgageparser.util.abbyy_xml_util import MinMax
from mortgageparser.util.math_util import number_of_digits
from mortgageparser.util.scan_util import improve_scanned_number


def remove_default_separator(text: str, replacement_separator="    "):
    if text:
        return text.replace(DEF_SEPARATOR, replacement_separator)


# Take input lines with format '  101 abcd' and extract a dict(101) -> 'abcd'
# Lines that start with something else will be ignored
# min_key is the minimum code to extract
# max_key is the maximum code to extract
def map_code_value_text(text_main_col, code_range: MinMax, separator=DEF_SEPARATOR):
    dict = {}
    if text_main_col:
        len_code = number_of_digits(code_range.max)
        lines = text_main_col.split("\n")
        for line in lines:
            line2 = line.strip()
            if len(line2) > len_code and separator in line2:
                chunks = line2.split(separator, maxsplit=1)
                code_text = chunks[0].strip()
                code_text = improve_scanned_number(code_text)
                try:
                    code_text = code_text.replace("-", "").replace("+", "").strip()
                    code = int(code_text)
                    if code >= 0:
                        if code_range.contains(code):
                            dict[code] = chunks[1].strip()  # l[len_code:]
                except:
                    continue
        return dict


def map_code_fields_to_text_currency_fields(code_map, fields_map):
    local_fields = {}
    for code, val in code_map.items():
        if code in fields_map:
            desc = fields_map[code]
            local_fields[desc.name] = clean_number_text(val)
    logging.info(f"Found extracted values={local_fields}")
    return local_fields
