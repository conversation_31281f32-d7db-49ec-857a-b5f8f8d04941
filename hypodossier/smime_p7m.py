import subprocess
from pathlib import Path
from typing import <PERSON><PERSON>, Optional

import structlog

logger = structlog.getLogger(__name__)


def decode_smime_email_with_signature(
    p_in: Path, p_out: Path
) -> Tuple[bool, Optional[str]]:
    """
    Take an encoded email (PKCS7) and decode it via openssl. The result can be opened as an .eml file
    on Linux. p_out should have an extension ".eml" for this to work
    Example on command line:
    openssl smime -verify -in "sample.p7m" -noverify -out "sample_out_2.eml"
    :return : Tuple of success code (0 == success) and optional error message
    """

    cmd = f"openssl smime -verify -in {str(p_in)} -noverify -out {str(p_out)}"

    cmd_as_list = cmd.split(" ")

    result = subprocess.run(cmd_as_list, capture_output=True)

    success = result.returncode == 0
    error_msg = None if success else result.stderr
    # Caution: a successful operation will write this to result.stderr (not stdout!):
    # b'Verification successful\n'
    # print(result.returncode, result.stdout, result.stderr)

    # if result.returncode:
    #     raise Exception(f"Could not decode email with signature. p={p} cmd={cmd}")
    #
    return success, error_msg
