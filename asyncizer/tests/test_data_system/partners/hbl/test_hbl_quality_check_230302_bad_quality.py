from pathlib import Path

import pytest
from icecream import ic

from asyncizer.pdf2jpg_with_upscaling import convert_but_not_too_small, UpscalingParams
from asyncizer.tests.util_tests import DossierExpectations, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS

import structlog

logger = structlog.getLogger(__name__)


FOLDER_TYPE = "230302_bad_quality"
path_root_folder = Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS) / "hbl"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_hbl_quality_peyer_original(
    override_existing=True,
    use_ocr_cache=True,
    webbrowser=True,
    show_page=-1,
    show_filename=None,
    client_lang="de",
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=path_root_folder / FOLDER_TYPE,
        source_file_filter=["Susanne_Peyer_Original.pdf"],
        dest_folder_prefix="hbl_quality_check",
        dossier_expectations=DossierExpectations(),
        use_ocr_cache=use_ocr_cache,
        webbrowser=webbrowser,
        show_page=show_page,
        show_filename=show_filename,
        client_lang=client_lang,
    )


@pytest.mark.asyncio
async def test_hbl_quality_schier_original(
    override_existing=True,
    use_ocr_cache=True,
    webbrowser=True,
    show_page=-1,
    show_filename=None,
    client_lang="de",
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=path_root_folder / FOLDER_TYPE,
        source_file_filter=["Serpil_Schier_Original.pdf"],
        dest_folder_prefix="hbl_quality_check",
        dossier_expectations=DossierExpectations(),
        use_ocr_cache=use_ocr_cache,
        webbrowser=webbrowser,
        show_page=show_page,
        show_filename=show_filename,
        client_lang=client_lang,
    )


def test_img_trafo():
    p = path_root_folder / FOLDER_TYPE / "bad_quality_image_trafo"

    p_file = p / "single_page.pdf"
    # p_file = p / 'mt vr 10 searchable.pdf'

    p_out = p / "result"
    p_out.mkdir(exist_ok=True, parents=True)

    assert p.exists()
    assert p_file.exists()

    params = UpscalingParams(
        max_size_ratio_for_upscaling=3,
        max_num_tries=10,
        max_size_for_upscaling=-1,
        max_dpi_for_upscaling=-1,
    )

    logger.info(
        "run convert_but_not_too_small",
        p_file=p_file,
        p_out=p_out,
        dpi=100,
        upscaling_params=params,
    )
    ic(convert_but_not_too_small(p_file, p_out, dpi=100, upscaling_params=params))

    params = UpscalingParams(
        max_size_ratio_for_upscaling=0.5,
        max_num_tries=10,
        max_size_for_upscaling=100000,
    )
    logger.info(
        "run convert_but_not_too_small",
        p_file=p_file,
        p_out=p_out,
        dpi=100,
        upscaling_params=params,
    )
    ic(convert_but_not_too_small(p_file, p_out, dpi=100, upscaling_params=params))
