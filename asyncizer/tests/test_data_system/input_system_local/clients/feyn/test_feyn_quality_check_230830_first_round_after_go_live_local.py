from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "230830_first_round_after_golive"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/feyn/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_feyn_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_donation_cut_off_incorrectly():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "_ok_donation_cut_off_incorrectly",
        # source_file_filter=['single_page.pdf'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # TODO: one should be unknown, one should be Jahresrechnung
                DocumentCat.UNKNOWN_DE: 2  # Must be unknown, must not be DONATION
            },
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_wrong_cutting():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "_ok_wrong_cutting",
        # source_file_filter=['single_page.pdf'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1},
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_wrong_cutting_first_page():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "_ok_wrong_cutting_first_page",
        # source_file_filter=['single_page.pdf'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.SALARY_CERTIFICATE: 2,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_wrong_identity_on_page_3():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "wrong_identity_on_page_3",
        # source_file_filter=['single_page.pdf'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={
            #     # TODO: one passport incorrectly detected with very high confidence. it gets copied to separate page. -> retraining of graphical classification needed.
            #     DocumentCat.UNKNOWN_DE: 2,      # Must be unknown, must not be DONATION
            # },
            expected_num_processing_exceptions=0
        ),
    ).run()


@pytest.mark.asyncio
async def test_grundriss_fr_falsch():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "grundriss_fr_falsch",
        # source_file_filter=['single_page.pdf'],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={
            #     # TODO: one passport incorrectly detected with very high confidence. it gets copied to separate page. -> retraining of graphical classification needed.
            #     DocumentCat.UNKNOWN_DE: 2,      # Must be unknown, must not be DONATION
            # },
            expected_num_processing_exceptions=0
        ),
    ).run()
