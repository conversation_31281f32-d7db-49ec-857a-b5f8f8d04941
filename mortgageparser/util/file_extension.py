import shutil
import subprocess
from pathlib import Path
from typing import Tuple, Optional, List

from pydantic import BaseModel, Extra

import structlog

logger = structlog.getLogger(__name__)

# If the file has any of these extensions we do not try to fix the extension
# E.g. .eml could/will be detected as .html
# these must all be lower case
VALID_UNCHANGED_FILEEXTENSIONS = [
    ".htm",
    ".html",
    ".txt",
    ".msg",
    ".eml",
    ".json",
    ".xlsm",
    # These look like zip files but should not be renamed to zip
    # Processing is not supported so we want them to fail with the unchange
    # extension
    ".pages",
    # These are PPTX with macros so just show proper Error message
    ".ppsm",
]

# Do not apply the filename extension renaming if this would be renamed.
# So a .jpg could be renamed to a .png (if the file really is a png)
# but it will never be renamed to .jpeg
SKIPPED_FILEEXTENSION_CHANGES = {
    ".jpg": ".jpeg",  # No need to rename this
    ".tiff": ".tif",  # No need to rename this
    ".tif": ".tiff",  # No need to rename this
    # Do not convert them as heif can be treated the same as heic
    ".heif": ".heic",
}


# This mapping defines to which mimetype will be mapped to which
# file extension. None means that the file is ignored.
MIMETYPE_MAPPING = {
    "inode/x-empty": None,  # empty file
    # any kind of text file, could also be e.g. CSS
    "text/plain": None,
    # sometimes undetected PDF, could also be AVIF image format
    "application/octet-stream": None,
    "image/bmp": "bmp",
    "image/gif": "gif",
    "image/heic": "heic",
    "image/heif": "heic",  # Can be handled the same as heic
    "image/jpeg": "jpeg",
    "image/png": "png",
    "image/svg+xml": "svg",
    "image/tiff": "tif",
    "image/vnd.microsoft.icon": "ico",
    "application/csv": "csv",
    "application/CDFV2": "db",
    "application/gzip": "gz",
    "application/json": "json",
    "application/msword": "doc",
    "application/pdf": "pdf",
    "application/vnd.ms-excel": "xls",
    "application/vnd.ms-outlook": "msg",
    "application/vnd.oasis.opendocument.spreadsheet": "ods",
    "application/vnd.oasis.opendocument.text": "odt",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/x-7z-compressed": "7z",
    "application/zip": "zip",
    "text/html": "html",
    "text/vcard": "vcf",
}


class FilenameExtensionChangeResult(BaseModel):
    class Config:
        extra = Extra.forbid

    # True if the filename extension needs to be changed
    changed: bool = False

    # This is true if the true extension could be determined by the
    # linux file command with high degree of confidence
    identified: bool = False

    # Extension of the input file
    actual_extension: Optional[str] = None

    # Extension that has been identified by checking the file content
    true_extension: Optional[str] = None

    # New filename with corrected extension
    corrected_filename: Optional[str] = None

    # Corrected extension (e.g. '.jpeg')
    corrected_extension: Optional[str] = None


def optionally_fix_fileextension(
    p_file: Path, allowed_target_extensions=Optional[List[str]]
) -> FilenameExtensionChangeResult:
    """

    @param p_file:
    @param allowed_target_extensions: If set the potential target extensions of
    the renaming are limited to this list which is in format ['.jpeg', '.pdf', ...]
    @return:
    """
    ret = is_wrong_processable_file_extension(
        str(p_file), allowed_target_extensions=allowed_target_extensions
    )
    if ret.changed:
        # rename file in same dir to the correct extension
        p_corrected = Path(ret.corrected_filename)
        shutil.move(p_file, p_corrected)

    return ret


def apply_filename_extension_change(
    actual_extension: str,
    true_extension: str,
    allowed_target_extensions: Optional[List[str]],
) -> bool:
    """

    @param allowed_target_extensions:
    @param actual_extension:
    @param true_extension:
    @return: True if extension should be changed, False otherwise
    """
    never_change_extension = actual_extension in VALID_UNCHANGED_FILEEXTENSIONS
    if never_change_extension:
        # These extensions must never be changed because this could be wrong
        return False

    if allowed_target_extensions:
        allow_change = true_extension in allowed_target_extensions
        if not allow_change:
            # This true extension is not in the list of allowed extensions
            # so we do not change it
            return False

    if actual_extension.lower() in SKIPPED_FILEEXTENSION_CHANGES:
        if (
            SKIPPED_FILEEXTENSION_CHANGES[actual_extension.lower()]
            == true_extension.lower()
        ):
            # These mappings should not be changed (e.g. .jpg -> jpeg)
            return False

    if true_extension == actual_extension:
        # No need to change anything
        return False

    return True


def is_wrong_processable_file_extension(
    filename: str, fixsuffix="_fixext", allowed_target_extensions=None
) -> FilenameExtensionChangeResult:
    """

    @param allowed_target_extensions:
    @param fixsuffix:
    @param filename:
    @return: True if this is processable with a different file extension. Then the str contains the corrected filename (true extension added)
        False if nothing needs to be or can be done
    """

    if allowed_target_extensions is None:
        allowed_target_extensions = []
    actual_extension = Path(filename).suffix.lower()

    true_extension = get_file_extension_by_mimetype(filename)

    # Possible that correct file is not detected, in this case true_extension is None
    # and we do nothing
    if true_extension:
        if apply_filename_extension_change(
            actual_extension, true_extension, allowed_target_extensions
        ):
            # This file has the wrong extension and if it had the correct extension we could actually process it
            # So change the extension by adding the correct extension
            corrected_filename = filename + fixsuffix + true_extension
            return FilenameExtensionChangeResult(
                changed=True,
                identified=true_extension is not None,
                actual_extension=actual_extension,
                true_extension=true_extension,
                corrected_filename=corrected_filename,
                corrected_extension=true_extension,
            )
        else:
            # Do nothing
            return FilenameExtensionChangeResult(
                identified=true_extension is not None,
                actual_extension=actual_extension,
                true_extension=true_extension,
            )

    # If we do not explicitely know that we can process this better do nothing
    return FilenameExtensionChangeResult(
        identified=true_extension is not None,
        actual_extension=actual_extension,
        true_extension=true_extension,
    )


def get_file_extension_by_mimetype(filename: str, add_dot_prefix: bool = True) -> str:
    """
    Try to figure out the mime type of the file and then map that to the correct
    extension. Sometimes the mimetype extracted is meaningless or empty. In that
    case try to find the extension directly. For some reason the results between
    the calls differ quite a bit.
    @param filename: Path to file that should be analysed
    @param add_dot_prefix: If True return '.txt' instead of 'txt'
    @return: "True" exension of file
    """

    # Run the `file` command and capture its output
    output = subprocess.check_output(["file", "-b", "--mime-type", filename])

    # Extract the file extension from the output
    mimetype = output.decode("utf-8").strip().split()[-1]

    if mimetype in MIMETYPE_MAPPING:
        extension = MIMETYPE_MAPPING[mimetype]
    else:
        extension = get_file_extension(filename)
        logger.warning(
            "unknown mime type",
            mimetype=mimetype,
            filename=filename,
            extension=extension,
        )
        extension = get_file_extension(filename, add_dot_prefix=False)

    # if "/" in extension:
    #     extension = get_substring_before(extension, "/")
    #
    # if extension == '???':
    #     extension = None

    if extension and add_dot_prefix:
        extension = "." + extension
    return extension


def get_file_extension(filename: str, add_dot_prefix: bool = True) -> str:
    # Run the `file` command and capture its output
    output = subprocess.check_output(["file", "-b", "--extension", filename])

    # Extract the file extension from the output
    extension = output.decode("utf-8").strip().split()[-1]

    if "/" in extension:
        extension = get_substring_before(extension, "/")

    if extension == "???":
        extension = None

    if extension and add_dot_prefix:
        extension = "." + extension
    return extension


def get_substring_before(s, sub):
    if not s:
        return None
    index = s.find(sub)
    if index != -1:
        return s[:index]
    else:
        return s


def optionally_shorten_filename(
    filename: str, max_length: int = 180, suffix: str = "_cutfilename"
) -> Tuple[str, str, bool]:
    """
    Shorten the filename if longer than max_length. Optionally add suffix.
    @return: Tuple[new_filename, old_filename_if_it_was_shortened, true_if_changed]
    """
    if len(filename) > max_length:
        p = Path(filename)

        # Make the stem of the filename at least 5 chars long + suffix
        length_to_be_used = max(max_length - len(suffix) - len(p.suffix), 5)
        new_filename = p.stem[0:length_to_be_used] + suffix + p.suffix
        return new_filename, filename, True
    return filename, None, False
