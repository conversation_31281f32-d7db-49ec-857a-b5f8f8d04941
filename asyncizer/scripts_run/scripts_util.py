import shutil
import sys
from itertools import islice
from pathlib import Path

import structlog
from icecream import ic, datetime
from natsort import natsort

import global_settings
from asyncizer.dossier_processor import process_dossier
from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.rpc_pika import get_rpc_client
from asyncizer.s3 import upload_file, remove_prefix, download_prefix
from mortgageparser.util.file_extension import optionally_shorten_filename

logger = structlog.getLogger(__name__)


async def process_folder_of_dossier_folders(
    root_folder: Path,
    dest_root_folder: Path,
    min_index: int = 0,
    max_num_folders=sys.maxsize,
    source_file_filter=[],
    use_ocr_cache=True,
    client_lang="de",
):
    ic.includeContext = True
    start = datetime.now()
    ic(root_folder)

    assert root_folder.exists() and root_folder.is_dir()

    # setup rpc client, so there is just one instance
    await get_rpc_client()

    folder_list = [f for f in root_folder.glob("*/")]
    # folder_list = folder_list[1:]  # first folder is the root folder

    print(f"Found {len(folder_list)} folders in total")
    folder_list = list(islice(folder_list, min_index, min_index + max_num_folders))
    print(f"Found {len(folder_list)} folders to process...")

    for idx, folder in enumerate(folder_list):
        assert folder.is_dir(), f"Found non-folder: {folder}"

        dest_folder = dest_root_folder / folder.name

        await process_single_dossier_folder(
            folder,
            dest_folder,
            source_file_filter,
            use_ocr_cache=use_ocr_cache,
            client_lang=client_lang,
        )

        print(f"Handled {idx + 1}/{len(folder_list)} folders: {folder}")

    end = datetime.now()
    ic(end - start)
    print(f"All {len(folder_list)} dossiers handled.")


async def process_single_dossier_folder(
    source_folder: Path,
    dest_folder: Path,
    source_file_filter=[],
    bucket="dossier",
    use_ocr_cache=True,
    client_lang: str = "de",
    processing_config: OriginalFileProcessingConfig = OriginalFileProcessingConfig(),
):
    start = datetime.now()
    dossier_name = f"{source_folder.name}"
    assert source_folder.exists(), f'Folder "{source_folder}" does not exists.'

    # Handle all files but ignore subdirectories
    dossier_with_dirs = natsort.os_sorted(list(source_folder.rglob("*")))
    dossier = [file for file in dossier_with_dirs if file.is_file()]

    print(
        f"Now handle source folder {source_folder} with {len(dossier)} files. source_file_filter={source_file_filter}..."
    )

    file_filter = None  # f'{dossier_name}.zip'

    ic(dossier_name)
    await remove_prefix(bucket, f"{dossier_name}/")
    for file in dossier:
        if file_filter:
            if not str(file).endswith(file_filter):
                continue
        # dossier_name = f"{file.name}"

        filename, oldfilename, changed = optionally_shorten_filename(
            file.name,
            global_settings.MAX_FILENAME_LENGTH_FOR_PROCESSING,
            global_settings.SUFFIX_FOR_SHORTENED_FILENAME,
        )
        if changed:
            logger.warning(
                "Filename of originalfile was longer than allowed and therefore shorted.",
                filename=filename,
                oldfilename=oldfilename,
            )

        await upload_file(bucket, f"{dossier_name}/original_files/{filename}", file)
    await process_dossier(
        bucket,
        dossier_name,
        source_file_filter=source_file_filter,
        use_ocr_cache=use_ocr_cache,
        client_lang=client_lang,
        processing_config=processing_config,
    )
    shutil.rmtree(dest_folder / dossier_name, ignore_errors=True)
    dest_folder.mkdir(parents=True, exist_ok=True)
    # dossier_name is always a folder name. Trailing slash is necessary because other dossiers could have this dossier name as a prefix
    await download_prefix(bucket, f"{dossier_name}/", dest_folder)

    end = datetime.now()
    ic(end - start)
