from dataclasses import dataclass

from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.util.language_detector import ALL_LANGUAGES

from abbyyplumber.converter.ValueConverter import (
    StringSelectorConverter,
    CleanNameConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    create_labeled_field,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
    TemplateGenericLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
    FromBottomTextCond,
)


@dataclass
class TemplateAuthorizationInquiriesPageParser(TemplateGenericLetterPageParser):
    page_cat: PageCat = PageCat.GENERIC_SINGLE_PAGE
    doc_cat: DocumentCat = DocumentCat.AUTHORIZATION_FOR_INQUIRIES


def get_parsers_authorization_inquiries():
    parsers_de = [
        TemplateAuthorizationInquiriesPageParser(
            desc="BEKB FiPla DE 2025",
            # supported_languages=ALL_LANGUAGES,
            page_cat=PageCat.GENERIC_SINGLE_PAGE,
            ranked_titles_all=[
                RankedTitle("Auskunftsvollmacht für"),
                RankedTitle("Pensionskasse/AHV/Versicherungen/Banken"),
            ],
            required_text_conditions=[
                FromStartTextCond(
                    "Berner Kantonalbank AG, Finanzberatung", num_lines=10
                )
            ],
            required_tokens_any=[["bevollmächtigt hiermit die"]],
            company="BEKB",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="BEKB FiPla FR 2025",
            supported_languages=["fr"],
            page_cat=PageCat.GENERIC_SINGLE_PAGE,
            ranked_titles_all=[
                RankedTitle("Procuration pour l'échange"),
                RankedTitle("les assurances / les banques"),
            ],
            required_text_conditions=[
                FromStartTextCond(
                    "Banque Cantonale Bernoise SA, Conseil financier", num_lines=10
                )
            ],
            required_tokens_any=[["Le/la mandataire"]],
            company="BEKB",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="feyn first page 2023",
            supported_languages=ALL_LANGUAGES,
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            ranked_titles_all=[RankedTitle("feyn")],
            ranked_titles_any=[
                RankedTitle("Auskunftsermächtigung"),
                RankedTitle("Authorization to provide"),
                RankedTitle("Autorisation de divulgation"),
                RankedTitle("Autorizzazione all'informa"),
            ],
            required_text_conditions=[FromStartTextCond("feyn", num_lines=3)],
            required_tokens_any=[
                [
                    "schliesst hiermit mit der feyn AG",
                    "hereby concludes a broker agreement with feyn",
                    "Le client susnommé conclut par la présente un accord de courtage",
                    "Il cliente summenzionato stabilisce von",
                ]
            ],
            company="feyn",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="feyn last page 2023",
            supported_languages=ALL_LANGUAGES,
            page_cat=PageCat.GENERIC_LAST_PAGE,
            ranked_titles_all=[RankedTitle("feyn", rank=1)],
            required_text_conditions=[FromStartTextCond("feyn", num_lines=2)],
            required_tokens_any=[
                [
                    "Auskunftsermächtigung",
                    "Authorization to provide",
                    "Autorisation de divulgation",
                    "Autorizzazione all'informa",
                ],
                [
                    "dass die Vermögenswerte, die einerseits",
                    "confirms that the assets used on the one hand as their",
                    "Le requérant confirme que les actifs utilisés",
                    "richiedente conferma che i valori patrimoniali, che da",
                ],
            ],
            company="feyn",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="Moneypark Autorisierung DE #1a/1 July 2021",
            ranked_titles_all=[
                RankedTitle("Ermächtigung für das Einholen von Auskünften"),
                RankedTitle("Akteneinsichtsrecht"),
            ],
            required_tokens=[
                "MoneyPark AG",
                "Helvetia Versicherungen",
                "Der Vollmachtgeber",
            ],
            company="MoneyPark",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="Moneypark Autorisierung DE #1b/1 May 2021",
            ranked_titles_all=[
                RankedTitle("Ermächtigung für das Einholen von Auskünften")
            ],
            required_tokens=[
                "MoneyPark AG",
                "Cresura AG",
                "Der Vollmachtgeber",
            ],  # Isch nix Helvetia hier
            company="MoneyPark",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="VZ page 1 2024 DE/FR",
            supported_languages=["de", "fr"],
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            ranked_titles_any=[
                RankedTitle("Auskunftsvollmacht und Auftrag"),
                RankedTitle("Mandat et procuration"),
            ],
            required_text_conditions=[
                # must be 15 lines for FR, for DE 10 is enough
                FromBottomTextCond(
                    ["ermächtigt der Kunde das HZ", "Le mandant autorise HZ"],
                    num_lines=15,
                )
            ],
            required_tokens_any=[
                ["den Schwestergesellschaften des HZ", "une société du Groupe VZ"]
            ],
            # Extraction in FR does not work because different layout
            regions=[
                SearchElementConstrainedArea(
                    "region_lastname_p1",
                    None,
                    text_top="Anrede:",
                    text_right="Geburtsdatum",
                    text_bottom="PLZ:",
                ),
                SearchElementConstrainedArea(
                    "region_firstname_p1",
                    None,
                    text_top="Datum:",  # text_bottom="Vorname:",
                    x_range=PercentageRange(0, 0.5),
                )
                .align_vertical_with("region_lastname_p1")
                .rightof("region_lastname_p1"),
            ],
            se=GenericLetterSearchElements(
                firstname=create_labeled_field("Vorname:", "region_firstname_p1"),
                lastname=create_labeled_field("Name:", "region_lastname_p1"),
            ),
            company="VZ",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="VZ page 2 2024 DE",  # supported_languages=ALL_LANGUAGES,
            page_cat=PageCat.GENERIC_LAST_PAGE,
            required_text_conditions=[
                FromStartTextCond(
                    "Allgemeinen Geschäftsbedingungen des VZ", num_lines=10
                ),
                FromBottomTextCond("Seite 2 von 2", num_lines=3),
                FromBottomTextCond("Doc ID HZAH", num_lines=3),
            ],
            regions=[
                SearchElementConstrainedArea(
                    "region_lastname_p1",
                    None,
                    text_top="Datum:",
                    text_bottom="Vorname:",
                    x_range=PercentageRange(0, 0.5),
                ),
                SearchElementConstrainedArea(
                    "region_firstname_p1",
                    None,
                    text_top="Datum:",
                    x_range=PercentageRange(0, 0.5),
                ),
            ],
            se=GenericLetterSearchElements(
                firstname=create_labeled_field("Vorname:", "region_firstname_p1"),
                lastname=create_labeled_field("Name:", "region_lastname_p1"),
            ),
            company="VZ",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="ZKB Autorisierung DE 2021",
            ranked_titles_all=[
                RankedTitle("Zustimmung zur Einholung"),
                RankedTitle("Einholung von Informationen"),
                RankedTitle("im Rahmen der Kreditprüfung"),
            ],
            required_tokens=["Zürcher", "Angaben zum Kreditnehmer"],
            company="ZKB",
            regions=[
                SearchElementConstrainedArea(
                    "region_first",
                    None,
                    text_top="Angaben zum Kreditnehmer 1",
                    text_right="Angaben zum Kreditnehmer 1",
                    text_bottom="Bürgerort",
                )
                .include_right(5)
                .include_vertical(),
                # SearchElementConstrainedArea(FIELD_FIRSTNAME.sr_inside, None, target_name="region_first", text_top="Angaben zum Kreditnehmer",
                #                              text_left="Angaben zum Kreditnehmer 1",
                #                              text_bottom="PLZ, Ort").include_all(1, -2, 5, 1)
            ],
            se=GenericLetterSearchElements(
                fullname=create_labeled_field("Name, Vorname / Firma", "region_first"),
                street=create_labeled_field("Adresse", "region_first"),
                date_of_birth=create_labeled_field("Geburtsdatum", "region_first"),
                zip=create_labeled_field(["PLZ, Ort"], "region_first").with_converter(
                    StringSelectorConverter(suffix=", ")
                ),
                city=create_labeled_field(["PLZ, Ort"], "region_first").with_converter(
                    StringSelectorConverter(prefix=", ")
                ),
            ),
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="ZKB Autorisierung Finanzplanung DE 2021",
            ranked_titles_all=[RankedTitle("Auskunftsvollmacht", 4, 7)],
            required_tokens=[
                "Vollmachtgeber",
                "mit der Erarbeitung der Finanzplanung zusammenhängende",
                "Zürcher Kantonalbank",
            ],
            company="ZKB",
            regions=[
                SearchElementConstrainedArea(
                    "region_adr",
                    None,
                    texts_top=["Vollmachtgeber", "Unterzeichnende"],
                    text_bottom="geboren",
                )
            ],
            se=GenericLetterSearchElements(
                fullname=SearchElementConstrainedArea(
                    None,
                    None,
                    target_name="region_adr",
                    converter=CleanNameConverter(max_num_lines_valid=1),
                )
            ),
        ),
    ]

    parsers_en = [
        TemplateAuthorizationInquiriesPageParser(
            desc="Credit Suisse Autorisierung EN #1/2 Nov 2021",
            supported_languages=["en"],
            ranked_titles_all=[RankedTitle("Authorization to Obtain Information")],
            required_tokens=[
                "Credit Suisse",
                "In connection with the mortgage",
                "The Principal may revoke this authorization",
                "Credit Information (ZEK)",
            ],
            company="Credit Suisse",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="Credit Suisse Autorisierung EN #2/2 Nov 2021",
            supported_languages=["en"],
            required_tokens=[
                "Client No. (CIF)",
                "Last name, First name / Company of the Principal",
                "To be completed by the Bank",
            ],
            max_num_chars=500,
            company="Credit Suisse",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="Moneypark Autorisierung EN #1/1 Nov 2021",
            supported_languages=["en"],
            ranked_titles_all=[
                RankedTitle("Authorization to obtain information", 5, 3)
            ],
            required_tokens=[
                "MoneyPark AG",
                "The Principal explicitly releases MoneyPark AG",
                "to obtain information about",
            ],
            company="MoneyPark",
        ),
        TemplateAuthorizationInquiriesPageParser(
            desc="Swiss Life Autorisierung Zusatz Anfrage EN,FR,IT #1/1 2022",
            supported_languages=["en", "fr", "it"],
            ranked_titles_any=[
                RankedTitle("Supplement to the mortgage application Swiss Life"),
                RankedTitle("Complément en matière de demande de prêt hypothécaire"),
                RankedTitle(
                    "Aggiunta alla domanda per un prestito ipotecario Swiss Life"
                ),
            ],
            required_text_conditions=[
                FromStartTextCond(["Swiss Life Ltd", "Swiss Life SA"], num_lines=1),
                FromStartTextCond(
                    [
                        "Permission to collect information",
                        "autorise Swiss Life",
                        "autorizza Swiss Life a procurarsi",
                    ],
                    num_lines=20,
                ),
            ],
            company="Swiss Life",
        ),
    ]

    return parsers_de + parsers_en
