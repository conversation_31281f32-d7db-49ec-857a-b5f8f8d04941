import json
import uuid
from enum import Enum
from typing import Optional

from pydantic import Extra
from pydantic.main import BaseModel

from asyncizer.exceptions import (
    FileProcessingException,
    TooManyPagesError,
    PdfSplitException,
    UnsupportedFileType,
    OcrFiletypeProcessingException,
)
from asyncizer.schemas import ProcessOriginalFileRequestV1
from asyncizer.pdf_password_protected import PasswordProtectedFile
from asyncizer.publisher import DossierEventPublisher


class HypoDossierException(Enum):
    UNKNOWN_EXCEPTION = 1
    NOT_READABLE = 2
    PASSWORD_PROTECTED = 3
    UNSUPPORTED_FILETYPE = 4
    TOO_SMALL_FILE = 5
    XLSM_FILE_CANNOT_BE_CONVERTED = 6
    OCR_FILETYPE_PROCESSING = 7
    VIRUS_DETECTED = 8
    TOO_MANY_PAGES_IN_DOCUMENT = 9


class ExceptionDetails(BaseModel):
    type: HypoDossierException
    en: str
    de: str
    fr: Optional[str] = None
    it: Optional[str] = None
    details: Optional[str] = None
    stacktrace: Optional[str] = None

    class Config:
        extra = Extra.forbid

    def get_fr_with_fallback(self):
        return self.fr if self.fr else self.en

    def get_it_with_fallback(self):
        return self.it if self.it else self.en


def create_exception_details_password_protected(
    details: Optional[str] = None,
) -> ExceptionDetails:
    return ExceptionDetails(
        type=HypoDossierException.PASSWORD_PROTECTED,
        en="The file is password-protected.",
        de="Die Datei ist passwortgeschützt.",
        fr="Le fichier est protégé par un mot de passe.",
        it="Il file è protetto da password.",
        details=details,
    )


def create_exception_ocr_filetype_processing(
    details: Optional[str] = None,
) -> ExceptionDetails:
    return ExceptionDetails(
        type=HypoDossierException.OCR_FILETYPE_PROCESSING,
        en="File corrupt or incorrect file type assigned and real file type not supported.",
        de="Datei beschädigt oder falscher Dateityp zugewiesen und echter Dateityp nicht unterstützt.",
        fr="Fichier corrompu ou type de fichier incorrect attribué et type de fichier réel non pris en charge.",
        it="File danneggiato o tipo di file non corretto assegnato e tipo di file reale non supportato.",
        details=details,
    )


def create_exception_details_not_readable(
    details: Optional[str] = None,
) -> ExceptionDetails:
    return ExceptionDetails(
        type=HypoDossierException.NOT_READABLE,
        en="Could not read the file. Possibly the file is corrupt.",
        de="Die Datei konnte nicht gelesen werden. Eventuell ist die Datei defekt.",
        fr="Le fichier n'a pas pu être lu. Le fichier est peut-être défectueux.",
        it="Non è stato possibile leggere il file. Il file potrebbe essere difettoso.",
        details=details,
    )


def create_exception_details_too_many_pages(
    num_pages: int, max_num_pages_allowed_in_pdf: int, details: Optional[str] = None
) -> ExceptionDetails:

    return ExceptionDetails(
        type=HypoDossierException.NOT_READABLE,
        en=f"Too many pages in PDF ({num_pages}), max number {max_num_pages_allowed_in_pdf}.",
        de=f"Zu viele Seiten im PDF ({num_pages}), maximale Anzahl {max_num_pages_allowed_in_pdf}.",
        fr=f"Trop de pages dans le PDF ({num_pages}), nombre maximum {max_num_pages_allowed_in_pdf}.",
        it=f"Troppe pagine nel PDF ({num_pages}), numero massimo {max_num_pages_allowed_in_pdf}.",
        details=details,
    )


async def handle_processed_file_exception(
    dossier_event_publisher: DossierEventPublisher,
    request: ProcessOriginalFileRequestV1,
    processed_file: FileProcessingException,
    extracted_file_uuid: uuid,
):
    exception_details: ExceptionDetails = await map_file_exception(processed_file)
    await dossier_event_publisher.publish(
        "DossierEvent.FileProcessingExceptionV1",
        json.dumps(
            {
                "dossier_uuid": request.dossier_uuid,
                "extracted_file_uuid": str(extracted_file_uuid),
                "type": "Processed",
                "exception_type": exception_details.type.name,
                "de": exception_details.de,
                "en": exception_details.en,
                "fr": exception_details.get_fr_with_fallback(),
                "it": exception_details.get_it_with_fallback(),
                "details": exception_details.details,
            }
        ).encode(),
    )


async def map_file_exception(processed_file) -> ExceptionDetails:
    if isinstance(processed_file.base_exception, TooManyPagesError):
        exception_details = create_exception_details_too_many_pages(
            details=str(processed_file.details),
            num_pages=processed_file.base_exception.num_pages,
            max_num_pages_allowed_in_pdf=processed_file.base_exception.max_num_pages_allowed_in_pdf,
        )
    elif isinstance(processed_file.base_exception, PdfSplitException):
        exception_details = create_exception_details_not_readable(
            str(processed_file.details)
        )
    elif isinstance(processed_file.base_exception, PasswordProtectedFile):
        exception_details = create_exception_details_password_protected(
            processed_file.details
        )
    elif isinstance(processed_file.base_exception, OcrFiletypeProcessingException):
        exception_details = create_exception_ocr_filetype_processing(
            processed_file.details
        )
    elif isinstance(processed_file.base_exception, UnsupportedFileType):
        e: UnsupportedFileType = processed_file.base_exception
        exception_details = ExceptionDetails(
            type=HypoDossierException.UNSUPPORTED_FILETYPE,
            de=f"Dateityp '{e.filetype}' nicht unterstützt. Aktuell unterstützte Dateitypen: {e.supported_filetypes}.",
            en=f"File type '{e.filetype}' not supported. Currently supported file types: {e.supported_filetypes}.",
            fr=f"Le type de fichier '{e.filetype}' n'est pas pris en charge. Types de fichiers actuellement pris en charge : {e.supported_filetypes}.",
            it=f"Il tipo di file '{e.filetype}' non è supportato. Tipi di file attualmente supportati: {e.supported_filetypes}.",
            details=str(processed_file.details),
        )
    else:
        exception_details = ExceptionDetails(
            type=HypoDossierException.UNKNOWN_EXCEPTION,
            en=f"During processing the files an unkown error occured in the file {processed_file.extracted_file}.",
            de=f"Während der Verarbeitung des Dossiers ist in der Datei {processed_file.extracted_file} ein Fehler aufgetaucht.",
            fr=f"Une erreur est survenue dans le fichier {processed_file.extracted_file} pendant le traitement du dossier.",
            it=f"Si è verificato un errore nel file {processed_file.extracted_file} durante l'elaborazione del dossier.",
            details=str(processed_file.details),
        )
    return exception_details
