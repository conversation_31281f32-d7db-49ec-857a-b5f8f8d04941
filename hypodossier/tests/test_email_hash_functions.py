import re
import hashlib
import json
from pathlib import Path
from tempfile import TemporaryDirectory

from constants import EXTRACTED_EMAIL_FILENAME_PREFIX
from hypodossier.email_processor import create_email_body_filename
from hypodossier.unpack import (
    find_pdf_with_content_hash,
    is_email_content_pdf_with_hash,
    extract_content_hash_from_filename,
)


def test_create_email_body_filename_with_hash():
    """Test that create_email_body_filename correctly formats filenames with hash"""
    # Create a sample hash
    content_hash = hashlib.sha256(b"test content").hexdigest()

    # Generate filename with hash
    filename = create_email_body_filename(content_hash)

    # Check that the filename has the correct format
    assert filename.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
    assert filename.endswith(".pdf")
    assert content_hash in filename

    # Check that the hash is properly positioned in the filename
    parts = filename.split("_")
    assert content_hash in parts[-1]


def test_create_email_body_filename_without_hash():
    """Test that create_email_body_filename works without a hash"""
    # Generate filename without hash
    filename = create_email_body_filename()

    # Check that the filename has the correct format
    assert filename.startswith(EXTRACTED_EMAIL_FILENAME_PREFIX)
    assert filename.endswith(".pdf")

    # Check that the filename contains a UUID part (5 characters)
    uuid_pattern = r"_[a-f0-9]{5}\.pdf$"
    assert re.search(uuid_pattern, filename) is not None


def test_create_email_body_filename_truncation():
    """Test that create_email_body_filename truncates long filenames correctly"""
    # Create a very long prefix to force truncation
    long_prefix = "X" * 300
    content_hash = hashlib.sha256(b"test content").hexdigest()

    # Temporarily modify the constant for this test
    original_prefix = EXTRACTED_EMAIL_FILENAME_PREFIX
    import constants

    constants.EXTRACTED_EMAIL_FILENAME_PREFIX = long_prefix

    try:
        # Generate filename with hash that should be truncated
        filename = create_email_body_filename(content_hash)

        # Check that the filename is truncated but still contains the hash
        assert len(filename) <= 240
        assert content_hash in filename
        assert filename.endswith(".pdf")
    finally:
        # Restore the original prefix
        constants.EXTRACTED_EMAIL_FILENAME_PREFIX = original_prefix


def test_is_email_content_pdf_with_hash():
    """Test that is_email_content_pdf_with_hash correctly identifies email PDFs with hash"""
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create a sample hash
        content_hash = hashlib.sha256(b"test content").hexdigest()

        # Create a valid email PDF filename with hash
        valid_filename = (
            f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{content_hash}.pdf"
        )
        valid_path = temp_path / valid_filename

        # Create an invalid filename (not starting with prefix)
        invalid_prefix = temp_path / f"Invalid_20230101_120000_{content_hash}.pdf"

        # Create an invalid filename (wrong extension)
        invalid_extension = (
            temp_path
            / f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{content_hash}.txt"
        )

        # Create an invalid filename (hash too short)
        invalid_hash = (
            temp_path
            / f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{content_hash[:32]}.pdf"
        )

        # Create empty files
        for path in [valid_path, invalid_prefix, invalid_extension, invalid_hash]:
            with open(path, "w") as f:
                f.write("")

        # Test the function
        assert is_email_content_pdf_with_hash(valid_path) is True
        assert is_email_content_pdf_with_hash(invalid_prefix) is False
        assert is_email_content_pdf_with_hash(invalid_extension) is False
        assert is_email_content_pdf_with_hash(invalid_hash) is False

        # Test with a non-existent file
        non_existent = temp_path / "non_existent.pdf"
        assert is_email_content_pdf_with_hash(non_existent) is False


def test_extract_content_hash_from_filename():
    """Test that extract_content_hash_from_filename correctly extracts hash from filename"""
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create a sample hash
        content_hash = hashlib.sha256(b"test content").hexdigest()

        # Create a valid email PDF filename with hash
        valid_filename = (
            f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{content_hash}.pdf"
        )
        valid_path = temp_path / valid_filename

        # Create a valid filename with hash followed by other content
        valid_with_suffix = (
            temp_path
            / f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{content_hash}_extra.pdf"
        )

        # Create an invalid filename (hash too short)
        invalid_hash = (
            temp_path
            / f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{content_hash[:32]}.pdf"
        )

        # Create empty files
        for path in [valid_path, valid_with_suffix, invalid_hash]:
            with open(path, "w") as f:
                f.write("")

        # Test the function
        assert extract_content_hash_from_filename(valid_path) == content_hash
        assert extract_content_hash_from_filename(valid_with_suffix) == content_hash
        assert extract_content_hash_from_filename(invalid_hash) is None


def test_find_pdf_with_content_hash():
    """Test that find_pdf_with_content_hash correctly finds files with matching hash"""
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create sample hashes
        hash1 = hashlib.sha256(b"content1").hexdigest()
        hash2 = hashlib.sha256(b"content2").hexdigest()

        # Create filenames with different hashes
        file1 = (
            temp_path / f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120000_{hash1}.pdf"
        )
        file2 = (
            temp_path / f"{EXTRACTED_EMAIL_FILENAME_PREFIX}_20230101_120001_{hash2}.pdf"
        )

        # Create empty files
        for path in [file1, file2]:
            with open(path, "w") as f:
                f.write("")

        # Test finding files with matching hash
        found_file1 = find_pdf_with_content_hash(temp_path, hash1)
        found_file2 = find_pdf_with_content_hash(temp_path, hash2)

        assert found_file1 == file1
        assert found_file2 == file2

        # Test with non-existent hash
        non_existent_hash = hashlib.sha256(b"non-existent").hexdigest()
        assert find_pdf_with_content_hash(temp_path, non_existent_hash) is None

        # Test with None hash
        assert find_pdf_with_content_hash(temp_path, None) is None


def test_integration_of_hash_functions():
    """Test the integration of all hash-related functions"""
    with TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # Create sample content and hash
        content = {
            "subject": "Test Email",
            "sender": "<EMAIL>",
            "body": "This is a test email body",
            "html": "<html><body>This is a test email body</body></html>",
        }
        content_str = json.dumps(content, sort_keys=True)
        content_hash = hashlib.sha256(content_str.encode("utf-8")).hexdigest()

        # Create a filename using the hash
        filename = create_email_body_filename(content_hash)
        file_path = temp_path / filename

        # Create an empty file
        with open(file_path, "w") as f:
            f.write("")

        # Test that the file is recognized as an email PDF with hash
        assert is_email_content_pdf_with_hash(file_path) is True

        # Test that the hash can be extracted from the filename
        extracted_hash = extract_content_hash_from_filename(file_path)
        assert extracted_hash == content_hash

        # Test that the file can be found using the hash
        found_file = find_pdf_with_content_hash(temp_path, content_hash)
        assert found_file == file_path
