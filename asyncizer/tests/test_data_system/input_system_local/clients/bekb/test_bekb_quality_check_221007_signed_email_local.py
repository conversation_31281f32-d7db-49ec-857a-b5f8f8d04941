import shutil
from pathlib import Path

import pytest

from asyncizer.dossier_processor import async_unpack_path
from asyncizer.tests.util_tests import (
    DossierExpectations,
    DossierTest,
    configure_test_logging,
)
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.unpack import FileExtraction, unpack_path

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "221007_signed_email"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_bekb_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/220929_softlaunch_check"
)


@pytest.mark.asyncio
async def test_221007_signed_email_1():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email1",
        source_file_filter=["email1.msg"],
        # CAUTION: show_page does not work inside signed email because path gets modified by smime.p7m dir
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_221007_email_1_files_reservation():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email1_files",
        source_file_filter=["tophypo_reservation.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.AUTHORIZATION_FOR_INQUIRIES: 1
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_221007_email_1_files_tax():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email1_files",
        source_file_filter=["Zehnder-Stephan---Steuererklärung-2020 (1).pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_221007_email_1_files_sina():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email1_files",
        source_file_filter=["sina.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_221007_signed_email_3():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email3",
        source_file_filter=["signed_email_3_3.msg"],
        # CAUTION: show_page does not work inside signed email because path gets modified by smime.p7m dir
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_221007_email_3_files_plr():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "email3_files",
        source_file_filter=["626 plr.pdf"],
        show_page=-3,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Still Liegenschaftsinformation and not PLC because there really is a page of type land register inside
        ),
    ).run()


@pytest.mark.asyncio
async def test_unpack_only_2_3():
    configure_test_logging()

    p = path_root_folder / "unpack" / "signed_email_2_3.msg"
    assert p.exists()

    # Copy the file somewhere else because unpacking happens in-place
    p2 = p.parent / "sample_unpack_2_3_temp_directory.msg"

    if p2.exists():
        shutil.rmtree(str(p2))

    shutil.copy(p, p2)

    file_extraction: FileExtraction = await async_unpack_path(p2)

    assert file_extraction

    # Count PDF files (should be 1 email body PDF)
    pdf_files = [
        f
        for f in file_extraction.extracted_files
        if f.endswith(".pdf") and "email_body_" in f
    ]
    assert len(pdf_files) == 1, "Should have exactly one email body PDF"

    # Total files should now be 4 (original 3 + 1 PDF)
    assert len(file_extraction.extracted_files) == 4


def test_unpack_only_3_3():
    configure_test_logging()

    p = path_root_folder / "unpack" / "signed_email_3_3.msg"
    assert p.exists()

    # Copy the file somewhere else because unpacking happens in-place
    p2 = p.parent / "sample_unpack_3_3_temp_directory.msg"

    if p2.exists():
        shutil.rmtree(str(p2))

    shutil.copy(p, p2)

    file_extraction: FileExtraction = unpack_path(p2)

    assert file_extraction

    # Count PDF files (should be 1 email body PDF)
    pdf_files = [
        f
        for f in file_extraction.extracted_files
        if f.endswith(".pdf") and "email_body_" in f
    ]
    assert len(pdf_files) == 1, "Should have exactly one email body PDF"

    # Total files should now be 13 (original 12 + 1 PDF)
    assert len(file_extraction.extracted_files) == 13
