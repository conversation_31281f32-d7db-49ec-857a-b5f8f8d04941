from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "617_property_insurance"


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_mixed_be_mobiliar(override_existing=True):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/{FOLDER_TYPE}/issues/mixed_be_mobiliar"
        ),
        dest_folder_prefix=f"{FOLDER_TYPE}_",
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PROPERTY_INSURANCE: 2},
            expected_num_docs=2,
        ),
    )
