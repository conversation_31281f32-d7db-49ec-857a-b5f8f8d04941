from abbyyplumber.converter.ValueConverter import CleanNameConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)

parsers_de = [
    SmartLetterPageParser(
        desc="Valuu Summary #1",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="Valuu",
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Antragsteller",
            text_left="Liegenschaftsadresse:",
            text_right="risch)",
            text_bottom="Höhe der Hypothek:",
            compress_whitespace=True,
            converter=CleanNameConverter(max_num_lines=1),
        ),
        titles=["Daten zur Hypothek Zusammenfassung"],
        # 'Kommentar Valuu' is optional
        required_tokens=[
            "Abschlussdatum und Zeit auf Plattform:",
            "Kreditgeber:",
            "Dossier Nummer:",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Summary #2 Hypothek",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Details zur abgeschlossenen Hypothek"],
        required_tokens=["Dossier Nr.", "Hypothekarmodell", "Gesamtkosten in CHF"],
    ),
    SmartLetterPageParser(
        desc="Valuu Summary #3 Kreditnehmer",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Details Kreditnehmer"],
        required_tokens=[
            "Dossier Nr.",
            "Antragsteller",
            "Solidarschuldner",
            "Vertragssprache",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Summary #4 Finanzen",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Angaben zu den Finanzen"],
        required_tokens=[
            "Angaben zu den Eigenmittel",
            "Dossier Nr.",
            "Antragsteller",
            "Solidarschuldner",
            "Erwerbssituation",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Summary #5 Liegenschaft",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Details zur Liegenschaft"],
        required_tokens=[
            "Dossier Nr.",
            "Hoher Konfidenzintervall",
            "Daten zur Liegenschaft",
            "Kundenangaben",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Summary #6 Parameter",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Kreditgeber Parameter"],
        required_tokens=[
            "Dossier Nr.",
            "Tragbarkeitsparameter",
            "Belehnungsparameter",
            "Amortisationsparameter",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Summary #7 Tragbarkeit Details",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Details zur Tragbarkeitsberechnung"],
        required_tokens=[
            "Dossier Nr.",
            "Berechnungsgrundlagen",
            "unabhängig",
            "Finanzierung (kalkulatorisch)",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Abschlussbestätigung DE 1a/4 an Broker",
        doc_cat=DocumentCat.MORTGAGE_CONTRACT_CONFIRMATION,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Abschlussvereinbarung"],
        required_tokens=[
            "Dossier Nr.",
            "www.valuu.ch",
            "Postfinance AG",
            "hat der Kreditnehmer das Angebot des Kreditgebers verbindlich",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Abschlussbestätigung DE 1b/4 an Kunde",
        doc_cat=DocumentCat.MORTGAGE_CONTRACT_CONFIRMATION,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=["Abschlussbestätigung"],
        required_tokens=[
            "Dossier Nr.",
            "www.valuu.ch",
            "Postfinance AG",
            "Sehr geehrte",
            "Anbei fassen wir gerne",
        ],
    ),
    SmartLetterPageParser(
        desc="Valuu Abschlussbestätigung DE 2..4/4",
        doc_cat=DocumentCat.MORTGAGE_CONTRACT_CONFIRMATION,
        page_cat=PageCat.GENERIC_PAGE,
        company="Valuu",
        titles=[
            "Ihre persönlichen Daten",
            "Angaben zu den Finanzen",
            "Ihre Angaben zur Liegenschaft",
        ],
        required_tokens=[
            "Betreff Abschlussbestätigung - Dossier Nr.",
            "Datum",
            "Seite",
            ".20",
        ],
    ),
]


parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_valuu():
    return parsers_de + parsers_en + parsers_fr + parsers_it
