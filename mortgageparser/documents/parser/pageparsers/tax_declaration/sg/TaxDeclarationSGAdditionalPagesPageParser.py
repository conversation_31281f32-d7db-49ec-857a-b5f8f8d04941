from abbyyplumber.api import Page
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.tax_declaration_sg_fields_map import (
    create_search_elements_standard_sg,
)
from mortgageparser.util.string_utils import contains_all_strings


# Match this type of page by string because not in training set (maybe from 2019)
class TaxDeclarationSGAdditionalPagesPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_MISC
        self.canton_short = "SG"

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "Zusatzblätter Steuererklärung Kanton St. Gallen 20",
                "Register-Nummer:",
                "Formular",
                "Nr.",
                "Name:",
            ],
        )
        return success

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = create_search_elements_standard_sg(page)

        return elements
