from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_PORTFOLIO,
    INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_CHILDREN,
    INCOME_UNDISTRIBUTED_INHERITANCES,
    INCOME_OTHER,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    P1_CONTRIBUTION_PILLAR_2,
    P2_CONTRIBUTION_PILLAR_2,
    DEDUCTIONS_WEALTH_MANAGEMENT,
    P1_DEDUCTIONS_EDUCATION,
    P2_DEDUCTIONS_EDUCATION,
    DEDUCTIONS_OTHER,
    DEDUCTIONS_TOTAL,
    INCOME_NET_TOTAL,
    EXPENSE_CHILDREN_DAYCARE,
    DEDUCTIONS_ILLNESS,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_UNDISTRIBUTED_INHERITANCES,
    ASSETS_OTHER,
    ASSETS_GROSS_TOTAL,
    DEBT_PRIVATE,
    DEBT_BUSINESS,
    ASSETS_NET_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_BOARD_SEATS,
    INCOME_REAL_ESTATE_NET_OTHER,
    INCOME_REAL_ESTATE_NET,
    INCOME_LUMP_SUM,
    INCOME_GROSS_TOTAL,
    ASSETS_CASH_GOLD,
    ASSETS_NET_BUSINESS,
    ASSETS_REAL_ESTATE_TOTAL_GROSS,
    P1_INCOME_CHILD_BENEFITS,
    P2_INCOME_BOARD_SEATS,
    ASSETS_GROSS_BUSINESS,
)
from hypodossier.core.domain.SemanticField import FIELD_PROPERTY_MAINTENANCE_COST

tax_ai_codes_income = {
    1000: P1_INCOME_EMPLOYED_MAIN,
    1020: P2_INCOME_EMPLOYED_MAIN,
    1040: P1_INCOME_EMPLOYED_SIDE,
    1060: P2_INCOME_EMPLOYED_SIDE,
    1080: P1_INCOME_BOARD_SEATS,
    1090: P2_INCOME_BOARD_SEATS,
    1120: P1_INCOME_SELF_MAIN,
    1140: P2_INCOME_SELF_MAIN,
    # according to SG
    # true title: aus Kollektiv- Kommandit- und einfachen Gesellschaften
    1200: P1_INCOME_SELF_SIDE,
    1220: P2_INCOME_SELF_SIDE,
    1260: P1_INCOME_PENSION_AHV,
    1280: P2_INCOME_PENSION_AHV,
    1300: P1_INCOME_PENSION,
    1320: P2_INCOME_PENSION,
    1340: P1_INCOME_EO,
    1360: P2_INCOME_EO,
    1380: P1_INCOME_SOCIAL_SECURITY,
    1400: P2_INCOME_SOCIAL_SECURITY,
    1420: P1_INCOME_CHILD_BENEFITS,
    1460: INCOME_PORTFOLIO,
    # not mapped: 1465 aus Beteiligungen an Gesellschaften ab 10%
    # not mapped: 1470 Privilegierter Anteil (Abzug für Teilbesteuerung)
    # Einkünfte aus Liegenschaften: not sure whether net or gross
    # in SG mapped to net
    1480: INCOME_REAL_ESTATE_NET,
    1500: INCOME_REAL_ESTATE_NET_OTHER,
    1540: INCOME_ALIMONY_PARTNER,
    1560: INCOME_ALIMONY_CHILDREN,
    1580: INCOME_UNDISTRIBUTED_INHERITANCES,
    # not mapped: 1600 Einkünfte aus Urheberrechten, Lizenzen Patenten
    1620: INCOME_OTHER,
    1640: INCOME_LUMP_SUM,
    1680: INCOME_GROSS_TOTAL,
}

tax_ai_codes_deductions = {
    1830: P1_EXPENSE_EMPLOYMENT,
    1960: P2_EXPENSE_EMPLOYMENT,
    1980: INTEREST_PAID_ON_DEBT,
    # not mapped: 1990 Geschäftliche Schuldzinsen
    2000: EXPENSE_ALIMONY_PARTNER,
    2020: EXPENSE_ALIMONY_CHILDREN,
    2040: EXPENSE_ANNUITY_CONTRIBUTIONS,
    2080: P1_CONTRIBUTION_PILLAR_3A,
    2100: P2_CONTRIBUTION_PILLAR_3A,
    2110: P1_CONTRIBUTION_PILLAR_2,
    2120: P2_CONTRIBUTION_PILLAR_2,
    2130: INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    2150: FIELD_PROPERTY_MAINTENANCE_COST,
    2170: DEDUCTIONS_WEALTH_MANAGEMENT,
    2180: EXPENSE_CHILDREN_DAYCARE,
    # not mapped: 2190 Parteispenden
    2210: P1_DEDUCTIONS_EDUCATION,
    2220: P2_DEDUCTIONS_EDUCATION,
    2230: DEDUCTIONS_OTHER,
    # not mapped: 2260 Zweiverdienerabzug
    2300: DEDUCTIONS_TOTAL,
    2340: INCOME_NET_TOTAL,
    2360: DEDUCTIONS_ILLNESS,
    # not mapped: 2380: Krankheitskosten abzüglich Selbstbehalt
    # not mapped: 2400 Krankheitskosten Abzug
    # not mapped: 2350 Behinderungsbedingte Kosten
    # not mapped: 2410 Freiwillige Zuwendungen
    # not mapped: 2420 Freiwillige Zuwendungen abzüglich Selbstbehalt
    # not mapped: 2440 Freiwillige Zuwendungen Abzug
    2480: INCOME_NET_TOTAL,
    # not mapped: 2520 Sozialabzüge Kinder minderjährig
    # not mapped: 2560 Sozialabzüge Kinder Schule auswärts
    # not mapped: 2640 Sozialabzüge unterstützte Personen
    2680: INCOME_TAXABLE_LOCAL,
    # not mapped: 2800 Bruttolohn vereinfacht abgerechnet
}

tax_ai_codes_assets = {
    3000: ASSETS_PORTFOLIO_ACCOUNTS,
    3020: ASSETS_CASH_GOLD,
    3040: ASSETS_LIFE_INSURANCE,
    3060: ASSETS_CARS,
    3080: ASSETS_UNDISTRIBUTED_INHERITANCES,
    3100: ASSETS_OTHER,
    3120: ASSETS_REAL_ESTATE_TOTAL_GROSS,  # mortgage is deducted later so this is gross
    3140: ASSETS_NET_BUSINESS,  # assume that "Geschäftskapital" is net value of business
    3160: ASSETS_GROSS_BUSINESS,  # assume that "Aktiven gemäss Schlussbilanz" is gross value of business
    # (before deducting debt)
    # not mapped: 3170 Aktiven gemäss Schlussbilanz, Person 2
    3220: ASSETS_GROSS_TOTAL,
    3230: DEBT_PRIVATE,
    3240: DEBT_BUSINESS,
    # not mapped: 3250 Geschäftsschulden, Person 2
    3260: ASSETS_NET_TOTAL,
    # not mapped: 3300 Abzug für alleinstehende Steuerpflichtige
    # not mapped: 3320 Abzug für gemeinsame Steuerpflichtige
    # not mapped: 3340 Abzug für jedes minderjährige Kind
    3380: ASSETS_TAXABLE_LOCAL,
    # not mapped: 3400 Steuerbares Vermögen, davon Ermässung auf Beteiligungen 10%
}
