from dataclasses import dataclass

from abbyyplumber.converter.ValueConverter import StringConstantConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementSetChooseFirst,
    SearchElementStaticText,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardLetterPageParser,
)
from mortgageparser.util.search_element_util import (
    create_document_date_most_recent,
    create_search_elements_address,
)
from mortgageparser.util.string_utils import contains_at_least_one_string


class StandardVestedBenefitsPageParser(StandardLetterPageParser):
    def __init__(self):
        super().__init__()
        self.doc_cat = DocumentCat.VESTED_BENEFITS_ACCOUNT
        self.page_cat = PageCat.VESTED_BENEFITS_ACCOUNT


@dataclass
class GenericVestedBenefitsAccountDE(StandardVestedBenefitsPageParser):
    def __init__(self, company, titles, document_date_label, required_tokens):
        super().__init__()
        self.company = company
        self.titles = titles
        self.document_date_label = document_date_label
        self.required_tokens = required_tokens


company_map = {
    "Rendita": "Rendita Freizügigkeitsstiftung",
    "WIR": "Wir Bank",
}


class CommonVestedBenefitsAccountDEParser(StandardLetterPageParser):
    """
    Best effort try to find address and name and document date in the upper third of the page
    """

    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.VESTED_BENEFITS_ACCOUNT
        self.doc_cat = DocumentCat.VESTED_BENEFITS_ACCOUNT

    # There are cases where this matches, but it is acutally a page 2
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_at_least_one_string(
            text, ["Auszug"]
        ) and contains_at_least_one_string(text, ["Vorsorgeguthaben"])

        if success:
            count = 0
            if contains_at_least_one_string(text, ["Buchungstext", "Valuta", "Saldo"]):
                count += 1
            if contains_at_least_one_string(text, ["Freizügigkeitsstiftung"]):
                count += 1
            if contains_at_least_one_string(text, ["Total Vorsorgeguthaben"]):
                count += 1
            if contains_at_least_one_string(text, ["Zinssätze"]):
                count += 1
            return count >= 3

    def parse_page_header(self):
        return self.page.set_header_by_percentage(0.5)

    def create_content_extractor(self) -> ContentExtractor:
        elements_company = []
        for company_name, company_search_string in company_map.items():
            e = SearchElementStaticText(
                "company",
                self.page.fullpage,
                label=company_search_string,
                optional=True,
                converter=StringConstantConverter(value=company_name),
            )
            elements_company.append(e)

        element_first = SearchElementSetChooseFirst(
            elements=elements_company, change_name=False
        )

        return ContentExtractor(
            create_search_elements_address(self.page.header, self.page)
            + [create_document_date_most_recent(self.page.header)]
            + [element_first]
        )
