from hypodossier.util.language_detector import ALL_LANGUAGES
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)

from mortgageparser.util.string_utils import contains_at_least_one_string, count_strings

"""
Lohnausweis - Certificat de salaire - Certificato di salario
Beiblatt - Supplément - Supplemento ( 1 / 1 )
C 111.22.333.444 111.2222.3333.44
AHV-Nr. - No AVS - N. AVS Neue AHV-Nr. - Nouveua AVS - Nuovo N. AVS
D 2016 01.01.2016 31.12.2016
Jahr - Ann<PERSON>e - <PERSON><PERSON> von <PERSON>l bi<PERSON> - au - al
<PERSON>n
Firstname Lastname
Street
ZIP City
Detailangaben - Indications de détail - Dati del particolare
15 Bemerkungen - Observations - Osservazioni
Spesenreglement durch Kanton ZH am 14.01.2011 genehmigt.
Der Mitarbeiter hat den Arbeitsort Weisslingen
Anteil Aussendienst 100% Pauschal nach Funktions-/Berufsgruppenliste
"""


class SalaryCertificateAdditionalPagePageParser(AbstractPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ALL_LANGUAGES

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        text_header = self.page.get_text_by_range(
            0, 0.2
        )  # There might be an additional line on top 'Additional Page / Zusatzseite'

        num_hits_main = count_strings(
            text_header,
            [
                "Lohnausweis",
                "Certificat de salaire",
                "Certificato di salario",
                "Salary certificate",
                "Rentenbescheinigung",
                "Attestation de rentes",
                "Attestazione delle rendite",
                "Pension statement",
            ],
        )
        if num_hits_main < 4:
            return False

        num_hits_additional = count_strings(
            text_header, ["Additional Page", "Zusatzblatt"], dist=3
        )
        num_hits_additional += count_strings(
            text_header,
            ["Allegato", "Beiblatt", "Supplément", "Supplemento", "Supplement"],
            dist=2,
        )
        num_hits_additional += count_strings(
            text_header, ["Annexe"], dist=0
        )  # Année is in every one of them -> no dist
        success = num_hits_additional > 0

        if success:
            success = not contains_at_least_one_string(
                text,
                [
                    "soweit nicht unter Ziffer 2-7 aufzuführen",
                    "Beteiligungsrechte gemäss Beiblatt",
                    "Nettolohn / Rente",
                    "Bruttolohn total / Rente",
                    "Beiträge AHV/IV/EO",
                    "Beiträge für den Einkauf",
                ],
            )
        if success:
            success = contains_at_least_one_string(
                text, ["Seite 2", "Page 2", "Pagina 2"], hamming_dist=0
            )

        return success

    def parse(self) -> SemanticPage:
        return create_semantic_page(
            self,
            DocumentCat.SALARY_CERTIFICATE,
            PageCat.SALARY_CERTIFICATE_ADDITIONAL_PAGE,
        )
