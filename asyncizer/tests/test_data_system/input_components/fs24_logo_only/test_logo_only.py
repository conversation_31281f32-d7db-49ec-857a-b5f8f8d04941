from pathlib import Path

import pytest as pytest

from asyncizer.tests.util_tests import (
    run_single_dossier_test,
    DossierExpectations,
    validate_processed_dossier,
)
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS


@pytest.mark.asyncio
async def test_logo_only(override_existing=True):
    """
        Test logo that we know and want to ignore. So no files in this result

    :param override_existing:
    :return:
    """
    source_folder = Path(
        f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/fs24_logo_only"
    )
    source_file_filter = []

    dest_folder, duration_seconds = await run_single_dossier_test(
        source_folder, source_file_filter, override_existing=override_existing
    )

    expected_page_objects = {}

    dossier_expectations = DossierExpectations(
        expected_filenames=[],
        expected_page_objects=expected_page_objects,
        expected_num_docs=0,
        expected_num_extracted_file_exceptions=0,
        max_duration_seconds=50,
    )

    validate_processed_dossier(
        dest_folder,
        duration_seconds=duration_seconds,
        dossier_expectations=dossier_expectations,
    )
