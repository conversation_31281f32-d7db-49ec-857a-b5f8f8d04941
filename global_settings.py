import os
from distutils.util import strtobool
from pathlib import Path

from dotenv import load_dotenv

load_dotenv(verbose=True)


# sentry_sdk.init(
#     dsn="https://<EMAIL>/3",
#     # Set traces_sample_rate to 1.0 to capture 100%
#     # of transactions for performance monitoring.
#     # We recommend adjusting this value in production.
#     traces_sample_rate=0,
#     environment=os.getenv("SENTRY_ENVIRONMENT"),
# )

# If true then raise exceptions if parser throws an exception.
# For production this must be set to false (default)
# For development this must be set to true
RAISE_EXCEPTIONS_IN_PARSER = strtobool(
    os.getenv("RAISE_EXCEPTIONS_IN_PARSER", default="False")
)

S3_HOST = os.getenv("S3_HOST", default="localhost:9000")
S3_HOST_URL = os.getenv("S3_HOST_URL", default="http://localhost:9000")
S3_ACCESS_KEY = os.getenv("S3_ACCESS_KEY", "S3_ACCESS_KEY")
S3_SECRET_KEY = os.getenv("S3_SECRET_KEY", "S3_SECRET_KEY")
S3_SECURE = strtobool(os.environ.get("S3_SECURE", default="True"))

# S3_BUCKET is legacy, removed on 220704 by mt. Replaced by ORIGINAL_FILE_PROCESSOR_BUCKET
# S3_BUCKET = os.getenv("S3_BUCKET", "dossier")

S3_REGION = os.getenv("S3_REGION", None)

S3_CACHE_BUCKET = os.getenv("S3_CACHE_BUCKET", "cache")

RABBIT_URL = os.getenv("RABBIT_URL")

SGD_CLASSIFIER_JOBLIB_FILE = os.getenv(
    "SGD_CLASSIFIER_JOBLIB_FILE", "models/sgd_latest.models"
)

# access to the model server
MODEL_S3_HOST = os.getenv("MODEL_S3_HOST", default="http://localhost:9000")
MODEL_S3_ACCESS_KEY = os.getenv("MODEL_S3_ACCESS_KEY", "S3_ACCESS_KEY")
MODEL_S3_SECRET_KEY = os.getenv("MODEL_S3_SECRET_KEY", "S3_SECRET_KEY")
MODEL_S3_REGION = os.getenv("MODEL_S3_REGION", "ch-dk-2")


SPACY_S3_BASE_PATH = "hypodossier-models/hydocs_spacy"


# ALWAYS MAKE SURE YOU HAVE THE MOST RECENT MODELS FROM HYSGD LINKED HERE!
# IF YOU UPDATE A MODEL HERE, YOU ALSO NEED TO REDEPLOY THE HYEXTRACT STACK!
SPACY_MODEL_DE_S3_OBJECT_PATH = os.getenv(
    "SPACY_MODEL_DE_S3_OBJECT_PATH",
    # default="hypodossier-models/hydocs_spacy/hydocs_detail_de_20221111-1112-20221112-0959.tar.gz"
    # default = "hypodossier-models/hydocs_spacy/hydocs_detail_de_20221221-1808-20221222-0556.tar.gz"
    # f-score 82.7, weighted 94.3
    # default = "hypodossier-models/hydocs_spacy/hydocs_detail_de_20230217-2158-20230219-0401.tar.gz"
    # # f-score 82.5, weighted 95.1
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20230325-0254-20230326-1137.tar.gz"
    # f-score 85.4, weighted 95.1
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20230407-1352-20230408-1642.tar.gz"
    # # f-score 82.7, weighted 95.3
    # # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20230603-1151-20230604-1301.tar.gz"
    # f-score 86.4, weighted 95.5
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20230612-2033-20230614-0403.tar.gz",
    # train eval nobs 61230, f-score 86.0, weighted 95.1
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20240112-1852-20240114-0227.tar.gz",
    # f-score 84.3, weighted 95 with zkb fk
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20240511-1853-20240513-0458.tar.gz",
    # f-score 0.861, weighted 0.949
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20240517-1810-20240519-0410.tar.gz",
    # 240806 f-score 0.864, weighted 0.952 - with renamings for BILL_MISC / PROPERTY_BILL
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20240524-1234-20240526-0048.tar.gz",
    # 250127 f-score 0.861, weighted 0.955 - with some VZ stuff
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20250124-1801-20250126-0343.tar.gz",
    # 250225 f-score 0.8668, weighted 0.9xx
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20250222-2221-20250224-1401.tar.gz",
    # 250310 f-score 86.291
    default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_de_20250307-1842-20250309-1221.tar.gz",
)

SPACY_MODEL_EN_S3_OBJECT_PATH = os.getenv(
    "SPACY_MODEL_EN_S3_OBJECT_PATH",
    # default="hypodossier-models/hydocs_spacy/hydocs_detail_en_20230219-0402-20230219-0421.tar.gz"
    # f-score 0.818, weighted 0.974
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20230326-1137-20230326-1216.tar.gz"
    # f-score 0.761, weighted 0.961
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20230604-1301-20230604-1323.tar.gz"
    # f-score 0.826, weighted 0.973
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20230605-1631-20230605-1652.tar.gz"
    # f-score 0.855, weighted 0.975
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20230607-1852-20230607-1916.tar.gz"
    # f-score 0.863, weighted 0.975
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20230904-1825-20230904-1851.tar.gz",
    # train eval nobs 7693, f-score avg 0.825, weighted 0.973
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20240111-1839-20240111-1906.tar.gz",
    # train eval nobs 7800, f-score 0.8, weighted 0.973
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20240114-0227-20240114-0257.tar.gz",
    # f-score 84.9, weighted 0.972 without zkb fk stuff (deen was missing)
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20240513-0458-20240513-0528.tar.gz",
    # f.score 81.1, weighted 0.968
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20240514-0957-20240514-1032.tar.gz",
    # f-score 0.817, weighted 0.967
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20240519-0410-20240519-0447.tar.gz",
    # 240806 f-score 0.792, weighted 0.970 - with renamings for BILL_MISC / PROPERTY_BILL
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20240526-0049-20240526-0122.tar.gz",
    # 250127 f-score 0.876, weighted 0.9680 - with some VZ stuff
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20250126-0344-20250126-0418.tar.gz",
    # 250225 f-score 0.827
    default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_en_20250225-0955-20250225-1049.tar.gz",
)
SPACY_MODEL_FR_S3_OBJECT_PATH = os.getenv(
    "SPACY_MODEL_FR_S3_OBJECT_PATH",
    # default="hypodossier-models/hydocs_spacy/hydocs_detail_fr_20230219-0421-20230219-0922.tar.gz"
    # # fscore 0.905, weighted 0.955
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20230404-2014-20230405-0103.tar.gz"
    # fscore 0.924, weighted 0.955
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20230408-1702-20230408-2212.tar.gz"
    # fscore 0.918, weighted 0.952
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20230604-1323-20230604-1835.tar.gz"
    # fscore 0.903, weighted 0.95
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20230605-1653-20230605-2205.tar.gz"
    # fscore 0.927, weighted 0.958
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20230607-1916-20230608-0023.tar.gz"
    # fscore 0.926, weighted 0.963
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20230904-1851-20230905-0056.tar.gz",
    # train eval nobs 28022, fscore avg 0.917, weighted 0.955
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20240111-1906-20240112-0023.tar.gz",
    # train eval nobs 28099, avg. 0.916, weighted 0.956
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20240114-0257-20240114-0830.tar.gz",
    # f-score 0.927, weighted 0.959
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20240513-0528-20240513-1125.tar.gz",
    # f-score 0.917, weighted 0.959
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20240519-0447-20240519-1040.tar.gz",
    # 240806 f-score 0.930, weighted 0.963 - with renamings for BILL_MISC / PROPERTY_BILL
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20240526-0122-20240526-0708.tar.gz",
    # 250127 f-score 0.916, weighted 0.958 - with some VZ stuff
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20250126-0418-20250126-0953.tar.gz",
    # 250225 f-score 0.92134
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20250224-1440-20250224-2352.tar.gz",
    # 250310 f-score 91.924
    default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_fr_20250309-1309-20250309-1309.tar.gz",
)

SPACY_MODEL_IT_S3_OBJECT_PATH = os.getenv(
    "SPACY_MODEL_IT_S3_OBJECT_PATH",
    # default="hypodossier-models/hydocs_spacy/hydocs_detail_it_20230219-0922-20230219-1004.tar.gz"
    # fscore 73.2, weighted 0.960
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20230326-1825-20230326-1922.tar.gz"
    # fscore 70.0, weighted 0.950
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20230604-1836-20230604-1921.tar.gz"
    # fscore 68.1, weighted 0.953
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20230605-2205-20230605-2254.tar.gz"
    # # fscore 68.7, weighted 0.958
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20230608-0023-20230608-0109.tar.gz"
    # fscore 73.7, weighted 0.964
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20230624-1732-20230624-1821.tar.gz"
    # fscore 73.086
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20230905-1546-20230905-1637.tar.gz",
    # train eval nobs 9364, fscore avg 0.73, weighted 0.959
    # default = f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20240112-0023-20240112-0118.tar.gz"
    # train eval nobs 9382, fscore avg 0.754, weighted 0.958
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20240114-0830-20240114-0926.tar.gz",
    # f-score 0.764, weighted 0.956
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20240513-1125-20240513-1227.tar.gz",
    # f-score 0.755, weighted 0.955
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20240519-1040-20240519-1140.tar.gz",
    # 240806 f-score 0.745, weighted 0.968 - with renamings for BILL_MISC / PROPERTY_BILL
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20240526-0708-20240526-0808.tar.gz",
    # 250127 f-score 0.739, weighted 0.955 - with some VZ stuff
    # default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20250126-0953-20250126-1053.tar.gz",
    # 250225 f-score 0.75373
    default=f"{SPACY_S3_BASE_PATH}/hydocs_detail_it_20250224-2352-20250225-0121.tar.gz",
)

# If we find fewer letters than this on the page, no Spacy analysis will be performed
SPACY_MIN_NUM_LETTERS = 15

# If this flag is set and ABBYY produces a "larger PDF" (> 5 MB) that is significantly larger than the original
# then work with the original to save space. Be aware that OCR on that document may not work properly
REPLACE_LARGE_OCR_RESULTS_WITH_ORIGINAL = strtobool(
    os.getenv("REPLACE_LARGE_OCR_RESULTS_WITH_ORIGINAL", default="True")
)

OBJECT_DETECTOR_MODEL_S3_OBJECT_PATH = os.getenv(
    "OBJECT_DETECTOR_MODEL_S3_OBJECT_PATH",
    default="hypodossier-models/object_detector/hypo-objects_v11.tar.gz",
)

OBJECT_DETECTOR_URL = os.getenv(
    "OBJECT_DETECTOR_URL", default="http://localhost:8501/v1/models/ausweise:predict"
)
BOTO_S3_ENDPOINT_URL = os.getenv("BOTO_S3_ENDPOINT_URL", default="localhost:9000")

MINIO_S3_HOST = os.getenv("MINIO_S3_HOST", default="localhost:9000")

DOCUMENT_BROWSER_BUILD_PATH = os.getenv("DOCUMENT_BROWSER_BUILD_PATH")

POSTGRES_DB = os.getenv("POSTGRES_DB", default="hyadmin")
POSTGRES_USER = os.getenv("POSTGRES_USER", default="django")
POSTGRES_PASSWORD = os.getenv("POSTGRES_PASSWORD", default="django")
POSTGRES_HOST = os.getenv("POSTGRES_HOST", default="localhost")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", default="5432")

IMAP2MQS3_ROUTING_KEY = "imap2mqs3.S3Mail"
DOSSIER_LUIGI_WORKER_ROUTING_KEY = "DossierLuigiWorker.S3Dossier"
ASYNC_DOSSIER_PROCESSOR_WORKER_ROUTING_KEY = "AsyncDossierProcessorWorker.S3Dossier"
PICTURE_DETECTOR_QUEUE = "picturedector.S3DetectionRequest"

SGD_CLASSIFIER_JOBLIB_FILE_IT = os.getenv(
    "SGD_CLASSIFIER_JOBLIB_FILE_IT", os.getenv("SGD_CLASSIFIER_JOBLIB_FILE")
)

SGD_CLASSIFIER_JOBLIB_FILE_FR = os.getenv(
    "SGD_CLASSIFIER_JOBLIB_FILE_FR", os.getenv("SGD_CLASSIFIER_JOBLIB_FILE")
)

MAILPROCESSOR_SMTP_USER = os.getenv("MAILPROCESSOR_SMTP_USER")
MAILPROCESSOR_SMTP_PASSWORD = os.getenv("MAILPROCESSOR_SMTP_PASSWORD")

MAILPROCESSOR_SMTP_HOST = os.getenv("MAILPROCESSOR_SMTP_HOST", "mail.hypodossier.ch")
MAILPROCESSOR_SMTP_PORT = os.getenv("MAILPROCESSOR_SMTP_PORT", 587)

HYADMIN_ALLOWED_HOSTS = os.getenv(
    "HYADMIN_ALLOWED_HOSTS", default="0.0.0.0,127.0.0.1,localhost"
)

PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER = os.getenv(
    "PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER",
    default=f"{Path(__file__).parent}/hypodossier-data-dossier",
)
PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM = os.getenv(
    "PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM",
    default=f"{Path(__file__).parent}/hypodossier-data-system",
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system/doccat"
)
PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system/clients"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_FINHURDLE = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system/finhurdles"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/doccat"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local_todo"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local_todo/doccat"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_FINHURDLES = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system/finhurdles"
)
PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_FINHURDLES = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/finhurdles"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_components"
)

PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL = (
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_components_local"
)

# Duration that we wait for a single OCR job in seconds
# Default is 3600 = 1 hour
OCR_TIMEOUT_SECONDS = int(os.getenv("OCR_TIMEOUT_SECONDS", 3600))

FRE_LICENSE = os.getenv("FRE_LICENSE")
FRE_RABBIT_URL = os.getenv("FRE_RABBIT_URL")

# 'licenseSerialNumber': "SWAD-1201-1007-0006-8103-3364",
# 'licenseSerialNumber': "SWAR-1201-1007-0105-0766-2529",
# 'licenseSerialNumber': "SWAR-1201-1007-0006-8015-9363",

# Maximum number of pages that are accepted in a single document.
# If longer documents are received processing will abort.
MAX_NUM_PAGES_ALLOWED_IN_PDF = int(os.getenv("MAX_NUM_PAGES_ALLOWED_IN_PDF", 500))

DEFAULT_RULE_BASED_PARSER_CONFIDENCE = 0.98888

# Multiply confidence with this factor - we can never be 100% sure about a classification result.
# Also a factor below 1 allows us to distinguish between rule based and model based classification
# This should not be longer than NUM_DIGITS_SPACY_CONFIDENCE to avoid errors with rounding up
DEFAULT_SPACY_BASED_PARSER_CONFIDENCE = 0.97777

# Graphical object detection until August 2021

# Flag to decide if monk detection call should be made
MONK_PAGE_OBJECTS_VISIBLE = strtobool(os.getenv("MONK_PAGE_OBJECTS_VISIBLE", "false"))

# New style graphical object detection since August 2021

ENABLE_DETECTRON2_OBJECT_DETECTION = strtobool(
    os.getenv("ENABLE_DETECTRON2_OBJECT_DETECTION", "true")
)

# visibility status of new style detectron page objects (default true)
DETECTRON_PAGE_OBJECTS_VISIBLE = strtobool(
    os.getenv("DETECTRON_PAGE_OBJECTS_VISIBLE", "true")
)

# visibility status of new style detectron page objects of type 'rest_*' (default false)
DETECTRON_PAGE_OBJECTS_REST_VISIBLE = strtobool(
    os.getenv("DETECTRON_PAGE_OBJECTS_REST_VISIBLE", "false")
)

# Multiplier with which all graphical page object confidences are multiplied
GRAPHICAL_PAGE_OBJECT_CONFIDENCE_FACTOR = float(
    os.getenv("IMAGEPREDICT_CONFIDENCE_FACTOR", default=0.95)
)


# If this flag is set then whitespace is compressed for all page objects with extract=True
# This is minimal calculation overhead but renders more precise target bboxes for the page objects
ENABLE_COMPRESS_WHITESPACE_FOR_EXTRACTED_FIELDS = True

# Do mark all VISUAL page objects with lower confidence than this as visible=False
# Currently works only on page objects detected by graphical classifier
PAGE_OBJECT_MIN_VISIBLE_CONFIDENCE = float(
    os.getenv("PAGE_OBJECT_MIN_VISIBLE_CONFIDENCE", "0.8")
)

# If this is set to True then an Excel file will be kept as an Excel if the immutable_file flag is set for
# the corresponding DocumentCat. Else everything is printed to PDF.
# TODO: Improve print-to-page-width with https://stackoverflow.com/questions/30243576/openpyxl-page-setup-fit-to-vs-adjust-to
# This could also enable changing xlsm to xlsx
KEEP_IMMUTABLE_FILE_ORIGINAL = False

WIDTH_PIXELS_DEBUG = int(os.getenv("WIDTH_PIXELS_DEBUG", "1800"))

PAGE_ANALYSIS_REQUEST_NAME = os.getenv(
    "PAGE_ANALYSIS_REQUEST_NAME", "hyextract.PageAnalysisRequest"
)

ASYNC_ROT_DECIDER_QUEUE_NAME = os.getenv(
    "ASYNC_ROT_DECIDER_QUEUE_NAME", "RotDecider.ImagePredictRequestV1"
)

SEARCHABLE_PAGE_CACHE_ENABLED = strtobool(
    os.getenv("SEARCHABLE_PAGE_CACHE_ENABLED", default="False")
)

# # If this is true then everything that is not str or int is serialized as a json in the page_object.value before


# Open webbrowser with relevant offline dossier after running a DossierTest yes/no
ENABLE_PYTEST_SHOW_WEBBROWSER = strtobool(
    os.getenv("ENABLE_PYTEST_SHOW_WEBBROWSER", default="False")
)

# Running single page pytest evaluation with storing result in constants.OUTPUT_DOSSIER_PREVIEW
ENABLE_PYTEST_SHOW_PAGE = strtobool(
    os.getenv("ENABLE_PYTEST_SHOW_PAGE", default="False")
)

ENABLE_FINHURDLE_HANDLING = strtobool(
    os.getenv("ENABLE_FINHURDLE_HANDLING", default="True")
)
ENABLE_FINHURDLE_SENTIMENT_ANALYSIS = strtobool(
    os.getenv("ENABLE_FINHURDLE_SENTIMENT_ANALYSIS", default="True")
)
FINHURDLE_MIN_VISIBLE_CONFIDENCE_THRESHOLD = float(
    os.getenv("FINHURDLE_MIN_VISIBLE_CONFIDENCE_THRESHOLD", default=0.5)
)

# If this is True then finhurdles above threshold are set to visible=True, else all finhurdles are
# visible==False even if above threshold
ENABLE_FINHURDLE_VISIBILITY = strtobool(
    os.getenv("ENABLE_FINHURDLE_VISIBILITY", default="False")
)

# If this is enabled, only highest rated finhurdle token per 'FinHurdle' is shown. Default for production is True
# If False, all finhurdles are shown even though they can overlap
ENABLE_FINHURDLE_BEST_ITEM_PER_PAGE = strtobool(
    os.getenv("ENABLE_FINHURDLE_BEST_ITEM_PER_PAGE", default="True")
)

# If this is True then layoutlm extraction will be performed on sales documentation pages.
# Else this is skipped.
ENABLE_SALES_DOCUMENT_EXTRACTION = strtobool(
    os.getenv("ENABLE_SALES_DOCUMENT_EXTRACTION", default="False")
)


# If this is true the imagepredict service will be called to fetch additional page objects
ENABLE_IMAGEPREDICT_PROCESSING = strtobool(
    os.getenv("ENABLE_IMAGEPREDICT_PROCESSING", default="False")
)

# All confidences from imagepredict are multiplied with this factor (will be 1.00 at max)
IMAGEPREDICT_CONFIDENCE_FACTOR = float(
    os.getenv("IMAGEPREDICT_CONFIDENCE_FACTOR", default=0.98)
)

# All page objects from imagepredict with confidence below this value are invisible (after IMAGEPREDICT_CONFIDENCE_FACTOR)
IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD = float(
    os.getenv("IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD", default=0.5)
)
# If this is true then add a '[EXTRA TEXT]' prefix to all text page objects NOT created by imagepredict. Else no prefix
USE_IMAGEPREDICT_DEBUG_PREFIX = strtobool(
    os.getenv("USE_IMAGEPREDICT_DEBUG_PREFIX", default="False")
)

# If this is True the filename property for semantic documents will be computed. This is only necessary for the
# offline version and should not be used for staging / prod
GENERATE_OFFLINE_FILENAME = strtobool(
    os.getenv("GENERATE_OFFLINE_FILENAME", default="False")
)


# This flag is used during migration to new concept of document cat + title_suffix
# Should be set to False once the Frontend is capable of handling the new title suffix feature.
GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS = strtobool(
    os.getenv("GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS", default="False")
)

# Flag if filename and title should have a prefix to show legacy values
# On production this should be False to have "normal" legacy filenames. Can be set to true in development to
# distinguish between new titles composed of doccat + title_suffix and legacy titles
MARK_TITLE_AS_LEGACY = strtobool(os.getenv("MARK_TITLE_AS_LEGACY", default="False"))

# Prefix to be set before title if MARK_TITLE_AS_LEGACY is enabled
LEGACY_TITLE_PREFIX = "LEGACY_"

SPACY_REMOVE_MULTIPLE_SPACES = True

ENABLE_BOUNDED_BOX_FOR_EXTRACTION: bool = False

# If Spacy language detection decides with this confidence that it is non of language_detector.ALL_LANGUAGES then
# we consider this a page that is "certainly" in a non supported language. UnknownPageParser will apply but lang of
# page will be set to default language, so 'de'
MIN_CONFIDENCE_FOR_NON_SUPPORTED_LANGUAGES = 0.99


# If this is True, the small_files_filter is applied and the flag SKIP_SMALL_FILES
# is used. Else small files are just handled normally (not recommended for production)
APPLY_SMALL_FILES_FILTER = strtobool(os.getenv("APPLY_SMALL_FILES_FILTER", "True"))

# If this is True then small files are skipped without producing an exception,
# else an exception which will be visible to the user is created
# E.g. needs to be turned off to test the eicar virus test file as this is small
SKIP_SMALL_FILES = strtobool(os.getenv("SKIP_SMALL_FILES", "True"))


# If this is enabled PDFs with large resolution will be scaled down to A4 before handing them to ABBYY.
# This is needed because ABBYY can be stuck with super large PDFs (e.g. 3x7 meters)
ENABLE_PDF_DOWNSCALING = True

# If true, then salary in SALARY_CERTIFICATE and PAYSLIP. Else these fields are skipped.
ENABLE_LONG_FILENAMES = False


PRETTY_PRINT_LOG = strtobool(os.getenv("PRETTY_PRINT_LOG", "True"))

# Maximum time per individual conversion process after which we give up
# With retries several ones can be chained so total time can be higher
# Ghostscript can take e.g. 50 seconds for 500 pages so we make this longer.
TIMEOUT_PDF_CONVERSION_SECONDS: float = float(
    os.getenv("TIMEOUT_PDF_CONVERSION_SECONDS", default=75)
)

# Maximum number of tries to call FREP to process a single PDF page
# As long as there is no DLX it makes sense to retry. Afterwards this can be set to 1
MAX_TRIES_FREP_FOR_PDF: int = int(os.getenv("MAX_TRIES_FREP_FOR_PDF", default=3))


# Every filename longer than this will be shortened accordingly
MAX_FILENAME_LENGTH_FOR_PROCESSING: int = int(
    os.getenv("MAX_FILENAME_LENGTH_FOR_PROCESSING", default=175)
)

# Filename suffix that will be applied if a filename is longer than
# MAX_FILENAME_LENGTH_FOR_PROCESSING. If empty, no suffix is applied
# Suffix will be added before extension, e.g. "somename_longfilename.pdf"
SUFFIX_FOR_SHORTENED_FILENAME: str = os.getenv(
    "SUFFIX_FOR_SHORTENED_FILENAME", default="_cutfilename"
)

# Whether to Enable Antivirus Scan - currently just ClamAV
ENABLE_ANTIVIRUS_CLAMAV = strtobool(
    os.getenv("ENABLE_ANTIVIRUS_CLAMAV", default="False")
)

ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER = strtobool(
    os.getenv("ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER", default="False")
)

ENABLE_ANY_ANTIVIRUS = ENABLE_ANTIVIRUS_CLAMAV or ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER

# If there's a failure in the AV scan processing, raise an exception and let file_process_worker.py fail
ENABLE_ANTIVIRUS_RAISE_EXCEPTION = strtobool(
    os.getenv("ENABLE_ANTIVIRUS_RAISE_EXCEPTION", default="False")
)

# The request and response routing keys for the hyscan service - ClamAV
HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY = os.getenv(
    "HYSCAN_CLAMAV_SCAN_REQUEST_ROUTING_KEY", default="Hyscan.Clamav.Request.RoutingKey"
)

# The request and response routing keys for the hyscan service - Microsoft Defender
HYSCAN_MICROSOFTDEFENDER_SCAN_REQUEST_ROUTING_KEY = os.getenv(
    "HYSCAN_MICROSOFTDEFENDER_SCAN_REQUEST_ROUTING_KEY",
    default="Hyscan.MicrosoftDefender.Request.RoutingKey",
)

ORIGINAL_FILE_PROCESSOR_BUCKET = os.getenv(
    "ORIGINAL_FILE_PROCESSOR_BUCKET", default="processing"
)

# Timeout for request to hyscan service in seconds
HYSCAN_REQUEST_TIMEOUT = int(os.getenv("HYSCAN_REQUEST_TIMEOUT", default=5))

# If disable test's that call function DossierTest and therefore need OCR enabled
DISABLE_TEST_BATCH = strtobool(os.getenv("DISABLE_TEST_BATCH", default="False"))

# We use caching to speed up the tests - especially the ones that need things from other microservices
# that are not code based off hyextract. Additionally things like the hymsread
# use abby finereader - which costs use per page used. So we want to cache the results

# Ok, we have two types of flag for test caching
# e.g. TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED and TEST_CACHE_DETECT_PAGE_OBJECTS_FORCE_OVERWRITE
# The first one is the main flag to enable caching, the second one is to force overwrite of cache files
# example usage: we update page classifier and want to force overwrite of cache files
# so we run all unit tests, then we set TEST_CACHE_DETECT_PAGE_OBJECTS_FORCE_OVERWRITE to True
# and rerun the tests that failed before and afterwards unset the FORCE_OVERWRITE  flag again

TEST_CACHE_DETECT_PAGE_OBJECTS_FORCE_OVERWRITE = strtobool(
    os.getenv("TEST_CACHE_DETECT_PAGE_OBJECTS_FORCE_OVERWRITE", default="False")
)

TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED = strtobool(
    os.getenv("TEST_CACHE_DETECT_PAGE_OBJECTS_ENABLED", default="True")
)

TEST_CACHE_CLASSIFY_PAGE_OBJECTS_FORCE_OVERWRITE = strtobool(
    os.getenv("TEST_CACHE_CLASSIFY_PAGE_OBJECTS_FORCE_OVERWRITE", default="False")
)

TEST_CACHE_CLASSIFY_PAGE_OBJECTS_ENABLED = strtobool(
    os.getenv("TEST_CACHE_CLASSIFY_PAGE_OBJECTS_ENABLED", default="True")
)

TEST_CACHE_IMAGEPREDICT_ENABLED = strtobool(
    os.getenv("TEST_CACHE_IMAGEPREDICT_ENABLED", default="True")
)

# Whether to ignore existing cache files and force overwrite of cache files
TEST_CACHE_IMAGEPREDICT_FORCE_OVERWRITE = strtobool(
    os.getenv("TEST_CACHE_IMAGEPREDICT_FORCE_OVERWRITE", default="False")
)

TEST_FORCE_OVERWRITE_OCR_CACHE = strtobool(
    os.getenv("TEST_FORCE_OVERWRITE_OCR_CACHE", default="False")
)


TEST_CACHE_BUCKET = os.getenv("TEST_CACHE_BUCKET", default="test-cache-bucket")


# If a document has more than this number of pages we change the document category
# to UNKNOWN. This applies only to certain doc cats where we have a wild mix
# of included document category, e.g. SALES_DOCUMENTATION, PRPERTY_ESTIMATION,
# PROPERTY_INFO. Other categories can have very long documents which are correct
# (e.g. PROPERTY_DESCRIPTION) so this setting should not affect these documents
MIN_NUM_PAGES_UNKNOWN_OVERRIDE = int(
    os.getenv("MIN_NUM_PAGES_UNKNOWN_OVERRIDE", default=40)
)

# Whether the validate_pdf_in_minio function should open the pdf and check if it is valid (True)
# or just check the file mine type (False)
VALIDATE_PDF_CONTENT_VIA_PYPDF = os.getenv(
    "VALIDATE_PDF_CONTENT_VIA_PYPDF", default="True"
)

# Whether to enable the PDF repair functionality using Cairo if an OCR failure occurs
TRY_PDF_REPAIR_CAIRO = strtobool(os.getenv("TRY_PDF_REPAIR_CAIRO", default="True"))

# Whether extract_attachments and unpack_msg should convert the email content to PDF
# Otherwise just the email attachments are converted to PDF
CONVERT_EMAIL_CONTENT_TO_PDF = strtobool(
    os.getenv("CONVERT_EMAIL_CONTENT_TO_PDF", default="True")
)

# When converting the body of an email into pdf, max number of pages in new pdf
MAX_NUM_PAGES_EMAIL_CONTENT_TO_PDF = int(
    os.getenv("MAX_NUM_PAGES_EMAIL_CONTENT_TO_PDF", default=15)
)

# For some broken msg files the extraction of the html is stuck indefinitely.
# Abort after some seconds to continue without HTML body (or without email)
EMAIL_CONTENT_TO_PDF_PROCESSING_TIMEOUT_SECONDS = int(
    os.getenv("EMAIL_CONTENT_TO_PDF_PROCESSING_TIMEOUT_SECONDS", default=25)
)

# Whether to enable deduplication of email content based on content hash
# When enabled, emails with identical content will only generate one PDF file
ENABLE_EMAIL_CONTENT_DEDUPLICATION = strtobool(
    os.getenv("ENABLE_EMAIL_CONTENT_DEDUPLICATION", default="True")
)
