import zipfile
from itertools import islice
from pathlib import Path
from tempfile import TemporaryDirectory

import natsort

from analytics.semantic_documents2xlsx import analyse_semantic_dossier_in_folder
from constants import BASE_DIR

import structlog

logger = structlog.getLogger(__name__)


# base = Path("/home/<USER>/Documents/output/fs24")
# base = Path("/home/<USER>/Documents/output/hbl_210317")
# base = Path("/home/<USER>/Documents/output/hbl_210408")
# base = Path("/home/<USER>/Documents/output/axa_hd1_v3")
# base = Path("/home/<USER>/Documents/output/hbl_210408_v4")
# base = Path("/home/<USER>/Documents/output/hbl_210423_v1")


def add_packages(base, zip_file, pattern):
    dossier: Path
    for dossier in list(islice(natsort.os_sorted(base.glob(pattern)), 0, None)):
        try:
            dossier_name = dossier.name
            package = dossier / "package"

            if not package.exists():
                continue

            for file in package.glob("**/*"):
                if file.is_file():
                    zip_file.write(file, Path(dossier_name) / file.relative_to(package))
        except:
            logger.exception("could not copy file")


def add_niners(base, zip_file, pattern):
    dossier: Path
    for dossier in list(islice(natsort.os_sorted(base.glob(pattern)), 0, None)):
        try:
            dossier_name = dossier.name
            package = dossier / "package"

            if not package.exists():
                continue

            for file in package.glob("*.pdf"):
                if file.is_file() and file.name.startswith("9"):
                    zip_file.write(
                        file, f"9er/{file.name}_d_{dossier_name}{file.suffix}"
                    )

        except:
            logger.exception("could not copy file")


def add_summary(base, zip_file):
    with TemporaryDirectory() as temp_dir:
        summary_file = Path(temp_dir) / "summary.xlsx"
        analyse_semantic_dossier_in_folder(base, summary_file)
        zip_file.write(summary_file, summary_file.name)


if __name__ == "__main__":
    base = Path("/home/<USER>/Documents/output/axa_hd1_v4")
    base = Path(f"{BASE_DIR}/output/asyncizer_output_hbl_final_3")

    p = base.with_suffix(".zip")

    # target_path = Path(f'{BASE_DIR}/output/asyncizer_output_migros_selection_analysis')
    # target_path.mkdir(exist_ok=True, parents=True)
    # target_file_path = target_path / p.name

    use_folders_instead_of_zip = True

    pattern = "*/" if use_folders_instead_of_zip else "*.zip"

    with zipfile.ZipFile(p, "w") as zip_file:
        add_packages(base, zip_file, pattern)
        add_niners(base, zip_file, pattern)
        add_summary(base, zip_file)

        zip_file.close()
        # shutil.copyfile(str(p), str(target_file_path))

        print(f"Copied target zip file to {p}")
