from typing import Dict, Optional

from pydantic.main import BaseModel

from global_settings import SKIP_SMALL_FILES

# Ignore all images smaller than this because we assume they are icons and logos embedded in the email
# Icons are normally below 1kb
# 210629 mt: changed from 7kb to 10kb
SMALL_FILES_FILTER_LIMIT = 10 * 1024


BIG_FILES_FILTER_LIMIT = 1024 * 1024 * 1024  # 1GB - large enough for everyting

MIN_SIZE_EMAIL_ATTACHMENT_UNKOWN_TYPE_FILTER_LIMIT = 100


class SmallFilesFilter(BaseModel):
    small_files_map: Dict[str, int]

    # If this is True then small files are skipped without producing an exception, else an exception which
    # will be visible to the user is created
    skip_small_files: bool


def create_small_files_filter(
    custom_mapping: Optional[Dict[str, int]] = None,
) -> SmallFilesFilter:
    sff = SmallFilesFilter(
        small_files_map={
            ".png": SMALL_FILES_FILTER_LIMIT,
            ".jpg": SMALL_FILES_FILTER_LIMIT,
            ".jpeg": SMALL_FILES_FILTER_LIMIT,
            ".gif": SMALL_FILES_FILTER_LIMIT,
            ".heic": SMALL_FILES_FILTER_LIMIT,
            # These are textual attachments added by MS Exchange if inline attachments are present:
            # http://kb.mit.edu/confluence/pages/viewpage.action?pageId=4981187
            # Filename e.g. ATT00001.txt or ATT00001.c
            ".c": SMALL_FILES_FILTER_LIMIT,
            ".txt": 1 * 1024,
            ".htm": SMALL_FILES_FILTER_LIMIT,
            ".html": SMALL_FILES_FILTER_LIMIT,
            ".pdf": 1 * 1024,  # changed to 1kb after bekb showed a pdf with 7kb
            ".css": BIG_FILES_FILTER_LIMIT,  # Skip all these silently
            ".vcf": BIG_FILES_FILTER_LIMIT,  # Skip all these silently
            # This is a JS from HypoDossier, should be ignored
            "js.herunterladen": BIG_FILES_FILTER_LIMIT,
        },
        skip_small_files=SKIP_SMALL_FILES,
    )
    if custom_mapping:
        for key, val in custom_mapping.items():
            sff.small_files_map[key] = val

    return sff


DEFAULT_SMALL_FILES_FILTER = create_small_files_filter()
