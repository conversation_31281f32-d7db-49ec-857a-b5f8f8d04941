from dataclasses import dataclass
from typing import Dict

from abbyyplumber.converter.ValueConverter import (
    Value<PERSON>onverter,
    CleanNameConverter,
    MostRecentDateConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElement,
    SearchElementConstrainedArea,
)
from hypodossier.core.documents.bank_account.BankAccountPageData import (
    FIELDS_BANK_ACCOUNT,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_COMPANY,
    <PERSON><PERSON><PERSON><PERSON>ield,
    FIELD_FULLNAME,
    FIELD_DOCUMENT_DATE,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
    default_field,
)


@dataclass
class MortgageFrameworkContractSearchElements:
    document_date: SearchElement = None
    company: SearchElement = None

    # Name of person as derived from address_block
    fullname: SearchElement = None

    default_converters: Dict[SemanticField, ValueConverter] = default_field(
        {
            FIELD_FULLNAME.name: CleanNameConverter(max_num_lines=1),
            FIELD_DOCUMENT_DATE.name: MostRecentDateConverter(),
        }
    )


@dataclass
class TemplateMortgageFrameworkContractPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.MORTGAGE_FRAMEWORK_CONTRACT
    company: str = None

    se: MortgageFrameworkContractSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_BANK_ACCOUNT.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_mortgage_framework_contract():
    parsers = [
        TemplateMortgageFrameworkContractPageParser(
            desc="ZKB Rahmenvertrag 1-1LSNP9A6 2021",
            ranked_titles_all=[RankedTitle("Rahmenvertrag für Hypotheken")],
            required_text_conditions=[
                FromStartTextCond("zwischen", num_lines=20),
                FromStartTextCond("Darlehensnehmer", num_lines=20),
                FromStartTextCond(
                    "Zürcher Kantonalbank (nachstehend Bank genannt)", num_lines=20
                ),
            ],
            company="ZKB",
            se=MortgageFrameworkContractSearchElements(
                fullname=SearchElementConstrainedArea(
                    None,
                    None,
                    text_top="zwischen",
                    text_bottom="(nachstehend Darlehensnehmer genannt)",
                    text_right=", geb.",
                    text_right_offset_chars=1,
                ),
            ),
        ),
    ]
    return parsers
