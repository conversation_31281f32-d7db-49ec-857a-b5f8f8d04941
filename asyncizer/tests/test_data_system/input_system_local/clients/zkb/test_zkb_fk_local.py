from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "new_fk"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/zkb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_zkb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_oberzolldirektion_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "oberzolldirektion" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.ZKB_19643_AUSKUNFT_OBERZOLLDIREKTION_OZD_FUER_LSVA: 1
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_oberzolldirektion_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "oberzolldirektion" / "many",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.ZKB_19643_AUSKUNFT_OBERZOLLDIREKTION_OZD_FUER_LSVA: 16
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_erfassungsblatt_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "erfassungsblatt" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.ZKB_19807_ERFASSUNGSGESCHAEFT_HANDELSGESCHAEFTE: 2
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_erfassungsblatt_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "erfassungsblatt" / "many",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.ZKB_19807_ERFASSUNGSGESCHAEFT_HANDELSGESCHAEFTE: 15
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.ZKB_19807_ERFASSUNGSGESCHAEFT_HANDELSGESCHAEFTE: 30
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_leasingantrag_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "leasingantrag" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # DE, FR, IT documents with 2 pages each
            expected_doc_cat_frequency_per_page={
                DocumentCat.ZKB_18401_LEASINGANTRAG_FIRMENANGABE: 6
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_leasingantrag_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "leasingantrag" / "many",
        # source_file_filter=["Leasingantrag_Firmenangabe_18401_20191205_4.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # All correct
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 1,
                DocumentCat.HRA: 1,
                DocumentCat.ZKB_18401_LEASINGANTRAG_FIRMENANGABE: 32,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 3,
                DocumentCat.HRA: 1,
                DocumentCat.UNKNOWN_DE: 1,
                DocumentCat.UNKNOWN_IT: 1,
                DocumentCat.ZKB_18401_LEASINGANTRAG_FIRMENANGABE: 61,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_541_accounts_receivable_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "541_accounts_receivable" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY: 1
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_541_accounts_receivable_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "541_accounts_receivable" / "many",
        # source_file_filter=["Debitoren-Kreditorenliste_17684_20201120_54.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # This doc is unknown due to try_to_find_unknown_short_doc
            # Debitoren-Kreditorenliste_17684_20201104_45.pdf
            expected_doc_cat_frequency={
                # 1 wrong, 1 unknown, rest ok
                DocumentCat.ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY: 15,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 1,
                DocumentCat.UNKNOWN_DE: 1,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_leasingrahmen_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "leasingrahmen" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.ZKB_19833_LEASING_RAHMENLIMITE: 1
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_leasingrahmen_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "leasingrahmen" / "many",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.WHITE_PAGES: 1,
                DocumentCat.ZKB_19833_LEASING_RAHMENLIMITE: 22,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.UNKNOWN: 1,
                DocumentCat.WHITE_PAGES: 1,
                DocumentCat.ZKB_19833_LEASING_RAHMENLIMITE: 25,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_534_liquidity_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "534_liquidity" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.LIQUIDITY_PLAN_COMPANY: 2}
        ),
    ).run()


@pytest.mark.asyncio
async def test_534_liquidity_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "534_liquidity" / "many",
        # source_file_filter=["Liquiditätsplan_18416_20210101_106.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 2,  # wrong but accepted
                DocumentCat.LIQUIDITY_PLAN_COMPANY: 19,
                DocumentCat.UNKNOWN_DE: 2,  # 1 correct, 1 wrong
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 8,
                DocumentCat.LIQUIDITY_PLAN_COMPANY: 42,
                DocumentCat.UNKNOWN_DE: 4,
                DocumentCat.UNKNOWN_EN: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_unternehmensprofil_one():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "unternehmensprofil" / "one",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency_per_page={DocumentCat.LIQUIDITY_PLAN_COMPANY: 2}
        ),
    ).run()


@pytest.mark.asyncio
async def test_unternehmensprofil_many():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "unternehmensprofil" / "many",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ZKB_19747_UNTERNEHMENSPROFIL: 17},
            expected_doc_cat_frequency_per_page={
                DocumentCat.PROPERTY_VALUATION: 1,
                DocumentCat.UNKNOWN: 2,
                DocumentCat.UNKNOWN_DE: 3,
                DocumentCat.ZKB_19747_UNTERNEHMENSPROFIL: 79,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_unternehmensprofil_many2():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "unternehmensprofil" / "many2",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ZKB_19747_UNTERNEHMENSPROFIL: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
                DocumentCat.ZKB_19747_UNTERNEHMENSPROFIL: 4,
            },
        ),
    ).run()
