from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssie<PERSON><PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/swisslife"
)
dest_folder_prefix = "input_system_clients_swisslife_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_mortgage_request_sl():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_offer",
        source_file_filter=["Antrag.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.FINANCING_CHECKLIST_DOCUMENTS: 1,
                DocumentCat.FINANCING_OFFER: 5,
                DocumentCat.MORTGAGE_REQUEST_FORM: 1,
            },
            expected_doc_cat_frequency={DocumentCat.FINANCING_OFFER: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_mortgage_request_sl_swissfex():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_offer",
        source_file_filter=["Angebot SwissFEX.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.FINANCING_OFFER: 4}
        ),
    ).run()


@pytest.mark.asyncio
async def test_offer_proposal():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_offer",
        source_file_filter=["Offerte.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.FINANCING_OFFER: 5}
        ),
    ).run()


@pytest.mark.asyncio
async def test_broker_auth_sl_de():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_broker_auth",
        source_file_filter=["SFX_unterzeichnet.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.BROKER_MANDATE: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_broker_auth_sl_it():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_broker_auth",
        source_file_filter=["swisslife_auth_it.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.BROKER_MANDATE: 2},
            expected_page_objects={0: {"company": "Swiss Life"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_auszahlungsbeleg():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_internal",
        source_file_filter=["Auszahlungsbeleg.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.FINANCING_MISC: 1},
            expected_page_objects={
                0: {"company": "Swiss Life", "document_title": "Auszahlungsbeleg"}
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_worldcheck():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_internal",
        source_file_filter=["worldcheck.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "15.05.2020",
                    "document_title": "World-Check",
                    "fullname": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency_per_page={DocumentCat.FINANCING_MISC: 2},
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_auftrag_buha():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_internal",
        source_file_filter=["Auftrag_BUHA.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {"company": "Swiss Life", "document_title": "Auftrag an Buchhaltung"}
            },
            expected_doc_cat_frequency_per_page={DocumentCat.FINANCING_MISC: 1},
        ),
    ).run()


@pytest.mark.skip(
    reason="printing of xlsx to pdf yields bad page splitting - currently not useful"
)
@pytest.mark.asyncio
async def test_sl_pricing_excel():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_pricing_excel",
        source_file_filter=["pricing.xlsx"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                # DocumentCat.BROKER_MANDATE: 1,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_ocr_pdf_corrupt():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_ocr" / "eigenmittel_corrupt",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                # No document here as we have an error
            },
            expected_num_processing_exceptions=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_ocr_grundriss_ok():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "221101_ocr" / "grundriss",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALES_DOCUMENTATION: 1}
        ),
    ).run()
