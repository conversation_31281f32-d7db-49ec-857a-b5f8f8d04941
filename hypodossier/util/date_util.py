import re
import sys
import time
from datetime import datetime, date
from typing import List

from babel.dates import format_date


def get_localized_month(month_index: int, locale="en"):
    # changed because runs on windows only and leads to possible bad side effects
    # see https://stackoverflow.com/questions/985505/locale-date-formatting-in-python

    return format_date(date(2007, month_index, 1), format="MMMM", locale=locale)


def replace_all(s: str, list_strings: List[str], replacement: str):
    val = s
    for i in list_strings:
        val = val.replace(i, replacement)
    return val


def find_date(s: str, convert_text_months: bool = True, do_raise_exception=False):
    results = find_all_dates(s, convert_text_months, do_raise_exception, 1)
    if results:
        return results[0]


MAX_YEAR_FOR_BIRTH_DATE_DETECTION = 2005


def find_earliest_date(
    s, convert_text_months: bool = True, max_year=MAX_YEAR_FOR_BIRTH_DATE_DETECTION
):
    results = find_all_dates(s, convert_text_months)
    if results:
        results_sorted = sort_dates(results)
        d = results_sorted[0]
        if not max_year:
            return d
        year = int(d[-4:])
        if year < max_year:
            return d


def find_most_recent_date(s, convert_text_months: bool = True):
    results = find_all_dates(s, convert_text_months)
    if results:
        results_sorted = sort_dates(results)
        results_sorted.reverse()
        today = datetime.today()
        for date_string in results_sorted:
            d = datetime.strptime(date_string, "%d.%m.%Y")
            if d < today:
                return date_string


def find_all_dates(
    s: str,
    convert_text_months: bool = True,
    do_raise_exception=False,
    max_num_matches=sys.maxsize,
    birthday_mode=False,
):
    if not s or not s.strip():
        return

    val = s

    # Add spaces to ensure that date is never at beginning / end of string.
    # This is needed for \D match below
    val = f" {val} "

    val = val.replace(",", ".")

    if convert_text_months:
        # E.g. 'Bern, 5. März 2016' -> '5.03.2016'
        # Month is always formatted in 2 digits, day not
        val = val.lower()
        langs = ["de", "fr", "it", "en"]
        for i in range(1, 13):  # must be 13 because it stops before stop at 12
            replacement_list = []
            for lang in langs:
                replacement_list.append(get_localized_month(i, lang).lower())
            zero = "0" if i < 10 else ""

            # Add an extra space to support stuff like '1. Januar2020 as we handle optional spaces later generously
            val = replace_all(val, replacement_list, f" {zero}{i}. ")

    regex = r"""
                        \D              # Non-Digit (if there was a digit then we have a 3 digit sequence). We add a leading space above to the string
                        (\d\d?)         # day
                        \s?             # optional space
                        [.,\s\/]        # can also be that there is only a space like in '1 Januar 2020'
                        \s?             # optional space
                        \s?             # optional space
                        (\d\d?)         # month
                        [.,\s\/]
                        \s?             # optional space
                        \s?             # optional space
                        (20|19|2\s0|1\s9)\s?(\d\s?\d)   # year (20 or 19 then always 2 digit)
                    """

    # Group 1 = day, Group 2 = Month, Group 3 = 19|20, Group 4 = YY (last two digits)

    results = []

    matches = re.findall(regex, val, re.VERBOSE)
    if not matches:
        # On second try make the year YY instead of YYYY
        regex = regex.replace("""(20|19|2\s0|1\s9)""", """(20|19|2\s0|1\s9)?""")
        matches = re.findall(regex, val, re.VERBOSE)

    #    single_match = re.search(regex, val, re.VERBOSE)
    #    if not single_match:
    #        # On second try make the year YY instead of YYYY
    #        regex = regex.replace("(20|19|2\s0|1\s9)", "(20|19|2\s0|1\s9)?")
    #        single_match = re.search(regex, val, re.VERBOSE)
    #    match = (single_match.group(1), single_match.group(2), single_match.group(3), single_match.group(4))

    for match in matches:
        year = match[3].replace(" ", "")
        if match[2]:
            year = f'{match[2].replace(" ", "")}{year}'
        else:
            if birthday_mode:
                try:
                    year_int = int(year)
                    current_year = int(time.strftime("%y", time.localtime()))

                    # 77->1977, 17->2017
                    year = f"20{year}" if year_int < current_year else f"19{year}"
                except Exception as err:
                    if do_raise_exception:
                        raise ValueError(
                            f'Could not extract date from text="{s}. err={err}"'
                        )

            else:
                year = f"20{year}"

        day = int(match[0])
        str_day = f"0{day}" if day < 10 else str(day)
        month = int(match[1])
        str_month = f"0{month}" if month < 10 else str(month)

        p = f"{str_day}.{str_month}.{year}"
        try:
            d = datetime.strptime(p, "%d.%m.%Y")
            if d:
                results.append(p)
        except ValueError as err:
            if do_raise_exception:
                raise ValueError(
                    f'Invalid date "{p}" extracted from text="{s}. err={err}"'
                )

    if not results:
        # Try YYYY-MM-DD format
        d = parse_iso8601_date_from_string(s)
        if d:
            results.append(d.strftime("%d.%m.%Y"))

    return results


def parse_iso8601_date_from_string(s: str):
    """

    :param s: Input string with date in ISO 8601 format or in YYYY-MM-DD format.
    :return:
    """

    # Try parsing full ISO 8601 timestamp with timezone.
    try:
        return datetime.fromisoformat(s)
    except ValueError:
        pass

    # Strictly match YYYY-MM-DD where year starts with 19 or 20
    match = re.search(r"\b(19|20)\d{2}-\d{2}-\d{2}\b", s)
    if match:
        try:
            return datetime.strptime(match.group(), "%Y-%m-%d")
        except ValueError:
            pass

    return None


def sort_dates(dates: List[str]) -> List[str]:
    return sorted(dates, key=lambda x: datetime.strptime(x, "%d.%m.%Y"))


def convert_month_name_to_index(month):
    months = {
        "de": [
            "januar",
            "februar",
            "märz",
            "april",
            "mai",
            "juni",
            "juli",
            "august",
            "september",
            "oktober",
            "november",
            "dezember",
        ],
        "en": [
            "january",
            "february",
            "march",
            "april",
            "may",
            "june",
            "july",
            "august",
            "september",
            "october",
            "november",
            "december",
        ],
        "fr": [
            "janvier",
            "février",
            "mars",
            "avril",
            "mai",
            "juin",
            "juillet",
            "août",
            "septembre",
            "octobre",
            "novembre",
            "décembre",
        ],
        "it": [
            "gennaio",
            "febbraio",
            "marzo",
            "aprile",
            "maggio",
            "giugno",
            "luglio",
            "agosto",
            "settembre",
            "ottobre",
            "novembre",
            "dicembre",
        ],
    }
    val = month.lower()
    for lang, months in months.items():
        if val in months:
            return months.index(val) + 1
    return None


def format_timestamp(str_timestamp, empty_value="", add_time=False) -> str:
    ret = empty_value
    try:
        d = datetime.strptime(str_timestamp, "%Y-%m-%d %H:%M:%S")
        if d:
            return d.strftime("%d.%m.%Y")
    except ValueError:
        pass
    try:
        d = datetime.strptime(str_timestamp, "%d.%m.%Y")
        if d:
            return d.strftime("%d.%m.%Y")
    except ValueError:
        pass
    return ret


if __name__ == "__main__":
    print(f"test 1={format_timestamp('1977-09-04 11:12:13')}")
    print(f"test 2={format_timestamp('04.09.1977')}")
    print(f"test 2={format_timestamp('4.9.1977')}")
