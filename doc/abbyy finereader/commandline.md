FineCmd.exe PRESS2.TIFF /lang Mixed /out C:\temp\result.txt /quit

"C:\Program Files (x86)\ABBYY FineReader 15\FineCmd.exe" C:\temp\330.pdf /lang Mixed /optionsFile batch_options.xml /out C:\temp\outout77.txt /quit 

"C:\Program Files (x86)\ABBYY FineReader 15\FineCmd.exe" C:\temp\330.pdf /lang Mixed /out C:\temp\outout77.pdf /quit

general command line:  <open_keys/scanning> [<recognition_keys>] [<export_keys>] 

  <open_keys/scanning> ::= ImageFiles | /scan [SourceName] | /file [filename1 filename2], where
    ImageFiles - list of files for recognition
    SourceName - images source (scanner); if not specified, current is used
    filename.. -  list of files for recognition

  <recognition_keys> ::= [/lang Language] [/optionsFile OptionsFileName], where
    Language - name of language in English (russian, greek, Mixed)
    OptionsFileName - path to options file

  <export_key> ::= /out ExportFile | /send Target, where
    ExportFile - name of file with extension to save file to
      (txt, rtf, doc, docx, xml, htm(l), xls, xlsx, ppt, pptx, pdf, dbf, csv, lit); 
    Target - name of target app where to open
      (MSWord, MSExcel, WordPro, WordPerfect, StarWriter, Mail, Clipboard, WebBrowser, Acrobat, PowerPoint)