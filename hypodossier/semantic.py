import os
from collections import defaultdict
from copy import copy
from pathlib import Path
from statistics import harmonic_mean
from typing import List, Optional, Dict
import structlog

import sentry_sdk
from hdapii.processing.hyextract import PageObjectSource

import hypodossier.shared as shared
from asyncizer.email_body import is_email_body_filename
from asyncizer.processing_config import SemanticDocumentSplittingStyle
from asyncizer.semantic_document_processing_config import (
    SemanticDocumentsProcessingConfig,
    DEFAULT_SEMANTIC_DOCUMENTS_PROCESSING_CONFIG,
)
from classifier.spacy_classification import FILE_TEXT_SPACY_CLASSIFIER_NAME
from global_settings import KEEP_IMMUTABLE_FILE_ORIGINAL, GENERATE_OFFLINE_FILENAME
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.DocumentTopic import DocumentTopic
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import ExtractionType
from hypodossier.core.domain.document_category_util import (
    is_doc_cat_valid_as_independent_document,
    get_document_cat_by_name,
)
from hypodossier.document_cat_util import (
    documentcategory_from_doccat,
    pagecategory_from_pagecat,
)
from hypodossier.semantic_document_cut import (
    check_document_plausibility,
    cut2semantic_document,
    try_to_identify_long_document,
    extract_unknown_sem_pages,
    cut_semantic_pages,
    try_to_identify_tax_forms,
    try_to_find_unknown_short_doc,
    try_to_find_known_short_cuts,
    cut2immutable_file_document,
    get_doc_cats,
    create_unknown_document,
    ShortCutConfig,
    KNOWN_COMPOSITION_CUTS,
)
from hypodossier.semantic_graphical_page_object import (
    calc_graphical_page_object_validity,
)
from hypodossier.semantic_page_object_configuration import (
    page_object_configurations,
    PageObjectConfiguration,
)

" This module creates a semantic document based on processed files "

logger = structlog.getLogger(__name__)

# These extensions can be read by ABBYY but we do not rely on classification and want the unchanged file
# in the result package
LIST_IMMUTABLE_SEARCHABLE_FILE_EXTENSIONS = [".xls", ".xlsx"]

# Minimum value for confidence for top file classification so it will be considered. Else ignore top file classification
FILE_CLASSIFICATION_CONFIDENCE_MIN_THRESHOLD: float = 0.5


def try_to_identify_immutable_file(
    filename: str,
    all_semantic_pages,
    top_file_classification: shared.Classification,
    client_lang: str = "de",
    company_name: Optional[str] = None,
) -> List[shared.SemanticDocument]:
    """
    'Immutable searchable' are XLS and XLSX (later XLSM) that we can analyse but which could contain bad print
    settings or multiple sheets or many sheets

    Categories:
        1: known documents (MB Tragbarkeit)
            -> easy solution (same as category 2) for now: pdf with our filename
            TODO: IMMUTABLE, our filename (you want to keep MB Tragbarkeitsexcel unchanged but put a nice filename)
            -> In Frontend: No DnD, no reclassification, currently no renaming, mark as Excel with Excel - Logo

        2: nice, one page (handled by classifier with high confidence)
            -> handle as normal 1-page PDF -> use normal generated classified filename

        3: all others:
            - one page with low confidence
            - badly formatted (therefore multiple pages)
            - too large (many sheets or rows, more columns than 1 page)
            -> MUST BE EXCEL -> IMMUTABLE with prefix ‘910 XLS', e.g. '910 XLS asdfasfd.xlsx’
            -> In Frontend: No DnD, no reclassification, currently no renaming, mark as Excel with Excel - Logo

    :param filename: Filename of original file, can contain folder name if in zip/eml
    :param all_semantic_pages:
    :return:
    """
    all_docs: List[shared.SemanticDocument] = []

    stem, file_extension = os.path.splitext(filename)
    if file_extension in LIST_IMMUTABLE_SEARCHABLE_FILE_EXTENSIONS:
        if KEEP_IMMUTABLE_FILE_ORIGINAL:
            immutable_file = True

            # Remove white pages only to find out if afterwards exactly one page is left.
            known_short_cut_dicts, rest_semantic_pages = try_to_find_known_short_cuts(
                all_semantic_pages,
                client_lang,
                known_short_doc_cats={DocumentCat.WHITE_PAGES: ShortCutConfig()},
            )

            if len(rest_semantic_pages) == 1:
                # 210916 mt: for now keep all single page excels as PDF
                # Ignore the white pages here - they will be extracted later
                immutable_file = False

                # if all_semantic_pages[0].confidence >= DEFAULT_RULE_BASED_PARSER_CONFIDENCE:
                #     immutable_file = False

            if immutable_file:
                # If the immutable_file file comes as an extraction from an original file such as a zip or msg, we
                # need to skip the name of the original file (the zip) and keep only the name from the extracted file
                clean_filename = Path(filename).name
                doc = cut2immutable_file_document(
                    clean_filename,
                    all_semantic_pages,
                    client_lang=client_lang,
                    company_name=company_name,
                )
                all_docs.append(doc)

        else:
            # We do not keep the original file but we want to avoid any splitting. Also do not take out any shortcuts
            doc = try_to_identify_long_document(
                all_semantic_pages,
                top_file_classification,
                client_lang=client_lang,
                company_name=company_name,
            )
            if not doc:
                doc = create_unknown_document(
                    all_semantic_pages,
                    client_lang=client_lang,
                    company_name=company_name,
                )

            all_docs.append(doc)

    return all_docs


def create_semantic_documents_from_composition_cuts(
    composition_cuts: Dict[DocumentCat, List[shared.SemanticPage]],
    client_lang: str,
    company_name: Optional[str] = None,
) -> List[shared.SemanticDocument]:
    """
    Combine some of the composition_cuts
    :param composition_cuts: Dict
    :return: List of documents that have been created from the cuts
    """
    all_docs = []

    if (
        DocumentCat.PLAN_CADASTER in composition_cuts
        and DocumentCat.PLAN_SITUATION in composition_cuts
    ):
        composition_cuts[DocumentCat.PLAN_SITUATION] += composition_cuts.pop(
            DocumentCat.PLAN_CADASTER
        )

    for doc_cat, c in composition_cuts.items():
        # Now sort pages by doc_cat and then alphabetically by the name of the first page_object
        # (assumption: either there is only one page object or the sorting will be random)
        # Implicit assumption: sorting by name of page object works... this is currently true for photos
        # but not for 'all plans'.
        # TODO: rename plans so we have order: FLOOR, SIDE_VIEW, CADASTER, SITUATION, AERIAL if we want to combine
        # 'all plans'
        c.sort(
            key=lambda x: (
                x.document_category.name,
                x.page_category.id,
                f"{x.page_objects}",
            )
        )
        doc = cut2semantic_document(
            c,
            custom_doc_cat=doc_cat,
            client_lang=client_lang,
            company_name=company_name,
        )
        all_docs.append(doc)

    return all_docs


def merge_composition_cuts(
    all_composition_cuts: Dict[DocumentCat, List[shared.SemanticPage]],
    new_composition_cuts: Dict[DocumentCat, List[shared.SemanticPage]],
) -> Dict[DocumentCat, List[shared.SemanticPage]]:
    """
    Merge the new_composition_cuts structure into the all_composition_cuts structure.
    The intention is to have a list of pages per doc_cat that each should result in a single document
    (e.g. all photos in a photo doc or all tax pages in a tax doc.
    """
    for doc_cat, pages in new_composition_cuts.items():
        if doc_cat in all_composition_cuts:
            # Append to existing composition
            all_composition_cuts[doc_cat] += new_composition_cuts[doc_cat]
        else:
            # Set new composition
            all_composition_cuts[doc_cat] = new_composition_cuts[doc_cat]
    return all_composition_cuts


def create_semantic_documents(
    processed_files: shared.ProcessedFiles,
    client_lang: str = "de",
    company_name: Optional[str] = None,
    processing_config: SemanticDocumentsProcessingConfig = DEFAULT_SEMANTIC_DOCUMENTS_PROCESSING_CONFIG,
) -> List[shared.SemanticDocument]:
    """Transform a list of document_page elements into sets of elements each separate document
    Start a new document if
    - filename of source file is different
    - document_cat is different
    - if page_cat says, that a different file has to begin (e.g. first page of salary certificate)
    - if page_cat says that it is a single page document then it has to split before and after
    - if page_cat says that it is a last page in a document, then split after
    - if page_cat restriction 'allowed_next_page' of previous page does not contain the next page_cat

    BUT ... use these rules to skip splitting (afterwards):
    - if previous and next page are same DocumentCat of a "long" document (e.g. tax declaration).
      This avoids splitting before an unknown single page
    - if previous is unknown and previous-previous is the same DocumentCat as the current of a "long" document (e.g. tax declaration).
      This avoids splitting after an unknown single page"""

    with sentry_sdk.start_span(op="create_semantic_documents"):
        all_docs: List[shared.SemanticDocument] = []
        all_composition_cuts = {}  # DocumentCat.PROPERTY_PHOTOS: []

        for (
            original_filename,
            processed_file,
        ) in processed_files.processed_files.items():
            (
                all_docs_per_file,
                composition_cuts_per_file,
            ) = create_semantic_documents_per_file(
                original_filename,
                processed_file,
                client_lang,
                processing_config,
                company_name,
            )

            all_docs += all_docs_per_file
            all_composition_cuts = merge_composition_cuts(
                all_composition_cuts, composition_cuts_per_file
            )

        all_docs += create_semantic_documents_from_composition_cuts(
            all_composition_cuts, client_lang, company_name
        )

        if GENERATE_OFFLINE_FILENAME:
            make_filenames_unique(all_docs)

        sort_documents_by_category_id(all_docs)

        return all_docs


def create_semantic_document_with_forced_document_category(
    all_semantic_pages,
    client_lang: str,
    processing_config: SemanticDocumentsProcessingConfig,
) -> shared.SemanticDocument:
    custom_title_elements = []

    # Find document cat
    doc_cat = get_document_cat_by_name(processing_config.force_document_category_key)
    if not doc_cat:
        logger.error(
            f"Unknown value for {processing_config.force_document_category_key = } in create_semantic_document_with_forced_document_category(...)"
        )
        custom_title_elements.append(
            f"Invalid {processing_config.force_document_category_key}"
        )
        doc_cat = DocumentCat.UNKNOWN

    if processing_config.force_title_elements is not None:
        if processing_config.force_title_elements:
            custom_title_elements += processing_config.force_title_elements

    if processing_config.force_title_suffix is not None:
        if processing_config.force_title_suffix:
            custom_title_elements.append(processing_config.force_title_suffix)

    doc = cut2semantic_document(
        cut=all_semantic_pages,
        custom_doc_cat=doc_cat,
        custom_confidence=1.0,
        custom_title_elements=custom_title_elements,
        client_lang=client_lang,
    )
    return doc


def create_semantic_documents_per_file(
    original_filename: str,
    processed_file: shared.ProcessedFile,
    client_lang: str,
    processing_config: SemanticDocumentsProcessingConfig,
    company_name: Optional[str] = None,
):
    all_docs_per_file = []
    composition_cuts_per_file = {}
    all_semantic_pages = create_semantic_pages(processed_file)

    if processing_config.force_document_category_key:
        # This will always create exactly one file
        doc = create_semantic_document_with_forced_document_category(
            all_semantic_pages, client_lang, processing_config
        )
        all_docs_per_file.append(doc)
        return all_docs_per_file, composition_cuts_per_file

    top_file_classification = get_top_classification(processed_file)

    if (
        processing_config.semantic_document_splitting_style
        == SemanticDocumentSplittingStyle.NO_SPLITTING
    ):
        long_doc = try_to_identify_long_document(
            all_semantic_pages,
            top_file_classification,
            client_lang=client_lang,
            company_name=company_name,
        )
        if not long_doc:
            # No single file found, make it unknown to avoid splitting
            long_doc = create_unknown_document(
                all_semantic_pages, client_lang=client_lang, company_name=company_name
            )

        # We have found / forced a single file, return it
        all_docs_per_file.append(long_doc)
        return all_docs_per_file, composition_cuts_per_file

    docs = try_to_identify_immutable_file(
        original_filename,
        all_semantic_pages,
        top_file_classification,
        client_lang,
        company_name,
    )
    if docs:
        all_docs_per_file += docs
    else:
        # Take out all semantic pages that can safely be extracted from the wurst and
        # transform them to documents
        (
            known_short_cut_dict,
            rest_semantic_pages_after_short_cut,
        ) = try_to_find_known_short_cuts(all_semantic_pages, client_lang)
        for cut in known_short_cut_dict.values():
            docs = split_pages_into_docs(
                cut, client_lang=client_lang, company_name=company_name
            )
            all_docs_per_file += docs

        # Take out all semantic pages that should be composed as a semantic document from multiple
        # original files and store them in composition_cuts
        new_composition_cuts, rest_semantic_pages = try_to_find_known_short_cuts(
            rest_semantic_pages_after_short_cut,
            client_lang,
            known_short_doc_cats=KNOWN_COMPOSITION_CUTS,
        )
        if new_composition_cuts:
            for doc_cat, pages in new_composition_cuts.items():
                if doc_cat not in composition_cuts_per_file:
                    composition_cuts_per_file[doc_cat] = []
                composition_cuts_per_file[doc_cat] += pages

        if rest_semantic_pages:
            unknown_short_doc = try_to_find_unknown_short_doc(
                rest_semantic_pages, client_lang=client_lang, company_name=company_name
            )
            if unknown_short_doc:
                all_docs_per_file.append(unknown_short_doc)
            else:
                tax_doc, tax_attachments_doc = try_to_identify_tax_forms(
                    rest_semantic_pages,
                    client_lang=client_lang,
                    company_name=company_name,
                )
                if tax_doc:
                    # This is a longer document with tax pages and some mixed rest. Just separate the tax pages (including assessment) and the rest
                    all_docs_per_file.append(tax_doc)
                    if tax_attachments_doc:
                        all_docs_per_file.append(tax_attachments_doc)
                else:
                    long_doc = try_to_identify_long_document(
                        rest_semantic_pages,
                        top_file_classification,
                        client_lang=client_lang,
                        company_name=company_name,
                    )
                    if long_doc:
                        # Idea: If we have a single long_doc and it is unknown then we could give up and return a
                        # full unknown doc for the processed_file (skip known_short_cuts etc) ?
                        all_docs_per_file.append(long_doc)
                    else:
                        doc_unknown = check_independent_doc_cat(
                            rest_semantic_pages,
                            client_lang=client_lang,
                            company_name=company_name,
                        )
                        if doc_unknown:
                            all_docs_per_file.append(doc_unknown)
                        else:
                            docs = split_pages_into_docs(
                                rest_semantic_pages,
                                client_lang=client_lang,
                                company_name=company_name,
                            )
                            all_docs_per_file += docs

    return all_docs_per_file, composition_cuts_per_file


def get_top_classification(processed_file) -> Optional[shared.Classification]:
    top_file_classification: Optional[shared.Classification] = None
    if processed_file.classifications:
        if FILE_TEXT_SPACY_CLASSIFIER_NAME in processed_file.classifications:
            if processed_file.classifications[FILE_TEXT_SPACY_CLASSIFIER_NAME]:
                top_file_classification = processed_file.classifications[
                    FILE_TEXT_SPACY_CLASSIFIER_NAME
                ][0]

                if (
                    top_file_classification.confidence
                    < FILE_CLASSIFICATION_CONFIDENCE_MIN_THRESHOLD
                ):
                    top_file_classification = None
    return top_file_classification


def clean_page_objects(
    doc_cat: DocumentCat, page_objects: List[shared.PageObject]
) -> List[shared.PageObject]:
    cpo = []
    for po in page_objects:
        source = po.source

        if doc_cat in [DocumentCat.TAX_DECLARATION, DocumentCat.TAX_ASSESSMENT]:
            if source != PageObjectSource.PageParser:
                continue

        cpo.append(po)

    return cpo


def create_semantic_pages(
    processed_file: shared.ProcessedFile,
) -> List[shared.SemanticPage]:
    """creates currently just one semantic page out of the information on a single page in a processed file
    later could change to multiple semantic pages per processed page
    """
    semantic_pages = []
    for page_number, processed_page in processed_file.pages.items():
        # create semantic pages for page objects
        semantic_pages_for_special_objects = (
            create_semantic_pages_for_page_with_special_page_objects(
                processed_file, processed_page, page_number
            )
        )

        if semantic_pages_for_special_objects:
            semantic_pages.extend(semantic_pages_for_special_objects)
        else:
            # if there are no semantic pages for special objects on the page, just create a simple semantic page
            # from the classifier result

            doc_cat = DocumentCat[processed_page.document_category.name]
            cpo = clean_page_objects(doc_cat, processed_page.page_objects)

            page_category = processed_page.page_category
            document_category = processed_page.document_category

            semantic_pages.append(
                shared.SemanticPage(
                    lang=processed_page.lang,
                    source_file_path=processed_file.file_path,
                    source_page_number=page_number,
                    document_category=document_category,
                    page_category=page_category,
                    confidence=processed_page.confidence,
                    confidence_info=processed_page.confidence_info,
                    page_objects=cpo,
                )
            )

    return semantic_pages


def create_page_objects_for_semantic_page(
    grouped_page_objects: List[shared.PageObject],
    ungrouped_page_objects: List[shared.PageObject],
):
    used_page_objects = copy(grouped_page_objects)
    if ungrouped_page_objects:
        used_page_objects += ungrouped_page_objects

    # Now flatten the derived page objects (bring them up to the top level of the collection)
    for po in used_page_objects:
        if po.derived_page_objects:
            # Caution: adding this to the collection while looping over the collection makes the iteration also cover
            # the added elements. Thus this flattens page objects recursively. Not needed for now but does not hurt.
            used_page_objects += po.derived_page_objects
            po.derived_page_objects = None

    return used_page_objects


def create_semantic_pages_for_page_with_special_page_objects(
    processed_file,
    processed_page,
    page_number,
    skip_barcode_pages=True,
    skip_partner_processing=False,
):
    # Loop over all pages in processed file
    # If page contains PageObjects (above individual confidence thresholds) group them by DocumentCat
    # Create a semantic page per each DocumentCat

    if is_email_body_filename(processed_file.filename):
        # Do not search for graphical objects in emails, just keep them unchanged.
        doc_cat = DocumentCat[processed_page.document_category.name]
        return []

    if skip_barcode_pages:
        doc_cat = DocumentCat[processed_page.document_category.name]
        if doc_cat in [DocumentCat.TAX_DECLARATION, DocumentCat.TAX_ASSESSMENT]:
            # and processed_page.page_category.name == PageCat.TAX_DECLARATION_BARCODE.name:
            # As barcodes are often confused with special page objects (images) we ignore all special objects
            return []

    if skip_partner_processing:
        if doc_cat.value.topic == DocumentTopic.PARTNER:
            # Skip object detection on all partner documents as sometimes tables are identified as passport,
            # pictures
            return []

    if processed_page.document_category:
        doc_cat: DocumentCat = DocumentCat[processed_page.document_category.name]
    else:
        return []

    if processed_page.page_category:
        page_cat: PageCat = PageCat[processed_page.page_category.name]
    else:
        return []

    page_objects_per_doc_cat = defaultdict(list)
    ungrouped_page_objects = []
    for page_object in processed_page.page_objects:
        if page_object.key in page_object_configurations.keys():
            conf: PageObjectConfiguration = page_object_configurations[page_object.key]

            (
                po_valid,
                dim_percentages_ok,
                min_width_ok,
                min_height_ok,
                max_width_ok,
                max_height_ok,
            ) = calc_graphical_page_object_validity(
                processed_page, page_object, doc_cat, conf
            )

            if po_valid:  # and parser_ok:
                if conf.doc_cats_no_override and (doc_cat in conf.doc_cats_no_override):
                    # Do nothing for this page object. E.g. a propoerty_photo
                    # in a sales documentation
                    ungrouped_page_objects.append(page_object)
                elif conf.doc_cat == doc_cat and conf.page_cat == page_cat:
                    # This has already been matched correctly by a text matcher,
                    # do nothing here and ignore the detected object
                    pass
                else:
                    if conf.doc_cat_group:
                        # Use this page_object to create a semantic page (and
                        # replace the result of the text parser)
                        page_object.page_number = (
                            -1
                        )  # Forget the page number as this will change
                        page_objects_per_doc_cat[conf.doc_cat_group].append(page_object)
                    else:
                        ungrouped_page_objects.append(page_object)
            else:
                # This page_object does not dominate the page
                if not dim_percentages_ok:
                    # This object is probably a wrong detection because basic requirements are not met.
                    # Disable its visibility
                    page_object.visible = False
                    page_object.confidence /= 10
                    page_object.invisibility_reason = f"Minimum/Maximum size requirements not met: min_width_ok={min_width_ok}, min_height_ok={min_height_ok}, max_width_ok={max_width_ok}, max_height_ok={max_height_ok}"

                page_object.confidence_summary = shared.ConfidenceSummary.from_value(
                    page_object.confidence
                )
                ungrouped_page_objects.append(page_object)

        elif page_object.type not in [
            ExtractionType.STRING.name,
            ExtractionType.PARAGRAPH.name,
            ExtractionType.ADDRESS_BLOCK.name,
            ExtractionType.DATE.name,
            ExtractionType.INT.name,
            ExtractionType.CURRENCY.name,
            ExtractionType.OBJECT.name,
            ExtractionType.IMAGE.name,
            "MRZ",  # really needed in here?
            ExtractionType.FINHURDLE.name,
        ]:
            logger.warning(
                f"Found unknown type of special objects: {page_object.key} / {page_object.type} in {page_object} in processed_file {processed_file} in page {processed_page}"
            )

    if DocumentCat.IDENTITY_MISC in page_objects_per_doc_cat:
        # Keep the Misc object only if there is no other page objects on the page
        # (which would be DocumentCat.PASSPORT_CH, PASSPORT_XXX, or ID_XXX)
        if len(page_objects_per_doc_cat) > 1:
            page_objects_per_doc_cat.pop(DocumentCat.IDENTITY_MISC)

    semantic_pages_for_special_page_objects = []

    for doc_cat_group, page_objects in page_objects_per_doc_cat.items():
        doc_cat = page_objects[0].key
        conf = page_object_configurations[doc_cat]
        document_category = documentcategory_from_doccat(conf.doc_cat)
        page_category = pagecategory_from_pagecat(conf.page_cat)

        # Calculate custom confidence from all page objects of the same DocumentCat
        # Could be improved by taking processed_page.confidence into account if it is high and the related doc cat
        # would be relevant (e.g. confidences.append(processed_page.confidence)
        confidences = [po.confidence for po in page_objects]
        custom_confidence = harmonic_mean(confidences) * conf.confidence_weight

        used_page_objects = create_page_objects_for_semantic_page(
            page_objects, ungrouped_page_objects
        )

        semantic_page = shared.SemanticPage(
            lang=processed_page.lang,
            source_file_path=processed_file.file_path,
            source_page_number=page_number,
            document_category=document_category,
            page_category=page_category,
            confidence=custom_confidence,
            confidence_info=shared.PageConfidenceInfo(
                parsername="ObjectDetector", spacy_classifications=[]
            ),
            # Only keep page_objects from this doc_cat - ignore all others from processed_page.page_objects
            page_objects=used_page_objects,
            searchable_pdf=processed_page.searchable_pdf,
            searchable_txt=processed_page.searchable_txt,
        )
        semantic_pages_for_special_page_objects.append(semantic_page)
    return semantic_pages_for_special_page_objects


def sort_documents_by_category_id(all_docs: List[shared.SemanticDocument]):
    def by_document_category(x: shared.SemanticDocument):
        ret = f"{x.document_category.id} {x.filename}"
        return ret

    all_docs.sort(key=by_document_category)


def make_filenames_unique(
    semantic_documents: List[shared.SemanticDocument], also_modify_titles=True
):
    """
    Rename files by adding '001' / '002' / .... + original filename stem.
    E.g. if there are 2 images for floor plan and both would have a filename '604 Floor Plan'
    this function renames them to '604 Floor Plan 001 origimgone.pdf' and '604 Floor Plan 002 origimgtwo.pdf'
    :param semantic_documents: List of semantic documents that should be modified
    :param also_modify_titles: If this is true then the stem of the original file will be added after the number (e.g. ' origimgone')
    :return:
    """
    # replace with filenames = groupby(sorted(semantic_documents, key=lambda d: d.filename))
    filenames = {}
    for semantic_document in semantic_documents:
        filename_new = semantic_document.filename
        if filename_new:
            if filename_new in filenames:
                filenames[filename_new].append(semantic_document)
            else:
                filenames[filename_new] = [semantic_document]

    # Now check dics for lists with length > 1
    for filename, semantic_documents in filenames.items():
        if len(semantic_documents) > 1:
            # We need to rename all in the list. Count starts at 1001 but we cut off the first digit (so '001')
            counter = 1001
            for semantic_document in semantic_documents:
                suffix = str(counter)[1:]
                if also_modify_titles:
                    source_file_path = semantic_document.semantic_pages[
                        0
                    ].source_file_path
                    source_filename = Path(source_file_path)
                    source_filestem = source_filename.stem
                    suffix = f"{suffix} {source_filestem}"
                semantic_document.title = f"{semantic_document.title} {suffix}"

                if semantic_document.filename:
                    ext = os.path.splitext(semantic_document.filename)[1]
                    if ext:
                        semantic_document.filename = semantic_document.filename.replace(
                            ext, f" {suffix}{ext}"
                        )
                counter += 1


def order_twisted_pairs_of_pages(
    sem_pages: List[shared.SemanticPage], page_one: PageCat, page_two: PageCat
):
    """

    @param sem_pages: List of pages that potentially need reordering
    @param page_one: PageCat of the page that should come first
    @param page_two: PageCat of the page that should come after the first
    @return: List of pages, potentially reordered
    """
    last_page_cat: PageCat = None
    found_first_page_cat: bool = False
    for idx, semantic_page in enumerate(sem_pages):
        current_page_cat = PageCat[semantic_page.page_category.name]
        found_first_page_cat = found_first_page_cat or (current_page_cat == page_one)
        if current_page_cat == page_two:
            if found_first_page_cat:
                # Either the order is correct already or this is a Wurst.
                # do not try to reorder
                break

        next_page_cat = (
            PageCat[sem_pages[idx + 1].page_category.name]
            if idx + 1 < len(sem_pages)
            else None
        )

        # Try to swap e.g. P2 and P1 of pension statement
        if last_page_cat != page_one:
            if current_page_cat == page_two and next_page_cat == page_one:
                after_next_page_cat = (
                    PageCat[sem_pages[idx + 2].page_category.name]
                    if idx + 2 < len(sem_pages)
                    else None
                )
                if (
                    not last_page_cat == page_one
                    and not after_next_page_cat == page_two
                ):
                    # Now we can switch current and next
                    return swap_pages(sem_pages, idx, idx + 1)
        last_page_cat = current_page_cat
    return sem_pages


def check_independent_doc_cat(
    sem_pages: List[shared.SemanticPage],
    client_lang: str,
    company_name: Optional[str] = None,
) -> shared.SemanticDocument:
    """
        Check if any types of non-independent document types like e.g. DocumentCat.PROPERTY_INFORMATION ' \
        are included. If they are, make the whole set unknown (must be cut by hand and then detected).
    """
    contained_doc_cats = get_doc_cats(sem_pages)
    for doc_cat in contained_doc_cats:
        if not is_doc_cat_valid_as_independent_document(doc_cat):
            # This document type cannot be a standalone / independent document. So make all pages together unknown
            return create_unknown_document(
                sem_pages, client_lang=client_lang, company_name=company_name
            )

    # We return no unknown document so processing continues
    return None


def calculate_doc_cat_groups(
    sem_pages: List[shared.SemanticPage],
) -> tuple[dict[str, int], bool]:
    """
    Calculate doc_cat groups (compress sequence of pages with same doc_cat to 1, then count occurrences)
    So AABAAAACBBBBBB -> ABACB -> {A:2, B:2, C:1}, True
    :param sem_pages:
    :return:
    """
    doc_cat_groups = {}
    last_doc_cat = None
    has_repetitions = False
    for sp in sem_pages:
        if sp.document_category == last_doc_cat:
            continue
        key = sp.document_category.name
        if key in doc_cat_groups:
            doc_cat_groups[key] += 1
            has_repetitions = True
        else:
            doc_cat_groups[key] = 1
        last_doc_cat = sp.document_category
    # If occurrence > 1 it means we have >1 block of same doc cat. This is probably an undetected document, so do not cut
    return doc_cat_groups, has_repetitions


def split_pages_into_docs(
    sem_pages: List[shared.SemanticPage],
    client_lang: str,
    company_name: Optional[str] = None,
) -> List[shared.SemanticDocument]:
    ordered_sem_pages = order_twisted_pairs_of_pages(
        sem_pages,
        PageCat.PENSION2_CERTIFICATE_PAGE_ONE,
        PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
    )
    ordered_sem_pages = order_twisted_pairs_of_pages(
        ordered_sem_pages,
        PageCat.SALARY_CERTIFICATE,
        PageCat.SALARY_CERTIFICATE_ADDITIONAL_PAGE,
    )
    ordered_known_sem_pages, ordered_unknown_sem_pages = extract_unknown_sem_pages(
        ordered_sem_pages
    )

    doc_cat_groups, has_repetitions = calculate_doc_cat_groups(sem_pages)

    if len(ordered_unknown_sem_pages) >= 2:
        # At least 2 unknown pages... too dangerous to cut, make 1 unknown doc out of it
        # yield because we need to return a list
        yield create_unknown_document(
            sem_pages, client_lang=client_lang, company_name=company_name
        )
    elif has_repetitions:
        # doc_cat is repeated... to dangerous to cut
        yield create_unknown_document(
            sem_pages, client_lang=client_lang, company_name=company_name
        )
    else:
        # max 1 unknown page
        cuts = cut_semantic_pages(ordered_known_sem_pages, client_lang)
        cuts_clean, unknown_sem_pages = check_document_plausibility(cuts)

        doc_cat_names = set([p.document_category.name for p in sem_pages])
        num_unknown = len(unknown_sem_pages) + len(ordered_unknown_sem_pages)
        if len(doc_cat_names) == 2 and num_unknown == 1:
            # Only one unknown page and one document category... leave it as one document
            doc_cat = DocumentCat[cuts_clean[0][0].document_category.name]
            yield cut2semantic_document(
                sem_pages,
                client_lang=client_lang,
                company_name=company_name,
                custom_doc_cat=doc_cat,
            )
        else:
            # we dare to cut and see what happens...
            cuts_unknown = cut_semantic_pages(
                ordered_unknown_sem_pages + unknown_sem_pages, client_lang
            )
            all_cuts = cuts_clean + cuts_unknown

            for c in all_cuts:
                yield cut2semantic_document(
                    c, client_lang=client_lang, company_name=company_name
                )


def swap_pages(pages, pos1, pos2):
    """in-place swapping of two elements"""
    list2 = copy(pages)
    list2[pos1], list2[pos2] = list2[pos2], list2[pos1]
    return list2


def log_semantic_documents(
    docs: List[shared.SemanticDocument], log_pages=True, log_original_path=True
):
    msg = f"List of {len(docs)} semantic documents:\n"
    for idx, sd in enumerate(docs):
        msg += f"Doc #{idx}: {sd.filename}, {round(sd.confidence, 3)} confidence, {sd.confidence_info} confidence info, {len(sd.semantic_pages)} pages, {len(sd.aggregated_objects)} agg page objects, path={sd.filename}\n"
        if log_pages:
            for sp in sd.semantic_pages:
                msg += f"        Page #{sp.source_page_number} {sp}\n"
                msg += f"                {sp.source_file_path} #{sp.source_page_number} -> {sd.filename}\n"

    logger.info(msg)


# dossier_json_path is the full path to dossier-data.json
def log_semantic_dossier(dossier_json_path: Path):
    semantic_dossier = shared.SemanticDossier.parse_file(dossier_json_path)
    print(
        f"Here comes the semantic dossier for {dossier_json_path} ---------------------------------------------------------------"
    )
    logger.info("Semantic Documents with pages:")
    log_semantic_documents(semantic_dossier.semantic_documents, log_pages=True)
    logger.info("Semantic Documents without pages:")
    log_semantic_documents(semantic_dossier.semantic_documents, log_pages=False)
