from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.bank_account.bank_account_util import (
    GenericBankDocumentPageParser,
)
from mortgageparser.documents.parser.pageparsers.hra.hra_parsers import (
    GENERIC_HRA_PARSER,
)
from mortgageparser.documents.parser.pageparsers.landregister.land_register_util import (
    LandRegisterDEPageParser,
)
from mortgageparser.documents.parser.pageparsers.payslip.PayslipPageParser import (
    PayslipPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.CommonParsersDE import (
    ClassifierPensionCertificatePageTwoPageParser,
    ClassifierPensionCertificatePageThreePageParser,
    CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.CommonPropertyInsurancePageParsers import (
    PropertyInsuranceSHPageParser,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.GenericLegacyPropertyInsurancePageParser import (
    GenericLegacyPropertyInsurancePageParser,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.zh.PropertyInsuranceZHPageParsers import (
    property_insurance_parser_zh_default,
)
from mortgageparser.documents.parser.pageparsers.salarycertificate.SalaryCertificateAdditionalPagePageParser import (
    SalaryCertificateAdditionalPagePageParser,
)
from mortgageparser.documents.parser.pageparsers.salarycertificate.SalaryCertificatePageParser import (
    SalaryCertificatePageParser,
)
from mortgageparser.documents.parser.pageparsers.sgd.classification_parser_map.classification_parser_map_util import (
    PageCfg,
    C_TAX,
    C_TAX_MIN,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGAssetsPageParser import (
    TaxDeclarationAGAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGCoverPagePageParser import (
    TaxDeclarationAGCoverPagePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGDeductionsPageParser import (
    TaxDeclarationAGDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGIncomePageParser import (
    TaxDeclarationAGIncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGPVAPageParser import (
    TaxDeclarationAGPVAPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGPersonalDataPageParser import (
    TaxDeclarationAGPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.TaxDeclarationAGPropertyPageParser import (
    TaxDeclarationAGPropertyPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm1PersonalDataPageParser import (
    TaxDeclarationBEForm1PersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm2IncomePageParser import (
    TaxDeclarationBEForm2IncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm3AssetsPageParser import (
    TaxDeclarationBEForm3AssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm4DebtPageParser import (
    TaxDeclarationBEForm4DebtPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm5ChildSupportPageParser import (
    TaxDeclarationBEForm5ChildSupportPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm6DeductionsPageParser import (
    TaxDeclarationBEForm6DeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBEForm7PropertyPageParser import (
    TaxDeclarationBEForm7PropertyPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.TaxDeclarationBETaxMePageParser import (
    TaxDeclarationBETaxMePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bl.TaxDeclarationBLPageParsers import (
    TaxDeclarationBLPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bl.TaxDeclarationBLPropertyPageParser import (
    TaxDeclarationBLPropertyPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bl.bl_tax_declaration_parsers import (
    TAX_DECLARATION_BL_ASSETS,
    TAX_DECLARATION_BL_DEDUCTIONS,
    TAX_DECLARATION_BL_INCOME,
    TAX_DECLARATION_BL_PERSONAL_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bs.TaxDeclarationBSAssetsPageParser import (
    TaxDeclarationBSAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bs.TaxDeclarationBSDeductionsPageParser import (
    TaxDeclarationBSDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bs.TaxDeclarationBSPageParsers import (
    TaxDeclarationBSPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.bs.bs_tax_declaration_parsers import (
    TAX_DECLARATION_BS_INCOME,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.fr.fr_tax_declaration_parsers import (
    TAX_DECLARATION_FR_PERSONAL_DE_2020,
    TAX_DECLARATION_FR_INCOME_DE_2020,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.gl.gl_tax_declaration_parsers import (
    TAX_DECLARATION_GL_PERSONAL_2021,
    TAX_DECLARATION_GL_INCOME_2021,
    TAX_DECLARATION_GL_DEDUCTIONS_2021,
    TAX_DECLARATION_GL_ASSETS_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.gr.gr_tax_declaration_parsers import (
    TAX_DECLARATION_GR_PERSONAL_DE_2021,
    TAX_DECLARATION_GR_INCOME_DE_2021,
    TAX_DECLARATION_GR_DEDUCTIONS_DE_2021,
    TAX_DECLARATION_GR_ASSETS_DE_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.nw.nw_tax_declaration_parsers import (
    TAX_DECLARATION_NW_PERSONAL,
    TAX_DECLARATION_NW_INCOME,
    TAX_DECLARATION_NW_DEDUCTIONS,
    TAX_DECLARATION_NW_ASSETS,
    TAX_DECLARATION_NW_DEBT,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ow.ow_tax_declaration_parsers import (
    TAX_DECLARATION_OW_PERSONAL_2020,
    TAX_DECLARATION_OW_INCOME_2020,
    TAX_DECLARATION_OW_DEDUCTIONS_2020,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGPropertyDetailsPageParser import (
    TaxDeclarationSGPropertyDetailsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGPropertySummaryPageParser import (
    TaxDeclarationSGPropertySummaryPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sh.TaxDeclarationSHPageParsers import (
    TaxDeclarationSHPersonalDataPageParser,
    TaxDeclarationSHBarcodePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sh.sh_tax_declaration_parsers import (
    TAX_DECLARATION_SH_INCOME_2021,
    TAX_DECLARATION_SH_DEDUCTIONS_2021,
    TAX_DECLARATION_SH_ASSETS_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.so.so_tax_declaration_parsers import (
    TAX_DECLARATION_SO_PERSONAL,
    TAX_DECLARATION_SO_INCOME,
    TAX_DECLARATION_SO_DEDUCTIONS,
    TAX_DECLARATION_SO_ASSETS,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sz.TaxDeclarationSZPersonalDataPageParser import (
    TaxDeclarationSZPersonalData2016PageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sz.sz_tax_declaration_parsers import (
    TAX_DECLARATION_SZ_ASSETS_2021,
    TAX_DECLARATION_SZ_INCOME_2021,
    TAX_DECLARATION_SZ_INCOME_DETAILS_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tg.TaxDeclarationTGAssetsPageParser import (
    TaxDeclarationTGAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tg.TaxDeclarationTGPersonalDataPageParser import (
    TaxDeclarationTGPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tg.tg_tax_declaration_parsers import (
    TAX_DECLARATION_TG_INCOME_2021,
    TAX_DECLARATION_TG_DEDUCTIONS_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ur.ur_tax_declaration_parsers import (
    TAX_DECLARATION_UR_PERSONAL_2021,
    TAX_DECLARATION_UR_DEDUCTIONS_2021,
    TAX_DECLARATION_UR_INCOME_2021,
    TAX_DECLARATION_UR_ASSETS_2021,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.vs.vs_tax_declaration_parsers import (
    TAX_DECLARATION_VS_PERSONAL_DE,
    TAX_DECLARATION_VS_INCOME_2020,
    TAX_DECLARATION_VS_DEDUCTIONS_2020,
    TAX_DECLARATION_VS_ASSETS_2020,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zg.TaxDeclarationZGAssetsPageParser import (
    TaxDeclarationZGAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zg.TaxDeclarationZGDeductionsPageParser import (
    TaxDeclarationZGDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zg.TaxDeclarationZGIncomePageParser import (
    TaxDeclarationZGIncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zg.TaxDeclarationZGParsers import (
    TaxDeclarationZGPersonalDataPageParser,
    TaxDeclarationZGBarcodePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.TaxDeclarationZHAssetsPageParser import (
    TaxDeclarationZHAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.TaxDeclarationZHDebtInventoryPageParser import (
    TaxDeclarationZHDebtInventoryPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.TaxDeclarationZHDeductionsPageParser import (
    TaxDeclarationZHDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.TaxDeclarationZHIncomePageParser import (
    TaxDeclarationZHIncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.TaxDeclarationZHPersonalDataPageParser import (
    TaxDeclarationZHPersonalDataPageParser,
)

page_configurations_de = [
    # Filter this stuff out so it never appears (threshold > 1)
    PageCfg(prefix="DE/Rest_DE", confidence_threshold=1.1),
    PageCfg(
        prefix="DE/120-BEKB/Basisvertrag",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.BASE_CONTRACT,
    ),
    # Map multiple divorce classifications to generic 260
    PageCfg(
        # Be very strict here as we had some false positives with 98% confidence
        # All real pages should have confidence 1.0
        prefix="DE/240",
        confidence_threshold=0.98,
        doc_cat=DocumentCat.DEBT_COLLECTION_INFORMATION,
    ),
    PageCfg(
        prefix="DE/261", confidence_threshold=0.6, doc_cat=DocumentCat.DIVORCE_DOCUMENT
    ),
    PageCfg(
        prefix="DE/262", confidence_threshold=0.6, doc_cat=DocumentCat.DIVORCE_DOCUMENT
    ),
    PageCfg(
        prefix="DE/263", confidence_threshold=0.6, doc_cat=DocumentCat.DIVORCE_DOCUMENT
    ),
    PageCfg(
        prefix="DE/272/Erbvorbezug",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.INHERITANCE_ADVANCE,
    ),
    PageCfg(
        prefix="DE/278/Schenkungsvertrag",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.DEED_OF_GIFT,
    ),
    ################################################################################################
    # TAX 310 BEGIN
    PageCfg(
        prefix="DE/310/AG/Deckblatt",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGCoverPagePageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/Personalien",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGPersonalDataPageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGIncomePageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/Abzüge",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGDeductionsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/Vermögen",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGAssetsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/Liegenschaft",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGPropertyPageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/DA-1",
        confidence_threshold=C_TAX,
        page_cat=PageCat.TAX_DECLARATION_FORM_DA_1,
    ),
    PageCfg(
        prefix="DE/310/AG/Detailauflistung (Rubrik X)",
        confidence_threshold=C_TAX,
        page_cat=PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS,
    ),
    PageCfg(
        prefix="DE/310/AG/Wertschriftenverzeichnis Front",
        confidence_threshold=C_TAX,
        page_cat=PageCat.TAX_DECLARATION_ACCOUNTS_FORM_FRONT,
    ),
    PageCfg(
        prefix="DE/310/AG/Personen, Vermögen, Abschlusssituation (PVA)",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationAGPVAPageParser(),
    ),
    PageCfg(
        prefix="DE/310/AG/Steuerbudget für Eigengebrauch",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_BUDGET,
    ),
    PageCfg(
        prefix="DE/310/AG/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/AI/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/AR/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    # Put this before Formular 1 because of prefix
    PageCfg(
        prefix="DE/310/BE/Formular 11",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_MISC,
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 1",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm1PersonalDataPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 2",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm2IncomePageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 3",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm3AssetsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 4",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm4DebtPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 5",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm5ChildSupportPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 6",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm6DeductionsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Formular 7",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBEForm7PropertyPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Zusammenzug TaxMe/TaxMe Hauptseite",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBETaxMePageParser(),
    ),
    PageCfg(
        prefix="DE/310/BE/Zusatzblatt 7",
        confidence_threshold=C_TAX,
        page_cat=PageCat.TAX_DECLARATION_RENOVATIONS,
    ),
    # Parser for table style personal data up until 2019
    PageCfg(
        prefix="DE/310/BL/Personalien Tabelle",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBLPersonalDataPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BL/Deckblatt",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_BL_PERSONAL_2021,
    ),
    PageCfg(
        prefix="DE/310/BL/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_BL_INCOME,
    ),
    PageCfg(
        prefix="DE/310/BL/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_BL_DEDUCTIONS,
    ),
    PageCfg(
        prefix="DE/310/BL/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_BL_ASSETS,
    ),
    PageCfg(
        # legacy, can be removed
        prefix="DE/310/BL/Wertschriften Detailauflistung",
        # Make this strict as this attracts floor plans and other pages
        confidence_threshold=0.8,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_MISC,
    ),
    PageCfg(
        prefix="DE/310/BL/Wertschriftenverzeichnis Details//Detailauflistung",  # Make this strict as this attracts floor plans and other pages
        confidence_threshold=0.8,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_MISC,
    ),
    PageCfg(
        prefix="DE/310/BL/Liegenschaft Hypothek Unterhalt",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBLPropertyPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BS/Personalien",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBSPersonalDataPageParser(),
    ),
    # Old parser did only recognize 2016 but not 2021. Added a new classification and extraction parser that suits both.
    PageCfg(
        prefix="DE/310/BS/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_BS_INCOME,
    ),
    PageCfg(
        prefix="DE/310/BS/Abzüge",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBSDeductionsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BS/Vermögen",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationBSAssetsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/BS/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/CH/Steuerbudget für Eigengebrauch",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_BUDGET,
    ),
    PageCfg(
        prefix="DE/310/FR/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_FR_PERSONAL_DE_2020,
    ),
    # Pages income and deductions get recognized by the same Spacy classifier.
    # So, the same extraction parser is used for both of them.
    PageCfg(
        prefix="DE/310/FR/Einkommen und Vermögen im In- und Ausland",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_FR_INCOME_DE_2020,
    ),
    PageCfg(
        prefix="DE/310/FR/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/GL/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GL_PERSONAL_2021,
    ),
    PageCfg(
        prefix="DE/310/GL/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GL_INCOME_2021,
    ),
    PageCfg(
        prefix="DE/310/GL/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GL_DEDUCTIONS_2021,
    ),
    PageCfg(
        prefix="DE/310/GL/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GL_ASSETS_2021,
    ),
    PageCfg(
        prefix="DE/310/GL/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/GR/Deckblatt",  # - Hauptformular Seite 1",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GR_PERSONAL_DE_2021,
    ),
    PageCfg(
        prefix="DE/310/GR/Einkünfte",  # - Hauptformular Seite 2",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GR_INCOME_DE_2021,
    ),
    PageCfg(
        prefix="DE/310/GR/Abzüge",  # - Hauptformular Seite 3",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GR_DEDUCTIONS_DE_2021,
    ),
    PageCfg(
        prefix="DE/310/GR/Vermögen",  # - Hauptformular Seite 4",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_GR_ASSETS_DE_2021,
    ),
    PageCfg(
        prefix="DE/310/GR/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/LU/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/NW/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NW_PERSONAL,
    ),
    PageCfg(
        prefix="DE/310/NW/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NW_INCOME,
    ),
    PageCfg(
        prefix="DE/310/NW/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NW_DEDUCTIONS,
    ),
    PageCfg(
        prefix="DE/310/NW/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NW_ASSETS,
    ),
    PageCfg(
        prefix="DE/310/NW/Schuldenverzeichnis",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_NW_DEBT,
    ),
    PageCfg(
        prefix="DE/310/NW/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/OW/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_OW_PERSONAL_2020,
    ),
    PageCfg(
        prefix="DE/310/OW/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_OW_INCOME_2020,
    ),
    PageCfg(
        prefix="DE/310/OW/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_OW_DEDUCTIONS_2020,
    ),
    PageCfg(
        prefix="DE/310/OW/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/SG/Liegenschaften ABCD",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationSGPropertyDetailsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/SG/Liegenschaften Zusammenzug",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationSGPropertySummaryPageParser(),
    ),
    PageCfg(
        prefix="DE/310/SG/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/SH/Deckblatt",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationSHPersonalDataPageParser(),
    ),
    PageCfg(
        prefix="DE/310/SH/Stammdatenblatt mit Barcode",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationSHBarcodePageParser(),
    ),
    PageCfg(
        prefix="DE/310/SH/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SH_INCOME_2021,
    ),
    PageCfg(
        prefix="DE/310/SH/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SH_DEDUCTIONS_2021,
    ),
    PageCfg(
        prefix="DE/310/SH/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SH_ASSETS_2021,
    ),
    PageCfg(
        prefix="DE/310/SH/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/SO/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SO_PERSONAL,
    ),
    PageCfg(
        prefix="DE/310/SO/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SO_INCOME,
    ),
    PageCfg(
        prefix="DE/310/SO/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SO_DEDUCTIONS,
    ),
    PageCfg(
        prefix="DE/310/SO/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SO_ASSETS,
    ),
    PageCfg(
        prefix="DE/310/SO/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/SZ/Personalien",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationSZPersonalData2016PageParser(),
    ),
    PageCfg(
        prefix="DE/310/SZ/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SZ_INCOME_2021,
    ),
    PageCfg(
        prefix="DE/310/SZ/Erwerbseinkommen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SZ_INCOME_DETAILS_2021,
    ),
    PageCfg(
        prefix="DE/310/SZ/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_SZ_ASSETS_2021,
    ),
    PageCfg(
        prefix="DE/310/SZ/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/TG/Deckblatt",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationTGPersonalDataPageParser(),
    ),
    # The Spacy classifications for TAX TG only worked for 2015, not for 2021. Rule-based classification added instead.
    PageCfg(
        prefix="DE/310/TG/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_TG_INCOME_2021,
    ),
    PageCfg(
        prefix="DE/310/TG/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_TG_DEDUCTIONS_2021,
    ),
    PageCfg(
        prefix="DE/310/TG/Vermögen",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationTGAssetsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/TG/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/UR/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_UR_PERSONAL_2021,
    ),
    PageCfg(
        prefix="DE/310/UR/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_UR_INCOME_2021,
    ),
    PageCfg(
        prefix="DE/310/UR/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_UR_DEDUCTIONS_2021,
    ),
    PageCfg(
        prefix="DE/310/UR/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_UR_ASSETS_2021,
    ),
    PageCfg(
        prefix="DE/310/UR/Wertschriftenverzeichnis Front",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/UR/Wertschriftenverzeichnis Details",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/VS/Personalien",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_PERSONAL_DE,
    ),
    PageCfg(
        prefix="DE/310/VS/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_INCOME_2020,
    ),
    PageCfg(
        prefix="DE/310/VS/Abzüge",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_DEDUCTIONS_2020,
    ),
    PageCfg(
        prefix="DE/310/VS/Vermögen",
        confidence_threshold=C_TAX,
        parser=TAX_DECLARATION_VS_ASSETS_2020,
    ),
    PageCfg(
        prefix="DE/310/VS/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/ZG/Barcodeblatt",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZGBarcodePageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZG/Personalien",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZGPersonalDataPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZG/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZGIncomePageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZG/Abzüge",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZGDeductionsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZG/Vermögen",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZGAssetsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZG/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    PageCfg(
        prefix="DE/310/ZH/Deckblatt",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZHPersonalDataPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZH/Einkünfte",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZHIncomePageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZH/Abzüge",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZHDeductionsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZH/Vermögen",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZHAssetsPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZH/Schuldenverzeichnis",
        confidence_threshold=C_TAX,
        parser=TaxDeclarationZHDebtInventoryPageParser(),
    ),
    PageCfg(
        prefix="DE/310/ZH/Wertschriftenverzeichnis",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_ACOUNTS_FORM_GENERIC,
    ),
    # Skip parsing for these as they mix with DE/310/CH/Kapitalgesellschaften and we do not want an incorrect ZH extraction in there
    PageCfg(
        prefix="DE/310/ZH/Kapitalgesellschaft",
        confidence_threshold=C_TAX,
        doc_cat=DocumentCat.TAX_DECLARATION,
    ),
    PageCfg(
        prefix="DE/310/",  # Catch all remaining tax pages
        confidence_threshold=C_TAX_MIN,
        doc_cat=DocumentCat.TAX_DECLARATION,
        page_cat=PageCat.TAX_DECLARATION_MISC,
    ),
    # END OF TAX 310
    # #############################################################################################################3
    PageCfg(prefix="DE/312/Veranlagung Steuerrechnung", min_chars_alpha_per_page=200),
    PageCfg(
        prefix="DEFRIT/355/Rentenbescheinigung BVG/Einseitiges Dokument",
        confidence_threshold=0.85,
    ),
    PageCfg(
        prefix="DE/951/Versicherungsauszug Krankenkasse",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.HEALTH_INSURANCE,
    ),
    PageCfg(
        prefix="DE/952/Kinderbetreuung",
        confidence_threshold=0.97,
        doc_cat=DocumentCat.DAYCARE_CONFIRMATION,
    ),
    # 230830 mt: make this very strict (before 0.7) because this is a ShortCut document so we have to be sure.
    PageCfg(
        prefix="DE/953/Spendenbescheinigung",
        confidence_threshold=0.92,
        doc_cat=DocumentCat.DONATION_CONFIRMATION,
    ),
    # 210922 mt: changed threshold from 0.8 to 0.7...maybe to aggressive?
    PageCfg(
        prefix="DE/322/Bank",
        confidence_threshold=0.7,
        # doc_cat=DocumentCat.BANK_DOCUMENT,
        parser=GenericBankDocumentPageParser(),
    ),
    # Must be high because super reliable
    # 211014 mt: reduced from 0.75 to 0.7 because of bad OCR in correct Lohnausweis
    # 230601 mt: 0.65 can be already completely different page
    PageCfg(
        prefix="DEENFRIT/330/Lohnausweis/Erste Seite",
        confidence_threshold=0.7,
        min_chars_alpha_per_page=1000,
        parser=SalaryCertificatePageParser(),
    ),
    # 'DE/330/Lohnausweis/Erste Seite', confidence_threshold=0.8, SalaryCertificatePageParser()),
    PageCfg(
        prefix="DEENFRIT/330/Lohnausweis/Zusatzseite",
        confidence_threshold=0.8,
        parser=SalaryCertificateAdditionalPagePageParser(),
    ),
    # Make sure this is not a single page
    PageCfg(
        prefix="DEENFRIT/330/Lohnausweis/Info",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.SALARY_CERTIFICATE,
        page_cat=PageCat.SALARY_CERTIFICATE_INFO_PAGE,
    ),
    # Some pages with 0.55 wrong
    PageCfg(
        prefix="DE/334/Arbeitsvertrag",
        confidence_threshold=0.6,
        min_chars_alpha_per_page=200,
    ),
    # strict because quality very good
    PageCfg(
        prefix="DE/340/Salärabrechnung",
        confidence_threshold=0.77,
        parser=PayslipPageParser(),
    ),
    # 350 is legacy, new number is 345
    PageCfg(
        prefix="DE/345/Bonus",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALARY_BONUS,
    ),
    PageCfg(
        prefix="DE/350/Bonus",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALARY_BONUS,
    ),
    PageCfg(
        prefix="DE/375/Darlehen",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.LOAN_AGREEMENT,
    ),
    # Be strict because not reliable
    PageCfg(
        prefix="DE/372/Kleinkredit",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.CONSUMER_LOAN,
    ),
    # Be strict because not reliable
    PageCfg(
        prefix="DE/371/Leasing",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.LEASING_AGREEMENT,
    ),
    # Be strict because not reliable, 60% is not enough
    PageCfg(
        prefix="DE/410/Pensionskassenausweis/Erste Seite",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=500,
        min_num_lines=10,
        parser=CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER,
    ),
    PageCfg(
        prefix="DE/410/Pensionskassenausweis/Zweite Seite",
        confidence_threshold=0.75,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
        parser=ClassifierPensionCertificatePageTwoPageParser(),
    ),
    PageCfg(
        prefix="DE/410/Pensionskassenausweis/Dritte Seite",
        confidence_threshold=0.75,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
        parser=ClassifierPensionCertificatePageThreePageParser(None, None),
    ),
    PageCfg(
        prefix="DE/410/Pensionskassenausweis/Brief",
        confidence_threshold=0.7,
        page_cat=PageCat.PENSION2_CERTIFICATE_LETTER,
    ),
    PageCfg(
        prefix="DE/410/Pensionskassenausweis/Info-Seite",
        confidence_threshold=0.7,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_INFO,
    ),
    PageCfg(
        prefix="DE/411/Pensionskassenreglement",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.PENSION_REGULATIONS,
    ),
    # not very good matching, be conservative
    PageCfg(
        prefix="DE/418/Pensionskasse Austrittsabrechnung",
        confidence_threshold=0.9,
        min_chars_alpha_per_page=200,
        doc_cat=DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT,
    ),
    PageCfg(
        prefix="DE/424/Freizügigkeitskonto",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.VESTED_BENEFITS_ACCOUNT,
    ),
    # Be strict because not reliable
    PageCfg(
        prefix="DE/430/Vorsorgekonto 3a",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.PENSION3A_ACCOUNT,
    ),
    # This is a prefix
    PageCfg(
        prefix="DE/440/Vorsorgepolice 3",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.PENSION3A_INSURANCE_CONTRACT,
    ),
    # Be strict because these pages get extracted from the Wurst
    # 230830 mt: Make it a lot more strict because this is a ShortCut document and very specific
    PageCfg(prefix="DE/450/Vorsorge Bescheinigung Beiträge", confidence_threshold=0.75),
    PageCfg(
        prefix="DE/513/Handelsregisterauszug",
        confidence_threshold=0.9,
        parser=GENERIC_HRA_PARSER,
    ),
    # PageCfg(prefix='DE/530/Jahresrechnung Firma/Deckblatt und Inhaltsverzeichnis', doc_cat=DocumentCat.FINANCIAL_STATEMENT_COMPANY, page_cat=PageCat.FINANCIAL_STATEMENT_COMPANY_CONTENT),
    PageCfg(
        prefix="DE/530/Jahresrechnung Firma/Inhalt",
        doc_cat=DocumentCat.FINANCIAL_STATEMENT_COMPANY,
        page_cat=PageCat.FINANCIAL_STATEMENT_COMPANY_CONTENT,
    ),
    PageCfg(
        prefix="DE/606/Situationsplan",
        confidence_threshold=0.4,
        doc_cat=DocumentCat.PLAN_SITUATION,
    ),
    PageCfg(
        prefix="DE/612/Situationsplan",
        confidence_threshold=0.4,
        doc_cat=DocumentCat.PLAN_SITUATION,
    ),
    # legacy_doc_cat_id
    # Fix until 628 is in classifier
    PageCfg(
        prefix="DE/607/Katasterplan",
        confidence_threshold=0.4,
        doc_cat=DocumentCat.PLR_CADASTRE,
    ),
    PageCfg(
        prefix="DE/610/Inhaltsverzeichnis Verkaufsdokumentation",
        confidence_threshold=0.75,
        doc_cat=DocumentCat.SALES_DOCUMENTATION,
    ),
    PageCfg(prefix="DE/610/Verkaufsdokumentation", confidence_threshold=0.4),
    PageCfg(
        prefix="DE/611/Grundbuchauszug",
        confidence_threshold=0.5,
        parser=LandRegisterDEPageParser(),
    ),
    PageCfg(
        prefix="DE/616/Liste Renovationen",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=100,
        min_num_lines=6,
    ),
    PageCfg(
        prefix="DE/621/Kubische Berechnung SIA",
        confidence_threshold=0.65,
        min_chars_alpha_per_page=200,
        min_num_lines=6,
    ),
    # 211022 mt: Changed confidence of all 617 from 0.5 to 0.7 because too aggressive
    PageCfg(
        prefix="DE/617/SH",
        confidence_threshold=0.7,
        parser=PropertyInsuranceSHPageParser(),
    ),
    PageCfg(
        prefix="DE/617/ZH",
        confidence_threshold=0.7,
        parser=property_insurance_parser_zh_default,
    ),
    PageCfg(
        prefix="DE/617",
        confidence_threshold=0.7,
        parser=GenericLegacyPropertyInsurancePageParser(),
    ),
    PageCfg(
        prefix="DE/628/Liegenschaftsabrechnung",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=200,
        min_num_lines=10,
    ),
    PageCfg(
        prefix="DE/631/Begründung/Stockwerkeigentum bereinigt",
        confidence_threshold=0.7,
        min_num_lines=10,
    ),
    PageCfg(
        prefix="DE/640/Mieterspiegel",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=100,
        min_num_lines=8,
    ),
    # be conservative, too variable
    PageCfg(
        prefix="DE/662/Reservationsvertrag",
        confidence_threshold=0.8,
        min_chars_alpha_per_page=100,
        min_num_lines=10,
    ),
    PageCfg(prefix="DE/672/Baubewilligung", confidence_threshold=0.6),
    PageCfg(
        prefix="DE/671/Baubewilligung", confidence_threshold=0.6
    ),  # legacy_doc_cat_id
    PageCfg(prefix="DE/675/Generalunternehmervertrag", confidence_threshold=0.6),
    PageCfg(
        prefix="DE/680/Generalunternehmervertrag", confidence_threshold=0.6
    ),  # legacy_doc_cat_id
    PageCfg(
        prefix="DE/721/Produktvereinbarung",
        confidence_threshold=0.7,
        doc_cat=DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION,
    ),
    PageCfg(
        prefix="DE/723/Finanzierungsbestätigung",
        min_chars_alpha_per_page=350,
        min_num_lines=15,
    ),
    PageCfg(
        prefix="DE/724/Fälligkeitsanzeige",
        confidence_threshold=0.9,
        doc_cat=DocumentCat.MORTGAGE_DUE_NOTICE,
    ),
    PageCfg(
        prefix="DE/722/Kündigung/Hypthekarvertrag", confidence_threshold=0.6
    ),  # legacy_doc_cat_id
    PageCfg(prefix="DE/729/Kündigung Hypothekarvertrag", confidence_threshold=0.6),
    PageCfg(
        prefix="DE/750/Verpfändung Vorsorge",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.PENSION_PLEDGE,
    ),
    # This is the new entry 694, only needed temporarily until spacy retraining
    # in august 2024
    PageCfg(
        prefix="DE/694/Anmeldung Grundbuch",
        doc_cat=DocumentCat.REGISTRATION_LAND_REGISTER,
    ),
    # This is the legacy entry (new 694)
    PageCfg(
        prefix="DE/782/Anmeldung Grundbuch",
        doc_cat=DocumentCat.REGISTRATION_LAND_REGISTER,
    ),
    # Legacy, can be deleted after spacy model trained in August 2024
    PageCfg(
        prefix="DE/920/Rechnung Liegenschaft",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.PROPERTY_BILL,
    ),
    # This is actually DE/920/Rechnung allgemein. Put it after DE/920/Rechnung Liegenschaft
    # due to prefix issue
    PageCfg(
        prefix="DE/920/Rechnung",
        confidence_threshold=0.8,
        doc_cat=DocumentCat.BILL_MISC,
    ),
    PageCfg(
        prefix="DE/697/Rechnung Grundbuchamt", doc_cat=DocumentCat.LAND_REGISTER_BILL
    ),
    # Only needed until after retraining August 2024
    PageCfg(
        prefix="DE/955/Rechnung Grundbuchamt", doc_cat=DocumentCat.LAND_REGISTER_BILL
    ),
    PageCfg(prefix="DEENFRIT/245/Strafregisterauszug", confidence_threshold=0.85),
]
