import structlog

from abbyyplumber.api import Page
from abbyyplumber.converter.ValueConverter import (
    CurrencyConverter,
    ParagraphConverter,
    DateConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    Search<PERSON>lementLabeledField,
    SearchElementStaticText,
    SearchElementArea,
    SearchElementConstrainedArea,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    SearchRelationRightOf,
    ReferenceBoundaryHorizontal,
    ReferenceBoundaryVertical,
    SearchRelationAbove,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange, FieldPosition
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_DATE_OF_BIRTH,
    P2_DATE_OF_BIRTH,
    P2_FIRSTNAME,
)
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from mortgageparser.util.field_extraction_util import (
    map_code_value_text,
    map_code_fields_to_text_currency_fields,
)
from mortgageparser.util.search_element_util import (
    create_search_elements_address,
    create_search_element_p1_fullname_from_address,
)

TEXT_REF_CODE = "text_ref_code"

COLUMN_CODE = "column_code"

COLUMN_VALUE = "column_value"


logger = structlog.getLogger(__name__)


def get_metadata_from_code_map_page(page_handle, columns, code_range, fields):
    text_main_col = page_handle.extract_text_from_columns(columns)
    code_map = map_code_value_text(text_main_col, code_range=code_range)
    local_fields = map_code_fields_to_text_currency_fields(code_map, fields)
    return local_fields


def create_search_elements_column_code(
    page: Page,
    label: str,
    x_range=PercentageRange(0, 0.35),
    max_l_dist=0.3,
    text_top=None,
    text_bottom=None,
):
    elements = [
        SearchElementConstrainedArea(
            COLUMN_CODE,
            page.fullpage,
            text_left=label,
            text_top=text_top,
            text_bottom=text_bottom,
            x_range=x_range,
            extract=False,
            dist=max_l_dist,
        ),
    ]
    return elements


def create_search_elements_by_tax_code(
    page,
    fields_map,
    field_pos_page_horizontal=PercentageRange(),
    field_vertical_line_scale=5,
    allow_negative_values=True,
    column_code=COLUMN_CODE,
    column_value: str = None,
    cut_off_single_char_right: bool = False,
):
    """
    211002 mt: changed field_vertical_line_scale from 6.5 to 5... seemed a little too large as a default

    :param page:
    :param fields_map:
    :param field_pos_page_horizontal:
    :param field_vertical_line_scale:
    :param allow_negative_values: There can be negative values in self employed income. No other occurences known so far
    :param column_code:
    :param column_value: If set then a search element with this name (e.g. variable COLUMN_VALUE) is expected to be
            present and values are only searched in this field. Use this if code values are not next to the values
            but e.g. left aligned. Normally use either field_pos_page_horizontal OR column_value
    :param cut_off_single_char_right:
            Should be set to True if the values are in a box with vertical lines after the number where the vertical
            line could be identified as trailing '1' or ' 1'. Else this must be False to handle cases like '4 3 2 1' -> 4321
    :return:
    """
    search_elements = []
    last_key = None

    # Track last tax code to find out if we want the same code for two fields
    last_tax_code: str = None

    for tax_code_orig, elem in fields_map.items():
        tax_code = str(tax_code_orig).strip()
        logger.info(f"Found {tax_code}, {elem.name}")

        rel = [
            SearchRelationBelow(
                column_code,
                ref_boundary=ReferenceBoundaryVertical.TOP,
                offset=page.height_lines(-0.5),
            ),
            SearchRelationAbove(
                column_code,
                ref_boundary=ReferenceBoundaryVertical.BOTTOM,
                offset=page.height_lines(0.5),
            ),
        ]

        different_tax_code = last_tax_code != tax_code
        if last_key and different_tax_code:
            rel.append(
                SearchRelationBelow(
                    last_key, ref_boundary=ReferenceBoundaryVertical.CENTER
                )
            )

        target = None
        if column_value:
            target = column_value

        se = SearchElementLabeledField(
            elem.name,
            page.fullpage,
            target_name_label=column_code,
            target_name=target,
            label=str(tax_code),
            compress_whitespace=True,
            field_pos_page_horizontal=field_pos_page_horizontal,
            # Must be really large, vertical limitations come from SearchRelations
            field_vertical_line_scale=field_vertical_line_scale,
            max_l_dist=0,
            converter=CurrencyConverter(
                allow_negative_values=allow_negative_values,
                cut_off_single_char_right=cut_off_single_char_right,
                # As of 220304 we do not cut off fractions due to spacing in AI tax (e.g. '4 3 8 65' -> 43865)
                # If tax declarations with fractions occur a solution for these cantons needs to be found.
                cut_off_fractions=False,
            ),
            relations=rel,
        )
        search_elements.append(se)
        last_key = elem.name + "_anchor"
        last_tax_code = tax_code

    return search_elements


def create_search_elements_personal_data_lu_sh(page: Page):
    return (
        [
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                page.fullpage,
                text_top="PID-Nr.",
                text_left="Bei Trennung oder Scheidung im Jahr",
                text_bottom="Bei vertraglicher Vertretung ist nebenstehend",
            ),
        ]
        + create_search_elements_address(page.fullpage, page)
        + [
            create_search_element_p1_fullname_from_address(),
            SearchElementArea(
                "person_names",
                page.header,
                target_name=FIELD_FIRSTNAME.sr_inside,
                extract=False,
                converter=ParagraphConverter(max_num_lines=2),
            ),
            SearchElementConstrainedArea(
                P1_DATE_OF_BIRTH.name,
                page.fullpage,
                text_top="Einzelperson / Ehemann",
                text_left="Geburtsdatum",
                text_right="Ehefrau",
                text_bottom="Zivilstand",
                converter=DateConverter(),
            ),
            SearchElementConstrainedArea(
                P2_DATE_OF_BIRTH.name,
                page.fullpage,
                text_top="Einzelperson / Ehemann",
                text_left="Ehefrau / Partn.",
                text_bottom="Vorname",
                converter=DateConverter(),
            ),
            SearchElementStaticText(
                "title_personal_info",
                page.main,
                label="Personalien, Berufs- und Familienverhältnisse",
                max_l_dist=6,
                extract=False,
            ),
            SearchElementStaticText(
                "title_children",
                page.main,
                label="oder in Ausbildung stehende Kinder, deren Unterhalt",
                max_l_dist=6,
                extract=False,
            ),
            SearchElementArea(
                "section_personal_info",
                page.main,
                extract=False,
                relations=[
                    SearchRelationBelow("title_personal_info"),
                    SearchRelationRightOf(
                        "title_personal_info",
                        ref_boundary=ReferenceBoundaryHorizontal.LEFT,
                    ),
                    SearchRelationAbove("title_children"),
                ],
            ),
            SearchElementLabeledField(
                P2_FIRSTNAME.name,
                page.main,
                target_name="section_personal_info",
                label="Vorname",
                max_l_dist=3,
                field_position=FieldPosition.RIGHT_OF_LABEL,
            ),
        ]
    )
