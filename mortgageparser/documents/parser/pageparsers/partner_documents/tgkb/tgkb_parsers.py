from abbyyplumber.converter.ValueConverter import CleanNameConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)

parsers_de = [
    SmartLetterPageParser(
        desc="TGKB Ermächtigung",
        doc_cat=DocumentCat.BROKER_AUTHORIZATION_BANK_SECRECY,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        titles=["HINWEIS AUF VERMITTLUNGSKOMMISSION UND"],
        company="TKB",
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="vermittelt",
            text_right="(nachstehend Neukunde",
            text_bottom="als potentiellen Kunden",
            converter=CleanNameConverter(max_num_lines_valid=1),
        ),
        required_tokens=[
            "ENTBINDUNG VOM BANKKUNDENGEHEIMNIS",
            "als potentiellen Kunden",
            "Thurgauer Kantonalbank, Bankplatz 1",
            "im Zusammenhang mit diesen Erklärungen ist 8570 Weinfelden.",
        ],
    ),
]

parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_tgkb():
    return parsers_de + parsers_en + parsers_fr + parsers_it
