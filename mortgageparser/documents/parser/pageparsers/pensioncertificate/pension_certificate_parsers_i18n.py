from abbyyplumber.converter.ValueConverter import (
    ParagraphConverter,
    StringSelectorConverter,
    CurrencyConverter,
)
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementArea,
    SearchElementUpperHalf,
    create_labeled_field_vertical,
    create_labeled_field,
    SearchElementMultiLabeledField,
    SearchElementLabeledField,
    SearchElementTableCell,
    create_table_cols,
    TableCol,
)
from abbyyplumber.util.plumberstudio_util import (
    PercentageRange,
    RANGE_RIGHT,
    RANGE_LEFT,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.pension_certificate_util import (
    TemplatePensionCertificatePageParser,
    PensionCertificateSearchElements,
)


parsers_multi = [
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "fr"],
        desc="Pension2 Allianz Sammelstiftung DE/FR 2017",
        company="Allianz",
        ranked_titles_any=[
            RankedTitle("Vorsorge - Ausweis per"),
            RankedTitle("Certificat d'assurance au"),
        ],
        use_ahv_new=True,
        use_se=True,
        required_tokens_any=[
            ["Sammelstiftung BVG", "Fondation collective LPP"],
            ["der Allianz Suisse", "de l'Allianz Suisse"],
            ["Angaben zur Person", "Données personelles"],
        ],
        regions=[
            SearchElementConstrainedArea(
                "region_person",
                None,
                texts_top=["Angaben zur Person", "Données personelles"],
                texts_bottom=[
                    "Angaben zur Erwerbs",
                    "Indications sur la capacité de gain",
                ],
            ),
            SearchElementConstrainedArea(
                "region_salary",
                None,
                texts_bottom=[
                    "Angaben zum Altersguthaben",
                    "Données sur l'avoir de vieillesse",
                ],
            ).below("region_person"),
            SearchElementConstrainedArea(
                "region_assets",
                None,
                texts_bottom=["Altersleistungen", "Prestations de vieillesse"],
            ).below("region_salary"),
            SearchElementConstrainedArea("region_benefits", None).below(
                "region_assets"
            ),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field(
                ["Vorsorge - Ausweis per", "Certificat d'assurance au"]
            ),
            lastname=create_labeled_field({"Name": 0, "Nom": 0}, "region_person"),
            firstname=create_labeled_field(
                {"Vorname": 2, "Prénom": 1}, "region_person"
            ),
            date_of_birth=create_labeled_field(
                [
                    "Geburtsdatum / Geschlecht / AHV-Nr.",
                    "Date de naissance / Sexe / N° AVS",
                ],
                "region_person",
            ),
            marital_status=create_labeled_field(
                ["Zivilstand / Heiratsdatum", "Etat civil / Date du mariage"],
                "region_person",
            ).with_converter(StringSelectorConverter(suffix="/")),
            # employer=SearchElementArea(None, None, target_name="region_employer", converter=ParagraphConverter(max_num_lines=1, max_num_lines_valid=5)),
            # product=SearchElementArea(None, None, target_name="region_product"),
            applicable_annual_salary_declared=create_labeled_field(
                ["Gemeldeter Jahreslohn", "Salaire annuel annoncé"], "region_salary"
            ),
            applicable_annual_salary_insured=create_labeled_field(
                ["Versicherter Jahreslohn", "Salaire annuel assuré"],
                "region_salary",
                field_vertical_line_scale=2.5,
                field_pos_page_horizontal=PercentageRange(0.88, 1),
            ),
            degree_employment=create_labeled_field(
                ["Beschäftigungsgrad", "Degré d'occupation"]
            ),
            current_assets=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_assets",
                labels={
                    "Vorhandenes Altersguthaben": 3,
                    "Avoir de vieillesse disponible": 4,
                },
                field_vertical_line_scale=2.5,
                field_pos_page_horizontal=PercentageRange(0.88, 1),
            ),
            projected_assets_retirement=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_benefits",
                labels={"mit Zins": 2, "avec intérêts": 2},
                field_vertical_line_scale=2.5,
                field_pos_page_horizontal=PercentageRange(0.88, 1),
            ),
            projected_pension_retirement=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_benefits",
                labels={
                    "Voraussichtliche jährliche Altersrente": 5,
                    "Rente annuelle de vieillesse présumée": 5,
                },
                field_vertical_line_scale=2.5,
                field_pos_page_horizontal=PercentageRange(0.88, 1),
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        company="APK",
        desc="Pension2 APK Page 1/2 DE 2016 / 1",
        ranked_titles_any=[RankedTitle("Vorsorgeauseweis gültig ab")],
        required_tokens=[
            "@agpk.ch",
            "PK-Nr.",
            "Aarau",
            "Austrittsleistung gemäss Art. 15 Freizügigkeitsgesetz",
            "Entwicklung im Vorjahr",
        ],
        use_ahv_new=False,
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                text_bottom="Vorsorgeauseweis gültig ab",
                x_range=RANGE_RIGHT,
            ),
            SearchElementConstrainedArea(
                "region_person_left",
                None,
                texts_top=["Grundlagen"],
                texts_right=["Geburtsdatum"],
                texts_bottom=["Beiträge"],
            ),
            SearchElementConstrainedArea(
                "region_assets",
                None,
                texts_top=["Austrittsleistung"],
                texts_bottom=["Entwicklung im Vorjahr"],
            ),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"Vorsorgeausweis gültig ab": 3}),
            employer=create_labeled_field("Arbeitgeber", "region_person_left"),
            applicable_annual_salary_declared=create_labeled_field(
                ["Anrechenbarer Jahreslohn"], "region_person_left"
            ),
            applicable_annual_salary_insured=create_labeled_field(
                "Versicherter Lohn (VL)", "region_person_left"
            ),
            date_of_birth=create_labeled_field(["Geburtsdatum"]),
            current_assets=create_labeled_field("Total Austrittsleistung"),
        ),
    ),
    # Same for APK and AGPK
    TemplatePensionCertificatePageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="APK",
        desc="Pension2 APK Page 1/2 DE 2016 / 2",
        required_tokens=[
            "PK-Nr.",
            "Vorsorgeausweis gültig ab",
            "Todesfallleistungen",
            "Simulation Altersleistungen",
            "Ein Teil des Sparguthabens kann in Kapital- anstatt in Rentenform bezogen werden.",
            "Für die effektive Leistungspflicht der Vorsorgeeinrichtung ist einzig das",
        ],
        use_ahv_new=False,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                "region_table",
                None,
                texts_top=["Sparguthaben gemäss"],
                texts_bottom=["Die Alterskinderrente"],
            ).include_top()
        ]
        + create_table_cols(
            "region_table",
            [
                TableCol("Alter", "region_col_key"),
                TableCol("guthaben gemäss", "region_col_assets"),
                TableCol("Jährliche Rente", "region_col_pension"),
            ],
        ),
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"Vorsorgeausweis gültig ab": 3}),
            projected_assets_retirement=SearchElementTableCell(
                None, None, label="65", name_col_val="region_col_assets"
            ),
            projected_pension_retirement=SearchElementTableCell(
                None, None, label="65", name_col_val="region_col_pension"
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "en"],
        company="AXA",
        desc="Pension2 Axa Page 1 DE/EN 2017",
        ranked_titles_any=[
            RankedTitle("Pensionskassenausweis"),
            RankedTitle("Pension certificate dated"),
        ],
        required_tokens_any=[
            ["Ihre Personalien", "Personal details for"],
            ["AXA Leben AG"],
            ["Vertrag Nr"],
            ["Ihr Altersguthaben per"],
        ],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                "region_product",
                None,
                texts_top=["Pensionskassenausweis"],
                texts_right=["Pensionskassenausweis"],
                texts_bottom=["Pensionskassenausweis"],
            )
            .include_right()
            .include_top(-4),
            SearchElementConstrainedArea(
                "region_employer",
                None,
                texts_top=["Gültig ab"],
                texts_left=["Vertrag Nr"],
                texts_bottom=["Ihre Personalien"],
            ).include_left(),
            SearchElementConstrainedArea(
                "region_personal_left",
                None,
                texts_top=["Ihre Personalien"],
                texts_right=["Versicherungsbeginn"],
                texts_bottom=["Altersguthaben"],
            ).include_top(),
            SearchElementConstrainedArea(
                "region_personal_right",
                None,
                texts_top=["Ihre Personalien"],
                texts_left=["Versicherungsbeginn"],
                texts_bottom=["Altersguthaben"],
            )
            .include_top()
            .include_left(),
            SearchElementConstrainedArea(
                "region_assets",
                None,
                texts_top=["Altersguthaben"],
                texts_bottom=["Ihr Altersguthaben per"],
            ).include_vertical(),
            SearchElementConstrainedArea(
                "region_benefits_retirement",
                None,
                texts_top=["Voraussichtliche Leistungen im Alter"],
                texts_bottom=["Voraussichtliche Leistungen im Alter"],
            ).include_bottom(8),
        ],
        se=PensionCertificateSearchElements(
            document_date=SearchElementUpperHalf(),
            fullname=create_labeled_field(
                {"Name / Vorname": 3},
                "region_personal_left",
                field_vertical_line_scale=3,
            ),
            employer=SearchElementArea(
                None,
                None,
                target_name="region_employer",
                converter=ParagraphConverter(max_num_lines=1, max_num_lines_valid=5),
            ),
            product=SearchElementArea(None, None, target_name="region_product"),
            date_of_birth=create_labeled_field(
                ["Geburtsdatum"], "region_personal_left", field_vertical_line_scale=3
            ),
            applicable_annual_salary_declared=create_labeled_field(
                ["Jahreslohn"], "region_personal_right", field_vertical_line_scale=3
            ),
            current_assets=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_assets",
                x_range=PercentageRange(0.85, 0),
            ),
            projected_assets_retirement=create_labeled_field_vertical(
                ["Alterskapital"]
            ),
            projected_pension_retirement=create_labeled_field_vertical(["Altersrente"]),
        ),
    ),
    TemplatePensionCertificatePageParser(
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        supported_languages=["de", "en"],
        company="AXA",
        desc="Pension2 Axa Page 2 DE/EN 2017",
        ranked_titles_any=[RankedTitle("Pensionskassenausweis")],
        required_tokens_any=[
            ["AXA Leben AG"],
            ["Vertrag Nr"],
            ["Gültig ab"],
            [
                "www.axa.ch/meine-Pensionskasse",
                "im Auftrag Ihrer Pensionskasse durch die AXA Leben AG, 8401 Winterthur",
            ],
            [
                "Grundlage des Pensionskassenausweises",
                "Grundlage des Pensionskassenausweises ist das Reglement Ihrer Pensionskasse",
            ],
        ],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                "region_vested",
                None,
                texts_top=["Anspruch bei Austritt vor dem"],
                texts_left=[
                    "Überobligatorischer",
                    "Arbeitgeber",
                    "Ihrer Pensionskasse",
                ],
                texts_bottom=["Anspruch bei Austritt vor dem"],
            ).include_bottom(3),
            SearchElementConstrainedArea(
                "region_wef",
                None,
                texts_top=["Vorbezug für Wohneigentum"],
                texts_left=[
                    "Überobligatorischer",
                    "Arbeitgeber",
                    "Ihrer Pensionskasse",
                ],
                texts_bottom=["Vorbezug für Wohneigentum"],
            ).include_bottom(4),
        ],
        se=PensionCertificateSearchElements(
            fullname=create_labeled_field_vertical(
                ["Gültig ab"], offset_right=15
            ).above("region_vested"),
            withdrawal_benefit=SearchElementConstrainedArea(
                None, None, target_name="region_vested"
            ),
            wef_pledging_possible_amount=SearchElementArea(
                None, None, target_name="region_wef"
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "en"],
        company="Baloise",
        desc="Pension2 Baloise Page 1 DE/EN/FR 2021",
        ranked_titles_any=[
            RankedTitle("Vorsorgeausweis per"),
            RankedTitle("Pension certificate dated"),
            RankedTitle("Certificat de prévoyance au"),
        ],
        required_tokens_any=[
            ["Personendaten für", "Personal details for", "Données personelles pour"],
            ["Aeschengraben 21"],
            [
                "Massgebende Jahreslöhne",
                "Applicable annual salaries",
                "Salaires annuels déterminants",
            ],
            # This can be also on page 2
            # ['Voraussichtliche Leistungen im Alter', 'Projected benefits on retirement', '']
        ],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                texts_bottom=[
                    "Vorsorgeausweis per",
                    "Pension certificate dated",
                    "Certificat de prévoyance au",
                ],
                x_range=RANGE_RIGHT,
            ),
            SearchElementConstrainedArea(
                "region_personal",
                None,
                texts_top=[
                    "Personendaten",
                    "Personal details",
                    "Données personelles pour",
                ],
                texts_bottom=["Löhne für die", "Salaries for the", "Salaires pour le"],
            ).include_top(),
            SearchElementConstrainedArea(
                "region_marital",
                None,
                target_name="region_personal",
                texts_top=[
                    "Beschäftigungsgrad",
                    "degree of employment",
                    "degré d'occupation",
                ],
                texts_left=[
                    "Beschäftigungsgrad",
                    "degree of employment",
                    "degré d'occupation",
                ],
                texts_bottom=[
                    "Beschäftigungsgrad",
                    "degree of employment",
                    "degré d'occupation",
                ],
            ).include_vertical(),
            SearchElementConstrainedArea(
                "region_salary",
                None,
                texts_top=["Löhne für die", "Salaries for the"],
                texts_bottom=[
                    "Voraussichtliche Leistungen im",
                    "Projected retirement benefits",
                    "Prestations de vieillesse prévisibles",
                ],
            ).include_top(),
            # Section with the actual numbers (now) and projection to age 64
            SearchElementConstrainedArea(
                "region_assets",
                None,
                texts_top=[
                    "Voraussichtliche Leistungen im",
                    "Projected retirement benefits",
                    "Prestations de vieillesse prévisibles",
                ],
                texts_bottom=[
                    "Altersrente",
                    "Retirement pension",
                    "Rente de vieillesse",
                ],
            ).include_vertical(offset_bottom=2),
            SearchElementConstrainedArea(
                "region_assets_values",
                None,
                target_name="region_assets",
                texts_left=["Gemäss BVG", "Pursuant to the", "Selon la LPP"],
            ),
            # # Section with 64, 63, 62, ... This section is optional, can also be on the next page
            # SearchElementConstrainedArea("region_benefits_retirement", None,
            #                              texts_top=["Voraussichtliche Leistungen bei Pensionierung",
            #                                         "Projected benefits on retirement", "Prestations prévisibles au moment"],
            #                              texts_bottom=["Voraussichtliche Leistungen bei Pensionierung",
            #                                            "Projected benefits on retirement", "Prestations prévisibles au moment"]).include_top().include_bottom(
            #     8),
        ],
        se=PensionCertificateSearchElements(
            document_date=SearchElementUpperHalf(),
            fullname=create_labeled_field(
                {
                    "Personendaten für": 3,
                    "Personal details for": 3,
                    "Données personelles pour": 4,
                },
                "region_personal",
            ),
            marital_status=SearchElementArea(
                None,
                None,
                target_name="region_marital",
                converter=StringSelectorConverter(suffixes=["/", " i "]),
            ),
            degree_employment=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_marital",
                converter=StringSelectorConverter(prefixes=["/", " i "]),
            ),
            employer=create_labeled_field(
                {"Vorsorgekasse": 2, "Pension fund": 2, "Caisse de prévoyance": 4},
                "region_personal",
            ),
            product=create_labeled_field(
                {"Kategorie": 2, "Category": 2}, "region_personal"
            ),
            applicable_annual_salary_declared=create_labeled_field_vertical(
                ["Gemeldet", "Declared", "Annoncé"],
                offset_left=-5,
                offset_right=2,
                offset_bottom=4,
            ),
            current_assets=create_labeled_field(
                [
                    "Aktuelles Altersguthaben",
                    "Current pension assets",
                    "Avoir de viellesse actuel",
                ],
                "region_assets_values",
                target_name_label="region_assets",
            ),
            projected_assets_retirement=create_labeled_field(
                [
                    "Altersguthaben mit Zins",
                    "assets with interest",
                    "projeté avec intérêts",
                ],
                "region_assets_values",
                target_name_label="region_assets",
            ),
            projected_pension_retirement=create_labeled_field(
                ["Altersrente", "Retirement pension", "Rente de viellesse"],
                "region_assets_values",
                target_name_label="region_assets",
            ),
            # projected_pension_retirement=create_labeled_field_vertical(["Rente", "Pension", "Rente"],
            #                                                            "region_benefits_retirement",
            #                                                            offset_left=-5, offset_right=2,
            #                                                            offset_bottom=5).with_converter(
            #    CurrencyConverter(fail_if_multiple_lines=False)),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "en"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="Baloise",
        desc="Pension2 Baloise Page 2 EN 2021",
        required_tokens_any=[
            ["Versichertennummer", "Policy number"],
            ["Davon Sparbeitrag", "Of which, savings premium"],
            ["Austrittsleistung gem.Art 15 FZG", "Withdrawal benefit pursuant to Art."],
            [
                "Kapitaloption bei Pensionierung angemeldet",
                "Lump-sum payment option on retirement registered",
            ],
            ["Zusatzinformationen", "Additional information"],
        ],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                "region_additional",
                None,
                texts_top=["Zusatzinformationen", "Additional information"],
                texts_bottom=[
                    "Alle Beträge in Schweizer Franken",
                    "All amounts are stated in Swiss Francs",
                ],
            ),
            SearchElementConstrainedArea(
                "region_benefits_retirement",
                None,
                texts_top=[
                    "Voraussichtliche Leistungen bei Pensionierung",
                    "Projected benefits on retirement",
                ],
                texts_bottom=[
                    "Voraussichtliche Leistungen bei Pensionierung",
                    "Projected benefits on retirement",
                ],
            )
            .include_top()
            .include_bottom(8),
        ],
        se=PensionCertificateSearchElements(
            wef_pledging_registered_status=create_labeled_field(
                [
                    "Verpfändung für Wohneigentum angemeldet",
                    "Pledging for home ownership registered",
                ],
                "region_additional",
            ),
            withdrawal_benefit=create_labeled_field(
                [
                    "Austrittsleistung gem. Art. 15",
                    "Withdrawal benefit pursuant to Art. 15",
                ],
                "region_additional",
                field_pos_page_horizontal=PercentageRange(0.85, 1),
            ),
            projected_pension_retirement=create_labeled_field_vertical(
                ["Rente", "Pension"],
                "region_benefits_retirement",
                offset_left=-5,
                offset_right=2,
                offset_bottom=5,
            ).with_converter(CurrencyConverter(fail_if_multiple_lines=False)),
        ),
    ),
    TemplatePensionCertificatePageParser(
        page_cat=PageCat.GENERIC_PAGE,
        doc_cat=DocumentCat.PENSION_WITHDRAWL,
        # supported_languages=['de', 'en'],
        company="Baloise",
        desc="Pension2 Baloise Withdrawl DE 2021",
        required_tokens_any=[
            ["Vorbezug für Ihr Eigenheim"],
            ["Das vorbezogene Kapital unterliegt", "Bei einem Vorbezug von CHF"],
            ["Sehr geehrte", "Freundliche Grüsse"],
        ],
    ),
    TemplatePensionCertificatePageParser(
        # supported_languages=['de', 'en'],
        company="BVK",
        desc="Pension2 BVK Page 1/1 DE 2021",
        required_tokens=[
            "BVK Personalvorsorge des Kantons Zürich",
            "Vorsorgeausweis per",
            "Persönliche Daten",
            "Versicherter Lohn",
            "Voraussichtliche Altersleistungen",
        ],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                text_bottom="Vorsorgeausweis per",
                x_range=RANGE_LEFT,
            ),
            SearchElementConstrainedArea(
                "region_personal",
                None,
                text_top="Persönliche Daten",
                text_bottom="Beschäftigungsgrad",
                x_range=PercentageRange(0, 0.6),
            ).include_vertical(),
            SearchElementConstrainedArea(
                "region_assets",
                None,
                text_top="Davon BVG-Anteil",
                text_right="Davon BVG-Anteil",
                text_bottom="Davon BVG-Anteil",
            ).include_vertical(),
            SearchElementConstrainedArea(
                "region_benefits_retirement",
                None,
                text_top="Voraussichtliche Altersleistung",
                text_bottom="Die Höhe der Altersrente kann",
            ),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"Vorsorgeausweis per": 4}),
            date_of_birth=create_labeled_field("Geburtsdatum", "region_personal"),
            employer=create_labeled_field_vertical(
                "Arbeitgeber", None, offset_left=-20
            ),
            degree_employment=create_labeled_field(
                "Beschäftigungsgrad", "region_personal"
            ),
            # product=create_labeled_field({"Kategorie": 2, "Category":2}, "region_personal"),
            applicable_annual_salary_declared=create_labeled_field(
                "Anrechenbarer Jahreslohn in CHF", "region_personal"
            ),
            current_assets=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_assets",
                x_range=PercentageRange(0.75, 1),
            ).include_top(),
            projected_assets_retirement=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_benefits_retirement",
                text_top="Sparguthaben ohne künftige Zinsgutschriften:",
                text_left="Sparguthaben ohne künftige Zinsgutschriften:",
                text_bottom="Sparguthaben ohne künftige Zinsgutschriften:",
            ).include_top(-2),
        ),
    ),
    TemplatePensionCertificatePageParser(
        # supported_languages=['de', 'en'],
        company="comPlan",
        desc="Pension2 comPlan Page 1/2 DE 2016",
        ranked_titles_any=[
            RankedTitle("Vorsorgeausweis per"),
            RankedTitle("Persönliche Informationen"),
            RankedTitle("Beiträge"),
        ],
        required_tokens=["www.pk-complan.ch"],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                text_bottom="Vorsorgeausweis per",
                x_range=RANGE_LEFT,
            ),
            SearchElementConstrainedArea(
                "region_personal_left",
                None,
                text_top="Persönliche Informationen",
                text_bottom="Leistungen",
                text_right="Arbeitgeber",
                text_right_offset_chars=-2,
            ),
            SearchElementConstrainedArea("region_personal_right", None)
            .align_vertical_with("region_personal_left")
            .rightof("region_personal_left"),
            SearchElementConstrainedArea(
                "region_benefits_retirement",
                None,
                text_top="Leistungen",
                text_bottom="Leistungen",
            ).include_bottom(7),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"Vorsorgeausweis per": 4}),
            date_of_birth=create_labeled_field("Geburtsdatum:", "region_personal_left"),
            marital_status=create_labeled_field("Zivilstand:", "region_personal_left"),
            employer=create_labeled_field("Arbeitgeber:", "region_personal_right"),
            applicable_annual_salary_declared=create_labeled_field(
                "Jahreslohn:", "region_personal_right"
            ),
            degree_employment=create_labeled_field(
                "Beschäftigungsgrad:", "region_personal_right"
            ),
            projected_assets_retirement=create_labeled_field_vertical(
                "AGH Total",
                "region_benefits_retirement",
                offset_left=-4,
                offset_bottom=2.3,
            ),
            projected_pension_retirement=create_labeled_field_vertical(
                "AR/Jahr",
                "region_benefits_retirement",
                offset_left=-5,
                offset_bottom=2.3,
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        # supported_languages=['de', 'en'],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="comPlan",
        desc="Pension2 comPlan Page 2/2 DE 2016",
        ranked_titles_all=[
            RankedTitle("Kontoauszug"),
            RankedTitle("generelle Information"),
        ],
        required_tokens=[
            "reglementarisches Altersguthaben",
            "maximale Einkaufssumme für die ordentlichen Leistungen",
            "www.pk-complan.ch",
        ],
        use_ahv_new=False,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                "region_assets",
                None,
                text_top="Zinsen",
                text_bottom="generelle Informationen",
            ),
            SearchElementConstrainedArea(
                "region_additional",
                None,
                text_top="generelle Informationen",
                text_bottom="Bemerkungen",
            ),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"Kontobewegungen vom": 4}),
            current_assets=create_labeled_field(
                "reglementarisches Altersguthaben:", "region_additional"
            ),
            wef_pledging_possible_amount=create_labeled_field(
                "maximal möglicher Vorbezug für Wohneigentum", "region_additional"
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "en"],
        company="Helvetia",
        desc="Pension2 Helvetia Page 1 DE 2021",
        ranked_titles_any=[RankedTitle("helvetia")],
        required_text_conditions=[
            FromStartTextCond("Helvetia Versicherungen", num_lines=25),
            FromStartTextCond("Es betreut Sie", num_lines=25),
        ],
        required_tokens_any=[
            ["Vorsorgeausweis gültig ab"],
            ["Postfach 99"],
            ["Personendaten"],
            ["@helvetia.ch"],
            ["Organisationseinheit"],
            ["Personenkategorie"],
        ],
        use_ahv_new=False,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                texts_bottom=["Vertrag Nr"],
                x_range=RANGE_LEFT,
            ),
            # Section with the actual numbers (now) and projection to age 64
            SearchElementConstrainedArea(
                "region_assets", None, texts_top=["Altersguthaben"]
            ).include_top(),
            SearchElementConstrainedArea(
                "region_salary", None, texts_top=["Gehaltsdaten"]
            )
            .above("region_assets")
            .include_top(),
            SearchElementConstrainedArea(
                "region_personal",
                None,
                texts_top=["Personendaten", "Personal details", "Données personelles"],
            )
            .above("region_salary")
            .include_top(),
            create_labeled_field_vertical(
                ["Total in CHF"], offset_bottom=20, name="region_values"
            ),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field("gültig ab"),
            fullname=create_labeled_field(
                ["Name und Vorname"], "region_personal", field_vertical_line_scale=2.5
            ),
            date_of_birth=create_labeled_field(
                ["Geburtsdatum / Geschlecht"], "region_personal"
            ),
            marital_status=create_labeled_field(
                ["Zivilstand"], "region_personal", field_vertical_line_scale=2.5
            ),
            # These extractions are still untested because of skewed example
            applicable_annual_salary_declared=create_labeled_field(
                ["Gemeldetes Jahresgehalt"],
                "region_values",
                target_name_label="region_salary",
                field_vertical_line_scale=2.5,
            ),
            current_assets=create_labeled_field(
                ["Voraussichtliches Altersguthaben per"],
                "region_values",
                target_name_label="region_assets",
                field_vertical_line_scale=2.5,
            ),
            projected_assets_retirement=create_labeled_field(
                [
                    "Projiziertes Altersguthaben mit Zins",
                    "assets with interest",
                    "projeté avec intérêts",
                ],
                "region_values",
                target_name_label="region_assets",
                field_vertical_line_scale=2.5,
            ),
            # projected_pension_retirement=create_labeled_field_vertical(["Rente", "Pension", "Rente"],
            #                                                            "region_benefits_retirement",
            #                                                            offset_left=-5, offset_right=2,
            #                                                            offset_bottom=5).with_converter(
            #    CurrencyConverter(fail_if_multiple_lines=False)),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de"],
        company="Publica",
        desc="Pension2 Publica Page 1/2 DE 2017 (old version, 2021 is different)",
        required_tokens=[
            "Pensionskasse des Bundes PUBLICA",
            "Persönliche Daten",
            "Austrittsleistung",
            "publica.ch",
            "Persönlicher Ausweis per",
        ],
        use_ahv_new=True,
        use_se=True,
        page_main=SearchElementConstrainedArea(
            None, None, text_left="Persönlicher Ausweis per", text_right="pro Monat"
        ).include_all(),
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                text_bottom="Persönlicher Ausweis per",
                x_range=RANGE_RIGHT,
            ),
            SearchElementConstrainedArea(
                "region_table_top",
                None,
                text_top="Kontakt:",
                text_bottom="SV-Nr.",
                x_range=RANGE_LEFT,
            ).include_all(),
            SearchElementConstrainedArea(
                "region_personal_left",
                None,
                text_top="Persönliche Daten",
                text_right="Lohndaten",
                text_bottom="Beiträge versicherte Person",
            ),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field(["Persönlicher Ausweis per"]),
            employer=create_labeled_field("Arbeitgeber:", "region_table_top"),
            product=create_labeled_field("Vorsorgeplan:", "region_table_top"),
            date_of_birth=create_labeled_field(
                ["Geburtsdatum:"], "region_personal_left"
            ),
            degree_employment=create_labeled_field(
                ["Beschäftigungsgrad:"], "region_personal_left"
            ),
            applicable_annual_salary_declared=create_labeled_field(
                ["Massgebender Jahreslohn:"]
            ),
            current_assets=create_labeled_field("Reglementarische Austrittsleistung"),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="Publica",
        desc="Pension2 Publica Page 2/2 DE 2017 (old version, 2021 is different)",
        required_tokens=[
            "publica",
            "Voraussichtliche Altersrente mit Projektionszins",
            "Invalidenrente",
            "Bemerkung",
            "www.publica.ch",
        ],
        use_ahv_new=True,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                "region_table",
                None,
                text_top="Voraussichtliche Altersrente",
                text_bottom="Die Alters-Kinderrente",
            ).include_bottom(),
            SearchElementConstrainedArea(
                "region_col_age", None, target_name="region_table", text_right="Alter"
            ).include_all(),
            SearchElementConstrainedArea(
                "region_table_rest",
                None,
                target_name="region_table",
                text_top="Die Alters-Kinderrente",
                text_bottom="Die Alters-Kinderrente",
            )
            .include_top(-3)
            .rightof("region_col_age"),
        ],
        se=PensionCertificateSearchElements(
            projected_assets_retirement=SearchElementArea(
                None,
                None,
                target_name="region_table_rest",
                x_range=PercentageRange(0, 0.143),
            ),
            projected_pension_retirement=SearchElementArea(
                None,
                None,
                target_name="region_table_rest",
                x_range=PercentageRange(0.72, 0.86),
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_ONE,
        company="Swisscanto",
        desc="Pension2 Swisscanto Page 1/2 DE 2017 (for Kantonalbanken)",
        ranked_titles_any=[RankedTitle("Vorsorgeausweis gültig ab")],
        required_tokens_any=[
            ["Sammelstiftung der Kantonalbanken"],
            ["Personalvorsorge-Vertrag Nr."],
            ["Erreichen Terminalter am"],
            ["Projiziertes Altersguthaben ohne Zins"],
        ],
        use_ahv_new=True,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                text_bottom="Personalvorsorge-Vertrag",
                x_range=RANGE_LEFT,
            ),
            SearchElementConstrainedArea(
                "region_table",
                None,
                text_top="Vorsorgeausweis gültig ab",
                text_left="1. Personaldaten",
                text_right="(inkl. BVG-Anteil)",
            ).include_all(),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"gültig ab": 0}),
            fullname=create_labeled_field(["Name und Vorname"]),
            date_of_birth=create_labeled_field(["Geburtsdatum / Geschlecht"]),
            marital_status=create_labeled_field(["Zivilstand"]),
            degree_employment=create_labeled_field(
                ["Beschäftigungsgrad"]
            ).with_converter(StringSelectorConverter(prefix="/")),
            applicable_annual_salary_declared=create_labeled_field(
                ["Gemeldetes Jahresgehalt"]
            ),
            current_assets=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_table",
                labels={"Voraussichtliches Altersguthaben per": 3},
                field_pos_page_horizontal=PercentageRange(0.82, 1),
            ),
            projected_assets_retirement=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_table",
                labels={"Projiziertes Altersguthaben mit Zins ": 4},
                field_pos_page_horizontal=PercentageRange(0.82, 1),
            ),
            withdrawal_benefit=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_table",
                labels={"Austrittsleistung per ": 4},
                field_pos_page_horizontal=PercentageRange(0.82, 1),
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "en"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="SwissLife",
        desc="Pension2 SL Page 1 DE 2017,2021",
        ranked_titles_any=[RankedTitle("Persönlicher Vorsorgeausweis")],
        required_tokens_any=[
            ["gültig ab"],
            ["Swiss Life Sammelstiftung 2. Säule"],
            ["Versichertengruppe"],
            ["Altersguthaben"],
            ["Freizügigkeitsanspruch"],
            ["im Alter 6"],
        ],
        use_ahv_new=True,
        regions=[
            create_labeled_field_vertical(
                ["Swiss Life Sammelstifung 2. Säule"],
                offset_right=15,
                offset_bottom=3,
                name="region_employer",
            ),
            SearchElementConstrainedArea(
                "region_name",
                None,
                texts_top=["Persönlicher Vorsorgeausweis"],
                texts_bottom=["Persönlicher Vorsorgeausweis"],
            ).include_bottom(3),
            SearchElementConstrainedArea(
                "region_personal_left",
                None,
                texts_top=["Gemeldeter Jahreslohn"],
                texts_right=["Gemeldeter Jahreslohn"],
                texts_bottom=["Beschäftigungsgrad"],
            ).include_vertical(),
            SearchElementArea("region_personal_right", None)
            .align_vertical_with("region_personal_left")
            .leftof("region_person_left"),
            SearchElementConstrainedArea(
                "region_assets",
                None,
                texts_top=["Altersguthaben"],
                texts_bottom=["Freizügigkeitsanspruch"],
            ).include_vertical(),
            SearchElementConstrainedArea(
                "region_benefits_retirement",
                None,
                texts_top=["Leistungen im"],
                texts_bottom=["Leistungen im"],
            )
            .include_top(-2)
            .include_bottom(8),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"gültig ab": 0}),
            employer=SearchElementArea(None, None, target_name="region_employer"),
            fullname=SearchElementConstrainedArea(
                None, None, target_name="region_name", texts_left=["für"]
            ),
            product=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_personal_left",
                texts_top=["Versichertengruppe"],
                texts_left=["Versichertengruppe"],
                texts_bottom=["Versicherten Nr. 7"],
            ).include_left(),
            date_of_birth=create_labeled_field(
                ["Geburtsdatum"], "region_personal_left"
            ),
            degree_employment=create_labeled_field(
                ["Beschäftigungsgrad", "region_personal_left"]
            ),
            applicable_annual_salary_declared=create_labeled_field(
                ["Gemeldeter Jahreslohn"], "region_personal_right"
            ),
            current_assets=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_assets",
                labels={"Altersguthaben am": 3},
                field_pos_page_horizontal=PercentageRange(0.85, 1),
            ),
            withdrawal_benefit=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_assets",
                labels={"Freizügigkeitsanspruch am ": 2},
                field_pos_page_horizontal=PercentageRange(0.85, 1),
            ),
            projected_assets_retirement=create_labeled_field_vertical(
                ["Kapital"],
                "region_benefits_retirement",
                offset_left=-8,
                offset_right=1,
                offset_bottom=2.5,
            ),
            projected_pension_retirement=create_labeled_field_vertical(
                ["Rente"],
                "region_benefits_retirement",
                offset_left=-8,
                offset_right=2,
                offset_bottom=2.5,
            ),
            # .with_converter(
            # CurrencyConverter(fail_if_multiple_lines=False)),
        ),
    ),
    # class SwissLifePensionCertificatePageTwoPageParser(StandardPensionCertificatePageTwoPageParser):
    #
    #     def match_page_by_text(self, page_index: int, text: str) -> bool:
    #         return contains_all_strings(text, [
    #             'Vertrag', 'Versicherten-Nr', 'Erstellt von Swiss Life am ', 'im Auftrag Ihrer Vorsorgeeinrichtung'
    #
    #         ]) or contains_all_strings(text, ['Weitergehende Informationen', 'www.swisslife.ch/protect', 'noch heute unter myworld.swisslife.ch'])
    TemplatePensionCertificatePageParser(
        supported_languages=["de", "en", "fr"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="Swiss Life",
        desc="Pension2 SL Page 2 DE 2021",
        required_tokens_any=[
            ["Weitergehende Informationen", "Vous trouverez les conditions légales"],
            [
                "www.swisslife.ch/invest",
                "www.swisslife.ch/protect",
                "www.swisslife.ch/fr/invest",
            ],
            [
                "noch heute unter myworld.swisslife.ch",
                "dés aujourd'hui sur swisslife.ch/portailclientele",
            ],
        ],
        use_ahv_new=True,
        regions=[
            SearchElementConstrainedArea(
                "region_wef",
                None,
                texts_top=["Maximal möglicher Vorbezug", "Montant maximum disponibile"],
                texts_bottom=[
                    "Verpfändung für Wohneigentum",
                    "Mise en gage pour la propriété",
                ],
            ).include_vertical()
        ],
        se=PensionCertificateSearchElements(
            fullname=SearchElementConstrainedArea(
                None,
                None,
                texts_top=["Vertrag", "Contrat"],
                texts_right=["Geburtsdatum", "Date de naissance"],
                texts_bottom=["Geburtsdatum", "Date de naissance"],
            ).include_bottom(),
            wef_pledging_registered_status=create_labeled_field(
                [
                    "Verpfändung für Wohneigentum:",
                    "Mise en gage pour la propriété du logement:",
                ],
                "region_wef",
            ),
            wef_pledging_possible_amount=SearchElementMultiLabeledField(
                None,
                None,
                labels={
                    "Maximal möglicher Vorbezug für Wohneigentum": 5,
                    "Montant maximum disponible pour la propriété du logement": 7,
                },
                field_pos_page_horizontal=PercentageRange(0.85, 1),
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_ONE,
        company="Vita",
        desc="Pension2 Vita Sammelstiftung Page 1/2 DE 2017",
        required_tokens_any=[
            ["Sammelstiftung Vita"],
            ["Vertrags-Nummer:"],
            ["Policen-Nummer"],
            ["Vorsorgeausweis"],
            ["Name/Vorname"],
            ["Zivilstand/Heiratsdatum"],
            ["BVG-Jahreslohn"],
        ],
        use_ahv_new=True,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                "region_table",
                None,
                text_top="Altersleistungen",
                text_bottom="Voraussichtliche Altersleistungen bei vorzeitiger",
            )
            .include_top(-3)
            .include_bottom(-3),
            SearchElementConstrainedArea(
                "region_table_total",
                None,
                target_name="region_table",
                text_right="Gesamt",
            ).include_right(2),
        ],
        se=PensionCertificateSearchElements(
            document_date=create_labeled_field({"Stand am": 1}),
            fullname=create_labeled_field(["Name/Vorname"]),
            date_of_birth=create_labeled_field(["Geburtsdatum"]),
            marital_status=create_labeled_field(
                ["Zivilstand/Heiratsdatum"]
            ).with_converter(StringSelectorConverter(suffix="/")),
            applicable_annual_salary_declared=create_labeled_field(
                ["Gemeldeter Jahreslohn"]
            ),
            degree_employment=create_labeled_field(["Beschäftigungsgrad"]),
            current_assets=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_table_total",
                labels={"Stand Sparkapital": 3},
                field_pos_page_horizontal=PercentageRange(0.7, 1),
            ),
            projected_assets_retirement=SearchElementMultiLabeledField(
                None,
                None,
                target_name="region_table_total",
                labels={"mit Zins": 1},
                field_pos_page_horizontal=PercentageRange(0.7, 1),
            ),
        ),
    ),
    TemplatePensionCertificatePageParser(
        supported_languages=["de"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="Vita",
        desc="Pension2 Vita Sammelstiftung Page 2/2 DE 2017",
        required_tokens_any=[
            [
                "Dieser Ausweis ersetzt alle früheren Ausweise. Für die Leistungspflicht der Stiftung ist das aktuelle"
            ],
            ["Vorsorgereglement im Internet unter www.vita.ch massgebend."],
            ["Help Point BVG"],
        ],
        use_ahv_new=True,
        use_se=True,
        regions=[
            SearchElementConstrainedArea(
                "region_assets",
                None,
                text_top="Austrittsleistung",
                text_right="Gesamt",
                text_bottom="Austrittsleistung",
            )
            .include_top(-3)
            .include_bottom(3)
            .include_right(2),
            SearchElementConstrainedArea(
                "region_wef",
                None,
                text_top="Wohneigentumsförderung",
                text_bottom="Wohneigentumsförderung",
            ).include_bottom(3),
        ],
        se=PensionCertificateSearchElements(
            withdrawal_benefit=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_assets",
                text_top="Austrittsleistung",
                x_range=RANGE_RIGHT,
            ),
            wef_pledging_registered_status=SearchElementLabeledField(
                None,
                None,
                label="Verpfändung für Wohneigentumsförderung",
                target_name="region_wef",
            ),
        ),
    ),
]

parsers_fr = [
    TemplatePensionCertificatePageParser(
        supported_languages=["fr"],
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        company="Publica",
        desc="Pension2 Publica Page 2/2 FR 2019",
        required_tokens_any=[
            ["Prestation de sortie réglementaire "],
            ["La rente pour enfant de bénéficiaire"],
            ["concernant le certificat de prévoyance sous publica.ch"],
        ],
    )
]


parsers_i18n = parsers_multi + parsers_fr
