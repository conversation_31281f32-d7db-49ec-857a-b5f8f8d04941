from abbyyplumber.converter.ValueConverter import DateConverter, MostRecentDateConverter
from hypodossier.util.date_util import (
    get_localized_month,
    find_all_dates,
    sort_dates,
    find_earliest_date,
    find_date,
    find_most_recent_date,
)


def test_date_converter():
    t = "r . <PERSON> No ÀVS - Nuovo N. AVS    \n 31.12.2020]            G"
    c = DateConverter()
    x = c.convert(t)
    assert x == "31.12.2020"


def test_get_localized_month():
    assert get_localized_month(1, "en") == "January"
    assert get_localized_month(1, "de") == "Januar"
    assert get_localized_month(2, "it") == "febbraio"

    assert get_localized_month(12, "de") == "Dezember"


def test_sample():
    t = "31. Dezember 2019"
    assert DateConverter().convert(t) == "31.12.2019"


def test_date_yyyy():
    t = "asdfasdf 11.12.2013 222222222 4.9.1977 33333333 22.12.2022 4444444"
    ret = find_all_dates(t)

    assert ret == ["11.12.2013", "04.09.1977", "22.12.2022"]

    ret_sorted = sort_dates(ret)
    assert ret_sorted == ["04.09.1977", "11.12.2013", "22.12.2022"]


def test_find_earliest():
    t = "asdfasdf 11.12.2013 222222222 4.9.1977 33333333 22.12.2022 4444444"
    ret = find_earliest_date(t)
    assert ret == "04.09.1977"


def test_find_earliest_with_limit():
    t = "asdfasdf 11.12.2013 222222222 4.9.2005 33333333 22.12.2022 4444444"
    ret = find_earliest_date(t, max_year=2000)
    assert ret is None
    ret = find_earliest_date(t, max_year=2007)
    assert ret == "04.09.2005"


def test_date_underscores():
    t = "27.08.1978_____________________________"
    ret = find_earliest_date(t, max_year=2007)
    assert ret == "27.08.1978"


def test_date_underscores_with_converter():
    t = "27.08.1978_____________________________"

    ret2 = DateConverter().convert(t)
    assert ret2 == "27.08.1978"


def test_spacing():
    t = "bla 11. Dezember 201 5 hohoho"
    assert find_date(t) == "11.12.2015"


def test_find_most_recent():
    t = "asdfasdf 11.12.2013 222222222 4.9.1977 5.5.2020 33333333 22.12.2022 4444444"
    ret = find_most_recent_date(t)
    assert ret == "22.12.2022"


def test_date_yy():
    t = "asdfasdf 11.12.13 222222222 "
    ret = find_date(t)

    assert ret == "11.12.2013"


def test_recent():
    s = "25. Mai 2022"
    s2 = MostRecentDateConverter().convert(s)
    assert s2 == "25.05.2022"
