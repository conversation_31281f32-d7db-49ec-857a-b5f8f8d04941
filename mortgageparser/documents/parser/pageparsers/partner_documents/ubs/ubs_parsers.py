from abbyyplumber.converter.ValueConverter import AddressConverter, CleanNameConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
    RankedTitle,
    FromStartTextCond,
)

parsers_de = [
    SmartLetterPageParser(
        desc="UBS Empfehlungsschreiben/Flyer DE #1/2",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="UBS",
        ranked_titles_all=[RankedTitle("Zu Hause ankommen", 1)],
        required_tokens=["UBS können Sie Ihren Traum vom Eigenheim"],
    ),
    SmartLetterPageParser(
        desc="UBS Empfehlungsschreiben/Flyer DE #2/2",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_LAST_PAGE,
        company="UBS",
        ranked_titles_all=[RankedTitle("Jetzt und für die Zukunft", 1)],
        required_tokens=["Finanzierungsvorschlag", "Checkliste", "ubs.com/hypotheken"],
    ),
    SmartLetterPageParser(
        desc="UBS Autorisierung E-Mail DE #1/2",
        doc_cat=DocumentCat.AUTHORIZATION_EMAIL,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="UBS",
        titles=["Autorisierung für die Benutzung von E-Mail"],
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Juristische Person",
            text_bottom="Nachname, Vorname",
            converter=CleanNameConverter(max_num_lines_valid=1),
        ),
        required_tokens=[
            "Der Kunde ermächtigt UBS, die nachstehenden E-Mail-Adressen",
            "oder Domain-Namen zu verwenden",
        ],
    ),
    SmartLetterPageParser(
        desc="UBS Autorisierung E-Mail DE #2/2",
        doc_cat=DocumentCat.AUTHORIZATION_EMAIL,
        page_cat=PageCat.GENERIC_LAST_PAGE,
        company="UBS",
        titles=["Bankbeziehung"],
        required_tokens=[
            "Es liegt in der Verantwortung des Kunden, sich über die für öffentliche elektronische",
            "kontaktieren. UBS schliesst jegliche",
        ],
    ),
    SmartLetterPageParser(
        desc="UBS Financing Confirmation P1",
        doc_cat=DocumentCat.FINANCING_CONFIRMATION,
        page_cat=PageCat.GENERIC_PAGE,
        company="UBS",
        titles=["Finanzierungszusage"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Vertragsparteien",
            text_bottom="nachstehend Kreditnehmer",
            text_left="Vorname:",
            converter=AddressConverter(),
        ),
        use_document_date=True,
        required_tokens=[
            "UBS ist bereit, dem Kreditnehmer",
            "UBS Switzerland AG",
            "Hypothekarprodukte nach Wahl des Kreditnehmers",
            "UBS ist berechtigt, das Angebot an Hypo",
        ],
    ),
    SmartLetterPageParser(
        desc="UBS key4 Info DE #1/4",
        doc_cat=DocumentCat.PLATFORM_AGREEMENT,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="key4",
        titles=["Erläuterungen zur key4 Immobilien"],
        required_tokens=[
            "Der Abschluss einer Hypothek über die Plattform",
            "Die Finanzierungsanfrage auf der Plattform",
            "Die Rolle von UBS als",
        ],
    ),
    SmartLetterPageParser(
        desc="UBS key4 Info DE #2/4",
        doc_cat=DocumentCat.PLATFORM_AGREEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="key4",
        titles=["Die Drittinvestoren"],
        required_tokens=[
            "Die Vorteile der Plattform",
            "Die Einschränkungen der Plattform",
        ],
    ),
    SmartLetterPageParser(
        desc="UBS key4 Info DE #3/4",
        doc_cat=DocumentCat.PLATFORM_AGREEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="key4",
        titles=[
            "Weitergabe Ihrer Kundendaten / Entbindung vom Bankkundengeheimnis / Vermittler"
        ],
        required_tokens=[
            "Bitte beachten Sie, dass UBS keinerlei Haftung",
            "Bankbeziehung",
            "Damit UBS für Sie ein Finanzierungsangebot auf der Plattform ermitteln kann",
        ],
    ),
    SmartLetterPageParser(
        desc="UBS key4 Info DE #4/4",
        doc_cat=DocumentCat.PLATFORM_AGREEMENT,
        page_cat=PageCat.GENERIC_PAGE,
        company="key4",
        titles=["77451 D V2 001  11.2020"],
        required_tokens=[
            "Bankbeziehung",
            "(nur für bankinterne Zwecke)",
            "Ort / Datum",
        ],
    ),
]


parsers_en = [
    SmartLetterPageParser(
        desc="UBS Financing Request",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_LAST_PAGE,
        company="UBS",
        supported_languages=["en"],
        required_text_conditions=[
            FromStartTextCond("UBS Switzerland AG", num_lines=5),
            FromStartTextCond("Financing request", num_lines=20),
        ],
        required_tokens=[
            "Land registry",
            "Purchase price",
            "Buyer",
            "Seller",
            "we consider financing",
            "credit approval process by the bank",
        ],
    ),
]

parsers_fr = []

parsers_it = []


def get_parsers_ubs():
    return parsers_de + parsers_en + parsers_fr + parsers_it
