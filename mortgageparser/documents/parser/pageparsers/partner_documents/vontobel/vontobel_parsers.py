from abbyyplumber.plumberstudio.SearchElement import (
    create_labeled_field_horizontal,
    SearchElementConstrainedArea,
)
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.creditworthiness_misc.creditworthiness_misc import (
    TemplateCreditworthinessMiscPageParser,
)

parsers_de = [
    TemplateCreditworthinessMiscPageParser(
        desc="Vontobel Fragenkatalog Kreditwürdigkeit ",
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        document_title="RM Bestätigung Kreditwürdigkeit",
        # company="VZ",
        supported_languages=["de"],
        # min_num_chars_alpha=1000,
        # max_num_chars_digit=100,
        # ranked_titles_all=[
        #     RankedTitle("Fragebogen Investitionen in Liegenschaft", 2, 10)
        # ],
        required_text_conditions=[
            FromStartTextCond("Fragenkatalog Kreditwürdigkeit", num_lines=2),
            FromStartTextCond("GBZ Nummer:", num_lines=5),
            FromStartTextCond("Kundenname:", num_lines=5),
            FromStartTextCond("Würdigung", num_lines=8),
        ],
        regions=[
            SearchElementConstrainedArea(
                name="region_date", text_top="Bei Fragen 2 bis 5 muss zwingend"
            )
        ],
        se=GenericLetterSearchElements(
            document_date=create_labeled_field_horizontal(
                label="Datum:", target_name="region_date"
            )
        ),
        debug_breakpoint=True,
    )
]

parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_vontobel():
    return parsers_de + parsers_en + parsers_fr + parsers_it
