from dataclasses import field, dataclass
from typing import List, Optional

from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.PageParserException import (
    PageParserException,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardTaxDeclarationPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings


# This is for generic pages where we do not want to extract much content
@dataclass
class TaxDeclarationGenericPageParser(StandardTaxDeclarationPageParser):
    contains_all: List[str] = field(default_factory=list)
    hamming_dist: int = 4

    def match_page_by_text(self, page_index: int, text: str):
        if len(self.contains_all) == 0 or self.page_cat is None:
            raise PageParserException(
                f"must define matching strings. page_cat={self.page_cat}, strings={self.contains_all}"
            )
        success = contains_all_strings(
            text, self.contains_all, hamming_dist=self.hamming_dist
        )
        return success

    def parse_page_header(self):
        return self.page.set_header_by_percentage(4 / 30)

    def parse_page_footer(self):
        return self.page.set_footer_by_percentage(4 / 30)

    def create_content_extractor(self) -> ContentExtractor:
        ce = ContentExtractor([])
        # ce.search_elements += self.get_search_elements_footer(self.page)
        return ce

    @staticmethod
    def create(
        page_cat: PageCat, contains_all: Optional[List[str]] = None, hamming_dist=4
    ):
        if contains_all is None:
            contains_all = []
        return TaxDeclarationGenericPageParser(
            doc_cat=DocumentCat.TAX_DECLARATION,
            page_cat=page_cat,
            contains_all=contains_all,
            hamming_dist=hamming_dist,
        )
