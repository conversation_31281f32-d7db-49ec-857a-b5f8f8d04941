from dataclasses import dataclass
from typing import List

from abbyyplumber.plumberstudio.SearchElement import SearchElementArea
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from mortgageparser.documents.parser.pageparsers.PageParserException import (
    PageParserException,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardLetterPageParser,
    StandardPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import (
    StandardPensionCertificatePageTwoPageParser,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.pension_certificate_util import (
    TemplatePensionCertificatePageParser,
    PensionCertificateSearchElements,
)
from mortgageparser.util.string_utils import (
    contains_all_strings,
    DEFAULT_HAMMING_DISTANCE,
)


@dataclass
class SmartPensionCertificatePageOnePageParser(SmartLetterPageParser):
    page_cat: PageCat = PageCat.PENSION2_CERTIFICATE_PAGE_ONE


class GenericPensionCertificatePageOnePageParser(StandardLetterPageParser):
    def __init__(
        self,
        company,
        titles,
        document_date_label,
        required_tokens,
        product=None,
        text_above_address=None,
    ):
        super().__init__()
        self.company = company
        self.doc_cat = DocumentCat.PENSION_CERTIFICATE
        self.page_cat = PageCat.PENSION2_CERTIFICATE_PAGE_ONE
        self.titles = titles
        self.document_date_label = document_date_label
        self.required_tokens = required_tokens
        self.product = product
        self.text_above_address = text_above_address


# This is for generic pages where we do not want to extract much content
@dataclass
class GenericPensionCertificatePageTwoPageParser(StandardPageParser):
    desc: str = None  # Not used, just to describe the parser
    contains_all: List[str] = None
    hamming_dist: int = DEFAULT_HAMMING_DISTANCE

    def __init__(
        self,
        desc,
        contains_all,
        doc_cat=DocumentCat.PENSION_CERTIFICATE,
        page_cat=PageCat.PENSION2_CERTIFICATE_PAGE_TWO,
        hamming_dist=DEFAULT_HAMMING_DISTANCE,
    ):
        super().__init__()
        self.doc_cat = doc_cat
        self.page_cat = page_cat
        self.desc = desc
        self.contains_all = contains_all

    def match_page_by_text(self, page_index: int, text: str):
        if len(self.contains_all) == 0 or self.page_cat is None:
            raise PageParserException(
                f"must define matching strings. page_cat={self.page_cat}, strings={self.contains_all}"
            )
        success = contains_all_strings(text, self.contains_all)
        return success

    def __repr__(self):
        return f"GenericPensionCertP2({self.desc})"


class GenericPensionCertificatePageThreePageParser(
    GenericPensionCertificatePageTwoPageParser
):
    def __init__(self, desc, contains_all):
        super().__init__(desc, contains_all)
        self.page_cat = PageCat.PENSION2_CERTIFICATE_PAGE_TWO


token_list_relevant_salary = [
    "Anrechenbarer Jahreslohn",
    "Anrechenbarer Jahresverdienst",
    "Anrechenbarer Lohn",
    "Versicherter Lohn",
    "Versicherter Jahreslohn",
    "Versichertes Gehalt",
    "Versichertes Salär",
    "Versicherter Verdienst",
    "Gemeldeter Jahreslohn",
    "Massgebender Lohn",
    "Massgebendes Salär",
]


# Best effort try to find address and name and document date in the upper third of the page
# ClassifierPensionCertificatePageOneParser
CLASSIFIER_PENSION_CERTIFICATE_PAGE_ONE_PARSER = TemplatePensionCertificatePageParser(
    desc="Generic Classifier Pension2 parser",
    regions=[
        SearchElementArea(
            FIELD_FIRSTNAME.sr_inside, None, y_range=PercentageRange(0, 0.5)
        )
    ],
    se=PensionCertificateSearchElements(
        document_date=SearchElementArea(None, None, y_range=PercentageRange(0, 0.5)),
        date_of_birth=SearchElementArea(None, None),
        # AHV is added because use_ahv_new=True
    ),
)


class ClassifierPensionCertificatePageTwoPageParser(
    StandardPensionCertificatePageTwoPageParser
):
    pass


class ClassifierPensionCertificatePageThreePageParser(
    GenericPensionCertificatePageThreePageParser
):
    pass
