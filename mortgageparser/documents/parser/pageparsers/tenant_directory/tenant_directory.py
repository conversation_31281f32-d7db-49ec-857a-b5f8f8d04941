from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


def get_parsers_tenant_directory():
    parsers = [
        TemplatePageParser(
            desc="Mieterspiegel generisch DE",
            doc_cat=DocumentCat.TENANT_DIRECTORY,
            page_cat=PageCat.GENERIC_PAGE,
            ranked_titles_all=[
                RankedTitle("Mieterspiegel", rank=1, min_length_title=12)
            ],
        ),
    ]
    return parsers
