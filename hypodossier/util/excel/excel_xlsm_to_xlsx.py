# This script is made for convert xlsm files to xlsx
# Used for pass xlsm file to phpExcel
# From https://github.com/karec/python-xlsm-to-xlsx/blob/master/to_xlsx.py with modifications

import os
import zipfile
import shutil
import tempfile
from pathlib import Path
from typing import Optional, List
from xml.dom import minidom


def xlsm_to_xlsx(
    p_in: Path,
    p_out: Path = None,
    filenames: Optional[List[str]] = None,
    remove_p_in: bool = False,
) -> Path:
    """
    This function create a xlsx file from
    xlsm file

    Args:
        p_in (Path): file to convert
        p_out (Path): target file path. If None then stem of p_in + '.xslx' will be used

    Filenames:
        name (string): string representing files tu update
        @param remove_p_in:
        @param p_out:
        @param p_in:
        @param filenames:
    """

    if not filenames:
        filenames = ["[Content_Types].xml"]

    if not p_out:
        p_out = p_in.with_suffix(".xlsx")
        # p_out_str = str(p_in).split('.')[0]
        # p_out_str += '.xlsx'
        # p_out = Path(p_out_str)

    p_out.parent.mkdir(exist_ok=True, parents=True)

    fname = str(p_in)

    tempdir = tempfile.mkdtemp()
    try:
        tempname = os.path.join(tempdir, "new.zip")
        with zipfile.ZipFile(fname, "r") as zipread:
            with zipfile.ZipFile(tempname, "w") as zipwrite:
                for item in zipread.infolist():
                    if item.filename not in filenames:
                        data = zipread.read(item.filename)
                        zipwrite.writestr(item, data)
                    else:
                        zipwrite.writestr(
                            item,
                            update_files(item.filename, zipread.read(item.filename)),
                        )
        if remove_p_in:
            os.remove(fname)

        shutil.move(tempname, str(p_out))
    finally:
        shutil.rmtree(tempdir)

    return p_out


def update_files(filename, data):
    """
    This function dispatch the data for return good xml values
    Use it if you need to update more files
    """
    if "[Content_Types].xml" in filename:
        return update_content_types(data)


def update_content_types(data):
    """
    This function remove macro and set
    correct file type in headers
    """
    xml = minidom.parseString(data)
    types = xml.getElementsByTagName("Types")[0]
    for item in xml.getElementsByTagName("Types"):
        if (
            item.hasAttribute("PartName")
            and item.getAttribute("PartName") == "/xl/vbaProject.bin"
        ):
            item.parentNode.removeChild(item)

    for item in types.getElementsByTagName("Override"):
        if (
            item.hasAttribute("PartName")
            and item.getAttribute("PartName") == "/xl/workbook.xlk"
        ):
            item.setAttribute(
                "ContentType",
                "vnd.openxmlformats-officedocument.extended-properties+xml",
            )
    return xml.toxml()
