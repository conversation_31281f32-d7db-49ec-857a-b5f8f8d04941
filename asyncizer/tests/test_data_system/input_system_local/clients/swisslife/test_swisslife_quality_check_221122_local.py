from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>x<PERSON><PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/swisslife/221122"
)
dest_folder_prefix = "input_system_clients_swisslife_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_sl_debt():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "debt",
        source_file_filter=["debt.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.DEBT_COLLECTION_INFORMATION: 1
            },
            expected_page_objects={
                0: {"document_date": "08.11.2022", "fullname": PO_EXISTS}
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_situation():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "situation",
        source_file_filter=["situation.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_sl_salary():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "salary",
        source_file_filter=["salary.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_sl_salesdoc():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "salesdoc",
        source_file_filter=["salesdoc.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # Ist eine wurst
        ),
    ).run()


@pytest.mark.asyncio
async def test_sl_planfloor():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "planfloor",
        source_file_filter=["planfloor.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_sl_pension_application():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "pension3_insurance_application",
        source_file_filter=["pension3_insurance_application.pdf"],
        show_page=14,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()
