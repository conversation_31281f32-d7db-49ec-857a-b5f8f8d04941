from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import (
    PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER,
    ENABLE_DETECTRON2_OBJECT_DETECTION,
)
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER}/input_samples_demo/demo_v20/input_files_flat"
)
dest_folder_prefix = "input_system_demo_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_batch_01():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["batch_01.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.CRIMINAL_RECORDS: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 2,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.WHITE_PAGES: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.CRIMINAL_RECORDS: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 2,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.WHITE_PAGES: 4,
            },
        ),
        client_lang="de",
    ).run()


@pytest.mark.asyncio
async def test_fotos_ste_income():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        show_page=0,
        source_folder=path_root_folder / "fotos ste",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["IMG_9744.JPG"],
        dossier_expectations=DossierExpectations(
            expected_filenames=["310 Steuererklärung IMG_9744.pdf"],
            expected_page_objects={
                0: {
                    "document_date": "20.04.2020",
                    "p1_ahv_new": PO_EXISTS,
                    "p1_income_employed_main": "CHF 22'506",
                    "p2_income_employed_main": "CHF 128'991",
                    "p1_income_employed_side": "CHF 7'502",
                    "p2_income_eo": "CHF 4'388",
                    "p2_income_child_benefits": "CHF 4'800",
                    "income_portfolio": "CHF 15",
                    "income_real_estate_gross": "CHF 25'700",
                    "income_real_estate_net_primary": "CHF 20'560",
                    "income_gross_total": "CHF 188'762",
                    "property_maintenance_cost": "CHF 5'140",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_fotos_ste_assets():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        show_page=0,
        source_folder=path_root_folder / "fotos ste",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["IMG_9746.JPG"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={
                0: {
                    # "document_date": "20.04.2020",
                    "p1_ahv_new": PO_EXISTS,
                    "assets_portfolio": "CHF 2'458'532",
                    "assets_cars": "CHF 1'040",
                    "assets_other": "CHF 180'288",
                    "assets_real_estate_main_property": "CHF 1'172'000",
                    "address_real_estate_primary": PO_EXISTS,
                    "assets_gross_total": "CHF 3'811'860",
                    "assets_taxable_global": "CHF 2'676'860",
                    "assets_taxable_local": "CHF 2'676'860",
                    "debt_total": "CHF 1'135'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_fotos_ste_debt():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        show_page=0,
        source_folder=path_root_folder / "fotos ste",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["IMG_9751.JPG"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "year": "2019",
                    "document_date": "20.04.2020",
                    "p1_ahv_new": PO_EXISTS,
                    "debt_total": "CHF 1'135'000",
                    "interest_paid_on_debt": "CHF 11'257",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_fotos_ste_list_of_assets():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        show_page=0,
        source_folder=path_root_folder / "fotos ste",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["IMG_9753.JPG"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_fotos_ste_all():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "fotos ste",
        dest_folder_prefix=dest_folder_prefix,
        # source_file_filter=['IMG_9746.JPG'],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.ID: 2,
                DocumentCat.PASSPORT_CH: 1,
                DocumentCat.TAX_DECLARATION: 14,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_finhurdle_stockwerkeigentum():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        show_page=-1,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["finhurdle_stockwerkeigentum.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.AGREEMENT_CHARGE_IMMOVABLE_PROPERTY: 1,
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 1,
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 7,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.USER_REGULATIONS_CONDOMINIUM: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_obi():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        # show_page=0,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["preisliste-obi-mietgeraete-ch-de.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_all():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.BUILDING_RIGHTS_AGREEMENT: 1,
                DocumentCat.CRIMINAL_RECORDS: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 2,
                DocumentCat.FINANCING_OFFER: 1,
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1,
                DocumentCat.ID: 2,
                DocumentCat.LAND_REGISTER_MISC: 1,
                DocumentCat.PASSPORT_CH: 2,
                DocumentCat.PENSION3A_ACCOUNT: 1,
                DocumentCat.PENSION_CERTIFICATE: 2,
                DocumentCat.PLAN_FLOOR: 1,
                DocumentCat.PLAN_SITUATION: 1,
                DocumentCat.PROPERTY_INSURANCE: 2,
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.SALES_DOCUMENTATION: 1,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.UNKNOWN_DE: 1,
                DocumentCat.VESTED_BENEFITS_ACCOUNT: 1,
                DocumentCat.WHITE_PAGES: 2,
                DocumentCat.PENSION_WITHDRAWL: 1,
            }
        ),
    ).run()
