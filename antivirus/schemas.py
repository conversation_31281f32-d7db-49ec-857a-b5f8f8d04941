import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, AnyHttpUrl


class AntiVirusType(Enum):
    CLAMAV = "CLAMAV"
    MICROSOFTDEFENDER = "MICROSOFTDEFENDER"


class VirusScanRequest(BaseModel):
    correlation_uuid: UUID
    dossier_uuid: Optional[UUID] = None
    # We use AnyHttpUrl instead of HttpUrl as CI uses ports as part of the URL
    file_url: AnyHttpUrl


class VirusScanResponse(BaseModel):
    correlation_uuid: UUID
    dossier_uuid: Optional[UUID] = None
    # Detection result is optional due to possibility of error(s)
    detection_result: Optional[bool] = None
    details: Optional[str] = None
    anti_virus_type: AntiVirusType
    anti_virus_version: Optional[str] = None
    scan_timestamp: datetime.datetime
    anti_virus_signature_version: Optional[str] = None
    anti_virus_signature_update: Optional[datetime.datetime] = None
    error: Optional[str] = None
    anti_virus_all_info: Optional[str] = None

    # This string can help in finding out where the contaminated file comes from
    context: Optional[str] = None
