from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssie<PERSON><PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER}/input_samples_demo/demo_v22/input_files_totally_flat"
)
dest_folder_prefix = "input_system_doccat_sales_pitch_demo_v22_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_id():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["IMG_9739.JPG"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.ID: 1}
        ),
    ).run()


@pytest.mark.asyncio
async def test_all():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        # source_file_filter=['pk_mt_zwei_stück.pdf'],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.BANK_DOCUMENT: 4,
                DocumentCat.BUILDING_DESCRIPTION: 1,
                DocumentCat.BUILDING_RIGHTS_AGREEMENT: 1,
                DocumentCat.CONTRACT_OF_SALE: 1,
                DocumentCat.CRIF_QUICK_CONSUMER_CHECK: 1,
                DocumentCat.CRIMINAL_RECORDS: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 2,
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 1,
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 1,
                DocumentCat.FINANCING_OFFER: 1,
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1,
                DocumentCat.ID: 2,
                DocumentCat.LAND_REGISTER_MISC: 1,
                DocumentCat.PASSPORT_CH: 1,
                DocumentCat.PENSION3A_ACCOUNT: 1,
                DocumentCat.PENSION_CERTIFICATE: 3,
                DocumentCat.PENSION_CERTIFICATE_INFO: 1,
                DocumentCat.PLAN_FLOOR: 1,
                DocumentCat.PLAN_SITUATION: 1,
                DocumentCat.PROPERTY_INSURANCE: 2,
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.PROPERTY_VALUATION: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.SALES_DOCUMENTATION: 1,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.UNKNOWN_DE: 2,
                DocumentCat.VESTED_BENEFITS_ACCOUNT: 1,
                DocumentCat.WHITE_PAGES: 2,
            }
        ),
    ).run()


@pytest.mark.asyncio
async def test_fk():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["Fintech consult 2015.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 4,
                DocumentCat.WHITE_PAGES: 4,
            }
        ),
    ).run()
