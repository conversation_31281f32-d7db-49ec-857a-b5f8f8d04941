from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElementConstrainedArea,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.bank_account.BankAccountPageData import (
    FIELDS_BANK_ACCOUNT,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_COMPANY
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplateFinancingConfirmationPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.FINANCING_CONFIRMATION
    company: str = None

    se: GenericLetterSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_BANK_ACCOUNT.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_financing_confirmation():
    # Here goes everything with 'Schuldübergang'
    parsers = [
        TemplateFinancingConfirmationPageParser(
            desc="Finanzierungsbestätigung (z.B. GKB) 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            # required_text_conditions=[
            #     FromStartTextCond("Grundbuch", num_lines=20),
            #     FromStartTextCond("Anzeige des Schuldüberganges", num_lines=20),
            # ],
            required_tokens_any=[
                ["Sehr geehrte"],
                ["unsere Bank"],
                ["die gesamtfinanzierung des obenerwähnten"],
                ["Freundliche Grüsse"],
            ],
            se=GenericLetterSearchElements(
                document_date=SearchElementConstrainedArea(
                    None, None, y_range=PercentageRange(0, 0.5)
                )
            ),
        ),
    ]
    return parsers
