# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import logging
from pathlib import Path

BASE_DIR = str(Path(__file__).parent)
BASE_PATH = Path(BASE_DIR)

DATA_DIR = BASE_DIR + "/data"

DATA_DEMO_DIR = BASE_DIR + "/data_demo"

OUTPUT_DIR = BASE_DIR + "/output"

OUTPUT_DIR_LUIGI = OUTPUT_DIR + "/luigi_output"

SIMPLE_CACHE_DIR = OUTPUT_DIR + "/simple_cache"

OUTPUT_CLASSIFIER = OUTPUT_DIR + "/output_classifier"

OUTPUT_EXTRACT = OUTPUT_DIR + "/output_extract"

IMAGE_CACHE_DIR = SIMPLE_CACHE_DIR + "/images"

OUTPUT_DOSSIER_PREVIEW = OUTPUT_DIR + "/dossier_preview"

# Directory in which CommandLine output is created. If None it will be created in a subdirectory 'cmd' of the input
COMMAND_LINE_CACHE_DIR = f"{SIMPLE_CACHE_DIR}/command_line"
COMMAND_LINE_CACHE_PATH = Path(COMMAND_LINE_CACHE_DIR)

# ABBY_CMD = '/opt/ABBYY/FREngine12/Samples/CommandLineInterface/CommandLineInterface'
ABBY_CMD = (
    f"{BASE_DIR}/abbyyplumber/finereaderengine/commandline/CommandLineInterface.exe"
)

# Define a standard separator that certainly does not occur in text. Is used for column separation.
DEF_SEPARATOR = "_$%&_"


logging.basicConfig(level=logging.INFO)
logging.getLogger("pdfminer").setLevel(logging.WARN)

logging.info(f"BASE_DIR={BASE_DIR}, __file__={__file__}, DEMO_DIR={DATA_DEMO_DIR}...")

# Prefix to add to email body converted to pdf
EXTRACTED_EMAIL_FILENAME_PREFIX = "HypoDossier_Extracted_Email_Body"
