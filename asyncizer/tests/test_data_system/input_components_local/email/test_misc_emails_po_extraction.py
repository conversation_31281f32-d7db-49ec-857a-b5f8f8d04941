from pathlib import Path

import pytest
import structlog

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.util.date_util import find_most_recent_date

logger = structlog.getLogger(__name__)

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL}/email/misc_emails_for_po_extraction"
)
dest_folder_prefix = "input_system_components_local_email_extraction"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_01_date_parsing():
    target_folder = path_root_folder / "email_01"
    assert target_folder.exists()
    logger.info("target_folder:", t=target_folder)
    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[
            "HypoDossier_Extracted_Email_Body_20250326145021_7eeaddf8-8426-4b4f-8f85-a9bb883a0bca.pdf"
        ],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 7},
            expected_page_objects={
                0: {
                    "email_from": "Clément Bastien",
                    "email_date": "06.02.2025",
                    "email_to": "<EMAIL>",
                    "email_subject": "TR: Actualisation financement BCBE",
                }
            },
        ),
    ).run()


def test_email_01_regex_extraction():

    date_str = "2025-02-06 17:01:20.989745+01:00"

    found_date = find_most_recent_date(date_str)

    logger.info("extract...", found_date=found_date)
    assert found_date


@pytest.mark.asyncio
async def test_email_02_short():
    target_folder = path_root_folder / "email_02_short"
    assert target_folder.exists()
    logger.info("target_folder:", t=target_folder)
    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["email_short.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 1},
            expected_page_objects={
                0: {
                    "email_from": "<EMAIL>",
                    "email_to": "<EMAIL>",
                    "email_date": "10.03.2025",
                    "email_subject": "Johannes Hellmann Pensionskasse",
                }
            },
        ),
    ).run()


# does not work because not properly classified
# @pytest.mark.asyncio
# async def test_email_03_shift_right():
#     target_folder = path_root_folder / "email_03_shift_right"
#     assert target_folder.exists()
#     logger.info("target_folder:", t=target_folder)
#     await DossierTest(
#         override_existing=True,
#         source_folder=target_folder,
#         source_file_filter=["2.pdf"],
#         show_page=0,
#         dest_folder_prefix=dest_folder_prefix,
#         dossier_expectations=DossierExpectations(
#             expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 1},
#             expected_page_objects={},
#         ),
#     ).run()


@pytest.mark.asyncio
async def test_email_04_date():
    target_folder = path_root_folder / "email_04_date"
    assert target_folder.exists()
    logger.info("target_folder:", t=target_folder)
    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["0.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 1},
            expected_page_objects={
                0: {
                    "email_from": "<EMAIL>",
                    "email_to": "<EMAIL>",
                    "email_date": "11.09.2023",
                    "email_subject": "Datenlieferung Demo-Dossier #1",
                }
            },
        ),
    ).run()
