from typing import List

from global_settings import ENABLE_LONG_FILENAMES
from hypodossier.core.documents.salarycertificate.SalaryCertificatePageData import (
    FIELD_SALARY_GROSS,
)
from hypodossier.core.domain.DocumentCatElement import I18NElement
from hypodossier.core.domain.Extractions import Extractions
from hypodossier.core.domain.SemanticField import FIELD_YEAR
from hypodossier.core.filename.FileCreator import FileCreator


class SalaryCertificateFileCreator(FileCreator):
    def get_document_title_elements(
        self,
        elements: List[str],
        extractions: Extractions,
        filename: str,
        lang: str,
        pages: List,
        long_version: bool,
    ):
        elements.append(self.get_best_effort_name(extractions, require_firstname=False))

        if long_version:
            year = extractions.get_value(FIELD_YEAR.name)
            elements.append(year)

            if ENABLE_LONG_FILENAMES:
                salary = extractions.get_value(FIELD_SALARY_GROSS.name)
                if salary:
                    elements.append(
                        I18NElement("brutto", "gross", "brut", "lordo").trans(lang)
                    )
                    elements.append(salary)

        return elements

    def __repr__(self):
        return self.__class__.__name__
