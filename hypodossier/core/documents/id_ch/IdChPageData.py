from dataclasses import dataclass
from typing import Dict

from hypodossier.core.domain.PageData import (
    PageData,
)
from hypodossier.core.domain.SemanticField import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    fields_as_dict,
    FIELD_DOCUMENT_DATE,
    FIELD_FIRSTNAME,
    FIELD_LASTNAME,
    FIELD_FULLNAME,
    FIELD_PERSON_ID,
    FIELD_DATE_OF_BIRTH,
    ExtractionType,
    FieldTitles,
    FIELD_MRZ,
    FIELD_DOCUMENT_VALIDITY_END_DATE,
    FIELD_NATIONALITY,
    FIELD_HOMETOWN,
    FIELD_SEX,
)

FIELD_HEIGHT = SemanticField(
    "person_height",
    ExtractionType.STRING,
    FieldTitles("<PERSON>örpergrösse", "Person Height"),
    "Gr<PERSON><PERSON> der Person, z.B. '180cm'",
)


FIELDS_ID_CH = fields_as_dict(
    [
        FIELD_DOCUMENT_DATE,
        FIELD_FIRSTNAME,
        FIELD_LASTNAME,
        FIELD_FULLNAME,
        FIELD_PERSON_ID,
        FIELD_DATE_OF_BIRTH,
        FIELD_SEX,
        FIELD_DOCUMENT_VALIDITY_END_DATE,
        FIELD_HOMETOWN,
        FIELD_HEIGHT,
        FIELD_NATIONALITY,
        FIELD_MRZ,
    ]
)

# FIELDS_ID_CH = merge_dicts(new_fields, FIELDS_PERSON_DATA)


@dataclass
class IdChPageData(PageData):
    document_date: str = None

    firstname: str = None

    lastname: str = None

    fullname: str = None

    # can be specific id for canton or ahv_new
    person_id: str = None

    date_of_birth: str = None

    sex: str = None

    document_validity_end_date: str = None

    hometown: str = None
    person_height: str = None
    nationality: str = None
    mrz: str = None

    def get_invalidity_reason(self):
        msg = None
        return msg

    def __str__(self):
        return f"IdCH[NAME {self.lastname}, Firstname {self.firstname}]"

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        d = super().get_semantic_fields()
        return {**d, **FIELDS_ID_CH}


if __name__ == "__main__":
    for key, val in FIELDS_ID_CH.items():
        print(f"{key} -> {val}")
