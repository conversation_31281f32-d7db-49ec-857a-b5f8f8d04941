import re  # Import the regular expression library

Exclusions = [
    "a",
    "an",
    "the",  # Articles
    "and",
    "but",
    "or",
    "by",
    "nor",
    "yet",
    "so",  # Conjunctions
    "about",
    "above",
    "across",
    "after",
    "against",
    "along",
    "among",
    "around",
    "at",
    "before",  # Prepositions
    "behind",
    "between",
    "beyond",
    "but",
    "by",
    "concerning",
    "despite",
    "down",
    "during",
    "except",
    "following",
    "for",
    "from",
    "in",
    "including",
    "into",
    "like",
    "near",
    "of",
    "off",
    "on",
    "out",
    "over",
    "plus",
    "since",
    "through",
    "throughout",
    "to",
    "towards",
    "under",
    "until",
    "up",
    "upon",
    "with",
    "within",
    "without",
]


# Copied from here
# http://guidohenkel.com/2018/08/title-case-creation-python-csharp/
# TODO: Handle Double names with hyphen, e.g. '<PERSON>'
def proper_title_case(curText: str) -> str:
    """Take a string and return it in a fashion that follows proper title case guidelines"""
    outString = ""
    fragments = re.split(
        r"(\".*?\")|(\'.*?\')|(“.*?”)|(‘.*?’)", curText
    )  # Extract titles in quotation marks from string
    for fragment in fragments:  # Treat and re-assemble all fragments
        if fragment:  # skip empty matches generated by the OR in regex
            frag_string = ""
            tokens = fragment.split()  # Break string into individual words

            if tokens:
                for word in tokens:  # Check each word
                    punct = word[-1]  # Check for trailing punctuation mark
                    if punct.isalpha():
                        punct = ""
                    else:
                        word = word[:-1]

                    if word.lower() in Exclusions:  # if it is excluded,
                        frag_string += word.lower() + punct + " "  # make it lowercase
                    else:  # otherwise,
                        frag_string += word.capitalize() + punct + " "  # capitalize it

                cap = 1
                if not frag_string[0].isalpha():
                    cap = 2

                outString += (
                    frag_string[:cap].upper() + frag_string[cap:]
                ).strip() + " "

    return (
        outString[:1].upper() + outString[1:]
    ).strip()  # Capitalize first letter and strip trailing space
