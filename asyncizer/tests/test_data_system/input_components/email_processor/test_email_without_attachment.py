from pathlib import Path

import pytest

from asyncizer.tests.util_tests import (
    <PERSON><PERSON><PERSON>Expectations,
    DossierTest,
    configure_test_logging,
)
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/emails_without_attachment"
)
dest_folder_prefix = "input_system_components_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_message_html_polish():
    configure_test_logging()
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["html_polish.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_EN: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 4,
                DocumentCat.UNKNOWN_EN: 1,
            },
            expected_page_objects={
                "738 Email-Korrespondenz piotr.gryko_at_gmail.com.pdf": {
                    "email_from": "<EMAIL>",
                    "email_to": "<EMAIL>",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_html_test_email():
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["html_test_email.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 2},
            expected_page_objects={
                0: {
                    "email_from": "<EMAIL>",
                    "email_to": "<EMAIL>",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_html_test_email_eml():
    target_folder = path_root_folder / "html_test_email_eml"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_html_polish_eml():
    # Test with weird Polish characters
    target_folder = path_root_folder / "html_polish_eml"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "002 DOK EN Complaints Terms.pdf",
                "322 Bankdokument.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_gmail.com.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 4,
                DocumentCat.UNKNOWN_EN: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_plain_text_email_eml():
    target_folder = path_root_folder / "plain_text_email_eml"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_plain_text_email_eml_two_copies_zipped():
    target_folder = path_root_folder / "plain_text_email_eml_two_copies_zipped"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_plain_text_msg():
    target_folder = path_root_folder / "test_email_plan_text_msg"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch 04.03.2025.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_plain_text_msg_two_copies_zipped():
    # Identical to text above, except we've duplicated the msg file and zipped it
    # This is to test the de-duplication in unpack_path
    # Dossier expectations are the same as above
    target_folder = path_root_folder / "test_email_plan_text_msg_two_copies_zipped"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch 04.03.2025.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_test_email_plan_text_unicode_msg():
    target_folder = path_root_folder / "test_email_plan_text_unicode_msg"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch 04.03.2025.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_without_attachment_test_HTML_email_unicode_msg():
    target_folder = path_root_folder / "test_HTML_email_unicode_msg"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch 04.03.2025.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
            },
        ),
    ).run()


# All documents
@pytest.mark.asyncio
async def test_email_messages_without_attachment():
    # Lots of emails, most of them without attachments (I think there's one with attachments)
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "002 DOK EN Complaints Terms.pdf",
                "322 Bankdokument.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_gmail.com.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 20,
                DocumentCat.UNKNOWN_EN: 1,
            },
        ),
    ).run()
