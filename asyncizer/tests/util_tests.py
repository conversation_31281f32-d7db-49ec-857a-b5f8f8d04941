import collections
import json
import logging
import shutil
import sys
import webbrowser
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Union

import structlog
from icecream import ic
from jsonpath_ng import parse
from pydantic import BaseModel, Extra, Field

import global_settings
from abbyyplumber.util.DurationLog import DurationLog
from abbyyplumber.util.trial_parser_util import (
    parse_and_display_single_page_with_parser_manager,
)
from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.rpc_pika import get_rpc_client
from asyncizer.scripts_run.scripts_util import process_single_dossier_folder
from constants import BASE_DIR
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    FIELD_PROP_OWNER_FULLNAME,
    FIELD_PROP_OWNER_ADDRESS,
)
from hypodossier.core.documents.salarycertificate.SalaryCertificatePageData import (
    FIELD_COMPANY_CONTACT,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FIRSTNAME,
    P2_FIRSTNAME,
    P1_LASTNAME,
    P1_FULLNAME,
    P2_FULLNAME,
    P2_LASTNAME,
    P2_PHONE_PRIMARY,
    P1_AHV_NEW,
    P2_AHV_NEW,
    SECTION_CHILDREN,
    ADDRESS_REAL_ESTATE_PRIMARY,
    DEBT_DETAIL_LINES,
    DEBT_DETAIL_LINES_2,
    P2_EMAIL,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.SemanticField import (
    FIELD_PHONE_PRIMARY,
    FIELD_PHONE_SECONDARY,
    FIELD_EMAIL,
    FIELD_AHV_NEW,
    FIELD_IBAN,
    FIELD_EMPLOYER,
    FIELD_MRZ,
)
from hypodossier.core.domain.document_category_util import get_document_cat_by_name
from mortgageparser.documents.parser.ParserManager import DefaultParserManager
from mortgageparser.util.histogram import Histogram

"""
    Helper functions to be used in automated dossier testing
"""


def configure_test_logging():
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Set up root logger
    logging.basicConfig(format="%(message)s", stream=sys.stdout, level=logging.INFO)

    # Configure specific loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("aio_pika").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.INFO)
    logging.getLogger("botocore").setLevel(logging.INFO)
    logging.getLogger("s3fs").setLevel(logging.INFO)
    logging.getLogger("pdfminer").setLevel(logging.WARNING)
    logging.getLogger("asyncizer").setLevel(logging.INFO)

    for name in [
        "fontTools",
        "fontTools.subset",
        "fontTools.ttLib",
        "fontTools.ttLib.ttFont",
    ]:
        logging.getLogger(name).setLevel(logging.WARNING)

    # For asyncizer.unpacker.logger, import asyncizer and set it directly
    import asyncizer.unpacker

    asyncizer.unpacker.logger.setLevel(logging.INFO)

    # Create and return a structlog logger
    return structlog.get_logger()


logger = configure_test_logging()
logger.info("Logging configured")

PO_EXISTS = "asdfasdfasdfasasdf3223498k"

ALL_FIELDS_PO_EXISTS = [
    "fullname",
    "firstname",
    "lastname",
    "street",
    "address_block",
    "account_no",
    "ahv_new",
    "iban",
    FIELD_MRZ.name,
    P1_FULLNAME.name,
    P1_FIRSTNAME.name,
    P1_LASTNAME.name,
    P1_AHV_NEW.name,
    P2_FULLNAME.name,
    P2_FIRSTNAME.name,
    P2_LASTNAME.name,
    P2_AHV_NEW.name,
    FIELD_PHONE_PRIMARY.name,
    FIELD_PHONE_SECONDARY.name,
    FIELD_EMAIL.name,
    P2_PHONE_PRIMARY.name,
    P2_EMAIL,
    SECTION_CHILDREN.name,
    FIELD_AHV_NEW,
    FIELD_IBAN,
    FIELD_COMPANY_CONTACT.name,
    FIELD_EMPLOYER.name,
    ADDRESS_REAL_ESTATE_PRIMARY.name,
    DEBT_DETAIL_LINES.name,
    DEBT_DETAIL_LINES_2.name,
    # Insurance stuff
    FIELD_PROP_OWNER_FULLNAME.name,
    FIELD_PROP_OWNER_ADDRESS.name,
]


class DossierExpectations(BaseModel):
    class Config:
        extra = Extra.forbid  # Disallow extra fields

    expected_filenames: List[str] = None
    expected_page_objects: Dict[Union[str, int], Dict[str, Optional[str]]] = None
    expected_num_docs: int = -1
    expected_num_extracted_file_exceptions: int = 0
    expected_num_processing_exceptions: int = 0

    # How often a document with this doccat occurs in dossier
    # Sum of all expected doccats should match number of documents in dossier
    expected_doc_cat_frequency: Dict[DocumentCat, int] = None

    # How often a page with this doccat occurs in dossier
    # Sum of all expected doccats should match number of pages in dossier
    expected_doc_cat_frequency_per_page: Dict[DocumentCat, int] = None

    max_duration_seconds: int = -1

    # @field_validator("expected_page_objects", mode="before")
    # def cast_int_keys_to_str(cls, v):
    #     if isinstance(v, dict):
    #         return {str(k): val for k, val in v.items()}
    #     return v


class DossierException(Exception):
    pass


async def run_single_dossier_test(
    source_folder: Path,
    source_file_filter: List[str],
    override_existing=True,
    dest_folder_prefix="",
    use_ocr_cache=True,
    client_lang: str = "de",
    processing_config: OriginalFileProcessingConfig = OriginalFileProcessingConfig(),
):
    """
    Process a single dossier in a single input folder
    :param source_folder:
    :param source_file_filter:
    :param override_existing:
    :return:
    """
    dest_root_folder = Path(f"{BASE_DIR}/output/dossier_tests")

    if dest_folder_prefix is None:
        dest_folder_prefix = ""

    if not isinstance(dest_folder_prefix, str):
        raise DossierException(
            f"dest_folder_prefix must be a str but was {type(dest_folder_prefix)}. dest_folder_prefix={dest_folder_prefix}"
        )

    if source_file_filter:
        suffix = "$$".join(source_file_filter)
        dest_folder = (
            dest_root_folder / f"{dest_folder_prefix}{source_folder.name}__{suffix}"
        )
    else:
        dest_folder = dest_root_folder / f"{dest_folder_prefix}{source_folder.name}"

    if dest_folder.exists() and override_existing:
        shutil.rmtree(str(dest_folder))

    ic.includeContext = True
    start = datetime.now()
    ic(source_folder)

    if not dest_folder.exists():
        await get_rpc_client()

        if not source_folder.exists():
            raise DossierException(f"Source folder does not exist: {source_folder}")
        if not source_folder.is_dir():
            raise DossierException(
                f"Source folder must be a directory but is not: {source_folder}"
            )

        await process_single_dossier_folder(
            source_folder,
            dest_folder,
            source_file_filter,
            use_ocr_cache=use_ocr_cache,
            client_lang=client_lang,
            processing_config=processing_config,
        )

    end = datetime.now()
    duration = end - start
    duration_seconds = duration.total_seconds()
    ic(duration_seconds)
    logger.info(
        f"Single Dossier {source_folder} handled in {duration_seconds} seconds.",
        source_folder=source_folder,
        duration_seconds=duration_seconds,
    )

    return dest_folder, duration_seconds


def show_page_from_processed_dossier(
    dest_folder: Path,
    show_page: int,
    show_filename: str = None,
    use_single_page_pdf=True,
):
    """
    :param show_page: page number of page to be shown
    :param show_filename: filename of file that should be shown
    """

    # IF  there is a folder name searchable pages:
    #       then DEST_FOLDER / searchable_pages / show_filename / 0.pdf
    #       and  DEST_FOLDER / searchable_pages / show_filename / 0.txt

    if (dest_folder / "searchable_pages").exists():
        path_searchable = (
            dest_folder / "searchable_pages" / show_filename / f"{show_page}.pdf"
        )
        path_pdf_txt = path_searchable.parent / f"{show_page}.txt"
        # path_pdf_txt = path_searchable.parent / '0.txt'
    else:
        path_searchable = (
            dest_folder
            / "searchable"
            / show_filename
            / str(show_page)
            / "pdf"
            / "0.pdf"
        )
        path_pdf_txt = path_searchable.parent.parent / "txt" / "0.txt"
        if not path_searchable.exists():
            # This version is needed for xlsx
            path_searchable = (
                dest_folder
                / "searchable"
                / show_filename
                / "pages"
                / f"{str(show_page)}.pdf"
            )
            assert path_searchable.exists()
            path_pdf_txt = (
                dest_folder
                / "searchable"
                / show_filename
                / "full.pdf"
                / "txt"
                / f"{str(show_page)}.txt"
            )
            assert path_pdf_txt.exists()

    if not path_searchable.exists():
        raise DossierException(
            f"Could not find folder with searchables. Dossier already processed? path_searchable={path_searchable}"
        )

    if not path_pdf_txt.exists():
        raise DossierException(
            f"Could not find txt page (0.txt) at path={path_pdf_txt}"
        )

    path_pdf_orig = dest_folder / "original_files" / show_filename
    if not path_pdf_orig.exists():
        raise DossierException(
            f"Could not find source pdf. Maybe filename not complete? path_searchable={path_searchable}"
        )

    print(f"Found folder searchable: {path_searchable}")

    parser_manager = DefaultParserManager
    # parser_manager = DebtCertificateParserManager
    # parser_manager = SpacyClassifierPageParserManager

    client_lang = "de"

    print(f"Now parse doc_path={path_searchable}")

    # This is because all pdfs are single page
    page_zero_instead_of_show_page = 0

    page, sp = parse_and_display_single_page_with_parser_manager(
        path_searchable,
        path_pdf_txt,
        parser_manager,
        page_zero_instead_of_show_page,
        skip_engine_fallback=False,
        view_scale_ratio=0.8,
        display_page_on_error=True,
        client_lang=client_lang,
    )


def open_package_in_browser(dest_folder: Path):
    """
    Open Offline Document Browser in Webbrowser
    :param dest_folder: Parent folder of 'package'
    :return:
    """
    package_folder = dest_folder / "package"
    html_path = package_folder / "001 Hypodossier Document Browser.html"
    if not html_path.exists():
        raise DossierException(
            f"Could not find Offline Dossier Browser html: {html_path}"
        )
    webbrowser.open(str(html_path), new=2)


def find_agg_page_objects_in_node_sem_docs(filename: str, node_sem_docs: List[Dict]):
    """
    Loop over all aggregated page objects in document and put them into a dict

    """
    agg_page_objects = {}
    found_doc = False
    for node in node_sem_docs:
        if node["filename"] == filename:
            found_doc = True
            aggregated_object_nodes = node["aggregated_objects"]
            for po in aggregated_object_nodes:
                agg_page_objects[po["key"]] = po["value"]
    return found_doc, agg_page_objects


def validate_expected_page_objects(
    filename, page_objects, filenames, node_sem_docs, json_doc
):
    """
    :param filename: Filename of document that should be tested
    :param page_objects: dict of expected values
    :param filenames: List of all available filenames, only needed for error message
    :param node_sem_docs: list of dict with filename, title, semantic_pages, ...
    :param json_doc: json_doc of full dossier
    """
    logger.info(
        f"File {filename}: expect {page_objects}",
        filename=filename,
        page_objects=page_objects,
    )

    found_doc, agg_page_objects = find_agg_page_objects_in_node_sem_docs(
        filename, node_sem_docs
    )
    untested_page_objects: Dict = {}

    # for page_object_name, page_object_val in page_objects.items():
    #     sem_doc = parse(f"$.semantic_documents").find(json_doc)
    #     print(f'Check expected page object {page_object_name}...')

    # Check 0: does the document exist at all?
    if not found_doc and page_objects:
        raise DossierException(
            f"Did not find document '{filename}' from expected_page_objects. Available filenames={filenames}"
        )

    # Check #1: Check if all page objects in the document have the expected value and log those that have not been tested
    for key, val in agg_page_objects.items():
        # logger.info(f"Check page object {key}....")

        if key in page_objects:
            if page_objects[key] == PO_EXISTS:
                # we just check if it exists, but not the value
                if val is None:
                    raise DossierException(
                        f'Page object "{key} not found but it was expected.'
                    )
            else:
                if val != page_objects[key]:
                    raise DossierException(
                        f'Found page object "{key}" with val="{val}". Expected was "{page_objects[key]}"'
                    )

        else:
            untested_page_objects[key] = val

    if untested_page_objects:
        s = ""
        for key, val in untested_page_objects.items():
            if key in ALL_FIELDS_PO_EXISTS:
                v = "PO_EXISTS"
            else:
                if val:
                    v = val.replace("\n", "\\n")
                else:
                    v = val
                v = f'"{v}"'
            s += f'\n               "{key}": {v},'
        s = (
            "       expected_page_objects={\n           XXX: {"
            + s
            + "\n           }\n       },"
        )
        print(
            f"\n\nFound {len(untested_page_objects)} untested page object(s) in '{filename}':\n{s}\n\n"
        )

    # Check 2: Check if any values had been expected but are not present in document
    for key, val in page_objects.items():
        if key not in agg_page_objects:
            if val:
                # Only raise exception if a non-empty value was expected
                raise DossierException(
                    f"In {filename} page object '{key}' with value '{val}' is missing. Existing page objects: {agg_page_objects}"
                )


def validate_processed_dossier(
    dest_folder: Path,
    duration_seconds: float,
    dossier_expectations: DossierExpectations,
    webbrowser: bool = False,
    log_exceptions: bool = False,
):
    """
    Check various properties of the dossier with assertions
    @param dest_folder:
    @param duration_seconds:
    @param webbrowser:
    @param dossier_expectations:
    @param log_exceptions:

    """

    if webbrowser:
        if global_settings.ENABLE_PYTEST_SHOW_WEBBROWSER:
            open_package_in_browser(dest_folder)
        else:
            logger.info(
                f"Skip showing webbrowser because ENABLE_PYTEST_SHOW_WEBBROWSER={global_settings.ENABLE_PYTEST_SHOW_WEBBROWSER}. dest_folder={dest_folder}"
            )

    path_json_file = dest_folder / "semantic-document.json"

    if not path_json_file.exists() or not path_json_file.is_file():
        raise DossierException(f"path_json not found or not a file: {path_json_file}")

    with open(path_json_file) as json_file:
        json_doc = json.load(json_file)
        if not json_doc:
            raise DossierException("json_doc not found")
        node_sem_docs = json_doc["semantic_documents"]

        if dossier_expectations.expected_filenames:
            if not node_sem_docs:
                raise DossierException(
                    f"Filenames were expected ({len(dossier_expectations.expected_filenames)}) but no semantic documents found in output"
                )

        # print(json_doc)
        jsonpath_filenames = parse("semantic_documents[*].filename")
        # List of filenames
        filenames = [match.value for match in jsonpath_filenames.find(json_doc)]
        num_docs_total = len(filenames)

        jsonpath_page_cat_list = parse(
            "semantic_documents[*].semantic_pages[*].page_category.name"
        )
        page_cat_list = [match.value for match in jsonpath_page_cat_list.find(json_doc)]
        num_pages_total = len(page_cat_list)

        logger.info(f"Found {len(filenames)} filenames:")
        for match in jsonpath_filenames.find(json_doc):
            logger.info(f'  "{match.value}",')

        doc_cat_frequency = calc_doc_cat_frequency(
            "doc_cat_frequency",
            json_doc,
            "semantic_documents[*].document_category.name",
        )
        doc_cat_frequency_names = {
            key.name: value for key, value in doc_cat_frequency.items()
        }

        doc_cat_frequency_per_page = calc_doc_cat_frequency(
            "doc_cat_frequency_per_page",
            json_doc,
            "semantic_documents[*].semantic_pages[*].document_category.name",
        )
        doc_cat_frequency_per_page_names = {
            key.name: value for key, value in doc_cat_frequency_per_page.items()
        }

        jsonpath_extracted_file_exceptions = parse("extracted_files.*.exceptions")
        extracted_file_exceptions = [
            match.value
            for match in jsonpath_extracted_file_exceptions.find(json_doc)
            if match.value != {}
        ]

        jsonpath_processing_exceptions = parse("processing_exceptions.*")
        processing_exceptions = [
            match.value
            for match in jsonpath_processing_exceptions.find(json_doc)
            if match.value != {}
        ]

    if dossier_expectations.expected_filenames:
        for expected_filename in dossier_expectations.expected_filenames:
            found = False
            for f in filenames:
                if expected_filename == f:
                    found = True
                    break
            if not found:
                raise DossierException(
                    f'Could not find expected filename="{expected_filename}" in filenames: {filenames}"'
                )

    if dossier_expectations.expected_num_docs >= 0:
        if len(filenames) != dossier_expectations.expected_num_docs:
            raise DossierException(
                f"Wrong number of documents ({len(filenames)}). Expected was {dossier_expectations.expected_num_docs}."
            )

    if dossier_expectations.expected_doc_cat_frequency:
        # If expected_doc_cat_frequency is present, the sum of all expected documents must match the sum of all created documents.

        num_docs = 0
        for key, val in dossier_expectations.expected_doc_cat_frequency.items():
            if val == 0:
                continue

            if key.name not in doc_cat_frequency_names:
                raise DossierException(
                    f"Expected {val} document(s) of type {key} but found none. Found instead: {doc_cat_frequency_names}"
                )
            num_docs += val
            if key.name in doc_cat_frequency_names:
                real_val = doc_cat_frequency_names[key.name]
                if real_val != val:
                    logger.error(
                        f"Wrong number of documents of type doc cat {key}. Found {real_val} but expected was {val}. expected_doc_cat_frequency={dossier_expectations.expected_doc_cat_frequency}"
                    )

                    # Use the dict with DocumentCat -> int here instead of doc_cat_frequency_names
                    sorted_result = dict(sorted(doc_cat_frequency.items()))

                    sorted_expectations = dict(
                        sorted(dossier_expectations.expected_doc_cat_frequency.items())
                    )
                    assert sorted_result == sorted_expectations
        if num_docs_total != num_docs:
            raise DossierException(
                f"Wrong number of documents. Based on expected_doc_cat_frequency a total of {num_docs} documents was expected by actual number of documents is {num_docs_total}."
            )

    if dossier_expectations.expected_doc_cat_frequency_per_page:
        # If expected_doc_cat_frequency is present, the sum of all expected documents must match the sum of all created documents.

        num_pages = 0
        sorted_result = dict(sorted(doc_cat_frequency_per_page.items()))

        sorted_expectations = dict(
            sorted(dossier_expectations.expected_doc_cat_frequency_per_page.items())
        )
        for (
            key,
            val,
        ) in dossier_expectations.expected_doc_cat_frequency_per_page.items():
            if val == 0:
                continue

            if key.name not in doc_cat_frequency_per_page_names:
                logger.error(
                    f"Expected {val} pages of type {key} but found none. Found instead: {doc_cat_frequency_per_page_names}"
                )
                assert sorted_result == sorted_expectations

            num_pages += val
            if key.name in doc_cat_frequency_per_page_names:
                real_val = doc_cat_frequency_per_page_names[key.name]
                if real_val != val:
                    logger.error(
                        f"Wrong number of pages of type doc cat {key}. Found {real_val} but expected was {val}. expected_doc_cat_frequency_per_page={dossier_expectations.expected_doc_cat_frequency_per_page}. found doc_cat_frequency_per_page={doc_cat_frequency_per_page_names}"
                    )
                    assert sorted_result == sorted_expectations

        if num_pages_total != num_pages:
            logger.error(
                f"Wrong number of pages. Based on expected_doc_cat_frequency_per_page a total of {num_pages} pages was expected by actual number of pages is {num_pages_total}."
            )
            assert sorted_result == sorted_expectations

    do_log_exceptions = len(processing_exceptions) > 0 and (
        log_exceptions
        or dossier_expectations.expected_num_processing_exceptions
        != len(processing_exceptions)
    )
    if do_log_exceptions:
        logger.info(
            "Found processing exceptions, some of them might be expected",
            processing_exceptions=len(processing_exceptions),
        )
        for pe in processing_exceptions:
            logger.info(pe)

    if dossier_expectations.expected_num_extracted_file_exceptions >= 0:
        if (
            len(extracted_file_exceptions)
            != dossier_expectations.expected_num_extracted_file_exceptions
        ):
            raise DossierException(
                f"Wrong number of extracted file exceptions ({len(extracted_file_exceptions)}). Expected was {dossier_expectations.expected_num_extracted_file_exceptions}."
            )

    if dossier_expectations.expected_num_processing_exceptions >= 0:
        if (
            len(processing_exceptions)
            != dossier_expectations.expected_num_processing_exceptions
        ):
            raise DossierException(
                f"Wrong number of processing exceptions ({len(processing_exceptions)}). Expected was {dossier_expectations.expected_num_processing_exceptions}."
            )

    for index, d in enumerate(node_sem_docs):
        logger.info(
            f"Found document #{index} '{d['filename']}' with {len(d['aggregated_objects'])} page objects "
        )

    # if dossier_expectations.expected_page_objects is not None:
    if dossier_expectations.expected_page_objects:
        for key, page_objects in dossier_expectations.expected_page_objects.items():
            # key is either filename of expected file or index of expected file
            try:
                key_int = int(key)
                # key is the index of the document in the list of documents, so fetch correct filename
                filename = filenames[key_int]
            except Exception:
                filename = key
            validate_expected_page_objects(
                filename, page_objects, filenames, node_sem_docs, json_doc
            )
    elif filenames:
        # This shows the untested files
        for filename in filenames:
            validate_expected_page_objects(
                filename, {}, filenames, node_sem_docs, json_doc
            )

    if dossier_expectations.max_duration_seconds >= 1:
        if duration_seconds > dossier_expectations.max_duration_seconds:
            raise DossierException(
                f"Process took too long ({duration_seconds}). Max accepted is {dossier_expectations.max_duration_seconds}."
            )


def calc_doc_cat_frequency(
    freq_type: str, json_doc, json_query: str
) -> Dict[DocumentCat, int]:
    jsonpath_doccat = parse(json_query)
    doc_cats_str = [
        get_document_cat_by_name(match.value)
        for match in jsonpath_doccat.find(json_doc)
    ]
    print(f"Found {len(doc_cats_str)} items for {freq_type}:")
    h_doc_cat = Histogram()
    for match in jsonpath_doccat.find(json_doc):
        print(f"  {match.value}")
        h_doc_cat.add(match.value)
    print(f"{freq_type} histogram: {h_doc_cat.get_sorted_by_key()}")
    print(f"       expected_{freq_type}={{")  # escape the brace {
    for key, val in h_doc_cat.get_sorted_by_key().items():
        print(f"              DocumentCat.{key}: {val},")
    print("       },")
    counter = collections.Counter(doc_cats_str)
    doc_cat_frequency = dict(counter)
    return doc_cat_frequency


async def util_test_batch_with_doc_frequency(
    source_folder: Path,
    source_file_filter: List[str] = [],
    expected_doc_cat_frequency: Dict[DocumentCat, int] = {},
    expected_num_docs: int = -1,
    override_existing=True,
    dest_folder_prefix: str = "",
    max_duration_seconds=500,
    show_page=-1,
):
    dossier_expectations = DossierExpectations(
        expected_filenames=[],
        expected_page_objects={},
        expected_num_docs=expected_num_docs,
        expected_doc_cat_frequency=expected_doc_cat_frequency,
        max_duration_seconds=max_duration_seconds,
    )
    return await util_test_batch(
        source_folder=source_folder,
        source_file_filter=source_file_filter,
        override_existing=override_existing,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=dossier_expectations,
        show_page=show_page,
    )


class DossierTest(BaseModel):
    class Config:
        extra = Extra.forbid

    # Folder that contains all original files of the dossier
    source_folder: Path

    # Optional comma separated list of filenames inside source_folder that will be processed for test. If empty
    # whole folder is processed.
    source_file_filter: List[str] = Field(default_factory=list)

    # If true, offline version is re-created for every call. Else offline version is unchanged (False is useful for
    # single page test and changes to expected resuls)
    override_existing: bool = True

    # String prefix of result folder (which contains offline version) inside OUTPUT_DIR
    dest_folder_prefix: str = "dossier_test_"

    # Configuration of expected document categories and page objects
    dossier_expectations: DossierExpectations = DossierExpectations()

    # Page index of original file inside source_folder which is used for single page test (with show_filename or source_file_filter)
    show_page: int = -1

    # Filename of original file inside source_folder which is used for single page test with show_page
    show_filename: bool = False

    # If true, webbrowser window is opened after test. Is overridden by global flag ENABLE_PYTEST_SHOW_WEBBROWSER
    webbrowser: bool = True

    # If true, cache will be used after first processing, else every call re-creates ocr (and stores in cache)
    use_ocr_cache: bool = True

    # Language in which all document titles are generated
    client_lang: str = "de"

    # Optional instructions for the processing
    processing_config: OriginalFileProcessingConfig = OriginalFileProcessingConfig()

    page_number: int = None

    # If this is set to true then all processing exceptions will be printed to the log
    log_exceptions: bool = False

    async def run(self):

        if global_settings.TEST_FORCE_OVERWRITE_OCR_CACHE:
            self.use_ocr_cache = False

        await util_test_batch(
            self.source_folder,
            self.source_file_filter,
            self.override_existing,
            "dossiertest/" + self.dest_folder_prefix,
            self.dossier_expectations,
            self.show_page,
            self.show_filename,
            self.webbrowser,
            self.use_ocr_cache,
            self.client_lang,
            self.processing_config,
            self.log_exceptions,
        )


async def util_test_batch(
    source_folder: Path,
    source_file_filter: List[str] = [],
    override_existing=True,
    dest_folder_prefix: str = "",
    dossier_expectations: DossierExpectations = DossierExpectations(),
    show_page=-1,
    show_filename=None,
    webbrowser: bool = False,
    use_ocr_cache=True,
    client_lang: str = "de",
    processing_config: OriginalFileProcessingConfig = OriginalFileProcessingConfig(),
    log_exceptions: bool = False,
):
    if global_settings.DISABLE_TEST_BATCH:
        assert False  # So that test needing this fails
    dlog = DurationLog("util_test_batch(...)", silent=False)

    configure_test_logging()

    # rpc_pika.RPC_CLIENT = None

    source_file_filter = source_file_filter

    if not source_folder.exists():
        Exception(f"Destination folder {source_folder} does not exist")

    if global_settings.RAISE_EXCEPTIONS_IN_PARSER:
        logger.info(
            f"RAISE_EXCEPTIONS_IN_PARSER={global_settings.RAISE_EXCEPTIONS_IN_PARSER} for development"
        )
    else:
        logger.error(
            f"global_settings.RAISE_EXCEPTIONS_IN_PARSER={global_settings.RAISE_EXCEPTIONS_IN_PARSER} but should be True to run tests in development"
        )

    if not global_settings.GENERATE_OFFLINE_FILENAME:
        raise Exception(
            "Must set GENERATE_OFFLINE_FILENAME=True for local offline tests"
        )

    if not global_settings.GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS:
        raise Exception(
            "Must set GENERATE_LEGACY_TITLE_PROPERTY_FOR_ALL_DOCUMENTS=True for local offline tests"
        )

    dest_folder, duration_seconds = await run_single_dossier_test(
        source_folder,
        source_file_filter,
        override_existing=override_existing,
        dest_folder_prefix=dest_folder_prefix,
        use_ocr_cache=use_ocr_cache,
        client_lang=client_lang,
        processing_config=processing_config,
    )

    if not dest_folder.exists():
        raise DossierException(f"Destination folder {dest_folder} does not exist")

    if show_page >= 0:
        await show_single_page(
            dest_folder, source_file_filter, show_page, show_filename
        )

    validate_processed_dossier(
        dest_folder,
        duration_seconds=duration_seconds,
        dossier_expectations=dossier_expectations,
        webbrowser=webbrowser,
        log_exceptions=log_exceptions,
    )

    logger.info(dlog)


async def show_single_page(dest_folder, source_file_filter, show_page, show_filename):
    if global_settings.ENABLE_PYTEST_SHOW_PAGE:
        if source_file_filter:
            if len(source_file_filter) == 1 and not show_filename:
                show_filename = source_file_filter[0]
                show_page_from_processed_dossier(dest_folder, show_page, show_filename)
            else:
                Exception(
                    f"show_page={show_page} but source_file_filter must contain exactly 1 element to be able to show page"
                )
        else:
            raise DossierException(
                f"show_page={show_page} but no source_file_filter was set. source_file_filter must contain exactly 1 element to be able to show page"
            )
    else:
        logger.info(
            f"Skip showing single page with index={show_page} because ENABLE_PYTEST_SHOW_PAGE={global_settings.ENABLE_PYTEST_SHOW_PAGE}. dest_folder={dest_folder}"
        )
