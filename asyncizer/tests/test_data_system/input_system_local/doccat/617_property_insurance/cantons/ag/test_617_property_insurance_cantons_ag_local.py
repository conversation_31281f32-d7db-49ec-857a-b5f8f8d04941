from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>x<PERSON><PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

USE_OCR_CACHE = True
FOLDER_TYPE = "617_property_insurance"

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{FOLDER_TYPE}/cantons/ag"
)
dest_folder_prefix = f"input_system_local_doccat_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_ag_bill():
    await DossierTest(
        override_existing=True,
        show_page=0,
        show_filename=False,
        webbrowser=True,
        use_ocr_cache=USE_OCR_CACHE,
        source_folder=path_root_folder,
        dest_folder_prefix=f"{FOLDER_TYPE}_",
        source_file_filter=["617_ag_bill.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "canton_short": "AG",
                    "document_date": "16.11.2015",
                    "property_address": "Rothüslifeldstrasse 13",
                    "owner_name": "Nitoma AG",
                    "police_no": "84445",
                    "insurance_amount": "CHF 1'191'000",
                    "insurance_yearly_bill_amount": "CHF 475",
                    "property_estimation_date": "08.11.2005",
                    "zip": "5001",
                    "city": "Aarau",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_INSURANCE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_617_ag_bill_2016_baden():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["617_ag_bill_2016_baden.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "28.11.2016",
                    "canton_short": "AG",
                    "property_address": "Ehrendingen, Geb. Nr. 888, Breitwies 21,23\nEinstellhalle",
                    "police_no": "330886",
                    "insurance_amount": "CHF 801'000",
                    "insurance_yearly_bill_amount": "CHF 302",
                    "property_estimation_date": "10.03.2015",
                    "zip": "5001",
                    "city": "Aarau",
                }
            },
            expected_doc_cat_frequency={
                DocumentCat.PROPERTY_INSURANCE: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_multipage_2011():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["617_ag_multipage_2011.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={},
            expected_doc_cat_frequency={DocumentCat.PROPERTY_DOCUMENTATION: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_police_2019_two_pages():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["ag_police_2019_two_pages.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "16.11.2019",
                    "canton_short": "AG",
                    "property_address_street": "Pi lat u sw eg",  # TODO BAD OCR "
                    "property_address_city": "Zufikon",
                    "property_desc": "Einfamilienhaus",
                    "year_construction": "1970",
                    "cubature": "1798",
                    "cadaster_no": "400",
                    "insurance_amount": "CHF 1'674'000",
                    "insurance_yearly_bill_amount": "CHF 1'284",
                    "property_estimation_date": "21.08.2007",
                    "street": "Pilatusweg 3",
                    "zip": "5621",
                    "city": "Zufikon",
                    "owner_name": PO_EXISTS,
                    "police_no": "191912",
                    "address_block": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "fullname": PO_EXISTS,
                },
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_INSURANCE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_police_2019_cubature():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["617_ag_police_cubature_problem_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "18.11.2019",
                    "canton_short": "AG",
                    "property_address": "Euelgrabeweg 5",
                    "owner_name": PO_EXISTS,
                    "police_no": "314082",
                    "insurance_amount": "CHF 662'000",
                    "insurance_yearly_bill_amount": "CHF 412",
                    "property_estimation_date": "17.05.2013",
                    "address_block": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "fullname": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "5426",
                    "city": "Lengnau AG",
                },
                # 1: {
                #     "document_date": "16.11.2019",
                #     "canton_short": "AG",
                #     "owner_name": PO_EXISTS,
                #     "police_no": "314082",
                #     "insurance_amount": "CHF 662'000",
                #     "insurance_yearly_bill_amount": "CHF 528",
                #     "property_estimation_date": "17.05.2013",
                #     "address_block": PO_EXISTS,
                #     "firstname": PO_EXISTS,
                #     "fullname": PO_EXISTS,
                #     "street": PO_EXISTS,
                #     "zip": "5426",
                #     "city": "Lengnau AG",
                # }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_INSURANCE: 1},
        ),
    ).run()
