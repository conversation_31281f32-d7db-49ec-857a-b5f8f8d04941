from copy import deepcopy

from abbyyplumber.converter.ValueConverter import (
    ParagraphConverter,
    MostRecentDateConverter,
    DateConverter,
    CompositeConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElementArea,
    SearchElementConstrainedArea,
    SearchElementLabeledField,
    SearchElementMultiStaticText,
    create_labeled_field,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    SearchRelationLeftOf,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    PI_FIELD_INSURANCE_AMOUNT,
    FIELD_PROP_OWNER_ADDRESS,
    FIELD_PROP_ADDRESS,
    FIELD_PROP_ESTIMATION_DATE,
    FIELD_PROP_PROPERTY_DESC,
    PI_FIELD_INSURANCE_YEARLY_BILL_AMOUNT,
    FIELD_PROP_ESTIMATION_REASON,
    FIELD_PROP_POLICE_NO,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_CANTON_SHORT,
    FIELD_FIRSTNAME,
    FIELD_DOCUMENT_DATE,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.CommonPropertyInsurancePageParsers import (
    StandardPropertyInsurancePageParser,
)
from mortgageparser.documents.parser.pageparsers.property_insurance.property_insurance_util import (
    PI_CONVERTER_INSURANCE_AMOUNT,
    PI_CONVERTER_YEARLY_FEE,
    PI_CONVERTER_ESTIMATION_REASON,
    TemplatePropertyInsurancePageParser,
    PropertyInsuranceSearchElements,
    PI_CONVERTER_CUBATURE,
    PI_CONVERTER_PROPERTY_ADDRESS,
)
from mortgageparser.util.search_element_util import create_search_elements_address
from mortgageparser.util.string_utils import contains_all_strings


# created on version from 2013
class PropertyInsuranceBEPolicePageParser(StandardPropertyInsurancePageParser):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Wir versichern Ihr Gebäude",
                "Ittingen",
                "Gebäudeversicherung Bern (GVB)",
                "Versicherungspolice 1/2",
                "Versicherungsbeginn",
                "Objektstandort",
                "Allgemeine Versicherungsbedingungen",  # Sometimes with or without (AVB)
            ],
        )

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementConstant(FIELD_CANTON_SHORT.name, "BE"),
                SearchElementConstrainedArea(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.main,
                    text_bottom="Datum",
                    text_bottom_offset_lines=1,
                    converter=MostRecentDateConverter(),
                ),
                SearchElementLabeledField(
                    FIELD_PROP_POLICE_NO.name,
                    self.page.main,
                    label="Vertrag Nr.",
                    converter=ParagraphConverter(max_num_spaces_per_line=0),
                ),
                SearchElementConstrainedArea(
                    "target_table",
                    self.page.main,
                    text_top="Vertrag Nr.",
                    text_bottom="5 % Stempelsteuer",
                    text_bottom_offset_lines=1,
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_ESTIMATION_REASON.name,
                    self.page.main,
                    target_name="target_table",
                    text_left="Mutationsgrund",
                    text_bottom="Versicherungsnehmer",
                    converter=PI_CONVERTER_ESTIMATION_REASON,
                ),
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.main,
                    target_name="target_table",
                    text_top="Versicherungsnehmer",
                    text_top_offset_lines=-1,
                    text_left="Versicherungsnehmer",
                    text_bottom="Versicherungebeginn",
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_OWNER_ADDRESS.sr_inside,
                    self.page.main,
                    target_name="target_table",
                    text_top="Versicherungsnehmer",
                    text_top_offset_lines=-1,
                    text_left="Versicherungsnehmer",
                    text_bottom="Versicherungebeginn",
                ),
                SearchElementLabeledField(
                    FIELD_PROP_ESTIMATION_DATE.name,
                    self.page.main,
                    target_name="target_table",
                    label="Letzte Gebäudeschätzung",
                    converter=DateConverter(),
                ),
                # SearchElementLabeledField(FIELD_PROP_CADASTER_NO.name, self.page.main,
                #                           target_name="target_table",
                #                           label="Grundbuchnummer", converter=ValidNumberConverter(max=9999)
                #                           ),
                SearchElementConstrainedArea(
                    FIELD_PROP_ADDRESS.name,
                    self.page.main,
                    target_name="target_table",
                    text_top="Objektstandort",
                    text_top_offset_lines=-1,
                    text_left="Objektstandort",
                    text_bottom="Versichertes Objekt",
                    converter=ParagraphConverter(
                        do_capitalize_uppercase=True, max_num_lines_valid=3
                    ),
                ),
                # SearchElementConstrainedArea(PI_FIELD_YEAR_CONSTRUCTION.name, self.page.main, target_name="target_prop",
                #                              text_top="Baujahr",
                #                              text_left="Baujahr", text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                #                              text_right="Länge",
                #                              text_bottom="Baujahr",
                #                              text_bottom_offset_lines=3.5,
                #                              converter=CompositeConverter(converters=[
                #                                  ParagraphConverter(max_num_lines=1),
                #                                  YearConverter()
                #                                  ])
                #                              ),
                #
                # SearchElementConstrainedArea(FIELD_PROP_CUBATURE.name, self.page.main, target_name="target_prop",
                #                              text_top="Baujahr",
                #                              text_left="Höhe",
                #                              text_right="Vers.Wert",
                #                              text_bottom="Baujahr",
                #                              text_bottom_offset_lines=8.5,
                #                              converter=CompositeConverter(converters=[
                #                                  ParagraphConverter(max_num_lines=1),
                #                                  ValidNumberConverter()
                #                                  ])
                #                              ),
                SearchElementLabeledField(
                    FIELD_PROP_PROPERTY_DESC.name,
                    self.page.main,
                    target_name="target_table",
                    label="Versichertes Objekt",
                    converter=ParagraphConverter(max_num_lines=1),
                ),
                SearchElementMultiStaticText(
                    "amount_suffix",
                    self.page.main,
                    target_name="target_table",
                    labels={"(Neuwert)": 2, "(Zeitwert)": 2},
                    extract=False,
                    relations=[SearchRelationBelow(FIELD_PROP_PROPERTY_DESC.name)],
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_AMOUNT.name,
                    self.page.main,
                    target_name="target_table",
                    label="Versicherungssumme",
                    converter=PI_CONVERTER_INSURANCE_AMOUNT,
                    relations=[
                        SearchRelationLeftOf("amount_suffix"),
                        SearchRelationBelow(FIELD_PROP_PROPERTY_DESC.name),
                    ],
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_YEARLY_BILL_AMOUNT.name,
                    self.page.main,
                    label="inkl. 5 % Stempelsteuer",
                    field_vertical_line_scale=7,  # can be in this line or the line above
                    converter=PI_CONVERTER_YEARLY_FEE,
                ),
            ]
            + create_search_elements_address(self.page.main, self.page)
        )


class PropertyInsuranceBEPolicePrivatePageParser(StandardPropertyInsurancePageParser):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Zuständig",
                "Kundencenter",
                "Ittingen",
                "GVB Privatversicherungen AG",
                "Versicherungspolice 1/",
                "Jahresprämie",
            ],
        )

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementConstant(FIELD_CANTON_SHORT.name, "BE"),
                SearchElementConstrainedArea(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.main,
                    text_bottom="Datum",
                    text_bottom_offset_lines=1,
                    converter=MostRecentDateConverter(),
                ),
                SearchElementLabeledField(
                    FIELD_PROP_POLICE_NO.name,
                    self.page.main,
                    label="Vertrag Nr.",
                    converter=ParagraphConverter(max_num_spaces_per_line=0),
                ),
                SearchElementConstrainedArea(
                    "target_table",
                    self.page.main,
                    text_top="Vertrag Nr.",
                    text_bottom="5 % Stempelsteuer",
                    text_bottom_offset_lines=1,
                    extract=False,
                ),
                SearchElementConstrainedArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.main,
                    target_name="target_table",
                    text_top="Versicherungsnehmer",
                    text_top_offset_lines=-1,
                    text_left="Versicherungsnehmer",
                    text_bottom="Versicherungebeginn",
                ),
                SearchElementConstrainedArea(
                    FIELD_PROP_OWNER_ADDRESS.sr_inside,
                    self.page.main,
                    target_name="target_table",
                    text_top="Versicherungsnehmer",
                    text_top_offset_lines=-1,
                    text_left="Versicherungsnehmer",
                    text_bottom="Versicherungebeginn",
                ),
                SearchElementLabeledField(
                    FIELD_PROP_ESTIMATION_DATE.name,
                    self.page.main,
                    target_name="target_table",
                    label="Letzte Gebäudeschätzung",
                    converter=DateConverter(),
                ),
                # SearchElementLabeledField(FIELD_PROP_CADASTER_NO.name, self.page.main,
                #                           target_name="target_table",
                #                           label="Grundbuchnummer", converter=ValidNumberConverter(max=9999)
                #                           ),
                SearchElementConstrainedArea(
                    FIELD_PROP_ADDRESS.name,
                    self.page.main,
                    target_name="target_table",
                    text_top="Objektstandort",
                    text_top_offset_lines=-1,
                    text_left="Objektstandort",
                    text_bottom="Kanton",
                    converter=ParagraphConverter(
                        do_capitalize_uppercase=True, max_num_lines_valid=3
                    ),
                ),
                # SearchElementConstrainedArea(PI_FIELD_YEAR_CONSTRUCTION.name, self.page.main, target_name="target_prop",
                #                              text_top="Baujahr",
                #                              text_left="Baujahr", text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                #                              text_right="Länge",
                #                              text_bottom="Baujahr",
                #                              text_bottom_offset_lines=3.5,
                #                              converter=CompositeConverter(converters=[
                #                                  ParagraphConverter(max_num_lines=1),
                #                                  YearConverter()
                #                                  ])
                #                              ),
                #
                # SearchElementConstrainedArea(FIELD_PROP_CUBATURE.name, self.page.main, target_name="target_prop",
                #                              text_top="Baujahr",
                #                              text_left="Höhe",
                #                              text_right="Vers.Wert",
                #                              text_bottom="Baujahr",
                #                              text_bottom_offset_lines=8.5,
                #                              converter=CompositeConverter(converters=[
                #                                  ParagraphConverter(max_num_lines=1),
                #                                  ValidNumberConverter()
                #                                  ])
                #                              ),
                SearchElementLabeledField(
                    FIELD_PROP_PROPERTY_DESC.name,
                    self.page.main,
                    target_name="target_table",
                    label="Versichertes Objekt",
                    converter=ParagraphConverter(max_num_lines=1),
                ),
                SearchElementMultiStaticText(
                    "amount_suffix", self.page.main, labels={"(Neuwert))": 3}
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_AMOUNT.name,
                    self.page.main,
                    label="Versicherungssumme",
                    converter=CompositeConverter(
                        converters=[
                            ParagraphConverter(max_num_lines=1),
                            PI_CONVERTER_INSURANCE_AMOUNT,
                        ]
                    ),
                    relations=[
                        SearchRelationBelow(FIELD_PROP_PROPERTY_DESC.name),
                        SearchRelationLeftOf("amount_suffix"),
                    ],
                ),
                SearchElementLabeledField(
                    PI_FIELD_INSURANCE_YEARLY_BILL_AMOUNT.name,
                    self.page.main,
                    label="inkl. 5 % Stempelsteuer",
                    field_vertical_line_scale=7,  # can be in this line or the line above
                    converter=PI_CONVERTER_YEARLY_FEE,
                ),
            ]
            + create_search_elements_address(self.page.main, self.page)
        )


# currency_converter_with_commas = deepcopy(PI_CONVERTER_INSURANCE_AMOUNT)
# currency_converter_with_commas.remove_non_digits = True

cubature_converter = deepcopy(PI_CONVERTER_CUBATURE)
cubature_converter.valid_thousand_separators.append(",")

address_converter = deepcopy(PI_CONVERTER_PROPERTY_ADDRESS)
address_converter.do_capitalize_uppercase = (
    True  # Only BE makes ALL UPPERCASE when it comes to addresses
)

property_insurance_parsers_be = [
    TemplatePropertyInsurancePageParser(
        desc="Property Insurance BE Datenauskunft 2017",
        ranked_titles_all=[
            RankedTitle("Datenauskunft"),
            RankedTitle("Sehr geehrter Kunde"),
        ],
        required_tokens=["Gebäudeversicherung Bern", "GVB"],
        canton="BE",
        page_main=SearchElementConstrainedArea(
            None,
            None,
            text_top="Zuständig",
            text_left="Zuständig",
            text_right="wie folgt Auskunft erteilen",
            text_bottom="Auskunftsprodukt",
        ).include_all(-10, -1, 10, 10),
        regions=[
            SearchElementConstrainedArea(
                "region_date", None, text_bottom="Sehr geehrt"
            ),
            SearchElementConstrainedArea(
                FIELD_FIRSTNAME.sr_inside,
                None,
                text_bottom="Sehr geehrt",
                x_range=PercentageRange(0.5, 1),
            ),
            SearchElementConstrainedArea(
                "region_table",
                None,
                text_top="Sehr geehrt",
                text_bottom="Rechnungsstellung",
            ),
        ],
        se=PropertyInsuranceSearchElements(
            document_date=SearchElementArea(None, None, target_name="region_date"),
            property_address=create_labeled_field(
                "Hauptobjekt und", "region_table"
            ).with_converter(address_converter),
            cadaster_no=create_labeled_field("Grundbuchblatt Nr.", "region_table"),
            owner_address=SearchElementConstrainedArea(
                None,
                None,
                text_top="Eigentümer",
                text_left="Eigentümer",
                text_bottom="Korrespondenzadresse",
                target_name="region_table",
            ).include_top(),
            police_no=create_labeled_field("Policennummer", target_name="region_table"),
            insurance_amount=create_labeled_field("Versicherungssumme"),
            year_construction=create_labeled_field("Erstellungsjahr", "region_table"),
            cubature=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_table",
                text_top="Kubatur",
                text_bottom="Kubatur",
                text_left="Kubatur",
                text_right="M3",
                converter=cubature_converter,
            )
            .include_top()
            .include_bottom(),
            property_estimation_date=create_labeled_field(
                "Datum der letzten Schätzung", "region_table"
            ),
            property_desc=create_labeled_field("Objektbemerkungen", "region_table"),
        ),
        debug_breakpoint=False,
    ),
    TemplatePropertyInsurancePageParser(
        page_cat=PageCat.GENERIC_PAGE,
        desc="Property Insurance BE Checkliste 2017",
        ranked_titles_all=[
            RankedTitle("GVB Gruppe"),
            RankedTitle("Qualitätskontrolle für den Hauseigentümer - Checkliste"),
        ],
        required_tokens=["Gebäudeversicherung Bern", "GVB"],
        canton="BE",
    ),
    PropertyInsuranceBEPolicePageParser(),
    PropertyInsuranceBEPolicePrivatePageParser(),  # GVB Privatversicherungen (Zusatz)
    TemplatePropertyInsurancePageParser(
        page_cat=PageCat.GENERIC_PAGE,  # Not single and not last page
        desc="Property Insurance BE Page 2 2017",
        ranked_titles_all=[RankedTitle("GVB Privatversicherungen AG")],
        required_tokens=[
            "Versicherungspolice 2/2",
            "Siehe allgemeine Versicherungsbedingungen (AVB)",
        ],
        canton="BE",
    ),
]
