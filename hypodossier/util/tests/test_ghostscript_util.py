import pytest
from pathlib import Path

from constants import OUTPUT_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.util.ghostscript_util import (
    gs_pdf_fix,
    gs_check_valid_pdf_file,
    execute_gs_command_as_subprocess,
)
from hypodossier.util.pymupdf_util import pymupdf_pdf_fix

# Test data paths - these would be actual PDF files in the test data directory

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_components/pdf_fix"
)

VALID_PDF = path_root_folder / "valid_pdf/valid_pdf.pdf"
GS_FIXABLE_PDF = path_root_folder / "gs_fixable_pdf/gs_fixable_pdf.pdf"
PDFTOCAIRO_FIXABLE_PDF = Path("pdftocairo_fixable_pdf/pdftocairo_fixable_pdf.pdf")
OUTPUT_PATH_GHOSTSCRIPT = Path(OUTPUT_DIR) / "test_pdf_fix"


@pytest.fixture(autouse=True)
def setup_teardown():
    """Setup and teardown for each test."""
    # Create output directory if it doesn't exist
    OUTPUT_PATH_GHOSTSCRIPT.mkdir(parents=True, exist_ok=True)
    yield
    # Cleanup could be added here if needed


def test_gs_pdf_fix_valid_pdf():
    """Test fixing a valid PDF that doesn't need fixing."""
    valid_pdf = gs_check_valid_pdf_file(VALID_PDF, timeout=10)
    assert valid_pdf

    result = gs_pdf_fix(
        VALID_PDF, OUTPUT_PATH_GHOSTSCRIPT, context="test_gs_pdf_fix_valid_pdf"
    )

    assert result is not None
    assert result.exists()
    assert result.name == "valid_pdf_repairedgs.pdf"
    # Additional assertions could verify the content is unchanged


def test_gs_pdf_fix_gs_fixable_pdf():
    """Test fixing a PDF that can be repaired by ghostscript."""

    assert GS_FIXABLE_PDF.exists()

    # Test that this file needs fixing
    valid_pdf = gs_check_valid_pdf_file(
        GS_FIXABLE_PDF, timeout=10, context="blacontext"
    )
    assert not valid_pdf

    result1, changed = pymupdf_pdf_fix(
        GS_FIXABLE_PDF,
        OUTPUT_PATH_GHOSTSCRIPT,
        context="test_gs_pdf_fix_gs_fixable_pdf",
    )
    assert result1 is not None
    assert result1.exists()
    assert changed

    result2 = gs_pdf_fix(result1, OUTPUT_PATH_GHOSTSCRIPT)

    assert result2 is not None
    assert result2.exists()
    assert result2.name == f"{GS_FIXABLE_PDF.stem}_repairedmu_repairedgs.pdf"
    # Additional assertions could verify the content is now valid

    result_path = OUTPUT_PATH_GHOSTSCRIPT / result2.name
    valid_gs_fixed_pdf = gs_check_valid_pdf_file(result_path, timeout=10)
    assert valid_gs_fixed_pdf


def test_gs_pdf_fix_broken_pdf():
    """Test fixing a PDF that is too broken to be repaired."""

    # Test that this file needs fixing
    valid_pdf = gs_check_valid_pdf_file(
        PDFTOCAIRO_FIXABLE_PDF, timeout=10, context="blacontext"
    )
    assert not valid_pdf

    result = gs_pdf_fix(PDFTOCAIRO_FIXABLE_PDF, OUTPUT_PATH_GHOSTSCRIPT)

    assert result is None
    # Verify no output file was created
    assert not (OUTPUT_PATH_GHOSTSCRIPT / "pdftocairo_fixable_pdf.pdf").exists()


def test_gs_pdf_fix_custom_suffix():
    """Test fixing a PDF with a custom suffix."""
    custom_suffix = "_custom_fix"
    result = gs_pdf_fix(VALID_PDF, OUTPUT_PATH_GHOSTSCRIPT, suffix=custom_suffix)

    assert result is not None
    assert result.exists()
    assert result.name == f"valid_pdf{custom_suffix}.pdf"


def test_ghostscript_version():
    """Test that Ghostscript version starts with 10."""
    # Run gs --version to get the version number
    success, return_code, stdout_text, stderr_text = execute_gs_command_as_subprocess(
        ["gs", "--version"]
    )

    assert success, "Failed to execute gs --version command"
    assert (
        return_code == 0
    ), f"Ghostscript version check failed with return code {return_code}"

    # Get version string and check if it starts with "10."
    # 250523 mt: should be 10.0.0 or 10.02.0 or higher from now on.
    version = stdout_text.strip()
    assert version.startswith(
        "10."
    ), f"Ghostscript version {version} does not start with 10."
