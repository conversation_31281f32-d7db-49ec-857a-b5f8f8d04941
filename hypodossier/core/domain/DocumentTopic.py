from enum import Enum


class DocumentTopic(Enum):
    ERROR = 1

    PERSON_GENERAL = 20
    PERSON_FINANCE = 30
    PERSON_PENSION = 40

    COMPANY = 50

    PROPERTY = 60

    FINANCING = 70  # Everything about the mortgage

    PARTNER = 80  # Custom Documents of one bank / insurance company / broker

    MISC = 90  # Documents that can actually be thrown away, not needed for mortgage (e.g. bills)

    UNKNOWN = 99  # Un-Classified stuff (with or without language)


COMMON_TOPICS = [
    DocumentTopic.PERSON_GENERAL,
    DocumentTopic.PERSON_FINANCE,
    DocumentTopic.PERSON_PENSION,
    DocumentTopic.COMPANY,
    DocumentTopic.PROPERTY,
    DocumentTopic.FINANCING,
    DocumentTopic.MISC,
    DocumentTopic.UNKNOWN,
]

ALL_TOPICS = [e for e in DocumentTopic]
