import codecs
import json
from datetime import datetime
from pathlib import Path
from typing import Type, List

from constants import BASE_DIR
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageDataMapping import get_pagedata_by_doc_cat


def create_page_object_key_union_set_for_all_doc_cat(
    doc_cat_enum: Type[DocumentCat],
    dest_file_path: Path,
    exclude_doc_cats: List[DocumentCat] = None,
) -> int:
    """
    Creates a union set of page object keys for all document categories and writes it to a JSON file.

    Args:
        doc_cat_enum (Type[DocumentCat]): The enumeration of document categories.
        dest_file_path (Path): The destination file path to write the JSON data.
        exclude_doc_cats (List[DocumentCat], optional): List of document categories to exclude. Defaults to None.

    Returns:
        int: The number of unique page object keys.
    """
    page_object_keys = set()
    for doc_cat in doc_cat_enum:
        if exclude_doc_cats and doc_cat in exclude_doc_cats:
            continue
        pd = get_pagedata_by_doc_cat(doc_cat)
        if not pd:
            print(f"Doc cat has no associated page object data={doc_cat}")
            continue
        fields = pd.get_visible_semantic_fields()
        page_object_keys.update(fields.keys())

    sorted_page_object_keys = sorted(list(page_object_keys))

    formatted_data = []
    for index, key in enumerate(sorted_page_object_keys):
        formatted_data.append(
            {
                # disable id export "id": index,
                "key": key
            }
        )

    num_page_object_keys = len(formatted_data)

    # Write the contents of the set to the dest_file_path as a JSON file
    dest_file_path.parent.mkdir(exist_ok=True, parents=True)

    with codecs.open(str(dest_file_path), "w", "utf-8") as f:
        json.dump(formatted_data, f, ensure_ascii=False, indent=4)

    return num_page_object_keys


def create_hypodossier_document_cat(
    doc_cat_enum: Type[DocumentCat], dest_file_path: Path
) -> int:
    """
    Creates a JSON file with document categories.

    Args:
        doc_cat_enum (Type[DocumentCat]): The enumeration of document categories.
        dest_file_path (Path): The destination file path to write the JSON data.

    Returns:
        int: The number of document categories.
    """
    document_categories = []
    for doc_cat in doc_cat_enum:
        document_categories.append(
            {
                # disable id export "id": index,
                "key": str(doc_cat.name)
            }
        )

    num_document_categories = len(document_categories)
    dest_file_path.parent.mkdir(exist_ok=True, parents=True)
    with codecs.open(str(dest_file_path), "w", "utf-8") as f:
        json.dump(document_categories, f, ensure_ascii=False, indent=4)

    return num_document_categories


def export_page_object_keys_for_cdp(
    timestamp_version: str = datetime.today().strftime("%Y%m%d")[2:],
) -> None:
    """
    Exports page object keys for CDP to a JSON file.

    Args:
        timestamp_version (str, optional): The timestamp version for the file name. Defaults to today's date in YYMMDD format.
    """
    # Data to be used in the CDP
    p_page_object_keys = Path(
        f"{BASE_DIR}/doc/internal/cdp/{timestamp_version}_Hypodossier_Page_Object_Keys.json"
    )
    num_unique_page_objects = create_page_object_key_union_set_for_all_doc_cat(
        DocumentCat, p_page_object_keys, [DocumentCat.FINANCIAL_STATEMENT_COMPANY]
    )
    print(
        f"Exported {num_unique_page_objects} unique page object keys to {p_page_object_keys}"
    )


# This is not needed anymore; since we use the document categories from the dms assets directly
# p_document_categories = Path(
#     f"{BASE_DIR}/doc/internal/cdp/{timestamp_version}_Hypodossier_Document_Categories.json"
# )
# num_document_categories = create_hypodossier_document_cat(DocumentCat, p_document_categories)
# print(
#     f"Exported {num_document_categories} document categories to {p_document_categories}"
# ) 