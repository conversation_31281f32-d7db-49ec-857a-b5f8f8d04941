from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElementConstrainedArea,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.bank_account.BankAccountPageData import (
    FIELDS_BANK_ACCOUNT,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_COMPANY
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplateAssumptionDebtNoticePageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.ASSUMPTION_DEBT_NOTICE
    company: str = None

    se: GenericLetterSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_BANK_ACCOUNT.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_transfer_debt_notice():
    # Here goes everything with 'Schuldübergang'
    parsers = [
        TemplateAssumptionDebtNoticePageParser(
            desc="Grundbuch Anzeige Schuldübergang #1/2 DE 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            # ranked_titles_all=[RankedTitle('Rahmenvertrag für Hypotheken')],
            required_text_conditions=[
                FromStartTextCond("Grundbuch", num_lines=20),
                FromStartTextCond("Anzeige des Schuldüberganges", num_lines=20),
            ],
            required_tokens_any=[
                ["Betroffene Grundpfandrechte"],
                ["Betroffene Grundstücke"],
                ["Blatt"],
            ],
            se=GenericLetterSearchElements(
                document_date=SearchElementConstrainedArea(
                    None, None, y_range=PercentageRange(0, 0.5)
                )
            ),
        ),
    ]
    return parsers


def get_parsers_assumption_debt_notice():
    parsers = get_parsers_transfer_debt_notice() + [
        TemplateAssumptionDebtNoticePageParser(
            desc="Grundbuch Anzeige Schuldübernahme #1/2 DE 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            # ranked_titles_all=[RankedTitle('Rahmenvertrag für Hypotheken')],
            required_text_conditions=[
                FromStartTextCond("Grundbuch", num_lines=20),
                FromStartTextCond("Anzeige der Schuldübernahme", num_lines=20),
            ],
            required_tokens_any=[
                ["Betroffene Grundpfandrechte"],
                ["Betroffene Grundstücke"],
                ["Blatt"],
            ],
            se=GenericLetterSearchElements(
                document_date=SearchElementConstrainedArea(
                    None, None, y_range=PercentageRange(0, 0.5)
                )
            ),
        ),
        TemplateAssumptionDebtNoticePageParser(
            desc="Grundbuch Anzeige Schuldübernahme #2a/2 DE 2021",
            page_cat=PageCat.GENERIC_PAGE,
            required_tokens_any=[
                ["Eine gleichlautende Anzeige erhalten zu haben, bestätigt"],
                ["Grundbuchamt"],
            ],
        ),
        TemplateAssumptionDebtNoticePageParser(
            desc="Grundbuch Anzeige Schuldübernahme #2b/2 DE 2021",
            page_cat=PageCat.GENERIC_PAGE,
            # max_num_chars_digit=50,        # can also be longer
            required_tokens_any=[
                [
                    "Von dieser Schuldübernahme wird Ihnen als Schuldner",
                    "gleichlautendes Doppel",
                ],
                ["Grundbuchamt"],
            ],
        ),
    ]
    return parsers
