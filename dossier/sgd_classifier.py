import os
from dataclasses import dataclass
from pathlib import Path
from time import ctime
from typing import List

import joblib
import requests
from pydantic import Field
from pydantic.main import BaseModel

import global_settings
from hypodossier.core.domain.ClassificationValueObject import (
    ClassificationValueObject,
    ClassificationMeta,
)

base = Path(__file__).parent


class SgdClassiferData(BaseModel):
    data: dict = Field(
        default_factory=dict
    )  # dictionary with 3 items 'metrics', 'classifier', 'pipeline'
    classification_dict: dict = Field(default_factory=dict)


classifier_data_dict = {}

# This must be set to True only in Development at this will do a http call to fetch fresh classifications
FETCH_FRESH_CLASSIFICATIONS = False


def get_classification(name: str):
    c_dict = create_classification_dict_from_request()

    if name in c_dict:
        return c_dict[name]
    raise ValueError(f"Unknown classification: {name}")


"""
Return list of tupel (probability, ClassificationValueObject)
ClassificationValueObject
length of list is top_n ordered by probability decreasing
"""


def create_classification_dict_from_joblib(data):
    classifications: List[ClassificationValueObject] = data.get("classifications")
    classification_dict = {}
    if classifications:
        for c in classifications:
            classification_dict[c.name] = c
    return classification_dict


def create_classification_dict_from_request():
    classification_dict = {}
    print("create classification map from request")
    res = requests.get("http://localhost:3000/dossier_classification")
    classifications = res.json()
    print(f"Found {len(classifications)} classifications")
    for classification in classifications:
        cn = classification.get("name")
        cm = None
        j = classification.get("meta_json")
        if j:
            cm = ClassificationMeta.parse_obj(j)
        cvo = ClassificationValueObject(name=cn, meta_json=cm)
        classification_dict[cn] = cvo
    return classification_dict


@dataclass
class HierarchyNode:
    level: int  # depth level of node in tree (0 == root node)
    prob: float  # probability at this level of tree
    prob_hierarchy: (
        float  # probability of full path until this node (Pfadwahrscheinlichkeit)
    )
    classification: ClassificationValueObject


def extract_most_probable_path(
    nodes: List[HierarchyNode], threshold: float, level: int = 0, min_depth: int = 2
) -> (List[HierarchyNode], HierarchyNode):
    # sort by level, p_hierarchy to have the best matches at the top
    # start with level 0
    # find max p_hierarchy
    # choose that node and filter the rest by level + 1 minimum
    # if the depth of the result is less than min_depth, return nothing instead.
    # min_depth=2 means that 'DE' will never be returned

    path = []

    nodes_above_threshold = list(
        node for node in nodes if node.prob_hierarchy >= threshold
    )

    if nodes_above_threshold:
        nodes_at_level = list(
            node for node in nodes_above_threshold if node.level == level
        )
        nodes_at_level = sorted(
            nodes_at_level, key=lambda node: node.prob, reverse=True
        )
        if nodes_at_level:
            parent_node = nodes_at_level[0]
            path.append(parent_node)
            prefix = parent_node.classification.name
            child_nodes = list(
                node
                for node in nodes_above_threshold
                if node.classification.name.startswith(prefix) and node.level > level
            )
            if child_nodes:
                sub_path, _ = extract_most_probable_path(
                    child_nodes, threshold, level + 1
                )
                path += sub_path

        elif level == 0:
            # No nodes at level 0 but >0 nodes means this is a flat (not a hierarchical) classifier
            nodes_sorted = sorted(
                nodes_above_threshold, key=lambda node: node.prob, reverse=True
            )
            most_probable_node = nodes_sorted[0]
            return [most_probable_node], most_probable_node

    if level == 0 and len(path) < min_depth:
        # No sufficient depth path could be found. Abort and return empty result
        return [], None

    if path:
        most_probable_node: HierarchyNode = path[-1]
        return path, most_probable_node
    return path, None


def classify(
    text: str,
    top_n=1,
    fetch_fresh_classifications=FETCH_FRESH_CLASSIFICATIONS,
    path_str=global_settings.SGD_CLASSIFIER_JOBLIB_FILE_IT,
):
    global classifier_data_dict
    """ Return List[HierarchyNode]
    """
    if text is None:
        return []

    # data = None
    # classification_dict: Dict[str, ClassificationValueObject] = {}

    cd = None
    if path_str in classifier_data_dict:
        cd = classifier_data_dict[path_str]
    else:
        cd = create_sgd_classifier_data(fetch_fresh_classifications, path_str)
        classifier_data_dict[path_str] = cd

    vectorizer = cd.data.get("vectorizer")
    vec_text = None
    if vectorizer:
        vec_text = vectorizer.transform([text])
    else:
        vec_text = [text]
    text_clf = cd.data.get("classifier")
    confidences = text_clf.predict_proba(vec_text)[0]
    classification_names = text_clf.classes_
    classifications = []
    for name in classification_names:
        c: ClassificationValueObject
        if cd.classification_dict and name in cd.classification_dict:
            c = cd.classification_dict[name]
        else:
            c = ClassificationValueObject(name=name)

        classifications.append(c)

    z = zip(confidences, classifications)

    z_with_hierarchy = []
    name_to_p_hierarchy = {}  # str -> HierarchyNode in hierarchy (down the path)
    # max_prob_per_level = {}  # level as int -> max probability as float
    for p, c in z:
        if p > 0.0:
            p_hierarchy = p

            partition = c.name.rpartition("/")
            parent = partition[0]
            level = c.name.count("/")
            if parent:
                if parent in name_to_p_hierarchy:
                    # This is a hierarchical classifier
                    p_hierarchy = p * name_to_p_hierarchy[parent]
                else:
                    # This is a flat classifier, no need to multiply with parent probability
                    pass

            if p_hierarchy:
                z_with_hierarchy.append(HierarchyNode(level, p, p_hierarchy, c))

            name_to_p_hierarchy[c.name] = p_hierarchy

    return z_with_hierarchy


def create_sgd_classifier_data(fetch_fresh_classifications, path_str):
    p = Path(path_str)
    print(f"Current Joblib file: {path_str}, modified {ctime(os.path.getmtime(p))}")
    data = joblib.load(path_str)
    classification_dict = None

    if fetch_fresh_classifications:
        classification_dict = create_classification_dict_from_request()
    else:
        classification_dict = create_classification_dict_from_joblib(data)
    cd = SgdClassiferData(data=data, classification_dict=classification_dict)
    return cd


# tupel = (probability, ClassificationValueObject)
def separate_first_deep_path(list_of_tupel):
    idx = 0
    first_path = []
    rest = []
    last_level = None
    on_first_branch = True
    for prob, c in list_of_tupel:
        if prob == 0:
            break

        level = c.name.count("/")
        on_first_branch = on_first_branch and (last_level is None or last_level < level)
        if level >= idx and on_first_branch:
            first_path.append((prob, c))
        else:
            rest.append((prob, c))

        idx += 1
        last_level = level

    return first_path, rest


if __name__ == "__main__":
    c = get_classification("DE/310/ZH/Abzüge")
    print(f"classification={c}")
    print(f"page cat from meta: {c.meta_page_cat}")
    c = get_classification("DE/450/Vorsorge Bescheinigung Beiträge/Postfinance 3a")
    print(f"classification={c}")
    print(f"page cat from meta: {c.meta_page_cat}")
    print(f"company={c.meta_company}, product={c.meta_product}")

    c2 = get_classification("DE/310/AG/Personalien")
    print(f"classification AG ={c2}")
    print(f"page cat from meta: {c2.meta_parser}")
