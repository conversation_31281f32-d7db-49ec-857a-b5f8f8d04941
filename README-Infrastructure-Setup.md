cd hyextract/infrastructure/dev
docker login registry.gitlab.com
# -> <PERSON><PERSON> succeded
docker stack deploy -c hypodossier.yml -c hypodossier-mt.yml hypodossier --with-registry-auth


docker service scale hypodossier_object-detector=0
docker service scale hypodossier_image-detector=0
docker service scale classifier_classifier_db_1=0
docker service scale hypodossier_graphical-object-classification-server=0
docker service scale swarmpit_app=0

docker swarm init

# Install OpenVPN   
- connect to vpn01.hypodossier.ch (with certificate)


# Swarmpit

- Open Terminal
- Go to C:\dev\python\pycharmprojects\hyextract\infrastructure\dev>

# Admin 
- check which services are running
-- docker service ls

# Service for docker / swarm management
# http://localhost:8090/#/stacks
docker stack deploy -c swarmpit.yml swarmpit
docker stack ps swarmpit
docker service logs swarmpit_app

http://localhost:8090/ or http://**************:8090/

admin Letmein42!

# local S3 store: 
# http://localhost:9000/minio/ocr/ or http://**************:9000/minio/login
S3_ACCESS_KEY, S3_SECRET_KEY
docker stack deploy -c minio.yml minio

docker pull minio/console

-> Create 3 buckets 'tmp'/'ocr' / 'hypodossier-models'
-> inside hypodossier-models create path 'hydocs_spacy' and copy current model(s) over from prod

# rabbitmq (admin / admin)
#  http://localhost:15672/#/
docker stack deploy -c rabbitmq.yml rabbitmq
https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html#install-guide

# hypodossier core services

First time:
docker login -u gitlab-ci-token --password-stdin registry.gitlab.com ... then enter OAUTH token ...

After that:
docker login registry.gitlab.com

docker stack deploy -c hypodossier.yml -c hypodossier-mt.yml hypodossier --with-registry-auth

docker service scale hypodossier_graphical-object-detection-server=1

docker service scale hypodossier_graphical-object-detection-server=0
docker service scale hypodossier_frep=0

docker service scale hypodossier_hyextract=1


nachher nur noch docker stack deploy -c hypodossier.yml hypodossier

mpit (set num replicas from 1 to 0)
then start hyextract/hypodossier/consumer.py -> Then go to rabbitmq dashboard, PageAnalysis -> should have 1 consumer

adjust paths in lugi/.env and hyextract/.env

# run only once to create images
choco install poppler

# run only to install gpu support for object detector
choco install cuda

# update frep
docker service update hypodossier_frep --image registry.gitlab.com/hypodossier/frep:v6

docker service update hypodossier_frep --limit-cpu 3 --limit-memory 4096M --reserve-cpu 1 --reserve-memory 1024M
hypodosser_frep

docker service update hypodosser_frep --replicas 1

docker service update hypodossier_spacy-classifier --replicas 1


git checkout dev


docker stack rm hypodosser

docker stack deploy -c hypodossier.yml hypodossier --with-registry-auth


S3_ACCESS_KEY
S3_SECRET_KEY

also zuerst context erstellen: 
docker context create hyswarmprod --docker host="ssh://ubuntu@**************" --default-stack-orchestrator=swarm


ssh ubuntu@**************
docker context create swarm2 --docker host="ssh://ubuntu@**************" --default-stack-orchestrator=swarm


und dann entweder 
docker context use swarm2

or only in current terminal window
export DOCKER_CONTEXT=swarm2vpn

# go back to local docker
docker context use default

# show which context is currently active (the one with *)
docker context ls 

# list of services (after normal SSH login was successful)  services view in swarmpit
docker service ls

ssh swarm21
docker stack ps hyextract

#automatic updates
watch docker stack ps hyextract
# better:
watch docker stack ps --filter desired-state=running hyextract



# list of services of specific type (tasks view in swarmpit)
docker service ps processing-hypodossier_hyextract

# deploy local docker compose info (and version) to prod
docker stack deploy -c hyextract/docker-compose.yml hyextract --prune --with-registry-auth


# Increase memory for a service:
docker service update processing-hypodossier_hyextract --limit-memory 5000M
docker service update processing-hypodossier_imap2mqs3_fs24 --limit-memory 500M

docker service update processing-hypodossier_mail-processor --force

docker service update processing-hypodossier_dossier-processor --force

docker service scale hypodossier_pdf2img=3


# CAUTION: delete all stacks
docker stack rm
# Start up everything. Run this for all yaml files:
docker stack -c (für alle files einzeln)

#Upgrade of a code change

# create built locally with version tag (without tag it's always 'dev'
TAG=v77 docker-compose build

# Push to gitlab with new version tag v73

TAG=v76 docker-compose push hyextract

# go to prod-deployment -< processing-hypodossier
# replace 7 occurences of v72 with v73

# show logs on server
docker service logs --follow --tail 200 processing-hypodossier_frep
docker service logs --follow --tail 200 hypodossier_graphical-object-detection-server

docker service logs --follow --tail 200 hypodossier_graphical-object-classification-server

# single command in remote docker
# restart a single service
docker --context=hyswarmprod service update processing-hypodossier_dossier-processor --force 

docker --context=hyswarmprod service update processing-hypodossier_spacy-classifier --force



## swarmpit restart: 
docker service update swarmpit_app --force
docker service update hypodossier_spacy-classifier
docker service update hypodossier_frep

docker service update hypodossier_graphical-object-classification-server
docker service logs hypodossier_graphical-object-classification-server

# Check Mailserver

## Check Mailserver logs
ssh <EMAIL>
cd ~/mailu
df -h
docker-compose ps
docker-compose logs -f --tail=200 smtp

## Check webmail
https://mail.hypodossier.ch/webmail/
<NAME_EMAIL> or <EMAIL> and password from prod-deployment project


## Check DNS for mailserver
dig mx mail.hypodossier.ch
dig mail.hypodossier.ch

# Check mxtoolbox from externally
https://mxtoolbox.com/SuperTool.aspx?action=mx%3amail.hypodossier.ch&run=toolpage
https://mxtoolbox.com/SuperTool.aspx?action=mx%3amail.hypodossier.ch&run=toolpage#


  pdf2img:
    deploy:
      replicas: 4

spacy-classifier:
    deploy:
      replicas: 1

  imap2mqs3:
    deploy:
      replicas: 0

  imap2mqs3_fs24:
    deploy:
      replicas: 1

  mjml:
    deploy:
      replicas: 1
  image-detector:
    deploy:
      replicas: 4

  hymrz:
    deploy:
      replicas: 1
  

------------------------

On Minio create these buckets:
cache
dossier
hypodossier-models
ocr
tmp

needed for initial setup on ubuntu

check right venv environment, should be hyextract:
echo $VIRTUAL_ENV
# go to right directory hyextract
source venv/bin/activate

pip install wheel

python -m spacy download de_core_news_sm
sudo apt install imagemagick
# for some security reasons of old release of ghostscript PDF conversion is disabled. We need to enable it
sudo nano /etc/ImageMagick-6/policy.xml
# Comment OUT the block at the end that says: 
<policy domain="coder" rights="none" pattern="PS" />
  <policy domain="coder" rights="none" pattern="PS2" />
  <policy domain="coder" rights="none" pattern="PS3" />
  <policy domain="coder" rights="none" pattern="EPS" />
  <policy domain="coder" rights="none" pattern="PDF" />
  <policy domain="coder" rights="none" pattern="XPS" />

# And save the file... then run e.g. trial_fs24 to confirm pdf to image conversion)

sudo apt-get install -y git-lfs

# unpack hyextract/artefact to a new folder, e.g. /home/<USER>/PycharmProjects/document-browser-build (so data is in /build subfolder)
# Then set this in the env file: 
DOCUMENT_BROWSER_BUILD_PATH=/home/<USER>/PycharmProjects/document-browser-build/build

-----------------------------------------------------
For objectclassifier

# open shell
docker-compose run --rm objectclassifier

# show python version
python --version

# show tensorflow version
python -m pip show tensorflow

# show keras version

python -c 'import keras; print(keras.__version__)'

https://gitlab.com/hypodossier/graphical-object-analyzer-client.git

pip install git+https://gitlab+deploy-token-509655:
<EMAIL>/hypodossier/graphical-object-analyzer-client.git@master

# Check imports locally_

docker-compose build docker run -it registry.gitlab.com/hypodossier/hyextract:dev bash

(in container):
pip install -r requirements.txt

# show all local images with a version 'v' tag

docker images | grep v

# delete all local images that are not running

docker image prune

# locally build an image with a TAG

TAG=v77 docker-compose build

# push locally built image to gitlab

TAG=v77 docker-compose push hyextract

... then it can be used in yaml file

docker image ls | grep hyextract docker run -it --entrypoint=bash registry.gitlab.com/hypodossier/hyextract:v76

du -a /usr/local/lib/python3.8/site-packages | sort -n -r | head -n 10

git <NAME_EMAIL>:hypodossier/graphical-object-analyzer-client.git

du -a /usr/local/lib/python3.8/site-packages | sort -n -r | head -n 10

find largest 50 directories:
du -a / | sort -n -r | head -n 50

du -a * | sort -n -r | head -n 50

# Check local setup

cat /etc/hosts

find local IP:
ifconfig -a | grep 192 -A 7 -B 2

-> update hosts file if necessary

Log into server to see what the image consists of:
docker run -it --entrypoint=bash registry.gitlab.com/hypodossier/hyextract:dev
ls -al /usr/local/lib/python3.8/site-packages

docker service scale hypodossier_graphical-object-classification-server=1
docker service scale hypodossier_graphical-object-detection-server=0

docker service scale hypodossier_hyextract=0


docker-compose down hypodossier_graphical-object-detection-server --remove-orphans

docker-compose down hypodossier_hyextract --remove-orphans