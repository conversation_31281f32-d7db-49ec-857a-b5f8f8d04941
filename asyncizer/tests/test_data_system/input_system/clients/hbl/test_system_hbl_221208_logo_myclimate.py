from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS

FOLDER_TYPE = "221208_logo_myclimate"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS}/hbl/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_hbl_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_logo():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        # source_file_filter=['Finanzierungspotenzial_FK.xlsx'],
        # show_page=5,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0
        ),
    ).run()
