from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssie<PERSON><PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/email"
)
dest_folder_prefix = "input_system_components_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_eml_email_with_attachments():
    source_folder = path_root_folder
    assert source_folder.exists()
    # Contains a single email with two attachments (a return pdf and a complaints pdf)

    await DossierTest(
        override_existing=True,
        source_folder=source_folder,
        source_file_filter=["eml_with_attachment.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "002 DOK EN Complaints Terms.pdf",
                "322 Bankdokument.pdf",
                "738 Email-Korrespondenz powiadomienia_at_allegro.pl.pdf",
            ],
            expected_doc_cat_frequency={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_EN: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 4,
                DocumentCat.UNKNOWN_EN: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_eml_example():
    target_folder = path_root_folder
    assert target_folder.exists()

    # Single HTML email, food delivery order, yummy!

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["eml_example.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=["738 Email-Korrespondenz noreply_at_frisco.pl.pdf"],
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 4},
        ),
    ).run()
