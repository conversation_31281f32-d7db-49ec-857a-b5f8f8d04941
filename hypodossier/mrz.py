import logging
from datetime import datetime
from pathlib import Path
from typing import List
from uuid import uuid4

import s3fs
from pydantic import BaseModel, Field

import global_settings
from hypodossier.util.rpcclient import RPCClient

MRZ_DETECTOR_KEY = "mrz.MRZDetectionRequest"


class MRZDetectionRequest(BaseModel):
    bucket: str
    object: str


class MrzLine(BaseModel):
    confidence: float
    text: str
    warpedBox: List[int]


class MrzZone(BaseModel):
    lines: List[MrzLine]
    warpedBox: List[int]


class MRZDetection(BaseModel):
    ref_width: int
    ref_height: int

    duration: int
    frame_id: int
    zones: List[MrzZone] = Field(default_factory=list)


def detect_mrz(file_path):
    assert file_path.exists()
    folder = uuid4()
    bucket = "tmp"
    object_name = f"{folder}/{file_path.name}"
    url = f"{bucket}/{object_name}"
    s3 = s3fs.S3FileSystem(
        anon=False,
        client_kwargs={
            "endpoint_url": global_settings.S3_HOST_URL,
            "aws_access_key_id": global_settings.S3_ACCESS_KEY,
            "aws_secret_access_key": global_settings.S3_SECRET_KEY,
        },
    )
    s3.put(str(file_path), url, recursive=True)
    assert s3.exists(url)
    try:
        start = datetime.now()

        response = RPCClient(global_settings.RABBIT_URL).call(
            MRZ_DETECTOR_KEY,
            MRZDetectionRequest(bucket=bucket, object=object_name).json(),
        )

        end = datetime.now()
        print(end - start, response)
        return MRZDetection.parse_raw(response)
    except Exception:
        logging.exception("could not call service")
    finally:
        print("deleting temp s3 folders")
        s3.rm(f"{bucket}/{folder}", recursive=True)


if __name__ == "__main__":
    file_path = Path(
        "/home/<USER>/Documents/hypodossier/demo_dossier/output/sales_pitch_mix.zip/pageimages/sales_pitch_mix.zip/sales_pitch_mix/SCAN0023.JPG/0.jpg"
    )
    for i in range(100):
        detect_mrz(file_path)
