from dataclasses import dataclass

# There is a specification here: https://www.elohnausweis-ssk.ch/de/assets/documents/Benutzerhandbuch_eLA.pdf
# https://www.elohnausweis-ssk.ch/de/assets/documents/SalaryDeclarationElohnOnline.xsd
from typing import Dict

from hypodossier.core.documents.land_register.LandRegisterPageData import (
    FIELD_LAND_REGISTER_ID,
    FIELD_PROP_VALUE_RATIO,
)
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    FIELD_PROP_ADDRESS,
    FIELD_PROP_ADDRESS_STREET,
    FIELD_PROP_ADDRESS_STREET_NO,
    FIELD_PROP_ADDRESS_CITY,
    FIELD_PROP_ADDRESS_ZIP,
    FIELD_PROP_PROPERTY_DESC,
    FIELD_PROP_OWNER_FULLNAME,
    FIELD_PROP_OWNER_ADDRESS,
    PI_FIELD_YEAR_CONSTRUCTION,
    FIELD_PROP_CUBATURE,
    PI_FIELD_PLOT_SIZE,
    FIELD_PROP_CADASTER_NO,
    FIELD_PROP_ESTIMATION_DATE,
    FIELD_PROP_ESTIMATION_REASON,
    FIELD_PROP_NEW_VALUE,
    FIELD_PROP_TIME_VALUE,
)
from hypodossier.core.domain.PageData import PersonPageData, FIELDS_PERSON_DATA
from hypodossier.core.domain.SemanticField import (
    SemanticField,
    FieldTitles,
    ExtractionType,
    fields_as_dict,
    FIELD_CANTON_SHORT,
    merge_dicts,
    FIELD_DOCUMENT_VALIDITY_START_DATE,
    FIELD_PROPERTY_AREA_LIVING_GROSS,
)

# FIELD_PROP_CADASTER_NO = SemanticField("cadaster_no", ExtractionType.STRING, FieldTitles("Kataster Nr.", "Cadaster No."), desc_de="Katasternummer des zugehörigen Grundstücks. I. d. R. eine vierstellige Zahl, teilweise mit einem Buchstabenprefix z.B. '1234' oder 'AR1234' (Zürich)")


FIELD_PROP_TAX_VALUE_GROSS = SemanticField(
    "tax_value_gross",
    ExtractionType.STRING,
    FieldTitles("Steuerwert brutto", "Tax Value gross"),
    desc_de="Steuerwert des Grundstücks vor Abzügen/Abschlägen",
)
FIELD_PROP_TAX_VALUE_NET = SemanticField(
    "tax_value_net",
    ExtractionType.STRING,
    FieldTitles("Steuerwert netto", "Tax Value net"),
    desc_de="Steuerwert des Grundstücks nach Abzügen/Abschlägen",
)

FIELD_PROP_IMPUTED_RENTAL_VALUE_GROSS = SemanticField(
    "imputed_rental_value_gross",
    ExtractionType.STRING,
    FieldTitles("Eigenmietwert brutto", "Tax Rent gross"),
    desc_de="Eigenmietwert des Grundstücks vor Abzügen/Abschlägen",
)
FIELD_PROP_IMPUTED_RENTAL_VALUE_NET = SemanticField(
    "imputed_rental_value_net",
    ExtractionType.STRING,
    FieldTitles("Eigenmietwert netto", "Tax Rent net"),
    desc_de="Eigenmietwert des Grundstücks nach Abzügen/Abschlägen",
)


FIELD_RECIPIENT_NAME = SemanticField(
    "recipient_name",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Empfänger", "Recipient"),
    desc_de="Name des Empfängers",
)
FIELD_RECIPIENT_ADDRESS = SemanticField(
    "recipient_address",
    extraction_type=ExtractionType.STRING,
    titles=FieldTitles("Empfänger Adresse", "Recipient Address"),
    desc_de="Adresse des Empfängers (Strasse, Hausnummer, PLZ, Ortz)",
)


new_fields = fields_as_dict(
    [
        FIELD_DOCUMENT_VALIDITY_START_DATE,
        FIELD_CANTON_SHORT,
        FIELD_PROP_ADDRESS,
        FIELD_PROP_ADDRESS_STREET,
        FIELD_PROP_ADDRESS_STREET_NO,
        FIELD_PROP_ADDRESS_CITY,
        FIELD_PROP_ADDRESS_ZIP,
        FIELD_PROP_PROPERTY_DESC,
        FIELD_PROP_VALUE_RATIO,
        FIELD_PROP_OWNER_FULLNAME,
        FIELD_PROP_OWNER_ADDRESS,
        PI_FIELD_YEAR_CONSTRUCTION,
        FIELD_PROP_CUBATURE,
        PI_FIELD_PLOT_SIZE,
        FIELD_PROPERTY_AREA_LIVING_GROSS,
        FIELD_PROP_CADASTER_NO,
        FIELD_LAND_REGISTER_ID,
        # FIELD_PROP_BUILDING_ID
        FIELD_PROP_ESTIMATION_DATE,
        FIELD_PROP_ESTIMATION_REASON,
        FIELD_PROP_NEW_VALUE,
        FIELD_PROP_TIME_VALUE,
        FIELD_PROP_TAX_VALUE_GROSS,
        FIELD_PROP_TAX_VALUE_NET,
        FIELD_PROP_IMPUTED_RENTAL_VALUE_GROSS,
        FIELD_PROP_IMPUTED_RENTAL_VALUE_NET,
    ]
)

FIELDS_PROPERTY_VALUATION_GOV = merge_dicts(FIELDS_PERSON_DATA, new_fields)


@dataclass
class PropertyValuationGovPageData(PersonPageData):
    document_validity_start_date: str = None
    canton_short: str = None  # name of canton, e.g. AG

    property_address: str = None
    property_address_street: str = None
    property_address_street_no: str = None
    property_address_city: str = None
    property_address_zip: str = None
    property_desc: str = None

    property_value_ratio: str = None

    owner_fullname: str = None
    owner_address: str = None

    year_construction: int = None
    cubature: int = None
    plot_size: int = None
    property_area_living_gross: int = None
    cadaster_no: str = None

    land_register_id: str = None

    property_estimation_date: str = None
    property_estimation_reason: str = None
    property_new_value: str = None
    property_time_value: str = None

    tax_value_gross: str = None
    tax_value_net: str = None

    imputed_rental_value_gross: str = None
    imputed_rental_value_net: str = None

    property_value_ratio: str = None

    def get_invalidity_reason(self):
        return None

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        d = super().get_semantic_fields()
        return {**d, **FIELDS_PROPERTY_VALUATION_GOV}


if __name__ == "__main__":
    print(FIELDS_PROPERTY_VALUATION_GOV)
