from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "356_unemployment_salary_certificate"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_one_doc_356():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["356_sample_alv.jpg"],
        page_number=1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNEMPLOYMENT_SALARY_CERTIFICATE: 1},
        ),
    ).run()
