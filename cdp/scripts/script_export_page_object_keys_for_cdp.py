import sys
from pathlib import Path

# Add project root to sys.path to allow imports from cdp and other packages.
# The script is in cdp/scripts, so we go up 2 levels to the project root.
# project_root = Path(__file__).resolve().parents[2]
# if str(project_root) not in sys.path:
#     sys.path.insert(0, str(project_root))

from cdp.export_page_object_keys_for_cdp import export_page_object_keys_for_cdp

if __name__ == "__main__":
    export_page_object_keys_for_cdp() 