from typing import Optional
from uuid import UUID, uuid4

from hdapii.processing.hyextract import BoundingBox
from hdapii.processing.hylayoutlm import ImagePredictRequest, ImagePredictResponse
from pydantic import HttpUrl

import global_settings
from asyncizer.rpc_pika import get_rpc_client

QUEUE_NAME_IMAGEPREDICT = "hylayoutlm.labeling.ImagePredictionRequest"


async def call_imagepredict_service_remote(
    page_uuid: UUID,
    image_url: HttpUrl,
    model_name: str,
    bbox: Optional[BoundingBox] = None,
) -> ImagePredictResponse:
    # Calls both LayoutLLM and microsoft OCR services
    rpc_client = await get_rpc_client()
    request_uuid = uuid4()

    used_bbox = bbox if global_settings.ENABLE_BOUNDED_BOX_FOR_EXTRACTION else None

    result = await rpc_client.call(
        QUEUE_NAME_IMAGEPREDICT,
        ImagePredictRequest(
            request_uuid=request_uuid,
            image_url=image_url,
            model_key=model_name,
            bbox=used_bbox,
        ).json(),
    )
    response: ImagePredictResponse = ImagePredictResponse.parse_raw(result)
    return response
