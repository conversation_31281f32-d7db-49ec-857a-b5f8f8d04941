from abbyyplumber.converter.ValueConverter import CurrencyConverter
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementArea,
    SearchElementConstrainedArea,
    SearchElementMultiStaticText,
    create_labeled_field_vertical,
    create_labeled_field,
)
from abbyyplumber.util.plumberstudio_util import RANGE_RIGHT, PercentageRange
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import P1_FULLNAME
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_FIRSTNAME
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
    FromBottomTextCond,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ai.ai_tax_declaration_fields_map import (
    tax_ai_codes_income,
    tax_ai_codes_deductions,
    tax_ai_codes_assets,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    COLUMN_CODE,
    COLUMN_VALUE,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.template_tax_declaration_util import (
    TemplateTaxDeclarationPageParser,
    TaxDeclarationSearchElements,
)

TAX_DECLARATION_AI_PERSONAL_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Personal 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA,
    ranked_titles_all=[
        RankedTitle("Steuererklärung"),
        RankedTitle("Appenzell I.Rh."),
        RankedTitle("Staats-, Bezirks- und Gemeindesteuern"),
    ],
    required_text_conditions=[
        FromStartTextCond("9050 Appenzell", num_lines=8),
        FromStartTextCond("Direkte Bundessteuer", num_lines=8),
        FromStartTextCond("Marktgasse", num_lines=10),
    ],
    page_main=SearchElementConstrainedArea(
        None,
        None,
        text_top="Steuererklärung",
        text_left="Steuererklärung",
        text_bottom="Veranlagungsprotokoll",
        text_right="Steuererklärung",
    ).include_all(-1, -45, 20, 0),
    regions=[
        SearchElementConstrainedArea(
            "region_header",
            None,
            text_left="Steuererklärung",
            text_bottom="Direkte Bundessteuer",
        ).include_bottom(),
        SearchElementConstrainedArea(
            FIELD_FIRSTNAME.sr_inside,
            None,
            text_top="Direkte Bundessteuer",
            texts_bottom=["Rückfragen an", "Vorname"],
            x_range=RANGE_RIGHT,
        ).include_vertical(3, -3),
        SearchElementConstrainedArea(
            "region_table",
            None,
            text_top="Personalien, Berufs- und",
            text_bottom="deren Unterhalt Sie",
        ),
        SearchElementConstrainedArea(
            "region_table_p1",
            None,
            target_name="region_table",
            texts_right=["erwerbstätig", "(Person 1)"],
        ).include_right(0),
        SearchElementConstrainedArea(
            "region_table_p2_value",
            None,
            target_name="region_table",
            text_top="Ehefrau",
        ).rightof("region_table_p1"),
        SearchElementConstrainedArea(
            "region_children",
            None,
            text_top="deren Unterhalt Sie",
            text_right="zur Hauptsache aufkommen",
            text_bottom="Rückzahlungen",
        ).exclude_right(-2),
    ],
    se=TaxDeclarationSearchElements(
        year=SearchElementArea(None, None, target_name="region_header"),
        p1_fullname=SearchElementConstrainedArea(
            P1_FULLNAME.name, None, target_name=FIELD_FIRSTNAME.sr_inside
        ),
        p2_firstname=create_labeled_field(
            "Name, Vorname",
            "region_table_p2_value",
            target_name_label="region_table_p1",
        ),
        p1_date_of_birth=create_labeled_field("Geburtsdatum", "region_table_p1"),
        p2_date_of_birth=create_labeled_field("Geburtsdatum", "region_table_p2_value"),
        p1_marital_status=create_labeled_field("Zivilstand", "region_table_p1"),
        p2_marital_status=create_labeled_field("Zivilstand", "region_table_p2_value"),
        p1_profession=create_labeled_field("Beruf", "region_table_p1"),
        p2_profession=create_labeled_field("Beruf", "region_table_p2_value"),
        section_children=SearchElementConstrainedArea(
            None, None, target_name="region_children", text_top="Vorname, Name"
        ),
    ),
)

TAX_DECLARATION_AI_INCOME_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Income 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_INCOME,
    ranked_titles_all=[
        RankedTitle("Einkünfte im In- und Ausland", rank=2, min_length_title=10)
    ],
    required_text_conditions=[
        FromStartTextCond(
            "der Steuerpflichtigen und der minderjährigen Kinder", num_lines=5
        ),
        FromStartTextCond("ohne Erwerbseinkommen dieser Kinder", num_lines=7),
        FromStartTextCond("Direkte Bundessteuer", num_lines=10),
        FromStartTextCond("Es wird auf die beiliegende Wegleitung", num_lines=10),
    ],
    required_tokens=["3.5 Direkt ausbezahlte Kinder"],
    regions=[
        SearchElementMultiStaticText(
            "token_code_left", None, labels={"Bescheinigung": 4}
        ),
        create_labeled_field_vertical(
            "Einkünfte 202",
            offset_top=3,
            offset_left=-8,
            offset_right=-13,
            offset_bottom=1000,
            name=COLUMN_CODE,
        ).rightof("token_code_left"),
        create_labeled_field_vertical(
            "Einkünfte 202",
            name=COLUMN_VALUE,
            offset_left=-10,
            offset_right=5,
            offset_bottom=1000,
        ).rightof(COLUMN_CODE),
    ],
    tax_code_map=tax_ai_codes_income,
)

TAX_DECLARATION_AI_DEDUCTIONS_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Deductions 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_DEDUCTIONS,
    ranked_titles_all=[
        RankedTitle("Abzüge", rank=2, min_length_title=5),
        RankedTitle("Einkommens", rank=2, min_length_title=8),
    ],
    required_text_conditions=[
        FromStartTextCond("Abzüge 202", num_lines=3),
        FromBottomTextCond("Bruttolohn, vereinfacht abgerechnet", num_lines=5),
    ],
    required_tokens=["Erhebliche Mitarbeit"],
    regions=[
        SearchElementMultiStaticText(
            "token_code_left", None, labels={"Bescheinigung": 4}
        ),
        create_labeled_field_vertical(
            "Abzüge 202",
            offset_top=3,
            offset_left=-8,
            offset_right=-13,
            offset_bottom=1000,
            name=COLUMN_CODE,
        ).rightof("token_code_left"),
        create_labeled_field_vertical(
            "Abzüge 202",
            name=COLUMN_VALUE,
            offset_left=-10,
            offset_right=5,
            offset_bottom=1000,
        ).rightof(COLUMN_CODE),
    ],
    tax_code_map=tax_ai_codes_deductions,
)

TAX_DECLARATION_AI_ASSETS_2021 = TemplateTaxDeclarationPageParser(
    # This page is identical to SG Assets page except for the codes
    desc="Tax AI Assets 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_ASSETS,
    required_text_conditions=[
        FromStartTextCond("Vermögen im In- und Ausland", num_lines=3),
        FromStartTextCond("Steuerwert", num_lines=5),
        FromStartTextCond(
            "der Steuerpflichtigen und der minderjährigen Kinder", num_lines=6
        ),
        FromStartTextCond("einschliesslich Nutzniessungsvermögen", num_lines=7),
    ],
    required_tokens=[
        "wahrheitsgetreu ausgefüllt",
        "32.1",
        "3140",
        "34.1",
        "3230",
        "38.",
        "3400",
        "40.3",
        "3000",
        "3020",
    ],
    regions=[
        SearchElementMultiStaticText(
            "token_code_left", None, labels={"Bescheinigung": 4}
        ),
        create_labeled_field_vertical(
            "Steuerwert",
            offset_top=3,
            offset_left=-9,
            offset_right=-13,
            offset_bottom=1000,
            name=COLUMN_CODE,
        ).rightof("token_code_left"),
        create_labeled_field_vertical(
            "Steuerwert",
            name=COLUMN_VALUE,
            offset_left=-12,
            offset_right=7,
            offset_bottom=1000,
        ).rightof(COLUMN_CODE),
    ],
    tax_code_map=tax_ai_codes_assets,
)

TAX_DECLARATION_AI_DEBTS_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Debts 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_DEBT_INVENTORY,
    required_text_conditions=[
        FromStartTextCond("Schuldenverzeichnis", num_lines=3),
        FromStartTextCond("Freiwillige Zuwendungen", num_lines=5),
        FromStartTextCond("Appenzell I.Rh.", num_lines=7),
        FromStartTextCond("Die Belege sind auf", num_lines=8),
        FromStartTextCond("jeden Fall einzureichen.", num_lines=9),
        FromBottomTextCond("Steuererklärung", num_lines=5),
    ],
    se=TaxDeclarationSearchElements(
        debt_total=create_labeled_field(
            "Total private Schulden und private Schuldzinsen",
            converter=CurrencyConverter(),
            field_pos_page_horizontal=PercentageRange(0.6, 0.8),
        ),
        interest_paid_on_debt=create_labeled_field(
            "Total private Schulden und private Schuldzinsen",
            converter=CurrencyConverter(),
            field_pos_page_horizontal=PercentageRange(0.82, 1),
        ),
    ),
)

TAX_DECLARATION_AI_ACCOUNTS_FRONT_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Accounts Front 2021",
    canton="AI",
    page_cat=PageCat.TAX_DECLARATION_ACCOUNTS_FORM_FRONT,
    required_text_conditions=[
        FromStartTextCond("Wertschriften- und", num_lines=3),
        FromStartTextCond("Guthabenverzeichnis", num_lines=5),
        FromStartTextCond("Rückerstattungsantrag Verrechnungssteuer", num_lines=6),
        FromStartTextCond("Appenzell I.Rh.", num_lines=8),
        FromStartTextCond("PID-Nr.", num_lines=10),
    ],
)

TAX_DECLARATION_AI_ACCOUNTS_FORM1_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Accounts Form1 2021",
    canton="AI",
    page_cat=PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS,
    required_text_conditions=[
        FromStartTextCond("Wertschriften- und Guthabenverzeichnis", num_lines=3),
        FromStartTextCond("Rückerstattungsantrag Verrechnungssteuer", num_lines=7),
        FromStartTextCond("Appenzell I.Rh.", num_lines=9),
        FromStartTextCond("Bezeichnung der Vermögenswerte", num_lines=11),
    ],
)

TAX_DECLARATION_AI_ACCOUNTS_FORM2_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Accounts Form2 2021",
    canton="AI",
    page_cat=PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS,
    required_text_conditions=[
        FromBottomTextCond("www.ai.ch/steuern", num_lines=10),
        FromBottomTextCond("Seite 2 Ziffer 4.1", num_lines=10),
        FromBottomTextCond("Verrechnungssteueranspruch", num_lines=9),
        # required to distinguish it from AR
        FromBottomTextCond("Visum", num_lines=3),
    ],
)

TAX_DECLARATION_AI_OCC_EXP_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI occup. exp. 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_MISC,
    required_text_conditions=[
        FromStartTextCond("Berufskosten Person", num_lines=3),
        FromStartTextCond("Formular", num_lines=3),
        FromStartTextCond("Appenzell I.Rh.", num_lines=7),
        FromStartTextCond("Jahrespauschalen", num_lines=7),
        FromBottomTextCond("Steuererklärung", num_lines=5),
    ],
)

TAX_DECLARATION_AI_OTHER_FORMS_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI other forms 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_MISC,
    required_text_conditions=[
        FromStartTextCond(
            [
                "Schuldenverzeichnis",
                "Freiwillige Zuwendungen",
                "Versicherungsprämien",
                "Krankheits-, Unfall- und",
                "Liegenschaften",
            ],
            num_lines=3,
        ),
        FromStartTextCond("Formular", num_lines=3),
        FromStartTextCond("Appenzell I.Rh.", num_lines=7),
        FromStartTextCond("Die Belege sind auf", num_lines=8),
        FromStartTextCond(
            ["Verlangen einzureichen", "jeden Fall einzureichen."], num_lines=9
        ),
        FromBottomTextCond("Steuererklärung", num_lines=5),
    ],
)


TAX_DECLARATION_AI_ADMIN_COSTS_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI admin costs 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_MISC,
    required_text_conditions=[
        FromStartTextCond(
            [
                "Unterhalts- und Verwaltungskosten",
                "Rückbaukosten im Hinblick",
                "Zusammenzug aller Liegenschaften",
            ],
            num_lines=3,
        ),
        FromStartTextCond("Appenzell I.Rh.", num_lines=9),
        FromBottomTextCond("Steuererklärung", num_lines=7),
    ],
)

#
# TAX_DECLARATION_AI_GIFTS_2021 = TemplateTaxDeclarationPageParser(
#     desc="Tax AI Gifts 2021",
#     canton="AI",
#     doc_cat=DocumentCat.TAX_DECLARATION,
#     page_cat=PageCat.TAX_DECLARATION_MISC,
#     ranked_titles_all=[
#         RankedTitle("Freiwillige Zuwendungen", min_length_title=10)
#     ],
#     required_tokens=["Appenzell I.Rh."],
# )
#
# TAX_DECLARATION_AI_INS_PREM_2021 = TemplateTaxDeclarationPageParser(
#     desc="Tax AI Ins Prem 2021",
#     canton="AI",
#     doc_cat=DocumentCat.TAX_DECLARATION,
#     page_cat=PageCat.TAX_DECLARATION_INSURANCE_PREMIUMS,
#     ranked_titles_all=[
#         RankedTitle("Versicherungsprämien", min_length_title=10)
#     ],
#     required_tokens=["Appenzell I.Rh.", "Maximaler Abzug für Versicherungsprämien"],
# )
#
# TAX_DECLARATION_AI_HEALTH_2021 = TemplateTaxDeclarationPageParser(
#     desc="Tax AI Health Costs 2021",
#     canton="AI",
#     doc_cat=DocumentCat.TAX_DECLARATION,
#     page_cat=PageCat.TAX_DECLARATION_DETAILS_HEALTH_COSTS,
#     ranked_titles_all=[
#         RankedTitle("behinderungsbedingte Kosten", min_length_title=10)
#     ],
#     required_tokens=["Appenzell I.Rh.", "Name und Adresse des Arztes"],
# )
#
# TAX_DECLARATION_AI_PROP_OVERVIEW_2021 = TemplateTaxDeclarationPageParser(
#     desc="Tax AI Properties Overview 2021",
#     canton="AI",
#     doc_cat=DocumentCat.TAX_DECLARATION,
#     page_cat=PageCat.TAX_DECLARATION_DETAILS_PROPERTIES,
#     ranked_titles_all=[
#         RankedTitle("Liegenschaften", min_length_title=10)
#     ],
#     required_tokens=["Appenzell I.Rh."],
# )
#
# TAX_DECLARATION_AI_PROP_DETAILS_2021 = TemplateTaxDeclarationPageParser(
#     desc="Tax AI Properties Details and Form 2021",
#     canton="AI",
#     doc_cat=DocumentCat.TAX_DECLARATION,
#     page_cat=PageCat.TAX_DECLARATION_DETAILS_PROPERTIES,
#     ranked_titles_all=[
#         RankedTitle("Unterhalts- und Verwaltungskosten", min_length_title=10)
#     ],
#     required_tokens=["Appenzell I.Rh."],
# )

TAX_DECLARATION_AI_SUMMARY_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax AI Summary w/ Signature 2021",
    canton="AI",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_SUMMARY_PAGE_WITH_SIGNATURE,
    required_text_conditions=[
        FromStartTextCond("Datenblatt", num_lines=3),
        FromStartTextCond("Bitte der Steuerklärung beilegen", num_lines=5),
        FromStartTextCond("Appenzell I.Rh.", num_lines=7),
    ],
)


def get_parsers_tax_declaration_ai():
    return [
        TAX_DECLARATION_AI_PERSONAL_2021,
        TAX_DECLARATION_AI_INCOME_2021,
        TAX_DECLARATION_AI_DEDUCTIONS_2021,
        TAX_DECLARATION_AI_ASSETS_2021,
        TAX_DECLARATION_AI_DEBTS_2021,
        TAX_DECLARATION_AI_ACCOUNTS_FRONT_2021,
        TAX_DECLARATION_AI_ACCOUNTS_FORM1_2021,
        TAX_DECLARATION_AI_ACCOUNTS_FORM2_2021,
        TAX_DECLARATION_AI_OCC_EXP_2021,
        TAX_DECLARATION_AI_OTHER_FORMS_2021,
        TAX_DECLARATION_AI_ADMIN_COSTS_2021,
        # TAX_DECLARATION_AI_GIFTS_2021,
        # TAX_DECLARATION_AI_INS_PREM_2021,
        # TAX_DECLARATION_AI_HEALTH_2021,
        # TAX_DECLARATION_AI_PROP_OVERVIEW_2021,
        # TAX_DECLARATION_AI_PROP_DETAILS_2021,
        TAX_DECLARATION_AI_SUMMARY_2021,
    ]
