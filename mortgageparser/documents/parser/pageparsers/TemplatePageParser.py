import copy
from dataclasses import dataclass, field
from typing import List, Dict, Iterable

from abbyyplumber.api import find_characters_as_search_result, COLOR_PINK
from abbyyplumber.converter.ValueConverter import MostRecentDateConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElement,
    SearchElementArea,
    SearchElementLabeledField,
    SearchElementConstant,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationAbove,
    SearchRelationBelow,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_FIRSTNAME,
    FIELD_DOCUMENT_DATE,
    FIELD_DOCUMENT_TITLE,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitlePageParser,
)
from mortgageparser.util.search_element_util import (
    create_search_elements_address,
    create_field_document_date,
)


def default_field(obj):
    return field(default_factory=lambda: copy.copy(obj))


@dataclass
class TemplatePageParser(RankedTitlePageParser):
    page_main: SearchElement = None

    regions: List[SearchElement] = default_field([])

    # Flag for debugging. If this is false then the regions are extracted but not the other (generic) search elements
    use_se: bool = True

    # Flag for debugging. Only these names are extracted from the se
    use_se_names: List[str] = None

    document_date_label: str = None

    # Constant public document title can be assigned here. This overrides any dynamic extraction of the
    # document title e.g. for GenericLetter
    # Can be used in setting the title in FileCreator
    document_title: str = None

    # If this is True, then just search on the full page
    use_document_date: bool = False

    se: List[SearchElement] = default_field([])

    # This needs no override if no content is extracted
    def parse_page_header(self):
        if self.page_main:
            self.page.fullpage = find_characters_as_search_result(
                self.page.chars,
                self.page.bbox_text,
                name="Page Full",
                color=COLOR_PINK,
                extract=False,
            )

            self.page_main.name = "Page Main"
            self.page_main.extract = False
            self.page_main.target = self.page.fullpage
            sr = self.page_main.find(self.page)
            if sr:
                self.page.main = sr
                self.page.header = SearchElementArea(
                    "Page Header",
                    self.page.fullpage,
                    relations=[SearchRelationAbove("Page Main")],
                    extract=False,
                ).find(self.page)
                self.page.footer = SearchElementArea(
                    "Page Footer",
                    self.page.fullpage,
                    relations=[SearchRelationBelow("Page Main")],
                    extract=False,
                ).find(self.page)
                return True
            else:
                self.page.main = self.page.fullpage
                return True
                # raise Exception(f"Could not find main region for desc={self.desc}, se={self.page_main}")

        else:
            return super().parse_page_header()

    def parse_page_footer(self):
        if not self.page.footer:
            return super().parse_page_footer()
        return True

    def update_search_elements(self):
        if self.se:
            names = []
            se = {}
            if not isinstance(self.se, Iterable):
                raise Exception(
                    "se must be an iterable for TemplatePageParser. For custom types, subclasses of TemplatePageParser should override method update_search_elements"
                )
            for x in self.se:
                names.append(x.name)
                se[x.name] = x
            self.update_search_elements_generic(se, names)

    def create_content_extractor(self) -> ContentExtractor:
        self.update_search_elements()
        return super().create_content_extractor()

    def update_search_elements_generic(
        self, search_elements: Dict[str, SearchElement], field_names
    ):
        if self.debug_breakpoint:
            if 1 == 1:
                pass

        """
        Loop over all valid fields in this data type and prepare the search element for execution:
          - set target
          - add converter if not already set
        :return:
        """
        self.search_elements = []

        use_address = False

        if self.regions:
            for se in self.regions:
                se.target = self.page.fullpage
                se.extract = False
                self.search_elements.append(se)
                if se.name == FIELD_FIRSTNAME.sr_inside:
                    use_address = True
        if use_address:
            self.search_elements += create_search_elements_address(
                self.page.fullpage, self.page
            )

        if self.document_date_label:
            if isinstance(self.document_date_label, str):
                self.search_elements.append(
                    SearchElementLabeledField(
                        FIELD_DOCUMENT_DATE.name,
                        self.page.fullpage,
                        label=self.document_date_label,
                        converter=MostRecentDateConverter(),
                    )
                )
            else:
                raise Exception(
                    f"Invalid type for document_date_label. Must be str but is {type(self.document_date_label)} for self={self}"
                )
        elif self.use_document_date:
            self.search_elements.append(
                create_field_document_date(None, self.page.fullpage)
            )

        if self.document_title:
            self.search_elements.append(
                SearchElementConstant(FIELD_DOCUMENT_TITLE.name, self.document_title)
            )

        # Now apply default converters to self.search_elements
        # Also set default target page_main or page.fullpage
        if search_elements and self.use_se:
            for name in field_names:
                if not self.use_se_names or name in self.use_se_names:
                    se = None
                    if isinstance(search_elements, Dict):
                        if name in search_elements:
                            se = search_elements[name]
                    elif name in search_elements.__dict__:
                        se = search_elements.__dict__[name]

                    if se:
                        if not se.target:
                            se.name = name

                            # If a main region has been explicitly set then by default extract from there else from full page
                            if self.page_main:
                                se.target = self.page.main
                            else:
                                se.target = self.page.fullpage

                        if hasattr(self.se, "default_converters"):
                            if name in self.se.default_converters:
                                if not se.converter:
                                    se.converter = self.se.default_converters[name]

                        self.search_elements.append(se)
