import subprocess
from datetime import datetime
from pathlib import Path
from typing import Tuple, Union, Optional

from hypodossier.util.memory_utils import get_mem_stats

import structlog

logger = structlog.getLogger(__name__)


def im_fix_image(
    input_path: Path, output_path: Path, timeout: Union[float, None] = None
) -> Tuple[bool, Optional[Path]]:
    """
    Run an image file through Imagemagick to fix problems. Extension of input and output are normally the same
    but could be different.
    convert input.png output.png
    """
    params = ["convert", str(input_path), str(output_path)]

    success, retcode, res = execute_im_command_as_subprocess(params, timeout=timeout)
    if success:
        return True, output_path
    return False, None


def execute_im_command_as_subprocess(
    params, timeout: Union[float, None] = None
) -> Tuple[bool, int, subprocess.CompletedProcess]:
    ts_start = datetime.now()
    res = subprocess.run(params, capture_output=True, text=True, timeout=timeout)
    ts_end = datetime.now()
    duration = ts_end - ts_start

    if res.returncode == 0:
        # Success
        pass
    elif res.returncode == 3:
        logger.warning(f"Warning during execution of Imagemagick: {params}")
        logger.warning(f"stderr: {res.stderr}")
    else:
        logger.error(f"Error during execution of Imagemagick: {params}")
        logger.error(f"stderr: {res.stderr}")
    text, size_mb = get_mem_stats()
    logger.info(f"Imagemagick execution took {duration} seconds. {text}")
    success = res.returncode == 0
    return success, res.returncode, res
