from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

# Recheck 16 abrechnung kosten liegenschaft

dest_folder_prefix = None  # 'test_bekb_v3_',


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_001(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/001_32625181"
        ),
        # source_file_filter = ['Verurkundeter Kaufvertrag.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_001_gba(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/001_32625181"
        ),
        source_file_filter=["Grundbuchauszug"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_002(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/002_15159304"
        ),
        # source_file_filter=['Cessions immobilières - PROJET II - Envoyé le 26.05.2021.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.CONTRACT_OF_SALE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_002_2(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/002_15159304"
        ),
        source_file_filter=["B-F-Tramelan-446-1687.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 1,
                DocumentCat.PLAN_CADASTER: 1,
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=0,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_003(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/003_20705067"
        ),
        # source_file_filter=['Eingang 1. Akontozahlung.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_004(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/004_29160848"
        ),
        # source_file_filter=['Steuererklärung 2020.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_005(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/005_29902586"
        ),
        # source_file_filter=['Bonus 2019.pdf'],
        # source_file_filter=['Offre Sanitas Trösch.pdf'],
        source_file_filter=["SwissLife.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_005_bonus_2(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/005_29902586"
        ),
        # source_file_filter=['Bonus 2019.pdf'],
        # source_file_filter=['Offre Sanitas Trösch.pdf'],
        source_file_filter=["2. Bonus 2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_BONUS: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_006(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/006_32184667"
        ),
        # source_file_filter=['Bonus 2019.pdf'],
        # source_file_filter=['Offre Sanitas Trösch.pdf'],
        # source_file_filter=['Finanzierung Einfamilienhaus Familie Friederich.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_007(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/007_27410474"
        ),
        # source_file_filter=['Hypotheken'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.STATEMENT_OF_ASSETS: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_007_sal_cert(
    override_existing=True,
    show_page=-2,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/007_27410474"
        ),
        source_file_filter=["Lohnausweise 2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.SALARY_CERTIFICATE: 5,
                DocumentCat.SALARY_CONFIRMATION_FORM: 1,
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=6,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_007_pension_cert(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/007_27410474"
        ),
        source_file_filter=["PK_Sandra_31.12.2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_008_contract(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/008_22062544"
        ),
        source_file_filter=["Kaufvertragsentwurf.doc"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.CONTRACT_OF_SALE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_009(
    override_existing=True,
    show_page=0,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/009_23449571"
        ),
        # source_file_filter=['GVB-Auszug Spiez, Angernstrasse 56.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1,
            # expected_num_processing_exceptions=0,
            # expected_num_docs=0
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_010_divor(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/010_32344479"
        ),
        source_file_filter=["Unterhaltsbeiträge.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_010_mortgage(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/010_32344479"
        ),
        source_file_filter=["Darlehensvertrag.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.MORTGAGE_CONTRACT: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_011(
    override_existing=True,
    show_page=-2,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/011_32633720"
        ),
        # source_file_filter=['Assurance bâtiment.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_INSURANCE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_011_tax(
    override_existing=True,
    show_page=-2,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/011_32633720"
        ),
        source_file_filter=["Taxation 2019.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_ASSESSMENT: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_013_donation(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/013_29810792"
        ),
        source_file_filter=["Attestation donation.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION3A_ACCOUNT: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_014_tax(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/014_8811725"
        ),
        source_file_filter=["Steuererklärung 2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_015(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/015_21157715"
        ),
        source_file_filter=["3a Konto3.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION3A_ACCOUNT: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_015_pk(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/015_21157715"
        ),
        source_file_filter=["PK Ausweis ER.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_015_tax(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/015_21157715"
        ),
        source_file_filter=["Déclaration d'impôts 2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_016(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/016_1719768"
        ),
        source_file_filter=["Buchhaltung und Bilanz von Stockwerkeigentum 2018.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_ACCOUNTS: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_016_grundriss(
    override_existing=True,
    show_page=0,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/016_1719768"
        ),
        source_file_filter=["Architekturpläne.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_018_value(
    override_existing=True,
    show_page=1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/018_27193485"
        ),
        source_file_filter=["IAZI Verkehrswertschatzung 07.02.20 gut.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_VALUATION: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_018_tax_misc(
    override_existing=True,
    show_page=0,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/018_27193485"
        ),
        source_file_filter=["dvb_print_20210615-132017.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_MISC: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_020_pension(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/020_32184339"
        ),
        source_file_filter=["Certificat LPP 2021 M.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_020_prop(
    override_existing=True,
    show_page=4,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/020_32184339"
        ),
        source_file_filter=["Comptes de la PPE 2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_ACCOUNTS: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_020_padea(
    override_existing=True,
    show_page=0,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/020_32184339"
        ),
        source_file_filter=["Fiche client privé.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.BROKER_MISC: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_020(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/020_32184339"
        ),
        source_file_filter=["Certificat travail M.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_021(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/021_32553560"
        ),
        source_file_filter=["Immobilienbewertung SchopfScan_20210426_134151.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_VALUATION: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_021_gift(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/021_32553560"
        ),
        source_file_filter=["Schenkungsvertrag.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_022(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/022_32643608"
        ),
        source_file_filter=["Lohnausweis Manuela Esteban.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_024(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/024_31885451"
        ),
        source_file_filter=["Steuererklärung 2019.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_025(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/025_32801972"
        ),
        source_file_filter=["property_358475_xr4b77_Land-Kaufvertrag_Reservation.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.CONTRACT_OF_SALE: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_025_tax(
    override_existing=True,
    show_page=12,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/025_32801972"
        ),
        source_file_filter=["customer_1_358475_xr4b77_Steuererklaerung.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_026(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/026_28327392"
        ),
        source_file_filter=None,  # ['property_358475_xr4b77_Land-Kaufvertrag_Reservation.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 1,
                DocumentCat.PAYSLIP: 1,
                DocumentCat.PENSION_CERTIFICATE: 1,
                DocumentCat.PROPERTY_INSURANCE: 3,
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.UNKNOWN: 0,
                DocumentCat.UNKNOWN_DE: 4,
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=1,  # Tax declaration is corrupt
            expected_num_docs=11,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_026_single_unknown(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/026_28327392"
        ),
        source_file_filter=["spiegellohnabr20210528_130054.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={
                DocumentCat.PAYSLIP: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,  # Tax declaration is corrupt
            expected_num_docs=2,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_027(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/027_26080544"
        ),
        source_file_filter=None,  # ['property_358475_xr4b77_Land-Kaufvertrag_Reservation.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={},
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-1,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_028(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/028_32759195"
        ),
        # source_file_filter=['Scan_20210707_124200.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_doc_cat_frequency={
                DocumentCat.BASE_CONTRACT: 1,
                DocumentCat.ID: 1,
                DocumentCat.PAYSLIP: 1,
                DocumentCat.PENSION_CERTIFICATE: 1,
                DocumentCat.PENSION_PAYMENT_AHV: 1,
                DocumentCat.PROPERTY_INSURANCE: 1,
                DocumentCat.SALARY_CERTIFICATE: 2,
                DocumentCat.TAX_ASSESSMENT: 1,
                DocumentCat.TAX_DECLARATION: 1,
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_029(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/029_30054788"
        ),
        # source_file_filter=['Öffentliche Urkunde Begründung STWE.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_030(
    override_existing=True,
    show_page=-4,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/030_32777763"
        ),
        # source_file_filter=['customer_1_349976_jjgd6c_Pensionskassenausweis.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_031(
    override_existing=True,
    show_page=-4,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/031_32525044"
        ),
        source_file_filter=["contrat de vente et documents notaire.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_032(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/032_32807230"
        ),
        # source_file_filter=['internal_357554_yp29vh_Auftrag Finanzierungsabw bekb.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_033(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/033_28652953"
        ),
        # source_file_filter=['internal_357554_yp29vh_Auftrag Finanzierungsabw bekb.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_036(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/036_32757739"
        ),
        # source_file_filter=['Darlehensvertrag.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_037(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/037_32754635"
        ),
        # source_file_filter=['PK-Ausweis 2021 Thomas Wenger.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_038(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_1/038_32594325"
        ),
        # source_file_filter=['Kopie Begründung von Stockwerkeigentum.pdf'],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )
