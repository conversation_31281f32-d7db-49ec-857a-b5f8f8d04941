from pathlib import Path

import pytest

from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/pdf_fix"
path_root_folder = Path(folder)
dest_folder_prefix = "input_system_components_pdf_fix_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_ps_fixable():
    await DossierTest(
        use_ocr_cache=False,  # must be false to avoid ocr cache
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "gs_fixable_pdf",
        source_file_filter=["gs_fixable_pdf.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1},
            expected_doc_cat_frequency_per_page={
                # Dummy template doc, classification does not need to work properly
                DocumentCat.CIVIL_STATUS_DOCUMENT: 1,
                DocumentCat.UNKNOWN: 1,
            },
        ),
    ).run()


@pytest.mark.skip("needs abbyy timeout")
@pytest.mark.asyncio
async def test_pdftocairo_fixable_pdf(monkeypatch):
    """
    This test tries to do ocr on a file that abbyy has problems with. This
    runs into an ABBYY timeout, then the file gets processed with pdftocairo.
    The fixed file is tried again. This works but only if you have > 1 frep
    because the first frep is stuck with the inital file until the timeout
    occurs on the frep side.

    TODO: implement strict timeout on the frep side
    which can be configured by the caller.

    :param monkeypatch:
    :return:
    """
    # Wait only for 60 seconds.
    # First try with original filewill timeout due to broken file
    # Second try is with pdftocairo fixed file, should work in < 30 seconds
    monkeypatch.setattr(
        "asyncizer.dossier_processor.global_settings.OCR_TIMEOUT_SECONDS", 60
    )
    await DossierTest(
        use_ocr_cache=False,  # must be false to avoid ocr cache
        processing_config=OriginalFileProcessingConfig(
            # OCR fails due to content. No need for retry
            ocr_max_num_tries=1
        ),
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "pdftocairo_fixable_pdf",
        source_file_filter=["pdftocairo_fixable_pdf.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_ANY: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_ANY: 1},
        ),
    ).run()
