from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

# Recheck 16 abrechnung kosten liegenschaft

dest_folder_prefix = None  # 'test_bekb_v3_',


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_041(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/041_32800472"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_044(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/044_30368713"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_048(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/048_31276046"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_049(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/049_32761868"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_052(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/052_32746527"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_054(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/054_28509244"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_055(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/055_32812508"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_057(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/057_32793267"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_058(
    override_existing=True,
    show_page=-10,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/058_9474309"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_059(
    override_existing=True,
    show_page=-10,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/059_32634320"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_061(
    override_existing=True,
    show_page=-10,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/061_10153459"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_062(
    override_existing=True,
    show_page=-3,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/062_13592937"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_063(
    override_existing=True,
    show_page=0,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/063_32817139"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_064(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/064_31633801"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_066(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/066_32794978"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_067(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/067_1653817"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_072(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/072_29995888"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_073(
    override_existing=True,
    show_page=1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/073_13474113"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_074(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/074_31904295"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_077(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/077_6012342"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_bekb_batches_080(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/dossiers_bekb/Projekt HypoDossier_2/080_29347288"
        ),
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    #   'canton_short': 'ZH',
                }
            },
            expected_num_extracted_file_exceptions=0,
            expected_num_processing_exceptions=0,
            expected_num_docs=-10,
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )
