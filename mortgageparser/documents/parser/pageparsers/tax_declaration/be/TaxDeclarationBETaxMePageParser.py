from abbyyplumber.converter.ValueConverter import (
    CleanNameConverter,
    ValidNumberConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementMultiLabeledField,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FULLNAME,
    P1_MARITAL_STATUS,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_PERSON_ID
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.be.tax_declaration_be_fields_map import (
    tax_declaration_be_fields_map_tax_me,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    COLUMN_CODE,
    COLUMN_VALUE,
    create_search_elements_by_tax_code,
)


class TaxDeclarationBETaxMePageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["de", "fr"]
        self.page_cat = PageCat.TAX_DECLARATION_MISC
        self.canton_short = "BE"

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page

        ce = ContentExtractor(
            [
                SearchElementConstrainedArea(
                    "target_header",
                    page.main,
                    texts_top=["Angaben zu den persönlichen", "Identité et situation"],
                    texts_bottom=["Ihre Angaben", "Revenus 20"],
                    extract=False,
                ),
                SearchElementMultiLabeledField(
                    P1_FULLNAME.name,
                    page.main,
                    target_name="target_header",
                    labels={"Vorname und Name": 4, "Prénom et Nom": 3},
                    converter=CleanNameConverter(),
                ),
                SearchElementMultiLabeledField(
                    FIELD_PERSON_ID.name,
                    page.main,
                    target_name="target_header",
                    labels={"ZPV-Nummer": 2, "Numéro GCP": 2},
                    converter=ValidNumberConverter(),
                ),
                SearchElementMultiLabeledField(
                    P1_MARITAL_STATUS.name,
                    page.main,
                    target_name="target_header",
                    labels={"Zivilstand": 2, "Etat-civil": 2},
                    converter=CleanNameConverter(),
                ),
                SearchElementConstrainedArea(
                    COLUMN_CODE,
                    page.main,
                    texts_top=["Konfession", "Confession"],
                    texts_left=["Einkünfte im", "Revenus 20"],
                    texts_right=["Ihre Angaben", "déclaration"],
                    extract=False,
                )
                .include_left()
                .below("target_header"),
                SearchElementConstrainedArea(
                    COLUMN_VALUE,
                    page.main,
                    texts_top=["Steuerbar", "Imposable"],
                    text_top_required=True,
                    texts_left=["Steuerbar", "Imposable"],
                    extract=False,
                ).include_left(),
            ]
        )

        search_elements = create_search_elements_by_tax_code(
            self.page,
            fields_map=tax_declaration_be_fields_map_tax_me,
            field_vertical_line_scale=4,
            column_value=COLUMN_VALUE,
        )
        ce.search_elements += search_elements
        return ce
