from dataclasses import dataclass
from typing import Optional, Union

from abbyyplumber.api import SearchResult
from abbyyplumber.plumberstudio.BoundingBox import BoundingBox
from hypodossier.core.documents.debtcollectioninformation.DebtCollectionPageData import (
    DebtCollectionStatus,
)
from hypodossier.core.domain.PageLocation import PageLocation
from hypodossier.core.domain.SemanticField import FieldTitles, ExtractionType
from hypodossier.shared import FinhurdlePageObjectValue
from hypodossier.util.language_detector import ALL_LANGUAGES


@dataclass
class Extraction:
    name: str
    # value: Any
    value: Optional[Union[str, int, FinhurdlePageObjectValue, DebtCollectionStatus]]

    type: ExtractionType
    search_result: SearchResult

    # Bounding box of the page that this extraction was found on
    # x0, y0 should always be (0,0), so x1, y1 contains width and height of page
    bbox_ref: BoundingBox

    page_source: PageLocation = None
    titles: FieldTitles = None

    def get_title(self, lang="de"):
        if self.titles and lang in ALL_LANGUAGES:
            return self.titles.__dict__[lang]
        return self.name

    def value_formatted(self, lang="de", allow_multiline=False, empty_value=""):
        if self.type:
            return self.type.format_value(
                self.value,
                lang=lang,
                allow_multiline=allow_multiline,
                empty_value=empty_value,
            )

    def value_serialized(self) -> str:
        if self.type and self.type == ExtractionType.FINHURDLE:
            val = FinhurdlePageObjectValue.to_json(self.value)
            return val
        else:
            return self.value

    @property
    def ref_width(self) -> int:
        return self.bbox_ref.width

    @property
    def ref_height(self) -> int:
        return self.bbox_ref.height
