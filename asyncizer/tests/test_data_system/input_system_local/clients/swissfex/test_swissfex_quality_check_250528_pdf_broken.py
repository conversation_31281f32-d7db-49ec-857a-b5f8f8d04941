from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250528_pdf_broken"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/swissfex/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_local_clients_swissfex_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_doc_with_broken_page0():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "doc_with_broken_page0",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "20.05.2025",
                    "document_validity_start_date": "20.05.2025",
                    "marital_status": "ledig",
                    "degree_employment": "20%",
                    "address_block": PO_EXISTS,
                    "employer": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 25'920",
                    "applicable_annual_salary_insured": "CHF 25'920",
                    "url_email_company": "www.AXA.ch",
                    "fullname": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "date_of_birth": "14.10.1978",
                    "sex": "m",
                    "company": "AXA",
                }
            },
            expected_doc_cat_frequency_per_page={DocumentCat.PENSION_CERTIFICATE: 2},
        ),
    ).run()


@pytest.mark.asyncio
async def test_broken_single_page():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "broken_single_page",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "20.05.2025",
                    "document_validity_start_date": "20.05.2025",
                    "marital_status": "ledig",
                    "degree_employment": "20%",
                    "address_block": PO_EXISTS,
                    "employer": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 25'920",
                    "applicable_annual_salary_insured": "CHF 25'920",
                    "url_email_company": "www.AXA.ch",
                    "fullname": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "date_of_birth": "14.10.1978",
                    "sex": "m",
                    "company": "AXA",
                }
            },
            expected_doc_cat_frequency_per_page={DocumentCat.PENSION_CERTIFICATE: 1},
        ),
    ).run()
