from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "330_salary_certificate"
path_root_folder = Path(f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_DOCCAT}/{FOLDER_TYPE}")
dest_folder_prefix = "input_system_doccat_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_330_salary_single_mt_vr():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        source_folder=path_root_folder,
        source_file_filter=["330 mt vr 10 searchable.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_page_objects={
                0: {
                    "document_date": "20.01.2017",
                    "salutation": "Herr",
                    "fullname": PO_EXISTS,
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2016",
                    "accounting_period_from": "01.03.2016",
                    "accounting_period_to": "31.12.2016",
                    "salary_board": "CHF 10'000",
                    "salary_gross": "CHF 10'000",
                    "salary_benefits_ahv": "CHF 622",
                    "salary_net": "CHF 9'378",
                    "salary_comments": "Spesenreglement durch Kanton GR am 17.11.2010 genehmigt.",
                    "company_contact": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_330_salary_all():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        source_folder=path_root_folder,
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 11},
            expected_doc_cat_frequency_per_page={DocumentCat.SALARY_CERTIFICATE: 12},
        ),
    ).run()


@pytest.mark.asyncio
async def test_330_salary_certificate_4_lohnausweise():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        source_folder=path_root_folder,
        source_file_filter=["4 Lohnausweise.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 4},
            expected_num_docs=4,
        ),
    ).run()


@pytest.mark.asyncio
async def test_330_salary_certificate_dummy_data():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "330_Lohnausweis_mit_dummy_daten",
        source_file_filter=[
            "330 Lohnausweis Maximilian Mustermann 2024 dummy_daten.pdf"
        ],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
            expected_page_objects={
                0: {
                    "document_date": "20.01.2025",
                    "salutation": "Her",
                    "fullname": PO_EXISTS,
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2024",
                    "accounting_period_from": "01.03.2024",
                    "accounting_period_to": "31.12.2024",
                    "salary_base": "CHF 111'111",
                    "salary_benefits_car": "CHF 2'222",
                    "salary_benefits_other": "CHF 2'323",
                    "salary_irregular_benefits": "CHF 333'333",
                    "salary_irregular_benefits_desc": "Ausserordentlicher Bonus für 2024",
                    "salary_capital_benefits": "CHF 44",
                    "salary_ownership_rights": "CHF 555",
                    "salary_board": "CHF 10'000",
                    "salary_other": "CHF 77",
                    "salary_gross": "CHF 10'000",
                    # "salary_net": "CHF 102'102",     this is wrong, should be 9738 because layoutlm is not working with so many dummy values
                    "salary_expenses_actual_travel": "CHF 1'311",
                    "salary_expenses_actual_other": "CHF 1'312",
                    "salary_expenses_overall_representation": "CHF 1'321",
                    "salary_expenses_overall_car": "CHF 1'322",
                    "salary_expenses_overall_other": "CHF 1'323",
                    "salary_contributions_education": "CHF 133",
                    "salary_comments": "Spesenreglement durch Kanton GR am 17.11.2010 genehmigt.",
                    "company_contact": PO_EXISTS,
                }
            },
        ),
    ).run()
