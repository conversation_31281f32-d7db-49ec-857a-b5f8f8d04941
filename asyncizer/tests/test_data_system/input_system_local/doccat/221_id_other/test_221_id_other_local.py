from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT

FOLDER_TYPE = "221_id_other"

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_local_doccat_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_id_greece():
    await DossierTest(
        override_existing=True,
        show_page=0,
        source_file_filter=["sample_id_greece.pdf"],
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={}, expected_num_docs=1
        ),
    ).run()
