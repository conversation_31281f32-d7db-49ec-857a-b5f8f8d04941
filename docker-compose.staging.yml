version: '3.8'
services:
  pdf2img:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V31
        target: /app/.env
    command: python asyncizer/pdf2jpg_worker.py
    networks:
      core-services:
    deploy:
      resources:
        limits:
          cpus: '3'
          # For complicated plans (e.g. 10MB) 2GB is not enough, better 3GB
          memory: 4500M
      placement:
        constraints: [ node.labels.generic == true ]

  spacy-classifier:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V31
        target: /app/.env
    command: python classifier/spacy_worker.py
    networks:
      core-services:
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4500M
      placement:
        constraints: [ node.labels.generic == true ]

  hyextract:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V31
        target: /app/.env
    networks:
      core-services:
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1'
          memory: 4750M
      placement:
        constraints: [ node.labels.generic == true ]


  original_file_processor:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V31
        target: /app/.env
    command: python asyncizer/file_process_worker.py
    environment:
      - RABBIT_URL=amqp://dossier-processor:<EMAIL>:5672/dossier-processor
    networks:
      core-services:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 1500M
      placement:
        constraints: [ node.labels.generic == true ]

networks:
  core-services:
    external: true
  caddy:
    external: true


secrets:
  ENV_CONF_V31:
    file: .env-staging