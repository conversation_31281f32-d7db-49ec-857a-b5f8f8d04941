import subprocess
from datetime import datetime
from pathlib import Path
from typing import List, Tuple, Union, Optional

from asyncizer.pdf_password_protected import PasswordProtectedLocalFile
from hypodossier.util.memory_utils import get_mem_stats

import structlog

logger = structlog.getLogger(__name__)

GS_ERROR_STRING_PASSWORD_PROTECTION = "This file requires a password for access"


def gs_convert_pdf_to_jpg(
    input_path: Path,
    temp_image_dir_path: Path,
    dpi: int,
    quality: int = 100,
    debug: bool = False,
    timeout: Union[float, None] = None,
) -> List[Path]:
    """
    Convert a PDF to a JPEG image with ghostscript
    gs -dNOPAUSE -sDEVICE=jpeg -r100 -dJPEGQ=60 -sOutputFile=document-%02d.jpg 23_5_23_Voranfrage_Umgebung_A1.pdf -dBATCH
    """
    params = ["gs"]
    if debug:
        params.append("-dDEBUG")

    params += [
        "-dNOPAUSE",
        "-sDEVICE=jpeg",
        f"-r{dpi}",
        f"-dJPEGQ={quality}",
        f"-sOutputFile={str(temp_image_dir_path)}/pdf2jpeg_with_gs-%02d.jpg",
        str(input_path),
        "-dBATCH",
    ]

    success, retcode, _, _ = execute_gs_command_as_subprocess(params, timeout=timeout)

    paths = []
    if success:
        paths = list(temp_image_dir_path.glob("pdf2jpeg_with_gs-*.jpg"))
    return paths


def has_gs_password_protection_string(stdout_text, stderr_text) -> bool:
    """
    Between gs 9 and 10 the location of the password protection error
    string changed from stdout to stderr
    :param stdout_text:
    :param stderr_text:
    :return:
    """
    has_password_protection_gs_9_5 = GS_ERROR_STRING_PASSWORD_PROTECTION in stdout_text
    has_password_protection_gs_10 = GS_ERROR_STRING_PASSWORD_PROTECTION in stderr_text
    has_password_protection = (
        has_password_protection_gs_9_5 or has_password_protection_gs_10
    )
    return has_password_protection


def gs_check_valid_pdf_file(
    input_path: Path, timeout: Union[float, None] = None, context: Optional[str] = None
) -> bool:
    """

        :param input_path: File to check
        :param timeout: Timeout for the operation in seconds
        :param context: String that will be logged and can help with handling exceptions

        raises PasswordProtectedLocalFile if the file is password protected

    Check if a PDF "is valid" as in "can be processed without error by ghostscript". No PDF/A validity is checked.

    Password protected files return "True" as we assume that they are ok (we can just not access them)

    gs -dNOPAUSE -dBATCH -sDEVICE=nullpage 23_5_23_Voranfrage_Umgebung_A1.pdf
    """
    try:
        params = ["gs"]
        params += ["-dNOPAUSE", "-dBATCH", "-sDEVICE=nullpage", str(input_path)]

        _, _, stdout_text, stderr_text = execute_gs_command_as_subprocess(
            params, timeout=timeout
        )
        """
        Output in response.stout can be e.g. 
        
        For Ghostscript 10.x:
        
        GPL Ghostscript 10.02.0 (2023-09-13)
        Copyright (C) 2023 Artifex Software, Inc.  All rights reserved.
        This software is supplied under the GNU AGPLv3 and comes with NO WARRANTY:
        see the file COPYING for details.
        Processing pages 1 through 2.
        Page 1
        Page 2
        
        The following warnings were encountered at least once while processing this file:
                A Form XObject had a BBox with a width or height of 0
        
           **** This file had errors that were repaired or ignored.
           **** The file was produced by: 
           **** >>>> macOS Version 14.3.1 (Build 23D60) Quartz PDFContext <<<<
           **** Please notify the author of the software that produced this
           **** file that it does not conform to Adobe's published PDF
           **** specification.


        
        For Ghostscript 9.5: 
        
        GPL Ghostscript 9.50 (2019-10-15)
        Copyright (C) 2019 Artifex Software, Inc.  All rights reserved.
        This software is supplied under the GNU AGPLv3 and comes with NO WARRANTY:
        see the file COPYING for details.
        Processing pages 1 through 30.
        Page 1
        Loading NimbusSans-Regular font from /usr/share/ghostscript/9.50/Resource/Font/NimbusSans-Regular... 4446160 2940425 4099152 2741034 4 done.
        Loading NimbusSans-Bold font from /usr/share/ghostscript/9.50/Resource/Font/NimbusSans-Bold... 4532472 3126222 4139552 2775871 4 done.
           **** Error: File did not complete the page properly and may be damaged.
                       Output may be incorrect.
        Page 2
           **** Error: File did not complete the page properly and may be damaged.
                       Output may be incorrect.
        Page 3
        Page 4
        Page 5
        """
        has_error_text = stderr_text and len(stdout_text) > 0
        has_errors = has_error_text or "**** Error" in stdout_text

        has_password_protection = has_gs_password_protection_string(
            stdout_text, stderr_text
        )
        if has_gs_password_protection_string(stdout_text, stderr_text):
            raise PasswordProtectedLocalFile(input_path, context)

        success = not has_errors and not has_password_protection
    except PasswordProtectedLocalFile as e:
        raise e
    except Exception as e:
        # There can be errors thrown by the command line execution, e.g.
        # UnicodeDecodeError: 'utf-8' codec can't decode byte 0xfe in position 317: invalid start byte
        logger.warning("gs_check_valid_pdf_file", e=e)
        success = False
    return success


def gs_scale_pdf(
    input_path: Path,
    output_path: Path,
    papersize: str,
    timeout: Union[float, None] = None,
) -> Tuple[bool, int]:
    """
    Scale a pdf with ghostscript to a certain paper size ("a2", "a4" etc)
    gs \
     -o output.pdf \
     -sDEVICE=pdfwrite \
     -sPAPERSIZE=a4 \
     -dFIXEDMEDIA \
     -dPDFFitPage \
     -dCompatibilityLevel=1.4 \
      Pläne_Fassaden__Schnitte.pdf

    """
    params = [
        "gs",
        "-o",
        str(output_path),
        "-sDEVICE=pdfwrite",
        f"-sPAPERSIZE={papersize}",
        "-dFIXEDMEDIA",
        "-dPDFFitPage",
        "-dCompatibilityLevel=1.4",
        str(input_path),
    ]

    try:
        success, retcode, _, _ = execute_gs_command_as_subprocess(
            params, timeout=timeout
        )
        return success, retcode
    except Exception:
        # There can be errors thrown by the command line execution
        return False, -1


def gs_pdf_fix(
    input_path: Path,
    output_path_dir: Path,
    suffix: str = "_repairedgs",
    timeout: float = -1,
    context: Optional[str] = None,
) -> Union[Path, None]:
    """
    Fix and optimize a PDF file using ghostscript with prepress settings.
    This can help repair corrupted PDFs and optimize them for high-quality printing.

    gs -o output_file_with_suffix.pdf -sDEVICE=pdfwrite -dPDFSETTINGS=/prepress input_file.pdf

    Returns:
        Path: The path to the fixed PDF file if successful
        None: If the operation failed
    """
    dest_path = output_path_dir / f"{input_path.stem}{suffix}{input_path.suffix}"

    params = [
        "gs",
        "-o",
        str(dest_path),
        "-sDEVICE=pdfwrite",
        "-dPDFSETTINGS=/prepress",
        "-dALLOWPSTRANSPARENCY",
        # "dFIXEDMEDIA",        DO NOT USE THIS, defaults to US Letter
        # "-dNOOUTERSAVE",      unpredictable to do not use it
        "-dAutoRotatePages=/None",
        # This might turn an A4 Landscape into a US Letter Portrait
        # "-dPDFFitPage=false",
        # Probably not needed - better not use it
        # "-dUseCropBox=true",
        # "-dPreservePageSize",
        str(input_path),
    ]

    try:
        success, retcode, stdout_text, stderr_text = execute_gs_command_as_subprocess(
            params, timeout=timeout if timeout > 0 else None
        )

        if has_gs_password_protection_string(stdout_text, stderr_text):
            raise PasswordProtectedLocalFile(input_path, context)

        if retcode == 0:
            logger.info(
                "gs: Successfully fixed PDF",
                input_path=str(input_path),
                output_path=str(dest_path),
                context=context,
            )
            return dest_path
        else:
            logger.error(
                "gs: Failed to fix PDF",
                input_path=str(input_path),
                return_code=retcode,
                context=context,
            )
            return None
    except PasswordProtectedLocalFile as e:
        raise e
    except Exception as e:
        # There can be errors thrown by the command line execution
        logger.error(
            "gs: Exception while fixing PDF",
            input_path=str(input_path),
            error=str(e),
            context=context,
        )
        return None


def _decode_bytes_fallback(data: Union[bytes, None]) -> str:
    if not data:
        return ""
    try:
        return data.decode("utf-8")
    except UnicodeDecodeError:
        return data.decode("latin-1", errors="replace")


def execute_gs_command_as_subprocess(
    params, timeout: Union[float, None] = None
) -> Tuple[bool, int, str, str]:
    ts_start = datetime.now()

    cmd_for_debug = " ".join(params)
    logger.debug("execute_gs_command_as_subprocess", cmd_for_debug=cmd_for_debug)

    # According to ChatGPT it is possible that ghostscript returns different
    # encodings for stderr_text and stdout_text. So we need to decode them best
    # effort
    res = subprocess.run(params, capture_output=True, timeout=timeout)
    ts_end = datetime.now()
    duration = ts_end - ts_start

    stderr_text = _decode_bytes_fallback(res.stderr)
    stdout_text = _decode_bytes_fallback(res.stdout)

    # Return codes are these (source https://fossies.org/linux/ghostscript/base/gserrors.h):
    # enum gs_error_type {
    # 28     gs_error_ok = 0,
    # 29     gs_error_unknownerror = -1, /* unknown error */
    # 30     gs_error_dictfull = -2,
    # 31     gs_error_dictstackoverflow = -3,
    # 32     gs_error_dictstackunderflow = -4,
    # 33     gs_error_execstackoverflow = -5,
    # 34     gs_error_interrupt = -6,
    # 35     gs_error_invalidaccess = -7,
    # 36     gs_error_invalidexit = -8,
    # 37     gs_error_invalidfileaccess = -9,
    # 38     gs_error_invalidfont = -10,
    # 39     gs_error_invalidrestore = -11,
    # 40     gs_error_ioerror = -12,
    # 41     gs_error_limitcheck = -13,
    # 42     gs_error_nocurrentpoint = -14,
    # 43     gs_error_rangecheck = -15,
    # 44     gs_error_stackoverflow = -16,
    # 45     gs_error_stackunderflow = -17,
    # 46     gs_error_syntaxerror = -18,
    # 47     gs_error_timeout = -19,
    # 48     gs_error_typecheck = -20,
    # 49     gs_error_undefined = -21,
    # 50     gs_error_undefinedfilename = -22,
    # 51     gs_error_undefinedresult = -23,
    # 52     gs_error_unmatchedmark = -24,
    # 53     gs_error_VMerror = -25,     /* must be the last Level 1 error */

    text, size_mb = get_mem_stats()

    # Caution: if the file is password protected, std error will contain
    # "This file requires a password for access" (GS_ERROR_STRING_PASSWORD_PROTECTION)
    # but no exception is raised

    if res.returncode == 0:
        # Success
        pass
    elif res.returncode == 3:
        logger.error(
            "gs: Warning during execution of ghostscript",
            params=params,
            stderr_text=stderr_text,
            stdout_text=stdout_text,
            returncode=res.returncode,
        )
    else:
        logger.error(
            "gs: Error during execution of ghostscript",
            params=params,
            duration_seconds=duration,
            stderr_text=stderr_text,
            stdout_text=stdout_text,
            text=text,
            returncode=res.returncode,
        )
    logger.info(
        "Ghostscript execution done",
        params=params,
        duration_seconds=duration,
        stderr_text=stderr_text,
        stdout_text=stdout_text,
        text=text,
        returncode=res.returncode,
    )
    success = res.returncode == 0
    return success, res.returncode, stdout_text, stderr_text
