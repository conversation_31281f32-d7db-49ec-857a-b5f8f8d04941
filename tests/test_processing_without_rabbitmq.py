from unittest import mock
from unittest.mock import create_autospec, <PERSON><PERSON>, MagicMock

import pytest
from pytest_mock import MockerFixture

import global_settings
from asyncizer.file_process_worker import (
    OriginalFileRequestProcessor,
    OriginalFileProcessorConfig,
)
from asyncizer.publisher import DossierEventPublisher
from asyncizer.processing_config import SemanticDocumentSplittingStyle
from asyncizer.semantic_document_processing_config import (
    DEFAULT_SEMANTIC_DOCUMENTS_PROCESSING_CONFIG,
)


def test_processing_config():
    assert (
        DEFAULT_SEMANTIC_DOCUMENTS_PROCESSING_CONFIG.force_document_category_key is None
    )
    assert (
        DEFAULT_SEMANTIC_DOCUMENTS_PROCESSING_CONFIG.semantic_document_splitting_style
        == SemanticDocumentSplittingStyle.DEFAULT
    )


@pytest.mark.asyncio
async def test_original_file_process_worker(mocker: MockerFixture):
    # PG: this test is completely broken as the underlying function has completely changed
    dossier_event_publisher: Mock = create_autospec(DossierEventPublisher)
    request_mock = MagicMock()
    unpack_original_file_mock = mocker.patch(
        "asyncizer.file_process_worker.preprocess_and_unpack_original_file"
    )
    mocker.patch("asyncizer.file_process_worker.json.dumps")

    global_settings.ENABLE_ANY_ANTIVIRUS = False

    processor = OriginalFileRequestProcessor(
        dossier_event_publisher=dossier_event_publisher,
        small_files_filter=None,
        config=OriginalFileProcessorConfig(settings=global_settings),
    )
    await processor.process_original_file_request_v1(request_mock, trial=0)

    unpack_original_file_mock.assert_called_once()

    dossier_event_publisher.publish.assert_any_call(
        "DossierEvent.ProcessOriginalFileFinished", mock.ANY
    )
