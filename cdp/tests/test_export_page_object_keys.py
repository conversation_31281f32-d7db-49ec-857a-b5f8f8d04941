from tempfile import TemporaryDirectory

import json
from pathlib import Path
from hypodossier.core.domain.DocumentCat import DocumentCat
from doc.internal.cdp.scripts.export_page_object_keys_for_cdp import (
    create_page_object_key_union_set_for_all_doc_cat,
    create_hypodossier_document_cat,
    export_page_object_keys_for_cdp,
)


def test_creates_union_set_of_page_object_keys(mocker):
    mock_get_pagedata = mocker.patch(
        "doc.internal.cdp.scripts.export_page_object_keys_for_cdp.get_pagedata_by_doc_cat"
    )
    mock_get_pagedata.return_value.get_visible_semantic_fields.return_value = {
        "field1": "value1",
        "field2": "value2",
    }

    with TemporaryDirectory() as temp_dir:
        dest_file_path = Path(temp_dir) / "test_output.json"
        num_keys = create_page_object_key_union_set_for_all_doc_cat(
            DocumentCat, dest_file_path
        )
        assert num_keys == 2
        with open(dest_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        assert len(data) == 2
        assert data[0]["key"] == "field1"
        assert data[1]["key"] == "field2"



def test_handles_empty_page_data(mocker):
    mock_get_pagedata = mocker.patch(
        "doc.internal.cdp.scripts.export_page_object_keys_for_cdp.get_pagedata_by_doc_cat"
    )
    mock_get_pagedata.return_value = None

    with TemporaryDirectory() as temp_dir:
        dest_file_path = Path(temp_dir) / "test_output.json"
        num_keys = create_page_object_key_union_set_for_all_doc_cat(
            DocumentCat, dest_file_path
        )
        assert num_keys == 0
        with open(dest_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        assert len(data) == 0

def test_creates_document_categories_json():
    with TemporaryDirectory() as temp_dir:
        dest_file_path = Path(temp_dir) / "test_doc_categories.json"
        num_categories = create_hypodossier_document_cat(DocumentCat, dest_file_path)
        assert num_categories == len(DocumentCat)
        with open(dest_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        assert len(data) == len(DocumentCat)

def test_exports_page_object_keys(mocker):
    mock_create_union_set = mocker.patch(
        "doc.internal.cdp.scripts.export_page_object_keys_for_cdp.create_page_object_key_union_set_for_all_doc_cat"
    )
    mock_create_union_set.return_value = 5

    with TemporaryDirectory() as temp_dir:
        export_page_object_keys_for_cdp("230101")
        mock_create_union_set.assert_called_once()