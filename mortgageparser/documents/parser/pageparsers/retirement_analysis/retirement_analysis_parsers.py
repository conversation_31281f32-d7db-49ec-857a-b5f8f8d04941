from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    create_labeled_field_horizontal,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    TemplateGenericLetterPageParser,
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
)

parsers_de = [
    TemplateGenericLetterPageParser(
        doc_cat=DocumentCat.RETIREMENT_ANALYSIS,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="SL Vorsorgeplanung 2018 #1/x",
        required_text_conditions=[
            FromStartTextCond(["SwissLife", "Swiss Life"], num_lines=5),
            FromStartTextCond("Ihre persönliche Vorsorge", num_lines=5),
            FromStartTextCond("Finanzanalyse", num_lines=5),
            FromStartTextCond("erstellt für", num_lines=20),
        ],
        required_tokens=["Ihr Berater"],
        company="Swiss Life",
        se=GenericLetterSearchElements(
            document_date=create_labeled_field_horizontal("Gedruckt:")
        ),
    ),
    TemplateGenericLetterPageParser(
        doc_cat=DocumentCat.RETIREMENT_ANALYSIS,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        desc="SL Vorsorgeplanung 2018 #2..x/x",
        required_text_conditions=[
            FromStartTextCond(["SwissLife", "Swiss Life"], num_lines=5),
            FromStartTextCond(
                "Ihre persönliche Vorsorge- und Finanzanalyse",
                num_lines=2,
                hamming_dist=5,
            ),
        ],
        company="Swiss Life",
        regions=[
            SearchElementConstrainedArea(
                name="region_name",
                text_top="Persönliche Daten",
                text_bottom="Geburtsdatum",
                text_right="Partner",
            ).exclude_right(-6)
        ],
        se=GenericLetterSearchElements(
            fullname=SearchElementConstrainedArea(
                None,
                None,
                target_name="region_name",
                text_top="Kunde",
                text_left="Name",
            )
        ),
    ),
]


def get_parsers_retirement_analysis():
    return parsers_de
