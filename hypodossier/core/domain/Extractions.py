from dataclasses import replace
from typing import Dict, List

from abbyyplumber.plumberstudio.BoundingBox import BoundingBox
from hypodossier.core.domain.Extraction import Extraction
from hypodossier.core.domain.PageData import PageData
from hypodossier.core.domain.SemanticField import <PERSON>man<PERSON><PERSON><PERSON>, ExtractionType


class Extractions(Dict[str, Extraction]):
    def get_value(self, name: str):
        key = name.name if isinstance(name, SemanticField) else name
        if key in self.keys():
            return self.get(key).value

    def add_extraction(self, e: Extraction):
        self[e.name] = e

    def add_extractions(self, extraction_list: List[Extraction]):
        for e in extraction_list:
            self.add_extraction(e)


def extract_formatted_extractions(
    extractions: Extractions,
    page_data: PageData,
    client_lang: str,
    allow_multiline: bool,
    empty_value: str,
    show_empty_fields: bool,
):
    formatted_extractions: Extractions = Extractions()
    if page_data:
        for name, f in page_data.get_semantic_fields().items():
            e = extractions.get(name)
            e_formatted = None
            if e:
                val = e.value_formatted(
                    client_lang,
                    allow_multiline=allow_multiline,
                    empty_value=empty_value,
                )
                if val:
                    e_formatted = replace(e, value=val)

            if not e_formatted and show_empty_fields:
                val = empty_value
                bbox_ref = e.bbox_ref if e else BoundingBox(0, 0, 1, 1)
                e_formatted = Extraction(
                    name=name,
                    value=val,
                    type=f.extraction_type,
                    search_result=None,
                    bbox_ref=bbox_ref,
                    titles=f.titles,
                )

            if e_formatted:
                formatted_extractions.add_extraction(e_formatted)

    for name, e in extractions.items():
        if e.type == ExtractionType.FINHURDLE:
            formatted_extractions.add_extraction(e)

    return formatted_extractions
