import pytest

from asyncizer import dossier_processor
from classifier.clean_for_text_classification import (
    remove_multiple_spaces,
    filter_characters,
)
from classifier.spacy_classification import classify_with_spacy

import structlog

logger = structlog.getLogger(__name__)


text_de = """Solothurn eTax 2019 (1.1.35) Pers.-Nr.: 11091342, <PERSON><PERSON>, 4562 Biberist 22.07.2020 15:10:25 (Seite 6/15)
Wertschriften-und
Guthabenverzeichnis        2019
mit Verrechnungsantrag
Gemeinde Biberist
Veranlagungsbehörde         Solothurn
Emch Miro
Pestalozzistrasse 8
4562 Biberist




<PERSON><PERSON><PERSON> Hinweise siehe          Personalien am 31. Dezember 2019
Wegleitung Ziffer 700 und 
Steuerbuch  26 Nr. 2 und 3,         Person 1:                                                           Person 2: \\ 
 66 Nr. 1,  67 Nr. 1                                                       w4(p^^31.12.2019 
Wohnsitz am 31.12.2019
Einzureichende Belege:                     Biberist                                   GemeWér                                       Kt. 
Gemeinde                  Kt. SO
- Bescheinigung der Guthaben
- Bescheinigung der Erträge       Hatten Sie Ihren Wohnsitz im Jahr 2019 im Ausland?                        latten Sie Ihren Wohnsitz im Jahr 2019 im Ausland?
- Bescheinigung der Lotterie- 
 Ja Wo                                                    pia    Wo 
und anderen Spielgewinne
Von                        bis                                         Von                        bis 
Nein                                                 Nein


Zusammenfassung
Vermögen                                                                              CHF
Übertragen Sie den niedrigeren
Wert von Ziffer 7000 oder Ziffer        1. Total Steuerwert                                            Übertrag        7000            200320
7010 in die Ziffer 700 der Steuer-       2. Ertragswert (Total Bruttoerträge A + Briffer 3009, abzgl. allfällige
erklärung.
Lotterie- und andere Spielgewinne) p>u0:0,1                                 7001                     0
Ertragslose Kapitalien und Finanz-                           7/
instrumente, die keine Erträge           3. Total Steuerwert/Ertragswert                                                        7009              70 320      1
vorsehen (z.B. Darlehen, Optio-
*4 4. Durchschnittlicher^teiie^wSv^rtragswert
nen), beziehen Sie nicht in die                                                                                                                  J
7010            165160
Umrechnung ein.
► zu übertragen in die Ziffer 700 
der Steuererklärung
Bruttoerträge//^ '
5. Total'Bruttoerfrägp)it Verrechnungssteuerabzug         Übertrag    (A)   3000
6. Total 0jwte^rt(ag ohne Verrechnungssteuerabzug        Übertrag    (B)   3001
7. Tptal BruttoJertrag                                                      3009                                    un
wemder) Bruttoerträgen entfallen auf                                                                                                      s
► zu übertragen in die Ziffer 300 
27 Bbteüigdngen an Gesellschaften, für die das                                                            der Steuererklärung
/Pfepbesteuerungsverfahren beantragt wird
/errechnungsantrag
8. Verrechnungssteueranspruch 35% von Total A (Ziffer 3000) CHF/Rp,   3010
9. Zusätzlicher Steuerrückbehalt USA (Übertrag ab DA-1 )                        3011
10. Pauschale Steueranrechnung (Übertrag ab DA-1)                         3012
iiiiiiiiiTiiiiiiiiiiiiiiiiii iiiiii
il um in mu il ii in
"""

text_de_2 = """
                                                  Solothurn eTax 2019 (1.1.35) Pers.-Nr.: 11091342, Miro Emch, 4562 Biberist 22.07.2020 15:10:25 (Seite 6/15)                                                                                                    
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                  Wertschriften-und                                                                                                                                                                                              
                                                                                                                                                                                                                                                                 
                                                  Guthabenverzeichnis                                                                                                                                                          2019                              
                                                                                                                                                                                                                                                                 
                                                  mit Verrechnungsantrag                                                                                                                                                                                         
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                  Gemeinde Biberist                                                                                                                                                                                              
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
Veranlagungsbehörde                               Solothurn                                                                                                                                                                                                      
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                   Emch Miro                                                                                                     
                                                                                                                                                   Pestalozzistrasse 8                                                                                           
                                                                                                                                                                                                                                                                 
                                                                                                                                                   4562 Biberist                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
Weitere Hinweise siehe                            Personalien am 31. Dezember 2019                                                                                                                                                                               
Wegleitung Ziffer 700 und                         Person 1:                                                                                                  Person 2: \\                                                                                        
Steuerbuch §26 Nr. 2 und 3,                                                                                                                                  w4(p^^31.12.2019                                                                                    
§66 Nr. 1, §67 Nr. 1                              Wohnsitz am 31.12.2019                                                                                                                                                                                         
Einzureichende Belege:                            Gemeinde Biberist                                                                    Kt. SO                GemeWér                                                                            Kt.              
- Bescheinigung der Guthaben                                                                                                                                                                                                                                     
- Bescheinigung der Erträge                       Hatten Sie Ihren Wohnsitz im Jahr 2019 im Ausland?                                                            latten Sie Ihren Wohnsitz im Jahr 2019 im Ausland?                                               
- Bescheinigung der Lotterie-                      Ja Wo                                                                                                        pia                            Wo                                                                
und anderen Spielgewinne                                                                                                 bis                                                                   Von                          bis                                  
                                                                                                 Von                                                                                                                                                             
                                                  Nein                                                                                                        Nein                                                                                               
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                  Zusammenfassung                                                                                                                                                                                                
                                                  Vermögen                                                                                                                                                                          CHF                          
Übertragen Sie den niedrigeren                                                                                                                                                                                                                                   
Wert von Ziffer 7000 oder Ziffer       1. Total Steuerwert                                                                                                   Übertrag                                 7000                          200320                       
7010 in die Ziffer 700 der Steuer-             2. Ertragswert (Total Bruttoerträge A + Briffer 3009, abzgl. allfällige                                                                                                                                           
erklärung.                                     Lotterie- und andere Spielgewinne) p>u0:0,1                                                                                                            7001                                             0         
Ertragslose Kapitalien und Finanz-                                                                    7/                                                                                                                                                         
instrumente, die keine Erträge                                                                                                 3. Total Steuerwert/Ertragswert                                        7009                             70 320                1  
vorsehen (z.B. Darlehen, Optio-                •4 4. Durchschnittlicher^teiie^wSv^rtragswert                                                                                                                                                                     
nen), beziehen Sie nicht in die                                                                                                                                                                       7010                          165160                   J  
Umrechnung ein.                                                                                                                                                                                                                                                  
                                                                                                                                                                                                              ► zu übertragen in die Ziffer 700                  
                                                                                                                                                                                                              der Steuererklärung                                
                                                  Bruttoerträge//^ '                                                                                                                                                                                             
                                                                                                                                                                                                                                                                 
                                                  5. Total'Bruttoerfrägp)it Verrechnungssteuerabzug                                                          Übertrag                          (A)    3000                                                       
                                                  6. Total 0jwte^rt(ag ohne Verrechnungssteuerabzug                                                          Übertrag                          (B)    3001                                                       
                                                                                                                                                                                                                                                                 
                                                       7. Tptal BruttoJertrag                                                                                                                         3009                                                   un  
                                                                                                                                                                                                                                                                 
                                                                      wemder) Bruttoerträgen entfallen auf                                                                                                    ► zu übertragen in die Ziffer 300              s   
                                                       27 Bbteüigdngen an Gesellschaften, für die das                                                                                                         der Steuererklärung                                
                                                       /Pfepbesteuerungsverfahren beantragt wird                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                       /errechnungsantrag                                                                                                                                                                                        
                                                       8. Verrechnungssteueranspruch 35% von Total A (Ziffer 3000) CHF/Rp,                                                               3010                                                                    
                                                                                                                                                                                                                                                                 
                                                       9. Zusätzlicher Steuerrückbehalt USA (Übertrag ab DA-1 )                                                                          3011                                                                    
                                                                                                                                                                                                                                                                 
                                                  10. Pauschale Steueranrechnung (Übertrag ab DA-1)                                                                                      3012                                                                    
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
iiiiiiiiiTiiiiiiiiiiiiiiiiii iiiiii                                                                                                                                                                                                                              
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
                                                                                                                                                                                                                                                                 
il um in mu il ii in          """


def test_filter_characters():
    text = "asdf\nasdf\n"

    t2 = filter_characters(text)
    logger.info(t2)
    assert t2 == "asdf\nasdf"


def test_compress():
    text = """
     x1 x
     x2  x
     x3   x
     x4    x
     x5     x
     x6      x
     x7       x
     x8        x
     x9         x
    x10          x
             
    x11           x
    x12            x
    x13             x
    x14              x
    x15               x
    
    x16                x
    """

    t2 = remove_multiple_spaces(text)

    text_compressed = """
     x1 x
     x2  x
     x3   x
     x4    x
     x5     x
     x6      x
     x7      x
     x8      x
     x9      x
    x10      x

    x11      x
    x12      x
    x13      x
    x14      x
    x15            x

    x16            x
    """

    logger.info(t2)
    assert text_compressed == t2


def test_spacy_de_local():
    logger.info("start of test...")

    t1 = text_de_2
    t2 = remove_multiple_spaces(t1)

    res, name = classify_with_spacy(t2)

    logger.info(f"res={res}")
    logger.info(f"res[0]={res[0]}")

    assert len(res) >= 1
    assert res[0][0] == "DE/310/SO/Wertschriftenverzeichnis Front"
    assert res[0][1] >= 0.95
    assert name.startswith("hypodossier-models/hydocs_spacy/hydocs_detail_de_")


@pytest.mark.asyncio
async def test_spacy_remote():
    ret = await classify_with_spacy_direct(text_de)
    assert len(ret) == 1
    res = ret[0]
    assert res.classification == "DE/310/SO/Wertschriftenverzeichnis Front"
    assert res.confidence >= 0.95
    assert res.classifier.startswith(
        "hypodossier-models/hydocs_spacy/hydocs_detail_de_"
    )

    print(ret)


@pytest.mark.asyncio
async def classify_with_spacy_direct(text: str, top_n=5):
    spacy_classification_response = await dossier_processor.classify_with_spacy_rpc(
        text, top_n
    )
    spacy_page_classifications = spacy_classification_response.classifications
    return spacy_page_classifications
