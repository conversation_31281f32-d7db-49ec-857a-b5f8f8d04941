from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    TemplateGenericLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


def get_parsers_broker_misc():
    parsers = [
        TemplatePageParser(
            doc_cat=DocumentCat.BROKER_MISC,
            page_cat=PageCat.GENERIC_PAGE,
            supported_languages=["fr"],
            desc="Padea Kundenstammdatenformlar FR 2021",
            ranked_titles_all=[RankedTitle("Fiche client privé", 2)],
            required_tokens=["Conseiller Padea", "Client 1", "Déjà proprietaire"],
        ),
        TemplateGenericLetterPageParser(
            doc_cat=DocumentCat.BROKER_MISC,
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            desc="MoneyPark Vermarktungsauftrag Liegenschaft #1/4 2022",
            company="MoneyPark",
            document_title="Vermarktungsauftrag Liegenschaft",
            ranked_titles_all=[RankedTitle("Vermarktungsauftrag Liegenschaft", 2)],
            required_tokens=["MoneyPark", "als Makler im Sinne von Art. 412"],
        ),
        TemplateGenericLetterPageParser(
            doc_cat=DocumentCat.BROKER_MISC,
            page_cat=PageCat.GENERIC_PAGE,
            desc="MoneyPark Vermarktungsauftrag Liegenschaft #2/4 2022",
            company="MoneyPark",
            document_title="Vermarktungsauftrag Liegenschaft",
            ranked_titles_all=[
                RankedTitle("Leistungsumfang", 3),
                RankedTitle("Honorar und Aufwandsersatz", 3),
            ],
            required_tokens=[
                "MoneyPark",
                "für den Verkauf der Liegenschaft erforderlichen",
            ],
            min_num_chars_alpha=2500,
        ),
        TemplateGenericLetterPageParser(
            doc_cat=DocumentCat.BROKER_MISC,
            page_cat=PageCat.GENERIC_PAGE,
            desc="MoneyPark Vermarktungsauftrag Liegenschaft #3/4 2022",
            company="MoneyPark",
            document_title="Vermarktungsauftrag Liegenschaft",
            ranked_titles_all=[
                RankedTitle("Inkrafttreten, Dauer und Auflösung"),
                RankedTitle("Schlussbestimmungen"),
            ],
            required_tokens=[
                "MoneyPark",
                "mit der Vermittlung von Interessenten zu beauftragen",
            ],
            min_num_chars_alpha=2500,
        ),
        TemplateGenericLetterPageParser(
            doc_cat=DocumentCat.BROKER_MISC,
            page_cat=PageCat.GENERIC_PAGE,
            desc="MoneyPark Vermarktungsauftrag Liegenschaft #4/4 2022",
            company="MoneyPark",
            document_title="Vermarktungsauftrag Liegenschaft",
            ranked_titles_all=[
                RankedTitle("Rechtswahl und Gerichtsstand"),
            ],
            required_tokens=[
                "MoneyPark",
                "Dieser Vermarktungsauftrag untersteht Schweizer",
            ],
            min_num_chars_alpha=300,
        ),
    ]
    return parsers
