# from abbyyplumber.converter.ValueConverter import DateConverter
# from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
# from abbyyplumber.plumberstudio.SearchElement import SearchElement<PERSON><PERSON>led<PERSON>ield, \
#     SearchElementArea, SearchElementStaticText
# from abbyyplumber.plumberstudio.SearchRelation import SearchRelationBelow, ReferenceBoundaryHorizontal, SearchRelationRightOf, SearchRelationLeftOf
# from abbyyplumber.util.plumberstudio_util import PercentageRange
# from hypodossier.core.domain.SemanticField import FIELD_DOCUMENT_DATE, FIELD_ADDRESS_BLOCK, \
#     FIELD_PRODUCT, FIELD_FIRSTNAME
# from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import \
#     StandardPensionCertificatePageOnePageParser, \
#     StandardPensionCertificatePageTwoPageParser
# from mortgageparser.util.search_element_util import create_element_company, create_search_elements_address
# from mortgageparser.util.string_utils import contains_all_strings
#
#
# class PublicaPensionCertificatePageOnePageParserOld(StandardPensionCertificatePageOnePageParser):
#
#     def match_page_by_text(self, page_index: int, text: str) -> bool:
#         return contains_all_strings(text, [
#             'Pensionskasse des Bundes PUBLICA', 'Persönlicher Ausweis per', 'Persönliche Daten', 'Austrittsleistung', 'publica.ch'
#         ])
#
#     def parse_page_header(self):
#         success = self.page.set_header_by_text("Persönliche Daten", include_pattern=False)
#         return success
#
#     def parse_page_footer(self):
#         success = self.page.set_footer_by_percentage(2/30)
#         return success
#
#     def create_content_extractor(self) -> ContentExtractor:
#         return ContentExtractor([
#
#             SearchElementStaticText("text_first_line", self.page.header, label="Pensionskasse des Bundes", extract=False),
#             SearchElementStaticText("text_addressblock_above", self.page.header, label="Post CH AG", max_l_dist=0,
#                                     extract=False,
#                                     relations=[SearchRelationBelow("page_first_line"),
#                                                SearchRelationBelow("text_first_line")]),
#             #SearchElementLabeledField(FIELD_DOCUMENT_DATE.name, self.page.header, label="Persönlicher Ausweis per", converter=DateConverter()),
#             SearchElementArea (FIELD_DOCUMENT_DATE.name, self.page.fullpage, converter=DateConverter()),
#
#             SearchElementArea(FIELD_FIRSTNAME.sr_inside, self.page.header, relations=[SearchRelationBelow("text_addressblock_above", offset=50),
#                                          SearchRelationRightOf("text_first_line", ref_boundary=ReferenceBoundaryHorizontal.LEFT)]),
#
#             # Vorsorgeplan does not exist everywhere
#             SearchElementLabeledField(FIELD_PRODUCT.name, self.page.header, label="Vorsorgeplan:",
#                                       field_vertical_line_scale=2.5,
#                                       field_pos_page_horizontal=PercentageRange(0, 0.4),
#                                       optional=True,
#                                       compress_whitespace=True,
#                                       relations=[SearchRelationLeftOf(FIELD_ADDRESS_BLOCK.name)]),
#
#             create_element_company("Publica")
#
#         ] + create_search_elements_address(self.page.header, self.page))
#
#
#
# class PublicaPensionCertificatePageTwoPageParser(StandardPensionCertificatePageTwoPageParser):
#
#     def match_page_by_text(self, page_index: int, text: str) -> bool:
#         success = contains_all_strings(text, [
#             'publica', 'Voraussichtliche Altersrente mit Projektionszins', 'Invalidenrente', 'Bemerkung', 'www.publica.ch'
#         ])
#
#         return success
