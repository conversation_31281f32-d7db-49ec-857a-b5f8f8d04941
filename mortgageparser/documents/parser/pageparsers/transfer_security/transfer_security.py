from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstant,
    SearchElement,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.PageData import FIELDS_PERSON_DATA
from hypodossier.core.domain.SemanticField import FIELD_COMPANY
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TransferSecuritySearchElements:
    document_date: SearchElement = None
    company: SearchElement = None
    #
    # # Name of person as derived from address_block
    # fullname: SearchElement = None
    #
    # default_converters: Dict[Semantic<PERSON>ield, ValueConverter] = default_field({
    #     FIELD_FULLNAME.name: CleanNameConverter(max_num_lines=1),
    #     FIELD_DOCUMENT_DATE.name: MostRecentDateConverter(),
    #
    # })


@dataclass
class TemplateTransferSecurityPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.TRANSFER_OF_SECURITY
    company: str = None

    se: TransferSecuritySearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_PERSON_DATA.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_transfer_security():
    parsers = [
        TemplateTransferSecurityPageParser(
            desc="ZKB Transfer Security 2021 #2/3",
            # ranked_titles_all=[RankedTitle('Rahmenvertrag für Hypotheken')],
            required_tokens_any=[
                ["Fälligkeit des/der Schuldbriefe(s)"],
                ["Schuldbrieferhöhung, Umwandlung etc."],
                [
                    "Diese Sicherungsvereinbarung gilt auch bei einer Schuldbrieferhöhung"
                ],
                ["Zürcher"],
            ],
            company="ZKB",
        ),
        TemplateTransferSecurityPageParser(
            desc="ZKB Transfer Security 2021 #3/3",
            # ranked_titles_all=[RankedTitle('Rahmenvertrag für Hypotheken')],
            required_tokens_any=[
                [
                    "Diese Sicherungsvereinbarung gilt auch bei",
                    "Sobald die Bank gegen den/die Schuldner",
                ],
                ["Diese Sicherungsvereinbarung untersteht dem schweizerischen"],
                ["Zürcher"],
            ],
            company="ZKB",
        ),
        TemplateTransferSecurityPageParser(
            desc="ZKB Transfer Security Erklärung 2016 #1/2",
            ranked_titles_all=[
                RankedTitle("Kundeninformation"),
                RankedTitle("Sicherungsvereinbarung"),
            ],
            required_tokens=["Die Sicherungsvereinbarung kurz erklärt", "Zürcher"],
            company="ZKB",
            regions=[],
            se=TransferSecuritySearchElements(),
        ),
        TemplateTransferSecurityPageParser(
            desc="ZKB Transfer Security Erklärung 2016 #2/2",
            # ranked_titles_all=[RankedTitle('Kundeninformation'), RankedTitle('Sicherungsvereinbarung')],
            required_tokens=[
                "Ziffer 5 der Sicherungsvereinbarung: was umfasst",
                "Kundeninformation",
                "Zürcher",
            ],
            company="ZKB",
            regions=[],
            se=TransferSecuritySearchElements(),
        ),
    ]
    return parsers
