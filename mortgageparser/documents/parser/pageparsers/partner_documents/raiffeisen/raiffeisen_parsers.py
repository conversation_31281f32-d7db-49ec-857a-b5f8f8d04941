from abbyyplumber.converter.ValueConverter import CleanNameConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
)

parsers_de = [
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #1 Deckblatt",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_FIRST_PAGE,
        company="Raiffeisen",
        titles=["Wohneigentumsberatung"],
        # 'Persönlicher Finanzierungsvorschlag' not found by OCR
        required_tokens=["Ihr Kundenberater:", "@raiffeisen.ch", "Raiffeisenbank"],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #2 Inhaltsverzeichnis",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        titles=["Inhaltsverzeichnis"],
        required_tokens=[
            "Rechtliche Hinweise",
            "Standardberechnung",
            "Personalien und Objektdaten",
        ],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #3 Personalien",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        se_fullname=SearchElementConstrainedArea(
            None,
            None,
            text_top="Finanzielle Verhältnisse",
            text_left="Total nachhhaltige Einnahmen",
            text_bottom="Total Einnahmen",
            compress_whitespace=True,
            converter=CleanNameConverter(max_num_lines=1),
        ),
        titles=["Personalien und Objektdaten"],
        required_tokens=[
            "Verkehrswert RB (Belehnungsbasis)",
            "Finanzielle Verhältnisse",
            "Total Einnahmen",
        ],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #4 Objekt",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        titles=["Standardberechnung"],
        required_tokens=[
            "Angaben zum Objekt",
            "Verkehrswert RB (Belehnungsbasis)",
            "Hypothek (K1)",
        ],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #5 Tragbarkeit",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        titles=["Tragbarkeit"],
        required_tokens=[
            "Finanzierungsvorschlag",
            "Einnahmen ohne Liegenschaften",
            "Nebenkosten (inkl. allfällige",
            "Baurechtszinsen)",
            "Gesamteinnahmen pro Monat",
        ],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #6 Varianten",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        titles=["Übersicht Varianten"],
        required_tokens=[
            "Finanzierungsvorschlag",
            "Vorschlag",
            "Wohnkosten Liegenschaft pro Monat",
            "Kostenzusammenstellung",
            "Fremde Mittel",
            "Alle Beträge in CHF",
        ],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #7 Variante 1..n",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        titles=["Vorschlag"],
        required_tokens=[
            "Angaben zum Objekt",
            "Verkehrswert RB (Belehnungsbasis)",
            "Total Zusatzsicherheiten (ZS)",
        ],
    ),
    SmartLetterPageParser(
        desc="Raiffeisen Offerte #7 AGB",
        doc_cat=DocumentCat.FINANCING_OFFER,
        page_cat=PageCat.GENERIC_PAGE,
        company="Raiffeisen",
        titles=["Weitere Informationen"],
        required_tokens=[
            "Bedingungen",
            "Rechtliche Hinweise",
            "Die Angaben und Berechnungen in diesem Finanzierungsvorschlag",
            "Raiffeisen unternimmt alle",
        ],
    ),
]


parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_raiffeisen():
    return parsers_de + parsers_en + parsers_fr + parsers_it
