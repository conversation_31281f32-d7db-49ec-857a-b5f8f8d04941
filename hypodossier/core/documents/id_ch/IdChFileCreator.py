from dataclasses import dataclass
from typing import List

from abbyyplumber.converter.ValueConverter import FilenameDateConverter
from hypodossier.core.domain.Extractions import Extractions
from hypodossier.core.domain.SemanticField import FIELD_DATE_OF_BIRTH
from hypodossier.core.filename.FileCreator import <PERSON><PERSON>reator


@dataclass
class IdChFileCreator(FileCreator):
    def get_document_title_elements(
        self,
        elements: List[str],
        extractions: Extractions,
        filename: str,
        lang: str,
        pages: List,
        long_version: bool,
    ):
        elements.append(self.get_best_effort_name(extractions, self.require_firstname))

        if long_version:
            date = extractions.get_value(FIELD_DATE_OF_BIRTH.name)
            if date:
                elements.append(FilenameDateConverter().convert(date))

        return elements

    def __repr__(self):
        return self.__class__.__name__
