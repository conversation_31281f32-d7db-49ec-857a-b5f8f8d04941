from dataclasses import dataclass
from typing import Dict

from abbyyplumber.converter.ValueConverter import (
    CleanNameConverter,
    BirthDateConverter,
    NewAhvConverter,
    CurrencyConverter,
    YearConverter,
    ParagraphConverter,
    CleanEmployerConverter,
    CleanProfessionConverter,
    CleanEmailConverter,
    DoNothingConverter,
    CleanMaritalEmployerConverter,
)
from abbyyplumber.converter.ValueConverter import (
    ValueConverter,
    CleanPhonenumberConverter,
)
from abbyyplumber.plumberstudio.SearchElement import SearchElement
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstant
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FIRSTNAME,
    P2_FIRSTNAME,
    P1_LASTNAME,
    P2_LASTNAME,
    P1_FULLNAME,
    P2_FULLNAME,
    FIELDS_TAX_DECLARATION,
    P1_DATE_OF_BIRTH,
    P2_DATE_OF_BIRTH,
    P2_AHV_NEW,
    P1_AHV_NEW,
    ASSETS_UNDISTRIBUTED_INHERITANCES,
    ASSETS_GROSS_BUSINESS,
    ASSETS_GROSS_PRIVATE,
    ASSETS_NET_TOTAL,
    DEBT_BUSINESS,
    DEBT_PRIVATE,
    DEBT_MORTGAGES,
    ASSETS_REAL_ESTATE_TOTAL_NET,
    ASSETS_REAL_ESTATE_TOTAL_GROSS,
    P1_EMPLOYER_LOCATION,
    P1_EMPLOYER,
    P1_INCOME_EMPLOYED_BENEFITS,
    P2_EMPLOYER,
    P2_EMPLOYER_LOCATION,
    P2_INCOME_EMPLOYED_BENEFITS,
    P1_INCOME_BOARD_SEATS,
    P2_INCOME_BOARD_SEATS,
    P1_INCOME_ALIMONY_TOTAL,
    P2_INCOME_ALIMONY_TOTAL,
    P1_INCOME_OTHER,
    P2_INCOME_OTHER,
    P1_CONTRIBUTION_PILLAR_1,
    P2_CONTRIBUTION_PILLAR_2,
    P1_CONTRIBUTION_PILLAR_2,
    P1_DEDUCTIONS_ILLNESS,
    P2_DEDUCTIONS_ILLNESS,
    P1_DEDUCTIONS_EDUCATION,
    P2_DEDUCTIONS_EDUCATION,
    P1_INCOME_NET_TOTAL,
    P2_INCOME_NET_TOTAL,
    P1_EXPENSE_EMPLOYMENT_OTHER,
    P2_EXPENSE_EMPLOYMENT_OTHER,
    P1_PROFESSION,
    P2_PROFESSION,
    P2_PHONE_PRIMARY,
    EXPENSE_CHILDREN_DAYCARE,
    SECTION_CHILDREN,
    SECTION_CHILDREN_ADULT,
    P2_EMAIL,
    P1_INCOME_ALIMONY_PARTNER,
    P2_INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_PARTNER,
    INCOME_CHILD_BENEFITS,
    P1_INCOME_ALIMONY_CHILDREN,
    P2_INCOME_ALIMONY_CHILDREN,
    INCOME_ALIMONY_CHILDREN,
    INCOME_REAL_ESTATE_GROSS_OTHER,
    INCOME_REAL_ESTATE_GROSS,
    INCOME_REAL_ESTATE_NET_PRIMARY,
    INCOME_REAL_ESTATE_NET_OTHER,
    P1_INCOME_LUMP_SUM,
    INCOME_LUMP_SUM,
    P2_INCOME_LUMP_SUM,
    P1_INCOME_GROSS_TOTAL,
    P2_INCOME_GROSS_TOTAL,
    ASSETS_REAL_ESTATE_MAIN_PROPERTY,
    ASSETS_REAL_ESTATE_CURRENT_VALUE,
    ASSETS_REAL_ESTATE_CAPITALIZED,
    DEBT_DETAIL_LINES,
    DEBT_DETAIL_LINES_2,
    ADDRESS_REAL_ESTATE_PRIMARY,
    P2_NATIONALITY,
    P1_MARITAL_STATUS,
    P2_MARITAL_STATUS,
    SECTION_SUPPORTED_PERSONS,
    P2_CONTRIBUTION_PILLAR_1,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    INCOME_PORTFOLIO,
    INCOME_REAL_ESTATE_NET,
    INCOME_GROSS_TOTAL,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    DEDUCTIONS_TOTAL,
    INCOME_NET_TOTAL,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_CASH_GOLD,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_OTHER,
    ASSETS_GROSS_TOTAL,
    DEBT_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    INCOME_OTHER,
    P1_INCOME_CHILD_BENEFITS,
    P2_INCOME_CHILD_BENEFITS,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    CONTRIBUTIONS_PILLAR_1_2,
    DEDUCTIONS_DONATIONS,
    DEDUCTIONS_EDUCATION,
    ASSETS_NET_BUSINESS,
    ASSETS_TAXABLE_GLOBAL,
    DEDUCTIONS_WEALTH_MANAGEMENT,
    DEDUCTIONS_ILLNESS,
    INCOME_TAXABLE_GLOBAL,
    DEDUCTIONS_OTHER,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_UNDISTRIBUTED_INHERITANCES,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_CANTON_SHORT,
    FIELD_PHONE_PRIMARY,
    FIELD_YEAR,
    FIELD_PROPERTY_MAINTENANCE_COST,
    FIELD_PHONE_SECONDARY,
    FIELD_EMAIL,
    FIELD_NATIONALITY,
    FIELD_PROPERTY_TYPE,
    FIELD_PROPERTY_YEAR,
    FIELD_PROPERTY_PURCHASE_YEAR,
    FIELD_PROPERTY_IMPUTED_RENTAL_VALUE,
    FIELD_PROPERTY_IMPUTED_RENTAL_VALUE_CANTON,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    default_field,
    TemplatePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    create_search_elements_by_tax_code,
    COLUMN_CODE,
    COLUMN_VALUE,
)

DEBT_DETAIL_LINES_CONVERTER = ParagraphConverter(
    min_alpha_per_line=5, min_decimal_per_line=2
)


@dataclass
class TaxDeclarationSearchElements:
    unique_doc_id: SearchElement = None

    # can be specific id for canton or ahv_new, must be unique
    person_id: SearchElement = None

    # canton, year and person_name are used for the filename
    canton: SearchElement = None
    year: SearchElement = None
    municipality: SearchElement = None
    person_name: SearchElement = None

    # Address
    address_block: SearchElement = None
    street: SearchElement = None
    zip: SearchElement = None
    city: SearchElement = None

    p1_ahv_new: SearchElement = None
    p2_ahv_new: SearchElement = None

    # Page 01 Information
    p1_firstname: SearchElement = None
    p2_firstname: SearchElement = None
    p1_lastname: SearchElement = None
    p2_lastname: SearchElement = None
    p1_fullname: SearchElement = None
    p2_fullname: SearchElement = None

    email: SearchElement = None
    phone_primary: SearchElement = None
    phone_secondary: SearchElement = None
    nationality: SearchElement = None

    p2_email: SearchElement = None
    p2_phone_primary: SearchElement = None
    p2_nationality: SearchElement = None

    p1_date_of_birth: SearchElement = None
    p2_date_of_birth: SearchElement = None

    p1_profession: SearchElement = None
    p2_profession: SearchElement = None
    p1_employer: SearchElement = None
    p2_employer: SearchElement = None
    p1_employer_location: SearchElement = None
    p2_employer_location: SearchElement = None
    p1_marital_status: SearchElement = None
    p2_marital_status: SearchElement = None

    section_children: SearchElement = None
    section_children_adult: SearchElement = None
    section_supported_persons: SearchElement = None

    # Page 02 Income
    p1_income_employed_main: SearchElement = None
    p2_income_employed_main: SearchElement = None
    p1_income_employed_side: SearchElement = None
    p2_income_employed_side: SearchElement = None
    p1_income_employed_benefits: SearchElement = None
    p2_income_employed_benefits: SearchElement = None
    p1_income_self_main: SearchElement = None
    p2_income_self_main: SearchElement = None
    p1_income_self_side: SearchElement = None
    p2_income_self_side: SearchElement = None
    p1_income_board_seats: SearchElement = None
    p2_income_board_seats: SearchElement = None
    p1_income_pension_ahv: SearchElement = None
    p2_income_pension_ahv: SearchElement = None
    p1_income_pension: SearchElement = None
    p2_income_pension: SearchElement = None
    p1_income_eo: SearchElement = None
    p2_income_eo: SearchElement = None
    p1_income_social_security: SearchElement = None
    p2_income_social_security: SearchElement = None

    p1_income_alimony_partner: SearchElement = None
    p2_income_alimony_partner: SearchElement = None
    income_alimony_partner: SearchElement = None

    p1_income_alimony_children: SearchElement = None
    p2_income_alimony_children: SearchElement = None
    income_alimony_children: SearchElement = None

    p1_income_alimony_total: SearchElement = None
    p2_income_alimony_total: SearchElement = None

    p1_income_child_benefits: SearchElement = None
    p2_income_child_benefits: SearchElement = None
    income_child_benefits: SearchElement = None

    p1_income_other: SearchElement = None
    p2_income_other: SearchElement = None

    income_portfolio: SearchElement = None

    income_real_estate_gross_other: SearchElement = None
    income_real_estate_gross: SearchElement = None
    income_real_estate_net_primary: SearchElement = None
    income_real_estate_net_other: SearchElement = None
    income_real_estate_net: SearchElement = None

    income_other: SearchElement = None

    p1_income_lump_sum: SearchElement = None
    p2_income_lump_sum: SearchElement = None
    income_lump_sum: SearchElement = None
    income_undistributed_inheritances: SearchElement = None

    p1_income_gross_total: SearchElement = None
    p2_income_gross_total: SearchElement = None
    income_gross_total: SearchElement = None

    # Page 02 Deductions
    expense_alimony_children: SearchElement = None
    expense_alimony_partner: SearchElement = None
    expense_annuity_contributions: SearchElement = None

    p1_contribution_pillar_1: SearchElement = None
    p2_contribution_pillar_1: SearchElement = None
    p1_contribution_pillar_2: SearchElement = None
    p2_contribution_pillar_2: SearchElement = None
    p1_contribution_pillar_3a: SearchElement = None
    p2_contribution_pillar_3a: SearchElement = None
    contributions_pillar_1_2: SearchElement = None

    p1_expense_employment: SearchElement = None
    p2_expense_employment: SearchElement = None
    p1_expense_employment_other: SearchElement = None
    p2_expense_employment_other: SearchElement = None

    insurance_premiums_and_interest_on_savings_accounts: SearchElement = None
    deductions_wealth_management: SearchElement = None
    p1_deductions_illness: SearchElement = None
    p2_deductions_illness: SearchElement = None
    deductions_illness: SearchElement = None
    expense_children_daycare: SearchElement = None

    p1_deductions_education: SearchElement = None
    p2_deductions_education: SearchElement = None
    deductions_education: SearchElement = None
    deductions_donations: SearchElement = None
    deductions_other: SearchElement = None

    deductions_total: SearchElement = None

    p1_income_net_total: SearchElement = None
    p2_income_net_total: SearchElement = None
    income_net_total: SearchElement = None
    income_taxable_global: SearchElement = None
    income_taxable_local: SearchElement = None

    # Page 4 Assets
    assets_portfolio: SearchElement = None
    assets_cash_gold: SearchElement = None
    assets_life_insurance: SearchElement = None
    assets_cars: SearchElement = None
    assets_undistributed_inheritances: SearchElement = None
    assets_other: SearchElement = None

    assets_gross_business: SearchElement = None
    assets_net_business: SearchElement = None
    assets_gross_private: SearchElement = None
    assets_gross_total: SearchElement = None
    assets_net_total: SearchElement = None
    assets_taxable_global: SearchElement = None
    assets_taxable_local: SearchElement = None

    assets_real_estate_main_property: SearchElement = None
    assets_real_estate_current_value: SearchElement = None
    assets_real_estate_capitalized_earnings_value: SearchElement = None
    assets_real_estate_total_net: SearchElement = None
    assets_real_estate_total_gross: SearchElement = None

    # Debt Inventory
    debt_business: SearchElement = None
    debt_private: SearchElement = None
    debt_mortgages: SearchElement = None
    debt_total: SearchElement = None

    interest_paid_on_debt: SearchElement = None
    debt_detail_lines: SearchElement = None
    debt_detail_lines_2: SearchElement = None

    # Property stuff
    address_real_estate_primary: SearchElement = None
    property_type: SearchElement = None
    property_year: SearchElement = None
    property_purchase_year: SearchElement = None
    property_imputed_rental_value: SearchElement = None
    property_imputed_rental_value_canton: SearchElement = None
    property_maintenance_cost: SearchElement = None

    default_converters: Dict[str, ValueConverter] = default_field(
        {
            FIELD_YEAR.name: YearConverter(min_value=2001),
            P1_AHV_NEW.name: NewAhvConverter(),
            P2_AHV_NEW.name: NewAhvConverter(),
            P1_FIRSTNAME.name: CleanNameConverter(max_num_lines=1),
            P2_FIRSTNAME.name: CleanNameConverter(max_num_lines=1),
            P1_LASTNAME.name: CleanNameConverter(max_num_lines=1),
            P2_LASTNAME.name: CleanNameConverter(max_num_lines=1),
            P1_FULLNAME.name: CleanNameConverter(max_num_lines=1),
            P2_FULLNAME.name: CleanNameConverter(max_num_lines=1),
            FIELD_EMAIL.name: CleanEmailConverter(),
            FIELD_PHONE_PRIMARY.name: CleanPhonenumberConverter(),
            FIELD_PHONE_SECONDARY.name: CleanPhonenumberConverter(),
            FIELD_NATIONALITY.name: DoNothingConverter(),
            P2_EMAIL.name: CleanEmailConverter(),
            P2_PHONE_PRIMARY.name: CleanPhonenumberConverter(),
            P2_NATIONALITY.name: DoNothingConverter(),
            P1_DATE_OF_BIRTH.name: BirthDateConverter(),
            P2_DATE_OF_BIRTH.name: BirthDateConverter(),
            P1_PROFESSION.name: CleanProfessionConverter(),
            P2_PROFESSION.name: CleanProfessionConverter(),
            P1_EMPLOYER.name: CleanEmployerConverter(),
            P2_EMPLOYER.name: CleanEmployerConverter(),
            P1_EMPLOYER_LOCATION.name: CleanEmployerConverter(),
            P2_EMPLOYER_LOCATION.name: CleanEmployerConverter(),
            P1_MARITAL_STATUS.name: CleanMaritalEmployerConverter(),
            P2_MARITAL_STATUS.name: CleanMaritalEmployerConverter(),
            SECTION_CHILDREN.name: ParagraphConverter(
                do_capitalize_uppercase=True,
                do_compress_multiple_spaces=True,
                min_alpha_per_line=3,
            ),
            SECTION_CHILDREN_ADULT.name: ParagraphConverter(
                do_capitalize_uppercase=True,
                do_compress_multiple_spaces=True,
                min_alpha_per_line=3,
            ),
            SECTION_SUPPORTED_PERSONS.name: ParagraphConverter(
                do_capitalize_uppercase=True,
                do_compress_multiple_spaces=True,
                min_alpha_per_line=3,
            ),
            # Page 2 Income
            P1_INCOME_EMPLOYED_MAIN.name: CurrencyConverter(),
            P2_INCOME_EMPLOYED_MAIN.name: CurrencyConverter(),
            P1_INCOME_EMPLOYED_SIDE.name: CurrencyConverter(),
            P2_INCOME_EMPLOYED_SIDE.name: CurrencyConverter(),
            P1_INCOME_EMPLOYED_BENEFITS.name: CurrencyConverter(),
            P2_INCOME_EMPLOYED_BENEFITS.name: CurrencyConverter(),
            P1_INCOME_SELF_MAIN.name: CurrencyConverter(allow_negative_values=True),
            P2_INCOME_SELF_MAIN.name: CurrencyConverter(allow_negative_values=True),
            P1_INCOME_SELF_SIDE.name: CurrencyConverter(allow_negative_values=True),
            P2_INCOME_SELF_SIDE.name: CurrencyConverter(allow_negative_values=True),
            P1_INCOME_BOARD_SEATS.name: CurrencyConverter(),
            P2_INCOME_BOARD_SEATS.name: CurrencyConverter(),
            P1_INCOME_PENSION_AHV.name: CurrencyConverter(),
            P2_INCOME_PENSION_AHV.name: CurrencyConverter(),
            P1_INCOME_PENSION.name: CurrencyConverter(),
            P2_INCOME_PENSION.name: CurrencyConverter(),
            P1_INCOME_EO.name: CurrencyConverter(),
            P2_INCOME_EO.name: CurrencyConverter(),
            P1_INCOME_SOCIAL_SECURITY.name: CurrencyConverter(),
            P2_INCOME_SOCIAL_SECURITY.name: CurrencyConverter(),
            P1_INCOME_ALIMONY_PARTNER.name: CurrencyConverter(),
            P2_INCOME_ALIMONY_PARTNER.name: CurrencyConverter(),
            INCOME_ALIMONY_PARTNER.name: CurrencyConverter(),
            P1_INCOME_ALIMONY_CHILDREN.name: CurrencyConverter(),
            P2_INCOME_ALIMONY_CHILDREN.name: CurrencyConverter(),
            INCOME_ALIMONY_CHILDREN.name: CurrencyConverter(),
            P1_INCOME_ALIMONY_TOTAL.name: CurrencyConverter(),
            P2_INCOME_ALIMONY_TOTAL.name: CurrencyConverter(),
            P1_INCOME_CHILD_BENEFITS.name: CurrencyConverter(),
            P2_INCOME_CHILD_BENEFITS.name: CurrencyConverter(),
            INCOME_CHILD_BENEFITS.name: CurrencyConverter(),
            P1_INCOME_OTHER.name: CurrencyConverter(allow_negative_values=True),
            P2_INCOME_OTHER.name: CurrencyConverter(allow_negative_values=True),
            INCOME_PORTFOLIO.name: CurrencyConverter(allow_negative_values=True),
            INCOME_REAL_ESTATE_GROSS_OTHER.name: CurrencyConverter(),
            INCOME_REAL_ESTATE_GROSS.name: CurrencyConverter(),
            INCOME_REAL_ESTATE_NET_PRIMARY.name: CurrencyConverter(
                allow_negative_values=True
            ),
            INCOME_REAL_ESTATE_NET_OTHER.name: CurrencyConverter(
                allow_negative_values=True
            ),
            INCOME_REAL_ESTATE_NET.name: CurrencyConverter(allow_negative_values=True),
            INCOME_OTHER.name: CurrencyConverter(),
            P1_INCOME_LUMP_SUM.name: CurrencyConverter(),
            P2_INCOME_LUMP_SUM.name: CurrencyConverter(),
            INCOME_LUMP_SUM.name: CurrencyConverter(),
            INCOME_UNDISTRIBUTED_INHERITANCES.name: CurrencyConverter(),
            P1_INCOME_GROSS_TOTAL.name: CurrencyConverter(allow_negative_values=True),
            P2_INCOME_GROSS_TOTAL.name: CurrencyConverter(allow_negative_values=True),
            INCOME_GROSS_TOTAL.name: CurrencyConverter(allow_negative_values=True),
            # Page 2 Deductions
            EXPENSE_ALIMONY_CHILDREN.name: CurrencyConverter(),
            EXPENSE_ALIMONY_PARTNER.name: CurrencyConverter(),
            EXPENSE_ANNUITY_CONTRIBUTIONS.name: CurrencyConverter(),
            P1_CONTRIBUTION_PILLAR_1.name: CurrencyConverter(),
            P2_CONTRIBUTION_PILLAR_1.name: CurrencyConverter(),
            P1_CONTRIBUTION_PILLAR_2.name: CurrencyConverter(),
            P2_CONTRIBUTION_PILLAR_2.name: CurrencyConverter(),
            P1_CONTRIBUTION_PILLAR_3A.name: CurrencyConverter(),
            P2_CONTRIBUTION_PILLAR_3A.name: CurrencyConverter(),
            CONTRIBUTIONS_PILLAR_1_2.name: CurrencyConverter(),
            P1_EXPENSE_EMPLOYMENT.name: CurrencyConverter(),
            P2_EXPENSE_EMPLOYMENT.name: CurrencyConverter(),
            P1_EXPENSE_EMPLOYMENT_OTHER.name: CurrencyConverter(),
            P2_EXPENSE_EMPLOYMENT_OTHER.name: CurrencyConverter(),
            INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS.name: CurrencyConverter(),
            INTEREST_PAID_ON_DEBT.name: CurrencyConverter(),
            DEDUCTIONS_WEALTH_MANAGEMENT.name: CurrencyConverter(),
            P1_DEDUCTIONS_ILLNESS.name: CurrencyConverter(),
            P2_DEDUCTIONS_ILLNESS.name: CurrencyConverter(),
            DEDUCTIONS_ILLNESS.name: CurrencyConverter(),
            EXPENSE_CHILDREN_DAYCARE.name: CurrencyConverter(),
            P1_DEDUCTIONS_EDUCATION.name: CurrencyConverter(),
            P2_DEDUCTIONS_EDUCATION.name: CurrencyConverter(),
            DEDUCTIONS_EDUCATION.name: CurrencyConverter(),
            DEDUCTIONS_DONATIONS.name: CurrencyConverter(),
            DEDUCTIONS_OTHER.name: CurrencyConverter(),
            DEDUCTIONS_TOTAL.name: CurrencyConverter(),
            P1_INCOME_NET_TOTAL.name: CurrencyConverter(),
            P2_INCOME_NET_TOTAL.name: CurrencyConverter(),
            INCOME_NET_TOTAL.name: CurrencyConverter(allow_negative_values=True),
            INCOME_TAXABLE_GLOBAL.name: CurrencyConverter(),
            INCOME_TAXABLE_LOCAL.name: CurrencyConverter(),
            # Page 4 Assets
            ASSETS_PORTFOLIO_ACCOUNTS.name: CurrencyConverter(),
            ASSETS_CASH_GOLD.name: CurrencyConverter(),
            ASSETS_LIFE_INSURANCE.name: CurrencyConverter(),
            ASSETS_CARS.name: CurrencyConverter(),
            ASSETS_UNDISTRIBUTED_INHERITANCES.name: CurrencyConverter(),
            ASSETS_OTHER.name: CurrencyConverter(),
            ASSETS_GROSS_BUSINESS.name: CurrencyConverter(),
            ASSETS_NET_BUSINESS.name: CurrencyConverter(),
            ASSETS_GROSS_PRIVATE.name: CurrencyConverter(),
            ASSETS_GROSS_TOTAL.name: CurrencyConverter(),
            ASSETS_NET_TOTAL.name: CurrencyConverter(allow_negative_values=True),
            ASSETS_TAXABLE_GLOBAL.name: CurrencyConverter(allow_negative_values=True),
            ASSETS_TAXABLE_LOCAL.name: CurrencyConverter(allow_negative_values=True),
            ASSETS_REAL_ESTATE_MAIN_PROPERTY.name: CurrencyConverter(),
            ASSETS_REAL_ESTATE_CURRENT_VALUE.name: CurrencyConverter(),
            ASSETS_REAL_ESTATE_CAPITALIZED.name: CurrencyConverter(),
            ASSETS_REAL_ESTATE_TOTAL_NET.name: CurrencyConverter(),
            ASSETS_REAL_ESTATE_TOTAL_GROSS.name: CurrencyConverter(),
            # Debt Inventory
            DEBT_BUSINESS.name: CurrencyConverter(),
            DEBT_PRIVATE.name: CurrencyConverter(),
            DEBT_MORTGAGES.name: CurrencyConverter(),
            DEBT_TOTAL.name: CurrencyConverter(),
            DEBT_DETAIL_LINES.name: DEBT_DETAIL_LINES_CONVERTER,
            DEBT_DETAIL_LINES_2.name: DEBT_DETAIL_LINES_CONVERTER,
            # Property stuff
            ADDRESS_REAL_ESTATE_PRIMARY.name: DoNothingConverter(),
            FIELD_PROPERTY_TYPE.name: DoNothingConverter(),
            FIELD_PROPERTY_YEAR.name: YearConverter(),
            FIELD_PROPERTY_PURCHASE_YEAR.name: YearConverter(),
            FIELD_PROPERTY_IMPUTED_RENTAL_VALUE.name: CurrencyConverter(),
            FIELD_PROPERTY_IMPUTED_RENTAL_VALUE_CANTON.name: CurrencyConverter(),
            FIELD_PROPERTY_MAINTENANCE_COST.name: CurrencyConverter(),
        }
    )


@dataclass
class TemplateTaxDeclarationPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.TAX_DECLARATION_MISC
    doc_cat: DocumentCat = DocumentCat.TAX_DECLARATION

    canton: str = None

    se: TaxDeclarationSearchElements = None

    tax_code_map: Dict = None
    tax_code_map_field_vertical_line_scale: float = 3.5

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_TAX_DECLARATION.keys())
        if self.canton:
            self.search_elements.append(
                SearchElementConstant(FIELD_CANTON_SHORT.name, self.canton)
            )

        if self.tax_code_map:
            se = create_search_elements_by_tax_code(
                self.page,
                fields_map=self.tax_code_map,
                field_vertical_line_scale=self.tax_code_map_field_vertical_line_scale,
                column_code=COLUMN_CODE,
                column_value=COLUMN_VALUE,
            )
            self.search_elements += se
