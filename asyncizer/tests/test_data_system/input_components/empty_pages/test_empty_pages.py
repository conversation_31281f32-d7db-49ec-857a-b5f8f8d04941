from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/empty_pages"
)
dest_folder_prefix = "input_system_components_empty_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_quite_empty():
    target_folder = path_root_folder / "quite_empty_page"
    assert target_folder.exists()
    # Caution, there is one file that this file is not too small anymore
    # because PDF limit for small files filter was changed from 7kb to 1 kb
    target_file = (
        target_folder / "quite_empty_page_but_larger_than_small_files_filter.pdf"
    )
    assert target_file.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN: 10}
        ),
    ).run()
