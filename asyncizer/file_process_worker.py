import asyncio
import json
import logging
import os
import platform
import sys
import uuid
from contextvars import <PERSON>text<PERSON><PERSON>
from time import time
from typing import Dict

import aio_pika
import structlog
from sentry_sdk import start_transaction, set_context
from structlog.contextvars import bound_contextvars
from tblib import pickling_support

import global_settings
from antivirus.schemas import VirusScanResponse
from antivirus.service import handle_antivirus_requests
from asyncizer.file_process_service import (
    preprocess_and_unpack_original_file,
    publish_file_extracted_exceptions,
    MyEncoder,
    handle_retrials_of_processing,
    process_extracted_files,
)
from asyncizer.schemas import FileExtractedV1, ProcessOriginalFileRequestV1
from asyncizer.log_configuration import configure_structlog
from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.publisher import RabbitMQDossierEventPublisher
from asyncizer.randomword import randomword
from asyncizer.rpc_pika import get_rpc_client
from asyncizer.s3 import create_presigned_url
from asyncizer.small_files_filter import DEFAULT_SMALL_FILES_FILTER
from asyncizer.utils import async_log_timing, get_timing_summary
from hypodossier.unpack import FileExtraction

pickling_support.install()

logger = structlog.getLogger(__name__)

ASYNC_ORIGINAL_FILE_PROCESSOR_WORKER_V1_ROUTING_KEY = os.getenv(
    "ASYNC_ORIGINAL_FILE_PROCESSOR_WORKER_V1_ROUTING_KEY",
    "fileprocessworker.ProcessOriginalFileRequestV1",
)

_connection_pool = None


async def get_connection_pool():
    global _connection_pool

    async def get_connection() -> aio_pika.Connection:
        return await aio_pika.connect_robust(
            global_settings.RABBIT_URL,
            client_properties={
                "connection_name": f"file_process_worker_{platform.node()}"
            },
        )

    if _connection_pool is None:
        _connection_pool = aio_pika.pool.Pool(get_connection, max_size=2)
    return _connection_pool


_channel_pool = None


async def get_channel_pool():
    global _channel_pool

    async def get_channel() -> aio_pika.Channel:
        connection_pool = await get_connection_pool()
        async with connection_pool.acquire() as connection:
            return await connection.channel()

    if _channel_pool is None:
        _channel_pool = aio_pika.pool.Pool(get_channel, max_size=5)
    return _channel_pool


async def process_message(message: aio_pika.IncomingMessage):

    with bound_contextvars(cid=randomword(7)):
        start_message_processing = time()
        try:
            async with message.process(ignore_processed=True):
                if message.redelivered:
                    await logger.awarning("message redelivered", message=message)
                    await message.reject(requeue=False)
                    return

                await get_rpc_client()

                channel_pool = await get_channel_pool()

                event_publisher = RabbitMQDossierEventPublisher(
                    message.reply_to, channel_pool
                )

                processor = OriginalFileRequestProcessor(
                    dossier_event_publisher=event_publisher,
                    small_files_filter=DEFAULT_SMALL_FILES_FILTER,
                    config=OriginalFileProcessorConfig(settings=global_settings),
                )

                request = ProcessOriginalFileRequestV1.parse_raw(message.body)

                with start_transaction(
                    op="queue.process", name="process_original_file_request_v1"
                ):
                    set_context(
                        "original_file",
                        {
                            "original_file_uuid": request.original_file_uuid,
                            "dossier_uuid": request.dossier_uuid,
                            "account_name": request.account_name,
                        },
                    )
                    await processor.process_original_file_request_v1(
                        request, int(message.headers.get("x-dlx-count", 0))
                    )

        except aio_pika.exceptions.ChannelInvalidStateError:
            await logger.awarning(
                "Channel invalid, terminating message processing",
            )
            # Let docker restart the container and have the message reprocessed
            # by another worker
            sys.exit("Channel invalid, terminating message processing")

        except Exception as e:
            await logger.aexception(
                "error during processing of message", e=e, exc_info=True
            )
            # Exit retry loop for non-channel related errors
            raise e
        finally:
            await logger.ainfo(
                "processing of message finished",
                duration=time() - start_message_processing,
            )


class OriginalFileProcessorConfig:
    def __init__(self, settings: global_settings):
        self.ENABLE_ANY_ANTIVIRUS = settings.ENABLE_ANY_ANTIVIRUS
        self.ENABLE_IMAGEPREDICT_PROCESSING = settings.ENABLE_IMAGEPREDICT_PROCESSING
        self.IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD = (
            settings.IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD
        )
        self.USE_IMAGEPREDICT_DEBUG_PREFIX = settings.USE_IMAGEPREDICT_DEBUG_PREFIX
        self.ENABLE_ANTIVIRUS_RAISE_EXCEPTION = (
            settings.ENABLE_ANTIVIRUS_RAISE_EXCEPTION
        )
        self.ORIGINAL_FILE_PROCESSOR_BUCKET = settings.ORIGINAL_FILE_PROCESSOR_BUCKET


class OriginalFileRequestProcessor:
    def __init__(
        self,
        dossier_event_publisher,
        small_files_filter,
        config: OriginalFileProcessorConfig,
    ):
        self.dossier_event_publisher = dossier_event_publisher
        self.small_files_filter = small_files_filter
        self.config = config
        self.timing_context: ContextVar[Dict[str, float]] = ContextVar(
            "timing_context", default={}
        )

    async def process_original_file_request_v1(
        self, request: ProcessOriginalFileRequestV1, trial: int
    ):
        # Reset timing context at the start of main process
        self.timing_context.set({})

        if not await self._handle_retrials(request, trial):
            # We give up on processing, error event has already been sent
            return

        try:
            await logger.ainfo(
                "processing original file request", request=request.dict()
            )

            processing_config = (
                request.processing_config or OriginalFileProcessingConfig()
            )
            enable_virus_scan = (
                self.config.ENABLE_ANY_ANTIVIRUS
                and processing_config.enable_virus_scan is True
            )

            await self._log_configuration(enable_virus_scan)

            if await self._scan_original_file(request, enable_virus_scan):
                # Error for original file was already sent so we continue
                # without any extractions or exceptions

                # PG: Not sure if we should do this - publish an empty result as we currently do
                await publish_file_extracted_exceptions(
                    dossier_event_publisher=self.dossier_event_publisher,
                    request=request,
                    file_extraction=FileExtraction(
                        original_file=request.filename,
                        extracted_files=[],
                        exceptions=[],
                    ),
                    extracted_files={},
                )
                return

            file_extraction = await preprocess_and_unpack_original_file(
                request=request,
                file_url=request.file_url,
                filename=request.filename,
                small_files_filter=self.small_files_filter,
            )
            extracted_files = await self._process_extracted_files(
                request, file_extraction, enable_virus_scan
            )

            await self._publish_file_extracted_exceptions(
                request, file_extraction, extracted_files
            )
            await self._process_files(
                request, processing_config, file_extraction, extracted_files
            )

        except Exception as e:
            await logger.aexception(
                "exception in file_process_worker", e=e, exc_info=True
            )

        finally:
            # Wait here for a short time as this message is very short it would otherwisse
            # normally overtake the bigger ProcessingResult message. This is handled properly by DLX but
            # we try to deliver messages in proper order (0.5 is not enough in some cases)
            await asyncio.sleep(2.0)

            # Log execution times before the sleep
            logger.info(
                "file processing completed",
                execution_times=get_timing_summary(self.timing_context),
                dossier_uuid=request.dossier_uuid,
                original_file_uuid=request.original_file_uuid,
            )

            await self.dossier_event_publisher.publish(
                "DossierEvent.ProcessOriginalFileFinished",
                json.dumps(
                    {
                        "dossier_uuid": request.dossier_uuid,
                        "original_file_uuid": request.original_file_uuid,
                    },
                    cls=MyEncoder,
                ).encode(),
            )

    @async_log_timing
    async def _handle_retrials(
        self, request: ProcessOriginalFileRequestV1, trial: int
    ) -> bool:
        return await handle_retrials_of_processing(
            self.dossier_event_publisher, request, trial, max_num_trials=3
        )

    async def _log_configuration(self, enable_virus_scan: bool):
        await logger.ainfo(
            "configuration",
            ENABLE_IMAGEPREDICT_PROCESSING=self.config.ENABLE_IMAGEPREDICT_PROCESSING,
            IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD=self.config.IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD,
            USE_IMAGEPREDICT_DEBUG_PREFIX=self.config.USE_IMAGEPREDICT_DEBUG_PREFIX,
            enable_virus_scan=enable_virus_scan,
        )

    @async_log_timing
    async def _scan_original_file(
        self, request: ProcessOriginalFileRequestV1, enable_virus_scan: bool
    ) -> bool:
        if not enable_virus_scan:
            return False

        vsr_original: VirusScanResponse = await handle_antivirus_requests(
            file_url=request.file_url,
            dossier_uuid=request.dossier_uuid,
            dossier_event_publisher=self.dossier_event_publisher,
            message_type="DossierEvent.OriginalFileProcessingError",
            file_uuid=request.original_file_uuid,
            raise_exception=self.config.ENABLE_ANTIVIRUS_RAISE_EXCEPTION,
            context=f"original_file_uuid={request.original_file_uuid}",
        )
        return vsr_original and vsr_original.detection_result is True

    @async_log_timing
    async def _process_extracted_files(
        self,
        request: ProcessOriginalFileRequestV1,
        file_extraction: FileExtraction,
        enable_virus_scan: bool,
    ) -> Dict[str, FileExtractedV1]:
        extracted_files: Dict[str, FileExtractedV1] = {}
        for extracted_file_path in file_extraction.extracted_files:
            file_extracted_event = await self._create_file_extracted_event(
                request, extracted_file_path
            )
            extracted_files[extracted_file_path] = file_extracted_event

            if enable_virus_scan and await self._scan_extracted_file(
                request, file_extracted_event
            ):
                file_extraction.extracted_files.remove(extracted_file_path)

        return extracted_files

    @async_log_timing
    async def _create_file_extracted_event(
        self, request: ProcessOriginalFileRequestV1, extracted_file_path: str
    ) -> FileExtractedV1:
        extractedfile_objectname = (
            f"{request.dossier_uuid}/extracted_files/{extracted_file_path}"
        )
        file_url_extracted = create_presigned_url(
            self.config.ORIGINAL_FILE_PROCESSOR_BUCKET,
            extractedfile_objectname,
        )

        file_extracted_event = FileExtractedV1(
            dossier_uuid=uuid.UUID(request.dossier_uuid),
            original_file_uuid=uuid.UUID(request.original_file_uuid),
            file_url=file_url_extracted,
            extracted_file_uuid=uuid.uuid4(),
            path_from_original=extracted_file_path,
        )
        await self.dossier_event_publisher.publish(
            "DossierEvent.FileExtractedV1", file_extracted_event.json().encode()
        )
        return file_extracted_event

    @async_log_timing
    async def _scan_extracted_file(
        self,
        request: ProcessOriginalFileRequestV1,
        file_extracted_event: FileExtractedV1,
    ) -> bool:
        vsr_extracted: VirusScanResponse = await handle_antivirus_requests(
            file_url=file_extracted_event.file_url,
            dossier_uuid=request.dossier_uuid,
            dossier_event_publisher=self.dossier_event_publisher,
            message_type="DossierEvent.FileExtractedExceptionV1",
            file_uuid=str(file_extracted_event.extracted_file_uuid),
            raise_exception=self.config.ENABLE_ANTIVIRUS_RAISE_EXCEPTION,
            context=f"original_file_uuid={request.original_file_uuid}, extracted_file_uuid={file_extracted_event.extracted_file_uuid}",
        )
        return vsr_extracted and vsr_extracted.detection_result is True

    @async_log_timing
    async def _publish_file_extracted_exceptions(
        self,
        request: ProcessOriginalFileRequestV1,
        file_extraction: FileExtraction,
        extracted_files: Dict[str, FileExtractedV1],
    ):
        await publish_file_extracted_exceptions(
            self.dossier_event_publisher, request, file_extraction, extracted_files
        )

    @async_log_timing
    async def _process_files(
        self,
        request: ProcessOriginalFileRequestV1,
        processing_config: OriginalFileProcessingConfig,
        file_extraction: FileExtraction,
        extracted_files: Dict[str, FileExtractedV1],
    ):
        await process_extracted_files(
            self.dossier_event_publisher,
            request,
            processing_config,
            file_extraction,
            extracted_files,
        )


async def main():

    channel_pool = await get_channel_pool()
    channel: aio_pika.Channel

    async with channel_pool.acquire() as channel:
        await channel.set_qos(prefetch_count=1)
        queue = await channel.declare_queue(
            ASYNC_ORIGINAL_FILE_PROCESSOR_WORKER_V1_ROUTING_KEY, durable=True
        )
        await queue.consume(process_message)

    # Run until terminate
    await asyncio.Future()


def asyncio_run_main():
    # So we can run via watchgod via `watchgod -w . -- python -m asyncizer.file_process_worker.asyncio_run_main`
    configure_structlog()

    logging.getLogger("aio_pika").setLevel("INFO")

    logger.info("Started file process worker")
    logger.info(
        "configuration",
        ENABLE_IMAGEPREDICT_PROCESSING=global_settings.ENABLE_IMAGEPREDICT_PROCESSING,
        IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD=global_settings.IMAGEPREDICT_MIN_VISIBLE_CONFIDENCE_THRESHOLD,
        USE_IMAGEPREDICT_DEBUG_PREFIX=global_settings.USE_IMAGEPREDICT_DEBUG_PREFIX,
        ENABLE_ANTIVIRUS_CLAMAV=global_settings.ENABLE_ANTIVIRUS_CLAMAV,
        ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER=global_settings.ENABLE_ANTIVIRUS_MICROSOFT_DEFENDER,
        ENABLE_ANTIVIRUS_RAISE_EXCEPTION=global_settings.ENABLE_ANTIVIRUS_RAISE_EXCEPTION,
    )

    asyncio.run(main())


if __name__ == "__main__":
    asyncio_run_main()
