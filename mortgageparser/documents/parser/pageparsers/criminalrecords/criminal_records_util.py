from mortgageparser.documents.parser.pageparsers.criminalrecords.CriminalRecordsInfoPagePageParser import (
    CriminalRecordsInfoPagePageParser,
)

from mortgageparser.documents.parser.pageparsers.criminalrecords.CriminalRecordsPageParserCard import (
    CriminalRecordsPageParserCard,
)
from mortgageparser.documents.parser.pageparsers.criminalrecords.CriminalRecordsPageParserFullPage import (
    CriminalRecordsPageParserFullPage,
)


def get_parsers_criminal_records():
    parsers = [
        CriminalRecordsInfoPagePageParser(),  # must be before normal parser because the page has so much text
        CriminalRecordsPageParserFullPage(),
        CriminalRecordsPageParserCard(),
    ]
    return parsers
