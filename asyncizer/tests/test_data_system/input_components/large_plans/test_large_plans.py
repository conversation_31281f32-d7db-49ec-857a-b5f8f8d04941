import shutil
from pathlib import Path
from subprocess import TimeoutExpired
from typing import List

import pytest

from asyncizer.pdf_split import process_pdftocairo_pdf_fix
from asyncizer.tests.util_tests import util_test_batch, DossierExpectations, DossierTest
from global_settings import (
    PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS,
    ENABLE_DETECTRON2_OBJECT_DETECTION,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.util.ghostscript_util import gs_convert_pdf_to_jpg

import structlog

logger = structlog.getLogger(__name__)

dest_folder_prefix = "input_system_components_large_plans_"


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_large_plan_single_big_page(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=False,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/single_big_page"
        ),
        source_file_filter=["single_big_page.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_SITUATION: 1}
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_large_plan_that_fails_on_pdf2jpg(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/large_pdf_plans"
        ),
        source_file_filter=["plans_much_larger_than_a4.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1}
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_large_plan_3_pages_that_fails_on_pdf2jpg(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/3_pages"
        ),
        source_file_filter=["large_plan_3_pages.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_ANY: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.PLAN_FLOOR: 2,
                DocumentCat.PLAN_SITUATION: 1,
            },
        ),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_large_plan_mid_size(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/2mb_mid_size"
        ),
        dossier_expectations=DossierExpectations(),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_morelarge_plans_ultrawide(
    override_existing=True,
    show_page=-1,
    show_filename=None,
    webbrowser=True,
    use_ocr_cache=True,
):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/230421_more_plans"
        ),
        source_file_filter=["R10d_Tiefgarage.pdf"],
        dossier_expectations=DossierExpectations(),
        show_page=show_page,
        show_filename=show_filename,
        webbrowser=webbrowser,
        use_ocr_cache=use_ocr_cache,
    )


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_morelarge_plans_all():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check image objects")
    await DossierTest(
        override_existing=True,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/230421_more_plans"
        ),
        # source_file_filter=["R10d_Tiefgarage.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_FLOOR: 3}
        ),
    ).run()


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_rotation():
    await DossierTest(
        override_existing=True,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/rotation"
        ),
        # source_file_filter=["R10d_Tiefgarage_landscape_from_left.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_FLOOR: 4}
        ),
    ).run()


@pytest.mark.asyncio
async def test_rotation_single(
    override_existing=True, show_page=-1, webbrowser=True, use_ocr_cache=False
):
    await DossierTest(
        use_ocr_cache=use_ocr_cache,
        override_existing=override_existing,
        webbrowser=webbrowser,
        show_page=show_page,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/rotation"
        ),
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["R10d_Tiefgarage_landscape_from_right.pdf"],
        dossier_expectations=DossierExpectations(),
    ).run()


def test_large_plan_a1_pdf2jpg():
    source_folder = (
        Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/large_plan_a1"
        )
        / "original"
    )
    source_file = source_folder / "23_5_23_Voranfrage_Umgebung_A1.pdf"
    assert source_file.exists()
    destination_folder = source_folder.parent / "output"
    if destination_folder.exists():
        shutil.rmtree(str(destination_folder))
        # os.rmdir(str(destination_folder))
    destination_folder.mkdir(parents=False, exist_ok=False)

    # Try 1: directly convert with ghostscript (without repairing): works
    urls_with_gs: List[Path] = gs_convert_pdf_to_jpg(
        input_path=source_file,
        temp_image_dir_path=destination_folder,
        dpi=72,
        timeout=15,
    )
    assert urls_with_gs

    # Try 2: convert with ghostscript after repairing: does NOT work
    repaired_file = process_pdftocairo_pdf_fix(source_file, destination_folder)
    with pytest.raises(TimeoutExpired):
        urls_repaired: List[Path] = gs_convert_pdf_to_jpg(
            input_path=repaired_file,
            temp_image_dir_path=destination_folder,
            dpi=72,
            timeout=15,
        )
        assert urls_repaired


@pytest.mark.asyncio
async def test_large_plan_a1(
    override_existing=True, show_page=-1, webbrowser=True, use_ocr_cache=False
):
    """
    This pdf to jpg conversion fails with pdf2img / poppler, even with timeout 60 sec and unlimited cpu, ram.
    So it requires the fallback to ghostscript (or pypdfium2)

    @param override_existing:
    @param show_page:
    @param webbrowser:
    @param use_ocr_cache:
    @return:
    """

    await DossierTest(
        use_ocr_cache=use_ocr_cache,
        override_existing=override_existing,
        webbrowser=webbrowser,
        show_page=show_page,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/large_plan_a1"
        )
        / "original",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["23_5_23_Voranfrage_Umgebung_A1.pdf"],
        dossier_expectations=DossierExpectations(),
    ).run()


@pytest.mark.asyncio
async def test_231004_2mb_bekb(
    override_existing=True, show_page=-1, webbrowser=True, use_ocr_cache=True
):
    await DossierTest(
        use_ocr_cache=use_ocr_cache,
        override_existing=override_existing,
        webbrowser=webbrowser,
        show_page=show_page,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/231004_2MB_BEKB"
        ),
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_ANY: 1}
        ),
    ).run()


@pytest.mark.asyncio
# works for MT @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_large_plan_with_poppler_timeout():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/large_plans/large_plan_with_poppler_timeout/22"
        ),
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=[
            "22.pdf"
        ],  # ignore the file 22_repairedcairo.pdf if it exists
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_FLOOR: 1},
        ),
    ).run()
