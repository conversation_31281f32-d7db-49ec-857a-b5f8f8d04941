import shutil
from pathlib import Path
from tempfile import TemporaryDirectory

import pytest
import structlog
from freezegun import freeze_time

from asyncizer.dossier_processor import async_unpack_path
from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.unpack import FileExtraction

logger = structlog.getLogger(__name__)

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/email_messages_nested_small_synthetic"
)
dest_folder_prefix = (
    "input_system_components_email_processor_email_messages_nested_small_synthetic"
)


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_inner_eml():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "inner_email/eml",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE Abrechnung_Nebenkosten_2023.pdf",
                "001 DOK DE Wärmespeicher Wärmepumpe vor Nachfüllen.pdf",
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch 30.05.2025.pdf",
            ],
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.BEKB_FIPLA_FORM: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_inner_msg():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "inner_email/msg",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE Abrechnung_Nebenkosten_2023.pdf",
                "001 DOK DE Wärmespeicher Wärmepumpe vor Nachfüllen.pdf",
                "738 Email-Korrespondenz manuel.thiemann_at_hypodossier.ch 30.05.2025.pdf",
            ],
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.BEKB_FIPLA_FORM: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()


@freeze_time("2023-01-01 12:00:00")
@pytest.mark.asyncio
async def test_email_outer_msg_inner_msg_unpack():
    """
    We need to freeze the time to have the timestamp of the email fixed
    for the comparison.
    :return:
    """
    source_file = (
        path_root_folder
        / "outer_email/outer_msg_inner_msg/Outer Email in nested email test.msg"
    )
    assert source_file.exists()

    with TemporaryDirectory() as tmpdir:
        target_file = Path(tmpdir) / source_file.name
        shutil.copy(source_file, target_file)
        assert target_file.exists()
        file_ex: FileExtraction = await async_unpack_path(target_file)
        logger.info("found file: %s", file_ex)
        for ef in file_ex.extracted_files:
            logger.info("extracted file: %s", ef)

        assert sorted(file_ex.extracted_files) == sorted(
            [
                "Outer Email in nested email test.msg/HypoDossier_Extracted_Email_Body_20230101_120000_74c82921e49f919940f5db8521917a698647e23a18317c5a008648583cbb2e0e.pdf",
                "Outer Email in nested email test.msg/Wärmespeicher Wärmepumpe vor Nachfüllen.jpg",
                "Outer Email in nested email test.msg/bank bekb.pdf",
                "Outer Email in nested email test.msg/inner test email for nested test.msg/Abrechnung_Nebenkosten_2023.pdf",
                "Outer Email in nested email test.msg/inner test email for nested test.msg/HypoDossier_Extracted_Email_Body_20230101_120000_ec23541518bd8278ccf8558da5c2c3d92d5a254e79887c8fb5bc3abcbf6a7c34.pdf",
                "Outer Email in nested email test.msg/inner test email for nested test.msg/Wärmespeicher Wärmepumpe vor Nachfüllen.jpg",
            ]
        )


@pytest.mark.asyncio
async def test_email_outer_msg_inner_msg():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "outer_email/outer_msg_inner_msg",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # 5 documents, of which 2 emails,
                # 1 attachment de-duplicated,
                # 1 attachmentis in outer email
                # 1 attachment in inner email
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 2,
                DocumentCat.BEKB_FIPLA_FORM: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()


@pytest.mark.skip("TODO: fix nested eml mail parsing")
@pytest.mark.asyncio
async def test_email_outer_msg_inner_eml():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "outer_email/outer_msg_inner_eml",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # 5 documents, of which 2 emails,
                # 1 attachment de-duplicated,
                # 1 attachmentis in outer email
                # 1 attachment in inner email
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 2,
                DocumentCat.BEKB_FIPLA_FORM: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()


@pytest.mark.skip("TODO: fix nested eml mail parsing")
@pytest.mark.asyncio
async def test_email_outer_eml_inner_msg():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "outer_email/outer_eml_inner_msg",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # 5 documents, of which 2 emails,
                # 1 attachment de-duplicated,
                # 1 attachmentis in outer email
                # 1 attachment in inner email
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 2,
                DocumentCat.BEKB_FIPLA_FORM: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()


# @pytest.mark.skip("TODO: fix nested eml mail parsing")
@pytest.mark.asyncio
async def test_email_outer_eml_inner_eml():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "outer_email/outer_eml_inner_eml",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                # 5 documents, of which 2 emails,
                # 1 attachment de-duplicated,
                # 1 attachmentis in outer email
                # 1 attachment in inner email
                DocumentCat.BANK_DOCUMENT: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 2,
                DocumentCat.BEKB_FIPLA_FORM: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()
