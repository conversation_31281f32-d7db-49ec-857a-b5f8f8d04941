import os
from pathlib import Path

from asyncizer.pdf_fix import process_pdftocairo_pdf_fix_by_image_export

if __name__ == "__main__":
    """
    Helper script to fix corrupt PDF files. Exports all pages in temp folder to JPEG,
    then creates a new PDF file from the images. This gets rid of syntax problems
    in the PDF. Target file is created in same folder as input with suffix
    "_repairedcairoimg.pdf".
    """
    path = Path(os.getenv("FILE", default="/home/<USER>/Downloads/Kaufvertrag_2011.pdf"))

    assert path
    assert path.exists(), f"Could not find file {path}"

    output_path_dir = path.parent

    result1 = process_pdftocairo_pdf_fix_by_image_export(path, output_path_dir)
    assert result1.exists()
