from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON><PERSON><PERSON><PERSON><PERSON>, DossierExpectations
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL}/pdf_fix"
)
dest_folder_prefix = "input_system_components_local_pdf_fix_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_660_mortgage_contract_lukb():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "660_mortgage_contract_lukb",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # This works, document is not expected to be recognized
        ),
    ).run()


@pytest.mark.asyncio
async def test_660_pymupdf_issue():
    await DossierTest(
        use_ocr_cache=False,
        override_existing=True,
        source_folder=path_root_folder / "pymupdf_issue",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # This works, the problem was that Ghostscript rotated the document
            # from A4 landscape to US Letter Portrait during the pdf_fix before
            # the splitting. So the result should be A4 Landscape
        ),
    ).run()
