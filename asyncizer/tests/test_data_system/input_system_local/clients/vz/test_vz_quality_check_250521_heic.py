from pathlib import Path

import pytest
import structlog

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250521_heic"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/vz/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_vz_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_heic_1_msg():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "msg",
        source_file_filter=["4_5_Zimmer-Wohnung_1.msg"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PROPERTY_PHOTOS: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PROPERTY_PHOTOS: 3,
            },
        ),
    ).run()


async def test_heic_1_eml():
    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "eml",
        source_file_filter=["4_5_Zimmer-Wohnung_1.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PROPERTY_PHOTOS: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.PROPERTY_PHOTOS: 3,
            },
        ),
    ).run()
