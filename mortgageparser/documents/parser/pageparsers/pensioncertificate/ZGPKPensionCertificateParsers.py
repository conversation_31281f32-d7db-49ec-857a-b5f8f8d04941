from abbyyplumber.converter.ValueConverter import DateConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementStaticText,
    SearchElementLabeledField,
    SearchElementReference,
    SearchElementArea,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationLeftOf,
    SearchRelationBelow,
    SearchRelationRightOf,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_DOCUMENT_DATE,
    FIELD_AHV_NEW,
    FIELD_PERSON_ID,
    FIELD_FIRSTNAME,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import (
    StandardPensionCertificatePageParser,
    StandardPensionCertificatePageOnePageParser,
    StandardPensionCertificatePageTwoPageParser,
)
from mortgageparser.util.search_element_util import (
    create_element_company,
    create_search_elements_address,
)
from mortgageparser.util.string_utils import contains_all_strings


class ZGPKPensionCertificateLetterPageParser(StandardPensionCertificatePageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.PENSION2_CERTIFICATE_LETTER

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "An die Versicherten",
                "Ihr Versicherungsausweis",
                "Sehr geehrte",
                "Sie erhalten jeweils",
            ],
        ) or contains_all_strings(
            text,
            [
                "Jahresabschluss",
                "www.zugerpk.ch",
                "Wir sind gerne für Sie da",
                "Ihre Zuger Pensionskasse",
            ],
        )
        return success


class ZGPKPensionCertificatePageOnePageParser(
    StandardPensionCertificatePageOnePageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Zuger Pensionskasse",
                "Versicherungsausweis",
                "www.zugerpk.ch",
                "Persönliche Daten",
                "Risikoleistungen",
            ],
        )

    def parse_page_header(self):
        success = self.page.set_header_by_text(
            "Versicherungsausweis", include_pattern=False
        )
        if not success:
            success = self.page.set_header_by_text(
                "Persönliche Daten", include_pattern=False
            )
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text("Sparkapital")
        return success

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementStaticText(
                    "text_addressblock_above",
                    self.page.header,
                    label="Zug, Postfach",
                    extract=False,
                ),
                SearchElementLabeledField(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.header,
                    label="Zug,",
                    max_l_dist=0,
                    relations=[
                        SearchRelationRightOf("text_addressblock_above"),
                        SearchRelationBelow("text_addressblock_above"),
                    ],
                    converter=DateConverter(),
                ),
                SearchElementArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.header,
                    x_range=PercentageRange(0, 0.5),
                    relations=[
                        SearchRelationBelow("text_addressblock_above"),
                        SearchRelationLeftOf(FIELD_DOCUMENT_DATE.name),
                    ],
                ),
                SearchElementLabeledField(
                    FIELD_AHV_NEW.name,
                    self.page.main,
                    label="AHV-Nummer",
                    field_vertical_line_scale=2,
                ),
                SearchElementReference(FIELD_PERSON_ID.name, ref=FIELD_AHV_NEW.name),
                create_element_company("ZGPK"),
            ]
            + create_search_elements_address(self.page.header, self.page)
        )


class ZGPKPensionCertificatePageTwoPageParser(
    StandardPensionCertificatePageTwoPageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "www.zugerpk.ch",
                "Vers.-Nr.",
                "Versicherungsausweis",
                "Freiwilliger Einkauf",
                "Altersrente",
                "und der jeweils gültige Vorsorgeplan",
            ],
        )
