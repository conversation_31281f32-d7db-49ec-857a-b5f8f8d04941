from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, PO_EXISTS, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

doccat = "410_pension_certificate"

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{doccat}"
path_root_folder = Path(folder)
dest_folder_prefix = f"input_system_local_doccat_{doccat}_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_batch_01_bvk():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "batch_01_misc",
        source_file_filter=["bvk_de_2017.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "28.02.2017",
                    "fullname": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "date_of_birth": "13.02.1981",
                    "degree_employment": "37,93%",
                    "address_block": PO_EXISTS,
                    "company": "BVK",
                    "employer": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 42'215",
                    "current_assets": "CHF 85'886",
                    "projected_assets_retirement": "CHF 469'211",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_batch_01_complan_de():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "batch_01_misc",
        source_file_filter=["complan_de_2016.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "18.04.2016",
                    "fullname": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "date_of_birth": "11.12.1979",
                    "ahv_new": PO_EXISTS,
                    "marital_status": "Verheiratet",
                    "degree_employment": "100.00%",
                    "address_block": PO_EXISTS,
                    "company": "comPlan",
                    "employer": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 116'600",
                    "current_assets": "CHF 104'733",
                    "projected_assets_retirement": "CHF 1'152'034",
                    "projected_pension_retirement": "CHF 70'389",
                    "wef_pledging_possible_amount": "CHF 104'733",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_batch_01_publica_de():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "batch_01_misc",
        show_page=0,
        source_file_filter=["publica_de_2017_clean_2_pages.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "01.01.2017",
                    "fullname": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "date_of_birth": "25.08.1970",
                    "ahv_new": PO_EXISTS,
                    "degree_employment": "100.00%",
                    "address_block": PO_EXISTS,
                    "company": "Publica",
                    "product": "Kaderplan I",
                    "employer": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 186'561",
                    "current_assets": "CHF 299'573",
                    "projected_assets_retirement": "CHF 1'539'434",
                    "projected_pension_retirement": "CHF 86'978",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_batch_01_swisscanto_de():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "batch_01_misc",
        source_file_filter=["swisscanto_de_2017.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "01.07.2017",
                    "fullname": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "date_of_birth": "03.09.1980",
                    "marital_status": "verheiratet",
                    "degree_employment": "100.00%",
                    "address_block": PO_EXISTS,
                    "company": "Swisscanto",
                    "applicable_annual_salary_declared": "CHF 102'722",
                    "current_assets": "CHF 67'860",
                    "projected_assets_retirement": "CHF 574'909",
                    "withdrawal_benefit": "CHF 63'372",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_batch_01_sl():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "batch_01_misc",
        source_file_filter=["sl_de_2017.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "01.01.2017",
                    "fullname": PO_EXISTS,
                    "date_of_birth": "03.04.1957",
                    "ahv_new": PO_EXISTS,
                    "degree_employment": "100.00%",
                    "company": "SwissLife",
                    "product": "Geschäftsleitung",
                    "employer": PO_EXISTS,
                    "applicable_annual_salary_declared": "CHF 217'815",
                    "current_assets": "CHF 1'078'841",
                    "projected_assets_retirement": "CHF 2'156'468",
                    "projected_pension_retirement": "CHF 129'388",
                    "wef_pledging_registered_status": "nein",
                    "wef_pledging_possible_amount": "CHF 872'801",
                    "withdrawal_benefit": "CHF 1'745'602",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_batch_01_vita_de_2016():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder / "batch_01_misc",
        source_file_filter=["vita_de_2016.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "01.01.2016",
                    "fullname": PO_EXISTS,
                    "date_of_birth": "23.11.1971",
                    "ahv_new": PO_EXISTS,
                    "marital_status": "verheiratet",
                    "degree_employment": "100.00 %",
                    "company": "Vita",
                    "applicable_annual_salary_declared": "CHF 114'400",
                    "current_assets": "CHF 105'538",
                    "projected_assets_retirement": "CHF 375'291",
                    "wef_pledging_registered_status": "Nein",
                    "withdrawal_benefit": "CHF 116'999",
                }
            },
            expected_doc_cat_frequency={DocumentCat.PENSION_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()
