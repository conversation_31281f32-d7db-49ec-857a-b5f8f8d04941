from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.landregister.land_register_util import (
    TemplateExtractLandRegisterPageParser,
)


def get_parsers_land_register_misc():
    parsers = [
        TemplateExtractLandRegisterPageParser(
            doc_cat=DocumentCat.LAND_REGISTER_MISC,
            page_cat=PageCat.GENERIC_PAGE,
            desc="Generic Parser for land register letters to remove them from other classification attempts",
            required_text_conditions=[
                FromStartTextCond("Grundbuchamt", num_lines=3),
            ],
            required_tokens=["Sehr geehrte", "Freundliche Grüsse"],
        ),
    ]

    return parsers
