from dataclasses import dataclass

from abbyyplumber.converter.ValueConverter import DateConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    Search<PERSON>lementArea,
    SearchElementLabeledField,
    SearchElementStaticText,
    SearchElementReference,
    SearchElementMultiStaticText,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    ReferenceBoundaryVertical,
    SearchRelationAbove,
)
from abbyyplumber.util.plumberstudio_util import FieldPosition
from hypodossier.core.documents.debtcollectioninformation.DebtCollectionPageData import (
    DCI_FIELD_CONFIRMATION_NOT_EMPTY,
    DCI_FIELD_ADDRESS_LINE,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_DATE_OF_BIRTH,
    FIELD_DOCUMENT_DATE,
)
from mortgageparser.documents.parser.pageparsers.debtcollectioninformation.DebtCollectionInformationPageParser import (
    DebtCollectionInformationPageOnePageParser,
)
from mortgageparser.util.string_utils import (
    contains_at_least_one_string,
    contains_all_strings,
)


@dataclass
class DebtCollectionInformationPageOneNotEmptyFRPageParser(
    DebtCollectionInformationPageOnePageParser
):
    tokens = [
        "Nous attestons que, sauf erreur",
        "ou omission, les actes de poursuites énumérés",
        " ci-après sont enregistrés auprès",
    ]
    token2b = "actes de poursuites énumérés ci-après sont enregistrés"

    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(text, self.tokens)

    def parse_page_header(self):
        success = self.page.set_header_by_any_text(
            self.tokens, max_l_dist=8, include_pattern=False
        )
        if not success:
            success = self.page.set_header_by_percentage(0.4)
        return success

    def parse_page_footer(self):
        return self.page.set_footer_by_percentage(6 / 27)

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        return ContentExtractor(
            [
                SearchElementLabeledField(
                    "addressline",
                    page.main,
                    label="au nom de",
                    max_l_dist=0,
                    field_position=FieldPosition.RIGHT_OF_LABEL,
                ),
                SearchElementArea(
                    FIELD_DOCUMENT_DATE.name, page.footer, converter=DateConverter()
                ),
                SearchElementStaticText(
                    DCI_FIELD_CONFIRMATION_NOT_EMPTY.name,
                    page.main,
                    label=self.token2b,
                    max_l_dist=8,
                ),
            ]
        )


@dataclass
class DebtCollectionInformationPageOneNotEmptyV2FRPageParser(
    DebtCollectionInformationPageOnePageParser
):
    token2: str = (
        "Nous attestons que, auprès de notre office et au nom de / sous la raison"  # raison de commerce / raison sociale
    )
    token3a: str = (
        "les actes de poursuite énumérés dans les pages qui suivent sont enregistrés"
    )
    token3b: str = (
        "les actes de droit des poursuites énumérés aux pages qui suivent sont enregistrés à l'adresse indiquée."
    )

    token3c: str = (
        "les actes de droit des poursuites énumérés dans les pages qui suivent sont enregistrés à l’adresse indiquée."
    )

    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text, [self.token2]
        ) and contains_at_least_one_string(
            text, [self.token3a, self.token3b, self.token3c]
        )

    def parse_page_header(self):
        success = self.page.set_header_by_any_text(
            [self.token2], max_l_dist=8, include_pattern=False
        )
        if not success:
            success = super().parse_page_header()
        return success

    def parse_page_footer(self):
        return self.page.set_footer_by_percentage(6 / 27)

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        return ContentExtractor(
            [
                SearchElementStaticText("text_above", page.main, label=self.token2),
                SearchElementMultiStaticText(
                    DCI_FIELD_CONFIRMATION_NOT_EMPTY.name,
                    page.main,
                    labels={self.token3a: 7, self.token3b: 7, self.token3c: 7},
                ),
                SearchElementArea(
                    DCI_FIELD_ADDRESS_LINE.name,
                    page.main,
                    relations=[
                        SearchRelationBelow(
                            "text_above", ref_boundary=ReferenceBoundaryVertical.CENTER
                        ),
                        SearchRelationAbove(
                            DCI_FIELD_CONFIRMATION_NOT_EMPTY.name,
                            ref_boundary=ReferenceBoundaryVertical.CENTER,
                        ),
                    ],
                ),
                SearchElementArea(
                    FIELD_DOCUMENT_DATE.name, page.footer, converter=DateConverter()
                ),
                SearchElementReference(
                    FIELD_DATE_OF_BIRTH.name,
                    ref=DCI_FIELD_ADDRESS_LINE.name,
                    converter=DateConverter(),
                ),
            ]
        )
