from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_CHILD_BENEFITS,
    INCOME_PORTFOLIO,
    INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_CH<PERSON><PERSON><PERSON>,
    INCOME_UNDISTRIBUTED_INHERITANCES,
    INCOME_OTHER,
    P1_INCOME_GROSS_TOTAL,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    P1_CONTRIBUTION_PILLAR_1,
    P2_CONTRIBUTION_PILLAR_1,
    P1_CONTRIBUTION_PILLAR_2,
    P2_CONTRIBUTION_PILLAR_2,
    DEDUCTIONS_WEALTH_MANAGEMENT,
    P1_DEDUCTIONS_EDUCATION,
    P2_DEDUCTIONS_EDUCATION,
    DEDUCTIONS_OTHER,
    DEDUCTIONS_TOTAL,
    INCOME_NET_TOTAL,
    EXPENSE_CHILDREN_DAYCARE,
    DEDUCTIONS_DONATIONS,
    DEDUCTIONS_ILLNESS,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_UNDISTRIBUTED_INHERITANCES,
    ASSETS_OTHER,
    ASSETS_REAL_ESTATE_TOTAL_NET,
    ASSETS_GROSS_TOTAL,
    DEBT_PRIVATE,
    DEBT_BUSINESS,
    ASSETS_NET_TOTAL,
    ASSETS_TAXABLE_LOCAL,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_PROPERTY_IMPUTED_RENTAL_VALUE,
    FIELD_PROPERTY_MAINTENANCE_COST,
)

tax_ur_codes_income = {
    "100": P1_INCOME_EMPLOYED_MAIN,
    "101": P2_INCOME_EMPLOYED_MAIN,
    "104": P1_INCOME_EMPLOYED_SIDE,
    "105": P2_INCOME_EMPLOYED_SIDE,
    "110": P1_INCOME_SELF_MAIN,
    "111": P2_INCOME_SELF_MAIN,
    "114": P1_INCOME_SELF_SIDE,
    "115": P2_INCOME_SELF_SIDE,
    # "118": Kaufm. Personengesellschaften - P1,
    # "119": Kaufm. Personengesellschaften - P2,
    "130": P1_INCOME_PENSION_AHV,
    "131": P2_INCOME_PENSION_AHV,
    "132": P1_INCOME_PENSION,
    "133": P2_INCOME_PENSION,
    "134": P1_INCOME_SOCIAL_SECURITY,
    "135": P2_INCOME_SOCIAL_SECURITY,
    "140": P1_INCOME_EO,
    "141": P2_INCOME_EO,
    "145": INCOME_CHILD_BENEFITS,
    "150": INCOME_PORTFOLIO,
    "160": INCOME_ALIMONY_PARTNER,
    "161": INCOME_ALIMONY_CHILDREN,
    "164": INCOME_UNDISTRIBUTED_INHERITANCES,
    # "168": Geldwerter Vorteil bei Benützung eines Geschäftsfahrzeugs usw.,
    "170": INCOME_OTHER,
    "180": FIELD_PROPERTY_IMPUTED_RENTAL_VALUE,
    "181": FIELD_PROPERTY_MAINTENANCE_COST,
    # "182": Einkünfte aus Liegenschaften - Miet- und Pachtzinseinnahmen,
    # "183": Einkünfte aus Liegenschaften - Mietwert bei Nutzniessung,
    # "184": Einkünfte aus Liegenschaften - Wohnrecht,
    "199": P1_INCOME_GROSS_TOTAL,
}

tax_ur_codes_deductions = {
    "240": P1_EXPENSE_EMPLOYMENT,
    "241": P2_EXPENSE_EMPLOYMENT,
    "246": FIELD_PROPERTY_MAINTENANCE_COST,  # Liegenschaftsunterhalt - Prozent von Total Ziffer 6 oder effektive Kosten,
    "250": INTEREST_PAID_ON_DEBT,
    "254": EXPENSE_ALIMONY_PARTNER,
    "255": EXPENSE_ALIMONY_CHILDREN,
    "256": EXPENSE_ANNUITY_CONTRIBUTIONS,
    "260": P1_CONTRIBUTION_PILLAR_3A,
    "261": P2_CONTRIBUTION_PILLAR_3A,
    "278": INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    "280": P1_CONTRIBUTION_PILLAR_1,
    "281": P2_CONTRIBUTION_PILLAR_1,
    "285": P1_CONTRIBUTION_PILLAR_2,
    "286": P2_CONTRIBUTION_PILLAR_2,
    "287": DEDUCTIONS_WEALTH_MANAGEMENT,
    "288": P1_DEDUCTIONS_EDUCATION,
    "289": P2_DEDUCTIONS_EDUCATION,
    "290": DEDUCTIONS_OTHER,
    "299": DEDUCTIONS_TOTAL,
    "310": INCOME_NET_TOTAL,
    "312": EXPENSE_CHILDREN_DAYCARE,
    # "322": Zweiverdienerabzug,
    # "326": Abzug für massgebliche Beteiligungen,
    # "330": Nettoeinkommen II,
    "333": DEDUCTIONS_DONATIONS,
    # "335": Mitgliederbeiträge an pol. Parteien,
    "344": DEDUCTIONS_ILLNESS,
    # "346": Behinderungsbedingte Kosten,
    # "350": Reineinkommen,
    # "360": Sozialabzüge - in ungetrennter Ehe,
    # "362": Sozialabzüge - Alleinerziehende,
    # "364": Sozialabzüge - Übrige,
    # "370": Sozialabzüge - je Kind,
    # "371": Sozialabzüge - Kinder in auswärtiger Ausbildung,
    # "377": Sozialabzüge - je unterstützte Person,
    "380": INCOME_TAXABLE_LOCAL,
}

tax_ur_codes_assets = {
    "400": ASSETS_PORTFOLIO_ACCOUNTS,
    "406": ASSETS_LIFE_INSURANCE,
    "412": ASSETS_CARS,
    "414": ASSETS_UNDISTRIBUTED_INHERITANCES,
    "416": ASSETS_OTHER,
    "420": ASSETS_REAL_ESTATE_TOTAL_NET,
    # "439": Geschäftsvermögen total,
    # "442": Vermögensanteile an kaufmännischen Personengesellschaften P1,
    # "443": Vermögensanteile an kaufmännischen Personengesellschaften P1,
    "450": ASSETS_GROSS_TOTAL,
    "460": DEBT_PRIVATE,
    "461": DEBT_BUSINESS,  # P1
    # "462": Betriebsschulden P2,
    "470": ASSETS_NET_TOTAL,
    # "472": Sozialabzüge - in ungetrennter Ehe,
    # "473": Sozialabzüge - Übrige,
    # "474": Sozialabzüge - Kinder,
    "480": ASSETS_TAXABLE_LOCAL,
}
