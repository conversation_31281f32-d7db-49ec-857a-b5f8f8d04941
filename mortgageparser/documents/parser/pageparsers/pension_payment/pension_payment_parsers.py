from abbyyplumber.converter.ValueConverter import MostRecentDateConverter, DateConverter
from abbyyplumber.plumberstudio.SearchElement import SearchElementConstrainedArea
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)

parsers = [
    # not needed, spacy should know this
    # TemplatePageParser(
    #     desc="Rentenbescheinigung BE SVA",
    #     doc_cat=DocumentCat.PENSION_PAYMENT_AHV,
    #     page_cat=PageCat.GENERIC_PAGE,
    #     required_text_conditions=[
    #         FromStartTextCond("AUSGLEICHSKASSE DES KANTONS BERN", num_lines=5),
    #         # FromStartTextCond("Ihre Anfrage vom", num_lines=2),
    #     ],
    #     # required_tokens_any=[
    #     #     ['Leistungsausweis 20'],
    #     #     ['Um Ihnen das Ausfüllen der Steuererklärung zu'],
    #     #     ['Freundliche Grüsse'],
    #     # ],
    # ),
    TemplatePageParser(
        desc="Rentenbescheinigung BE Ausgleichskasse",
        doc_cat=DocumentCat.PENSION_PAYMENT_AHV,
        page_cat=PageCat.GENERIC_PAGE,
        required_text_conditions=[
            FromStartTextCond("AUSGLEICHSKASSE DES KANTONS BERN", num_lines=4),
            # FromStartTextCond("Ihre Anfrage vom", num_lines=2),
        ],
        required_tokens_any=[
            ["Leistungsausweis 20"],
            ["Steuerbare Leistungen"],
            ["Ausfüllen der Steuererklärung"],
            ["Freundliche Grüsse"],
        ],
    ),
    TemplatePageParser(
        desc="Rentenbescheinigung ZH SVA Page 2 mit Korrekturen",
        doc_cat=DocumentCat.PENSION_PAYMENT_AHV,
        page_cat=PageCat.GENERIC_PAGE,
        required_text_conditions=[
            FromStartTextCond("SVA Zürich", num_lines=1),
            # FromStartTextCond("Ihre Anfrage vom", num_lines=2),
        ],
        required_tokens_any=[
            ["Total der Rückforderungen"],
            ["Total der Nachzahlung"],
            ["Die Auszahlung der monatlichen Leistungen"],
            ["Wichtige Hinweise"],
        ],
    ),
    TemplatePageParser(
        desc="Rentenbescheinigung ZH SVA Page 3 mit Korrekturen",
        doc_cat=DocumentCat.PENSION_PAYMENT_AHV,
        page_cat=PageCat.GENERIC_PAGE,
        required_text_conditions=[
            FromStartTextCond("SVA Zürich", num_lines=1),
            FromStartTextCond("Rechtsmittelbelehrung", num_lines=7),
        ],
        required_tokens_any=[
            ["Gegen diese Verfügung kann innert"],
            ["Wir grüssen"],
            ["Ausgleichskasse"],
            ["Leistungen"],
        ],
        debug_breakpoint=False,
    ),
    SmartLetterPageParser(
        doc_cat=DocumentCat.PENSION_PAYMENT_BVG,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="Profond",
        titles=["Rentenausweis für das Jahr 20"],
        se_address_inside=SearchElementConstrainedArea(
            None,
            None,
            text_top="Profond Vorsorgeeinrichtung, Zollstrasse",
            text_bottom="Rentenausweis",
            x_range=PercentageRange(0, 0.5),
        ),
        se_date_of_birth=SearchElementConstrainedArea(
            None,
            None,
            text_top="SV-Nummer",
            text_bottom="Rentenart",
            text_left="Geburtsdatum",
            converter=DateConverter(),
        ),
        use_ahv_new=True,
        se_document_date=SearchElementConstrainedArea(
            None,
            None,
            y_range=PercentageRange(0.2, 0.6),
            converter=MostRecentDateConverter(),
        ),
        required_tokens=[
            "Profond Vorsorgeeinrichtung",
            "Zollstrasse 62",
            "Rentenart",
            "Total Auszahlung",
        ],
    ),
]


def get_parsers_pension_payment():
    return parsers
