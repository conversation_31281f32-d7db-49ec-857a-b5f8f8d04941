from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/password_protection"
path_root_folder = Path(folder)
dest_folder_prefix = "input_system_components_processing_config_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_password_protection():
    await DossierTest(
        use_ocr_cache=False,  # Must be false because if wrong ocr is cached the test does not work
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "01",
        source_file_filter=["passport_with_passwords_open_permission.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1
            expected_num_processing_exceptions=1
        ),
    ).run()


@pytest.mark.asyncio
async def test_password_protection_02_256():
    await DossierTest(
        use_ocr_cache=False,  # Must be false because if wrong ocr is cached the test does not work
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "02/encrypted_256",
        source_file_filter=["kaufvertragsentwurf_protected_256.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # expected_num_extracted_file_exceptions=1
            expected_num_processing_exceptions=1
        ),
    ).run()
