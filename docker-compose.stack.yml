version: '3.8'
services:
  pdf2img:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    networks:
      core-services:
      caddy:
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
    command: python asyncizer/pdf2jpg_worker.py
    deploy:
      resources:
        limits:
          cpus: '1'
          # For complicated plans (e.g. 10MB) 2GB is not enough, better 3GB
          memory: 3000M
      placement:
        constraints: [ node.labels.generic == true ]

  spacy-classifier:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
    command: python classifier/spacy_worker.py
#    environment:
#      - SPACY_MODEL_DE_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_de_20220730-0032-20220731-0530.tar.gz
#      - SPACY_MODEL_EN_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_en_20220731-0825-20220731-0836.tar.gz
#      - SPACY_MODEL_FR_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_fr_20220731-0909-20220731-1347.tar.gz
#      - SPACY_MODEL_IT_S3_OBJECT_PATH=hypodossier-models/hydocs_spacy/hydocs_detail_it_20220729-2254-20220729-2315.tar.gz
    networks:
      core-services:
      caddy:

    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 4500M
      placement:
        constraints: [ node.labels.generic == true ]

  hyextract:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
    networks:
      core-services:
      caddy:
    deploy:
      replicas: 1
      resources:
        limits:
          cpus: '1'
          memory: 4750M
      placement:
        constraints: [ node.labels.generic == true ]

  original_file_processor:
    image: registry.gitlab.com/hypodossier/hyextract:${TAG-latest}
    secrets:
      - source: ENV_CONF_V5
        target: /app/.env
    command: python asyncizer/file_process_worker.py
    networks:
      core-services:
      caddy:
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2048M
      placement:
        constraints: [ node.labels.generic == true ]

secrets:
  ENV_CONF_V5:
    file: .env-example

networks:
  core-services:
    external: true
  caddy:
    external: true