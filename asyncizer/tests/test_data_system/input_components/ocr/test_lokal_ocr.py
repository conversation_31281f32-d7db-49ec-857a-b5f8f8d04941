import asyncio
import json

import pytest

from asyncizer.rpc_pika import RpcClient

import structlog

logger = structlog.getLogger(__name__)


@pytest.mark.skip(reason="must provide valid document")
@pytest.mark.asyncio
async def test_simple_orc():
    rpc_client = await RpcClient(
        asyncio.get_running_loop(), "amqp://admin:admin@*************:5672/"
    ).connect()
    message = json.dumps(
        {
            "projectId": "giAVtibSxYfUwzAUuXZP",
            "licenseSerialNumber": "SWAR-1201-1007-0006-8015-9363",
            "sourceBucket": "production-v2-fs24-dms",
            "sourceObjectName": "02a299b2-0371-40ea-83ef-9f26e2ef6cf7/files/0264205f-5698-489c-b065-a44384027bf2/0.jpg",
            "destBucket": "test-dossier",
            "destObjectName": "document/",
        }
    )

    logger.info(f"Message={message}")
    logger.info("Now send OCR request...")
    response = await rpc_client.call("frep.trial.DossierFileRequest", message)
    print(response)
    result = json.loads(response)
    print(result)
