from hypodossier.core.domain.PageLocation import PageLocation
from mortgageparser.documents.parser.ParserManager import ParserManager
from mortgageparser.documents.parser.pageparsers.minergie.minergie_parsers import (
    get_parsers_minergie,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.partner_parsers import (
    get_parsers_partners,
)


class PartnerParserManager(ParserManager):
    def __init__(self, lang: str, page_source: PageLocation):
        super().__init__(lang, page_source)

        self.add_parsers(get_parsers_partners())
        self.add_parsers(get_parsers_minergie())

        # self.add_parser(SgdPageParser())

    #    self.add_parser(CatchAllUnknownDEPageParser())
