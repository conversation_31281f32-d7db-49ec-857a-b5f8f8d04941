from hypodossier.core.documents.debtcollectioninformation.DebtCollectionInformationSemanticPageCreator import (
    DebtCollectionInformationSemanticPageCreator,
)
from hypodossier.core.documents.debtcollectioninformation.DebtCollectionPageData import (
    DebtCollectionStatus,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.util.basis_string_util import substring_before_newline
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.util.string_utils import (
    substring_after_list,
    substring_before,
    contains_all_strings,
)

"""
DÉPARTEMENT DE LA JUSTICE,
 
DE LA SÉCURITÉ ET DE LA CULTURE
 
OFFICE DES POURSUITES
 
GUICHET UNIQUE - INFORMATIONS DÉBITEUR
 
 
 
Débiteur : LASTNAME FIRSTNAME
Du  14.03.2012 au  14.03.2017 - Intérêts au  14.03.2017 - Etat au  14.03.2017
 
 
Poursuites
Créancier Créance Solde dû Etat
Dossier Série
Représentant du créancier Résultat Perte Réf. créancier
Pas de poursuites.
"""


class DebtCollectionInformationPageOneEmptyNeuchatelFRPageParser(AbstractPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "OFFICE DES POURSUITES",
                "GUICHET UNIQUE - INFORMATIONS DÉBITEUR",
                "Débiteur",
                "Pas de poursuites",
            ],
        )

    def parse(self) -> SemanticPage:
        text = self.page.get_text()

        s = substring_after_list(text, ["OFFICE", "GUICHET", "Débiteur : "])
        s = substring_before_newline(s).strip()

        return DebtCollectionInformationSemanticPageCreator(
            PageCat.DEBT_COLLECTION_FIRST_PAGE,
            addressline=s,
            status=DebtCollectionStatus.EMPTY,
        ).create(self)


"""
RÉPUBLIQUE ET CANTON DE NEUCHÂTEL
DÉPARTEMENT DE LA JUSTICE, 
DE LA SÉCURITÉ ET DE LA CULTURE
OFFICE DES POURSUITES
Monsieur
LASTNAME Firstname
STREET_INFO 
2013 Colombier
Extrait du registre des poursuites
(art. 8a LP)
Nous certifions par la présente que sous le nom LASTNAME Firstname ( 18.10.1955) et sous l'adresse susmentionnée,
Poursuites détaillées
dans le registre des poursuites du 10.06.2011 au 10.06.2016 aucune poursuite n'a été enregistrée (CHF 0.00).
9
Actes de défaut de biens
dans le registre des actes de défaut de biens du 10.06.2011 au 10.06.2016 aucun 
(CHF 0.00).
Les éventuels acomptes versés depuis l'introduction de la(es) poursuite(s) ne sont pas compris dans le(s) montant(s) ci-dessus.
Conformément à l'art. 26 LiLP du canton de Neuchâtel, l'office des poursuites tient un état des débiteurs contre lesquels ont été délivrés des actes de défaut 
de biens définitifs au sens des articles 115 et 149 LP. Le droit de consultation est régi par l'article 8a LP. Dès lors, les actes de défaut de biens du présent 
extrait ne sont délivrés que sur une période de 5 ans.
La Chaux-de-Fonds, le 10 juin 2016
"""


class DebtCollectionInformationPageOneEmptyNeuchatel2FRPageParser(AbstractPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "OFFICE DES POURSUITES",
                "Extrait du registre des poursuites",
                "Poursuites détaillées",
                "aucune poursuite n'a été enregistrée",
                "Actes de défaut de biens",
                " aucun ",
            ],
        )

    def parse(self) -> SemanticPage:
        text = self.page.get_text()
        s = substring_after_list(text, ["Nous certifions", "le nom"])
        s = substring_before(s, "(", ignore_whitespace_for_hamming=False).strip()

        return DebtCollectionInformationSemanticPageCreator(
            PageCat.DEBT_COLLECTION_FIRST_PAGE,
            addressline=s,
            status=DebtCollectionStatus.EMPTY,
        ).create(self)
