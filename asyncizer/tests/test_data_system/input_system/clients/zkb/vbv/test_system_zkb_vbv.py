from pathlib import Path
from shutil import copyfile

import pytest

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.util.excel.excel_xlsm_to_xlsx import xlsm_to_xlsx

FOLDER_TYPE = "vbv"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_CLIENTS}/zkb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_zkb_vbv_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


def test_xlsm(keep_result=True):
    f = path_root_folder / "xlsm" / "Muster VBV.xlsm"

    assert f.exists()

    # copy file because it will be replaced
    f_copy = f.parent / "Muster VBV XLSX.xlsx"
    copyfile(f, f_copy)

    assert f_copy.exists()
    p_out = xlsm_to_xlsx(
        f_copy, remove_p_in=True
    )  # remove file here because it will be replaced
    assert p_out.suffix == ".xlsx"
    assert p_out.exists()
    assert p_out.is_file()

    if not keep_result:
        p_out.unlink()


@pytest.mark.asyncio
async def test_pdf():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder / "pdf",
        source_file_filter=["Muster VBV.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.ZKB_VBV: 3}
        ),
    ).run()
