from abbyyplumber.converter.ValueConverter import ParagraphConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementStaticText,
    SearchElementConstrainedArea,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    ADDRESS_REAL_ESTATE_PRIMARY,
)
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.zh.zh_tax_declaration_fields_map import (
    fields_map_page_04,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.tax_declaration_util import (
    create_search_elements_by_tax_code,
    COLUMN_CODE,
    COLUMN_VALUE,
)
from mortgageparser.util.string_utils import contains_all_strings

"""
Vermögen im In - und Ausland Steuerwert am 31. Dezember 2019
Ehemann / Einzelperson / P1, Ehefrau / P2 und minderjährige Kinder, einschliesslich Nutzniessungsvermögen
CHF ohne Rappen
30. Bewegliches Vermögen
2 458 532 *
30.1 Wertschriften und Guthaben Wertschriftenverzeichnis 400 
30.2 Bargeld, Gold und andere Edelmetalle 404 
30.3 Lebens - und Rentenversicherungen (Steuerwert gem. Bescheinigung der Versicherungsges.)
 Versicherungsgesellschaft Abschlussjahr Ablaufsjahr Steuerwert 
 
 
 
 Total 406
30.4 Motorfahrzeuge: Mercedes B200 CDI Kaufpreis: 33 400 Jahrgang: 2012 412 1 040
30.5 Anteile an unverteilten Erbschaften, Geschäfts - / Korporationsanteile Aufstellung 414 
30.6 Übrige Vermögenswerte; nähere Bezeichnung: Kryptowährungen USD 174'*********** 288
31. Liegenschaften, Verkehrswert gemäss Neufestsetzung ab 1.1.2009
31.1 Einfamilienhaus oder Stockwerkeigentum Bewertungsdiﬀ erenz ausserkantonale Liegenschaften 9020
 Gemeinde Zürich Kt. ZH Strasse STREET 578 420 1 172 000
31.2 Zum Verkehrswert besteuert Liegenschaftenverzeichnis 421 
31.3 Zum Ertragswert besteuert (Land - oder Forstwirtschaft) Liegenschaftenverzeichnis 422 
32. Eigenkapital Selbständigerwerbender ohne Geschäftswertschriften Hilfsblatt A 430 
33. Total der Vermögenswerte 460 3 811 860
*
34. Schulden Schuldenverzeichnis 470 – 1 135 000
35. Steuerbares Vermögen gesamt 490 2 676 860
36. Vom steuerbaren Vermögen gemäss Ziﬀ er 35 entfallen:
36.1 Auf steuerbare Vermögenswerte in anderen Kantonen 494 –
36.2 Auf steuerbare Vermögenswerte im Ausland 496 –
37. Steuerbares Vermögen im Kanton Zürich 498 2 676 860
Kapitalleistungen im Jahr 2019 Auszahlungsdatum
Bei mehreren Kapitalleistungen ist eine Auf stel lung einzureichen.
40. Auszahlung aus AHV / IV aus Einrichtung der beruﬂ ichen Vorsorge (2. Säule) CHF ohne Rappen
 aus Frei zü gig keits kon to / - police aus anerkannter Form der geb. Selbst vor sor ge (3. Säule a)
13213211
 infolge Tod oder für bleibende körperliche oder gesundheitliche Nachteile 510
50. Schenkungen Erbvorbezug Erbschaften Beteiligung an Er ben ge mein schaf ten
 (Name, Adresse und Verwandtschaftsgrad ein set zen) 
50.1 Am T T M M 2019 erhalten von Wert: 516 1133221133221111
50.2 Am T T M M 2019 ausgerichtet an Wert: 519 1133221133221111
60. Bemerkungen: 
Beilagen Diese Steuererklärung ist voll stän dig und wahrheitsgetreu ausgefüllt
 PC - Steuererklärung inkl. Barcode - Blatt Bescheinigungen 3. Säule a
 Wertschriftenverzeichnis Hilfsblatt / Fragebogen Ort und Datum
 Lohnausweise Bilanz und Erfolgsrechnung
 Berufsausl. / Versicherungsprämien Unterschrift Ehemann / Einzelperson / P1 Unterschrift Ehefrau / P2 
4
LASTNAME FIRSTNAME und FIRSTNAME, Zürich
756.4078.9585.31 
${zoli=0106192604761} ZHprivateTax Version 2.0.0 / FK - Version 2019 - 7abd4 - 0d321 Seite 01061926047/1 Seite 4 von 13 20.04.2020 17:11 unverbindlicher Einzelblattausdruck
"""


"""
Steuerwert am 31. Dezember 2019
CHF ohne Rappen
  400  2 458 532 *
  404 
  406
  412  1 040
  414 
  416  180 288
9020
  420  1 172 000
  421 
  422 
  430 
  460  3 811 860
  470  – 1 135 000 *
  490  2 676 860
  494  –
  496  –
  498  2 676 860
Auszahlungsdatum
CHF ohne Rappen
a)
  510 13213211
 schaf ten
ert: 516 1133221133221111
ert: 519 1133221133221111
 und wahrheitsgetreu ausgefüllt
1  Unterschrift Ehefrau / P2 
4
 
Seite
unverbindlicher Einzelblattausdruck
"""


class TaxDeclarationZHAssetsPageParser(StandardTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_ASSETS

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "Vermögen im In- und Ausland",
                "Wertschriften",
                "Wertschriftenverzeichnis",
                "Kapitalleistungen",
                "Steuerbares Vermögen",
            ],
            hamming_dist=4,
        )
        return success

    def parse_page_header(self):
        success = self.page.set_header_by_text(
            "Bewegliches Vermögen", include_pattern=False
        )
        if not success:
            success = super().parse_page_header()
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text(
            "Unterschrift Ehemann", include_pattern=False
        )
        if not success:
            success = super().parse_page_footer()
        return success

    def create_content_extractor(self) -> ContentExtractor:
        ce = ContentExtractor(
            [
                SearchElementStaticText(
                    "text_ref_tax_code",
                    self.page.main,
                    label="Wertschriftenverzeichnis",
                    max_l_dist=4,
                    extract=False,
                ),
                # Add overlap to the left here with the word Wertschriftenverzeichnis because the page might not be straight
                SearchElementConstrainedArea(
                    COLUMN_CODE,
                    self.page.main,
                    text_left="Wertschriftenverzeichnis",
                    text_right="Wertschriftenverzeichnis",
                    text_bottom="Kapitalleistungen im Jahr",
                    extract=False,
                )
                .include_left(6)
                .include_right(7),
                # SearchElementArea(COLUMN_CODE, self.page.main, x_range=PercentageRange(0, 0.3), extract=False,
                #                   relations=[SearchRelationRightOf("text_ref_tax_code")]),
                SearchElementConstrainedArea(
                    COLUMN_VALUE,
                    self.page.main,
                    text_bottom="Kapitalleistungen im Jahr",
                ).rightof(COLUMN_CODE),
                # Line already contains 'Gemeinde Kt. Strasse'
                SearchElementConstrainedArea(
                    ADDRESS_REAL_ESTATE_PRIMARY.name,
                    self.page.main,
                    text_top="Einfamilienhaus oder Stockwerkeigentum",
                    text_right="Wertschriftenverzeichnis",
                    text_bottom="Zum Verkehrswert besteuert",
                    converter=ParagraphConverter(
                        max_num_lines_valid=1, min_alpha_per_line=20
                    ),
                ).include_right(),
            ]
        )
        ce.search_elements += self.get_search_elements_footer(self.page)

        search_elements = create_search_elements_by_tax_code(
            self.page, fields_map=fields_map_page_04, column_value=COLUMN_VALUE
        )
        ce.search_elements += search_elements

        return ce


"""
    def parse(self) -> SemanticPage:
        text = self.page.get_text()

        dp = createTaxDeclarationSemanticPage(self.lang, PageCat.TAX_DECLARATION_PAGE_ASSETS, text, True)

        # Extract right column with 'Steuerwert'
        # y_tolerance 4..6 is good. 7 is no good because codes 9020 and 420 get mixed up
        text_main_col = self.page.extract_text_from_column(14.7 / 21, y_tolerance=10)
        code_map = map_code_value_text(text_main_col, min_code=400, max_code=519)
        local_fields = map_code_fields_to_text_currency_fields(code_map, fields_map_page_04)
        dp.meta.update(local_fields)

        return dp
"""
