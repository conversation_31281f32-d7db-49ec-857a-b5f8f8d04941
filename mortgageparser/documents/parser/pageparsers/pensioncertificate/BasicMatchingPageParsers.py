from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import (
    StandardPensionCertificatePageOnePageParser,
    StandardPensionCertificatePageTwoPageParser,
)
from mortgageparser.util.string_utils import (
    contains_all_strings,
    contains_at_least_one_string,
)


class IvfHartmannCertificatePageOnePageParser(
    StandardPensionCertificatePageOnePageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        # Todo: not specific enough
        return False and contains_all_strings(
            text,
            [
                "Vorsorgeausweis",
                "Personalvorsorgestiftung",
                "Leistungen im Alter",
                "Altersrente",
            ],
        )


class IvfHartmannCertificatePageTwoPageParser(
    StandardPensionCertificatePageTwoPageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        # Todo: not specific enough
        return contains_all_strings(
            text,
            [
                "Vorsorgeausweis",
                "im Alter 63",
                "im Alter 62",
                "Altersgutschrift",
                "Bei Fragen wenden Sie sich bitte an Frau Iris Wanner, Telefon intern 360",
            ],
        )


# class AllianzPensionCertificatePageOneFRParser(StandardPensionCertificatePageOnePageParser):
#     def __init__(self):
#         super().__init__()
#         self.supported_languages = ['fr']
#
#     def match_page_by_text(self, page_index: int, text: str) -> bool:
#         return contains_all_strings(text, ["Fondation collective LPP"]) \
#                and contains_at_least_one_string(text, ['Allianze Suisse']) \
#                and contains_at_least_one_string(text, ["Certificat d'asssurance"]) \
#                and contains_at_least_one_string(text, ['Prestations de vieillesse'])


# Canton de Vaud
class AllianzPensionCertificatePageTwoFRParser(
    StandardPensionCertificatePageTwoPageParser
):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return (
            contains_all_strings(text, ["Fondation collective LPP"])
            and contains_at_least_one_string(text, ["Allianze Suisse"])
            and contains_at_least_one_string(text, ["Certificat d'asssurance"])
            and contains_at_least_one_string(text, ["Cotisations totale par année"])
            and contains_at_least_one_string(
                text, ["Informations complémentaires"], hamming_dist=3
            )
        )


class SicpaPensionCertificatePageOneFRParser(
    StandardPensionCertificatePageOnePageParser
):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_at_least_one_string(
            text,
            [
                "CAISSE DE RETRAITE EN FAVEUR DU PERSONNEL DU GROUPE SICPA EN SUISSE",
                "FONDS DE PREVOYANCE EN FAVEUR DU PERSONNEL DU GROUPE SICPA EN SUISSE",
            ],
        ) and contains_at_least_one_string(text, ["Certifical Personnel de prévoyance"])


class CpevPensionCertificatePageOneFRParser(
    StandardPensionCertificatePageOnePageParser
):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return (
            contains_all_strings(text, ["CAISSE DE PENSIONS", "DE L'ÉTAT DE VAUD"])
            and contains_at_least_one_string(text, ["Situation de prévoyance"])
            and contains_at_least_one_string(
                text, ["Date d'entrée dans la caisse"], hamming_dist=2
            )
            and contains_at_least_one_string(text, ["Caisse de pensions"])
            and contains_at_least_one_string(text, ["Prestations de sortie"])
        )


# Canton de Vaud
class CpevPensionCertificatePageTwoFRParser(
    StandardPensionCertificatePageTwoPageParser
):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return (
            contains_at_least_one_string(text, ["Pour la CPEV"], hamming_dist=2)
            and contains_at_least_one_string(text, ["Prestations de"], hamming_dist=3)
            and contains_at_least_one_string(
                text, ["Total rente mensuelle de retraite"], hamming_dist=3
            )
        )


class CommonPensionCertificatePageTwoFRParser(
    StandardPensionCertificatePageTwoPageParser
):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return (
            contains_at_least_one_string(
                text, ["Certificat de prévoyance", "certificat"], hamming_dist=4
            )
            and contains_at_least_one_string(
                text, ["Prestation", "Rachat"], hamming_dist=3
            )
            and contains_at_least_one_string(
                text, ["Encouragement à la propriete du logement"], hamming_dist=4
            )
        )
