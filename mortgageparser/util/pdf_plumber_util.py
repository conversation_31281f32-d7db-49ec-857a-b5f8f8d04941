import logging
from dataclasses import dataclass
from decimal import Decimal
from statistics import mean
from typing import List

from pdfplumber.page import Page
from pdfplumber.utils import (
    DEFAULT_X_TOLERANCE,
    DEFAULT_Y_TOLERANCE,
    to_list,
    cluster_objects,
    collate_line,
)

from abbyyplumber.util.currency_util import clean_number_text
from hypodossier.core.domain.TextContentStats import TextContentStats
from hypodossier.util.basis_string_util import is_barcode
from mortgageparser.util.string_utils import DEFAULT_HAMMING_DISTANCE, contains_string


@dataclass
class SearchTokens:
    current_line: List[str]
    next_line: List[str]


# border: 1-based percentage that will be cut off based on full page size
def extract_text_from_column(
    page: Page,
    percent_border_left=0,
    percent_border_right=0,
    x_tolerance=DEFAULT_X_TOLERANCE,
    y_tolerance=DEFAULT_Y_TOLERANCE,
):
    x0 = page.bbox[0]
    x1 = page.bbox[2]
    width = x1 - x0

    # x pos from left border
    border_pos_left = width * Decimal(percent_border_left)
    border_pos_right = width * Decimal(1 - percent_border_right)

    if len(page.chars) == 0:
        return None

    chars = to_list(page.chars)
    chars_filtered = []
    for char in chars:
        if border_pos_left < char["x0"] < border_pos_right:
            chars_filtered.append(char)
    doctop_clusters = cluster_objects(chars_filtered, "doctop", y_tolerance)

    lines = (collate_line(line_chars, x_tolerance) for line_chars in doctop_clusters)

    coll = "\n".join(lines)
    return coll


def crop_right_column_currency_snippet_from_page(
    page,
    search_tokens,
    right_col_token,
    rows=None,
    hamming_dist=DEFAULT_HAMMING_DISTANCE,
):
    snippet = crop_right_column_snippet_from_page(
        page, search_tokens, right_col_token, rows, hamming_dist
    )

    num_text: str = snippet.extract_text()
    currency_item_text = num_text
    currency_item = None
    try:
        t = clean_number_text(num_text)
        currency_item = int(t)
    except:
        pass
    return currency_item_text, currency_item


def crop_right_column_snippet_from_page(
    page: Page,
    search_tokens,
    right_col_token: str,
    words=None,
    hamming_dist=DEFAULT_HAMMING_DISTANCE,
):
    if not words:
        # keep_blank_chars=True means that all words in one line get concatenated to one word
        # So with False we get a list with size == number of words on the page
        # So with True we get a list with size == number of lines on the page
        words = page.extract_words(keep_blank_chars=True)

    max_x = Decimal(0)
    x0 = None
    top = None
    last_word = None  # remember this if we need to go one line up
    for word in words:
        if word["text"] and word["text"].strip():
            if word["text"]:
                max_x = max(word["x1"], max_x)
            if not top:
                for snippet_text in search_tokens.current_line:
                    if contains_string(
                        word["text"], snippet_text, p_hamming_dist=hamming_dist
                    ):
                        # Sometimes top < bottom so switch the values
                        top = max(word["top"], word["bottom"])
                        bottom = min(word["bottom"], word["top"])
                        break
            if not top:
                for snippet_text in search_tokens.next_line:
                    if contains_string(
                        word["text"], snippet_text, p_hamming_dist=hamming_dist
                    ):
                        # Sometimes top < bottom so switch the values
                        top = max(last_word["top"], last_word["bottom"])
                        bottom = min(last_word["bottom"], last_word["top"])
                        break
            if not x0:
                if word["text"] == right_col_token:
                    x0 = word["x1"] * Decimal(1.01)
            last_word = word

    if not x0:
        # Fallback, just assume it is about the 3cm on the right of the 20cm page (without white border)
        x0 = Decimal(17 / 20) * max_x
    x1 = max_x
    if not top:
        logging.error(
            f'Could not find snippet "{snippet_text}" in page {page}. x0={x0}, x1={x1}'
        )
    box = (x0, bottom, x1, top)
    snippet = page.crop(bbox=box)
    return snippet


def extract_text_with_size(
    chars,
    mean_size_page=None,
    x_tolerance=DEFAULT_X_TOLERANCE,
    y_tolerance=DEFAULT_Y_TOLERANCE,
    skip_barcodes=True,
    min_title_length=3,
):
    t = []
    t_with_size = []

    if len(chars) > 0:
        chars = to_list(chars)
        doctop_clusters = cluster_objects(chars, "doctop", y_tolerance)

        for line_chars in doctop_clusters:
            text = collate_line(line_chars, x_tolerance)
            if text:
                sizes = [
                    float(char["size"])
                    for char in line_chars
                    if char["text"]
                    and (char["text"][0].isalpha() or char["text"][0].isdigit())
                ]
                if sizes:
                    mean_size = float(mean(sizes))
                    scaled_size = (
                        mean_size / mean_size_page if mean_size_page else mean_size
                    )
                    success = True

                    # Now remove all chars which are substantially smaller than mean_size (because this is normal text on the same line as a title)
                    line_chars_large = [
                        char for char in line_chars if char["size"] > 0.9 * mean_size
                    ]
                    if len(line_chars_large) < len(line_chars):
                        # title seems to be shorter than full line
                        text = collate_line(line_chars_large, x_tolerance)
                        sizes = [
                            float(char["size"])
                            for char in line_chars_large
                            if char["text"]
                            and (char["text"][0].isalpha() or char["text"][0].isdigit())
                        ]
                        mean_size = float(mean(sizes))
                        scaled_size = (
                            mean_size / mean_size_page if mean_size_page else mean_size
                        )
                    if min_title_length > 0:
                        if len(text) < min_title_length:
                            success = False

                    if success and skip_barcodes:
                        if is_barcode(text):
                            success = False

                    if success:
                        t.append(text)
                        t_with_size.append((text, scaled_size))
    return t, t_with_size


def clean_out_titles_with_too_many_special_chars(titles, titles_with_size):
    clean_titles = []
    clean_titles_with_size = []
    for i, t in enumerate(titles):
        tcs = TextContentStats.create(t)
        if tcs.percentage_chars_alpha > 0.6 or (
            tcs.percentage_chars_alpha > 0.3 and tcs.percentage_chars_special < 0.25
        ):
            # Valid text could be 'Kontoauszug 01.01.2022 - 31.12.203'. So it could be less than 60% alphas
            # if there are some valid numbers
            clean_titles.append(t)
            clean_titles_with_size.append(titles_with_size[i])
    return clean_titles, clean_titles_with_size


def get_page_layout_info_from_page(
    plumber_page,
    size_factor_title=1.1,
    size_factor_top_title=1.5,
    min_num_chars_for_title_extraction=50,
    max_num_valid_titles: int = 20,
) -> (List[str], List[str]):
    """

    :param plumber_page:
    :param size_factor_title:
    :param size_factor_top_title:
    :param min_num_chars_for_title_extraction:
    :param max_num_valid_titles: If there are more titles than this then we assume that this is an OCR artefact (e.g. useless characters on an image) and skip all titles
    :return: List of all title texts, list of large title texts, list of (title text, size of title), list of (large_title_text, size of large title)
    """

    sizes = [
        float(char["size"])
        for char in plumber_page.chars
        if char["text"] and char["text"][0].isalpha()
    ]

    if len(sizes) > min_num_chars_for_title_extraction:
        mean_size_page = float(mean(sizes))
        # max_size = float(max(sizes))

        # titles = filtered.extract_text()
        # if titles:
        #    titles = titles.split('\n')
        titles = []
        titles_with_size = []
        factor = size_factor_title
        for i in range(5):
            filtered = plumber_page.filter(
                lambda x: x.get("size", 0) > mean_size_page * factor
            )
            titles, titles_with_size = extract_text_with_size(
                filtered.chars, mean_size_page=mean_size_page * factor
            )
            if len(titles) <= max_num_valid_titles:
                break
            else:
                # Too many titles... try again with greater threshold
                # Sometimes there are 2 font sizes, one larger and one smaller than the mean_size_page * size_factor_title
                # In this case any real title will be larger than that
                factor = factor * size_factor_title * size_factor_title

        if len(titles) <= max_num_valid_titles:
            titles, titles_with_size = clean_out_titles_with_too_many_special_chars(
                titles, titles_with_size
            )

            top_filtered = filtered.filter(
                lambda x: x.get("size", 0) > mean_size_page * size_factor_top_title
            )
            # top_titles = top_filtered.extract_text()
            # if top_titles:
            #    top_titles = top_titles.split('\n')
            top_titles, top_titles_with_size = extract_text_with_size(
                top_filtered.chars, mean_size_page=mean_size_page
            )
            (
                top_titles,
                top_titles_with_size,
            ) = clean_out_titles_with_too_many_special_chars(
                top_titles, top_titles_with_size
            )

            return titles, top_titles, titles_with_size, top_titles_with_size
    return [], [], [], []


"""
# page A PDFPlumber page
# rows is rows = page.extract_words()... can be cached for performance
def extract_lines(page: Page, x_tolerance=DEFAULT_X_TOLERANCE, y_tolerance=DEFAULT_Y_TOLERANCE):
    if len(page.chars) == 0:
        return None

    chars = to_list(page.chars)
    doctop_clusters = cluster_objects(chars, "doctop", y_tolerance)

    lines = []


    # line_chars is a list of character objects that are all on one line
    for line_chars in doctop_clusters:
        line = collate_line(line_chars, x_tolerance)
        lines.append(line)

    return lines
    """
