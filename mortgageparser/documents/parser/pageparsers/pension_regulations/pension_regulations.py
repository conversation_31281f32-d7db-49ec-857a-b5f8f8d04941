from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import SearchElementConstant
from hypodossier.core.documents.generic_letter.GenericLetterPageData import (
    FIELDS_GENERIC_LETTER,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_COMPANY
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
    FromStartTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplatePensionRegulationsPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.PENSION_REGULATIONS
    company: str = None

    se: GenericLetterSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_GENERIC_LETTER.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_pension_regulations():
    parsers = [
        TemplatePensionRegulationsPageParser(
            desc="AMAG Vorsorgereglement 2021",
            company="AMAG",
            min_num_chars_alpha=1000,
            max_num_chars_special=150,
            required_tokens_any=[
                ["AMAG Group Pensionskasse"],
                ["Vorsorgereglement"],
                ["1.1.2021", "JANUAR 2021"],
            ],
        ),
        TemplatePensionRegulationsPageParser(
            # This has only large font size, so title does not work
            desc="AMAG Vorsorgereglement Title 2021",
            company="AMAG",
            max_num_chars_alpha=100,
            required_tokens_any=[
                ["AMAG Group Pensionskasse"],
                ["Vorsorgereglement"],
            ],
        ),
        TemplatePensionRegulationsPageParser(
            desc="Vorsorgereglement Titelseite generisch DE 2021",
            page_cat=PageCat.GENERIC_FIRST_PAGE,
            max_num_chars_alpha=160,
            max_num_chars_digit=15,
            max_num_chars_special=15,
            ranked_titles_all=[RankedTitle("Vorsorgereglement", 2, 10)],
        ),
        TemplatePensionRegulationsPageParser(
            desc="Vorsorgereglement BVK DE 2021",
            min_num_chars_alpha=1000,
            max_num_chars_special=150,
            required_text_conditions=[
                FromStartTextCond("Seite", num_lines=8),
                FromStartTextCond("Vorsorgereglement 1.1.", num_lines=8),
            ],
            required_tokens_any=[
                ["www.bvk.ch"],
                ["Obstgartenstrasse 21"],
                ["8090 Zürich"],
            ],
        ),
    ]
    return parsers
