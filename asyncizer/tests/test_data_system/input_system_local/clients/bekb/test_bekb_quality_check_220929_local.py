from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "220929_softlaunch_check"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/{FOLDER_TYPE}"
)
dest_folder_prefix = "input_system_clients_bekb_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/bekb/220929_softlaunch_check"
)


@pytest.mark.asyncio
async def test_220929_softlaunch_check_aktennotiz():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["aktennotiz.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.FILE_NOTE_FINANCING: 2},
            expected_page_objects={
                0: {
                    "document_date": "25.05.2022",
                    "document_title": "Skizzierung Spezial-Kassenobligation 2022",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_220929_softlaunch_check_amtlicher_wert():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["amtlicher_wert_be_gva_no_extract.pdf"],
        show_page=0,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_INSURANCE: 1},
            expected_page_objects={
                0: {
                    "document_date": "26.04.2010",
                    # WRONG BUT OK"company": "Der Kantonalen",
                    # "canton_short": "[EXTRA TEXT] FR",
                    "property_desc": "Wohnen",
                    "police_no": "30964",
                    "cubature": "1683.0",
                    "insurance_amount": "CHF 950'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_220929_softlaunch_check_id_single_page():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["single_page_check_id.jpg"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN_DE: 1},
            expected_page_objects={},
        ),
    ).run()


@pytest.mark.asyncio
async def test_220929_softlaunch_check_id():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["id_check_extraction.pdf"],
        show_page=5,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={
                # DocumentCat.PROPERTY_INSURANCE: 1,
            },
            expected_page_objects={},
        ),
    ).run()
