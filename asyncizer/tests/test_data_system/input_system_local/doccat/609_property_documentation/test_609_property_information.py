from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, util_test_batch
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "609_property_documentation"


@pytest.mark.asyncio
@pytest.mark.rotation
async def test_sample_aerial_photo_page_3(override_existing=True):
    await util_test_batch(
        override_existing=override_existing,
        source_folder=Path(
            f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_TODO_DOCCAT}/{FOLDER_TYPE}/sample_aerial_photo_page_3"
        ),
        dest_folder_prefix=f"{FOLDER_TYPE}_",
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_filenames=[],
            expected_page_objects={
                # 0: {
                #     "canton_short": "ZH",
                #     "document_date": "12.01.2017",
                #     "property_address_street": "Im Moos 9",
                #     "property_address_city": "Urdorf",
                #     "property_desc": "Wohnhaus",
                #     "insurance_yearly_bill_amount": "CHF 146",
                #     "firstname": "Roger",
                #
                #     "street": "Wiesenstrasse 3d",
                #     "zip": "8952",
                #     "city": "Schlieren",
                #
                # }
            },
            expected_doc_cat_frequency={DocumentCat.PROPERTY_INSURANCE: 1},
            expected_num_docs=1,
        ),
    )
