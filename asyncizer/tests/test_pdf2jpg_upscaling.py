from pathlib import Path
from tempfile import TemporaryDirectory

import pytest

from asyncizer.pdf2jpg import call_pdf2jpg_remote
from asyncizer.pdf2jpg_with_upscaling import (
    convert_but_not_too_small,
    DEFAULT_MAX_SIZE_RATIO_FOR_UPSCALING,
    UpscalingParams,
    DEFAULT_PARAMS_WITH_UPSCALING,
)
from asyncizer.pdf2jpg_worker import S3Pdf2JpgResponse
from asyncizer.s3 import download_single_file, make_bucket_available
from asyncizer.s3_util import upload_file_to_s3
from constants import BASE_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

TEST_OUTPUT_FOLDER = Path(f"{BASE_DIR}/output/pdf2jpg")


@pytest.mark.asyncio
async def test_large_plan():
    bucket = "test"

    make_bucket_available(bucket)

    p_in = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM)
        / "input_components"
        / "large_plans"
        / "large_pdf_plans"
        / "plans_much_larger_than_a4.pdf"
    )
    assert p_in.exists()
    object_name = (
        "pdf2jpg_test/test_large_plans/large_pdf_plans/plans_much_larger_than_a4.pdf"
    )
    await upload_file_to_s3(p_in, bucket, object_name)

    target_image_dir = "pdf2jpg_test/test_upscaling/plans_much_larger_than_a4.pdf"

    await check_a_single_pdf2jpg_trafo(
        bucket,
        object_name,
        target_image_dir,
        dpi=10,
        expected_size=329945,
        num_tries_used=1,
        upscaling_params=UpscalingParams(),
    )

    # Default behaviour
    await check_a_single_pdf2jpg_trafo(
        bucket,
        object_name,
        target_image_dir,
        dpi=400,
        expected_size=329945,
        num_tries_used=1,
        upscaling_params=UpscalingParams(),
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_large_plan_with_timeout():
    bucket = "test"

    make_bucket_available(bucket)

    p_in = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM)
        / "input_components"
        / "large_plans"
        / "large_plan_with_poppler_timeout"
        / "22"
        / "22.pdf"
    )
    assert p_in.exists()
    object_name = (
        "pdf2jpg_test/test_large_plans/large_plan_with_poppler_timeout/22/22.pdf"
    )
    await upload_file_to_s3(p_in, bucket, object_name)

    target_image_dir = (
        "pdf2jpg_test/test_upscaling/large_plan_with_poppler_timeout/22/22.pdf"
    )

    await check_a_single_pdf2jpg_trafo(
        bucket,
        object_name,
        target_image_dir,
        dpi=400,
        expected_size=193093,
        num_tries_used=1,
        upscaling_params=UpscalingParams(timeout=10),
    )


@pytest.mark.asyncio
@pytest.mark.broken_legacy_test
async def test_basic_call():
    bucket = "test"

    make_bucket_available(bucket)

    p_in = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM)
        / "input_components"
        / "empty_pages"
        / "quite_empty_page"
        / "quite_empty_page.pdf"
    )
    assert p_in.exists()
    object_name = "pdf2jpg_test/test_upscaling/quite_emptypage.pdf"
    await upload_file_to_s3(p_in, bucket, object_name)

    target_image_dir = "pdf2jpg_test/test_upscaling/quite_emptypage_result.jpg"

    # Default behaviour
    await check_a_single_pdf2jpg_trafo(
        bucket,
        object_name,
        target_image_dir,
        dpi=10,
        expected_size=37970,
        num_tries_used=1,
        upscaling_params=UpscalingParams(),
    )

    # Bigger file because of more dpi
    # await check_a_single_pdf2jpg_trafo(bucket, object_name, target_image_dir, dpi=800, expected_size=120205, num_tries_used=1, upscaling_params=UpscalingParams())
    #
    # # Still the same size because file generated in first try
    # await check_a_single_pdf2jpg_trafo(bucket, object_name, target_image_dir, dpi=400, expected_size=120205, num_tries_used=1, upscaling_params=UpscalingParams(max_size_for_upscaling=20000))
    #
    # # File generated in several tries
    # await check_a_single_pdf2jpg_trafo(bucket, object_name, target_image_dir, dpi=100, expected_size=52715, num_tries_used=5, upscaling_params=UpscalingParams(max_size_ratio_for_upscaling=-1, max_size_for_upscaling=44444))
    #


async def check_a_single_pdf2jpg_trafo(
    bucket,
    object_name,
    target_image_dir,
    dpi,
    expected_size,
    num_tries_used,
    upscaling_params: UpscalingParams,
):
    response: S3Pdf2JpgResponse = await call_pdf2jpg_remote(
        bucket,
        object_name,
        bucket,
        target_image_dir,
        dpi=dpi,
        upscaling_params=upscaling_params,
    )

    print(response.stats)

    assert response.stats.num_tries_used == num_tries_used

    assert len(response.images) == 1
    target_image_name = response.images[0]

    target_dir = TEST_OUTPUT_FOLDER

    target_dir.mkdir(parents=True, exist_ok=True)

    p_out = target_dir / f"result_{dpi}_{expected_size}.jpg"
    await download_single_file(bucket=bucket, prefix=target_image_name, dest_path=p_out)
    assert p_out.exists()
    assert p_out.stat().st_size == expected_size
    assert expected_size == response.stats.img_size


def test_upscaling_params():
    up = DEFAULT_PARAMS_WITH_UPSCALING
    print(up)

    up.max_num_tries = 777
    print(up)

    up2 = UpscalingParams(max_num_tries=777)

    print(up2)

    assert up == up2


@pytest.mark.parametrize(
    "max_size_ratio_for_upscaling, expected_size_bytes,num_tries_used",
    [
        pytest.param(
            DEFAULT_MAX_SIZE_RATIO_FOR_UPSCALING, 37101, 1, id="default params"
        ),
        pytest.param(
            -1,
            112662,
            4,
            id="no size ratio limit (slightl over DEFAULT_MAX_SIZE_FOR_UPSCALING",
        ),
    ],
)
@pytest.mark.broken_legacy_test
def test_basic_upscaling(
    max_size_ratio_for_upscaling, expected_size_bytes, num_tries_used
):
    p_in = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM)
        / "input_components"
        / "empty_pages"
        / "quite_empty_page"
        / "quite_empty_page.pdf"
    )
    assert p_in.exists()

    with TemporaryDirectory():
        p_out = p_in.parent
        # p_out = temp_dir

        paths, stats = convert_but_not_too_small(
            p_in,
            p_out,
            upscaling_params=UpscalingParams(
                max_size_ratio_for_upscaling=max_size_ratio_for_upscaling
            ),
        )

        print(stats)
        assert paths is not None
        assert len(paths) == 1

        p_out = Path(paths[0])
        out_size = p_out.stat().st_size
        assert out_size == expected_size_bytes

        assert num_tries_used == stats.num_tries_used
