

We start with a fresh batch of [1..n] documents that belong together

## Considerations
* we might need to have different pipelines for different page categories (of different document categories). E.g. one best effort and the other one with an area template.


## Glossary
| Term                       | Explanation                                                                              |
|----------------------------|------------------------------------------------------------------------------------------|
| File                       | Physical representation with filename and filepath, contains a list of FilePage elements |
| Document                   | Metainfo + Ordered list of DocPage that logically belong together                        |
| DocCat (Document Category) | Classification result for Document                                                       |
| DocPage                    | Metainfo, has a PageCat                                                                  |
| PageCat                    |  

## Processing

1. Loop over PDF files
    1. Loop over pages
        1. Run all the parsers we have to find a matching page type. This Page belongs always to a matching document type 
        
#### CH Passport
Problems
* How to handle several documents on one page?
Todo:    
1. Check if this exists:
    Pass Passeport Passaporto Passaport Passport
    Schweiz Suisse Svizzera Svizra Switzerland
    PM CHE
    ********
    PMCHEUENAL<<AYHAN<<<<<<<<<<<<<<<<<<<<<<<<<<<
    ********<5CHEYYMMDD4MYYMMDD<<<<<<<<<<<<<<<2 (first date is birthdate, last is expiry, in between is sex M/F)
    
#### Tax declaration
### Baseline
* Most tax pages are detected with high reliability

* Some attachments are very reliable (e.g. Lohnausweis), others random (e.g. Spesenabrechnung Bank)

### Requirements
1. We want a separate document 'Steuererklärung' with all tax formp pages
2. Wertschriftenverzeichnis is so important that we might want to extract that in the future (easy because reliable) e.g. "311 Steuererklärung Wertschriftenverzeichnis"
3. Reliable attachments could be extracted and really "removed" from tax document
4. Unreliable stuff should be in one separate doc called "312 Steuererklärung Beilagen"

### Detection of start / end
Find first and last page index of identified tax pages. Define num_tax_pages = index_last - index_first

There are 3 cases:
1. File contains only tax form pages
2. File contains tax form pages and attachments (some of them can be identified)
3. File is a wurst

### Output
1. Create one file 'Steuererklärung' from first to last identified tax form
2. Find first page after last tax form page that is clearly not in attachments. E.g. pension statement, mortgage contract, etc. This marks the end of attachments
3. Create one file 'Steuererklärung Beilagen' from all unknown pages after first tax page until end of attachments
4. Extract from 'Steuererklärung Beilagen' all reliably identified pages. E.g. Lohnausweis, pillar 3a confirmation etc.  

Some pages will be duplicated in 'Steuererklärung' and 'Steuererklärung Beilagen':
1. E.g. unidentified tax form page
2. E.g. unidentified attachment that comes before the last identified tax form
This is accepted - not a big problem. But it would be good to track the duplicated pages.



