from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpectations, PO_EXISTS, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

FOLDER_TYPE = "330_salary_certificate"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/{FOLDER_TYPE}"
)


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_2020_de_fr_en_simple():
    await DossierTest(
        source_folder=path_root_folder / "2020_de_fr_en_simple",
        override_existing=True,
        show_page=-1,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=["2020_de_fr_en_simple.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "26.01.2021",
                    "fullname": PO_EXISTS,
                    # "firstname": PO_EXISTS,
                    # "street": PO_EXISTS,
                    # "zip": "5300",
                    # "city": "Turgi",
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2020",
                    "accounting_period_from": "01.02.2020",
                    "accounting_period_to": "31.12.2020",
                    "salary_base": "CHF 67'100",
                    "salary_irregular_benefits": "CHF 3'260",
                    "salary_irregular_benefits_desc": "BonusZahlung",
                    "salary_other": "CHF 0",
                    "salary_gross": "CHF 70'360.10",
                    "salary_benefits_ahv": "CHF 5'177",
                    "salary_pillar_2_regular": "CHF 2'935",
                    "salary_net": "CHF 62'247",
                    "salary_expenses_overall_other": "CHF 1'900",
                    "salary_contributions_education": "CHF 12'029",
                    # "salary_comments": "B0%-Stelle.",
                    "company_contact": PO_EXISTS,
                    # Unfortunately Salary is missing in OCR
                }
            },
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_2025_fix_additional_0():
    await DossierTest(
        source_folder=path_root_folder / "2025_fix_additional" / "0",
        override_existing=True,
        show_page=-1,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=["330 Lohnausweis Yvonne.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
            expected_page_objects={
                0: {
                    "document_date": "05.01.2025",
                    "fullname": PO_EXISTS,
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2024",
                    "accounting_period_from": "01.01.2024",
                    "accounting_period_to": "31.12.2024",
                    "salary_base": "CHF 50'400",
                    "salary_irregular_benefits": "CHF 15'000",
                    "salary_irregular_benefits_desc": "Gratifikation",
                    "salary_gross": "CHF 65'400",
                    "salary_benefits_ahv": "CHF 4'185",
                    "salary_pillar_2_regular": "CHF 3'157",
                    "salary_net": "CHF 58'057",
                    "salary_comments": "60.00%-Stelle.\nJ",
                    "company_contact": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_2025_fix_additional_1():
    await DossierTest(
        source_folder=path_root_folder / "2025_fix_additional" / "1",
        override_existing=True,
        show_page=-1,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=["330 Lohnausweis Frank.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
            expected_page_objects={
                0: {
                    "document_date": "05.01.2025",
                    "fullname": PO_EXISTS,
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2024",
                    "accounting_period_from": "01.01.2024",
                    "accounting_period_to": "31.12.2024",
                    "salary_base": "CHF 108'000",
                    "salary_irregular_benefits": "CHF 15'000",
                    "salary_irregular_benefits_desc": "Gratifikation",
                    "salary_gross": "CHF 123'000",
                    "salary_benefits_ahv": "CHF 7'488",
                    "salary_pillar_2_regular": "CHF 5'329",
                    "salary_net": "CHF 110'182",
                    "company_contact": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_bad_scan():
    await DossierTest(
        source_folder=path_root_folder / "bad_scan",
        override_existing=True,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "07.02.2019",
                    "fullname": PO_EXISTS,
                    "firstname": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "5436",
                    "city": "Würenlos",
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2018",
                    "accounting_period_from": "01.01.2018",
                    "accounting_period_to": "31.12.2018",
                    "salary_base": "CHF 78'832",
                    "salary_other": "CHF 1'292",
                    "salary_gross": "CHF 80'124",
                    "salary_benefits_ahv": "CHF 5'557",
                    "salary_pillar_2_regular": "CHF 4'395",
                    "salary_net": "CHF 70'172",
                    "salary_comments": "siehe Zusatzblatt",
                    "company_contact": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_2019_clean():
    await DossierTest(
        source_folder=path_root_folder / "2019_clean",
        override_existing=True,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=["2019_clean.pdf"],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "document_date": "24.01.2020",
                    # "city": "Baden",
                    "year": "2019",
                    "accounting_period_from": "01.01.2019",
                    "accounting_period_to": "31.12.2019",
                    "salary_base": "CHF 101'650",
                    "salary_gross": "CHF 101'650",
                    "salary_net": "CHF 86'707",
                    "fullname": PO_EXISTS,
                    # "firstname": PO_EXISTS,
                    # "street": PO_EXISTS,
                    # "zip": "5405",
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "salary_benefits_ahv": "CHF 8'226",
                    "company_contact": PO_EXISTS,
                    "salary_pillar_2_regular": "CHF 6'717",
                    "salary_contributions_education": "CHF 2'195",
                    "salary_comments": "Spesenreglement durch die Steuerverwaltung genehmigt AG 21.08.06, Teilzeitbeschäftigt\nOhne Kantinenverpflegung 01.01.2019 - 31.12.2019",
                }
            },
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_2019_with_desc():
    await DossierTest(
        use_ocr_cache=True,
        source_folder=path_root_folder / "2019_with_desc",
        override_existing=True,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_page_objects={
                0: {
                    "salutation": "Herr",
                    "document_date": "31.12.2020",
                    "salary_base": "CHF 139'933",
                    "salary_irregular_benefits": "CHF 13'761",
                    "salary_irregular_benefits_desc": "Leistungsprämie (6'000), Treueprämien (7'761)",
                    "salary_other": "CHF 750",
                    "salary_gross": "CHF 154'444",
                    "salary_benefits_ahv": "CHF 9'453",
                    "salary_pillar_2_regular": "CHF 11'251",
                    "salary_net": "CHF 133'740",
                    "salary_expenses_overall_other": "CHF 240",
                    "fullname": PO_EXISTS,
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2020",
                    "accounting_period_from": "01.01.2020",
                    "accounting_period_to": "31.12.2020",
                    "company_contact": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_bad_ext():
    await DossierTest(
        source_folder=path_root_folder / "bad_ext",
        override_existing=True,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_page_objects={
                0: {
                    "document_date": "31.12.2020",
                    "fullname": PO_EXISTS,
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "accounting_period_from": "01.01.2020",
                    "accounting_period_to": "31.12.2020",
                    "salary_base": "CHF 30'000",
                    "salary_irregular_benefits": "CHF 500",
                    "salary_irregular_benefits_desc": "Bonus",
                    "salary_gross": "CHF 301'500",  # this one is really bad but OCR problem
                    "salary_benefits_ahv": "CHF 2'278",
                    "salary_pillar_2_regular": "CHF 775",
                    "salary_net": "CHF 27'445.30",
                    "salary_comments": "504 Stelle.",
                    "company_contact": PO_EXISTS,
                }
            },
            expected_num_docs=1,
        ),
    ).run()


@pytest.mark.asyncio
async def test_two_pages():
    await DossierTest(
        source_folder=path_root_folder / "two_pages",
        override_existing=True,
        dest_folder_prefix="330_salary_certificate_",
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
        ),
    ).run()
