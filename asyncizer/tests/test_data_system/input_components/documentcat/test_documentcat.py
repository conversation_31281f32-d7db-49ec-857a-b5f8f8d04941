from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.DocumentCatElement import DocumentCatElement
from hypodossier.core.domain.DocumentTopic import DocumentTopic


def test_documentcat():

    sorted_doc_cat = sorted([doccat.name for doccat in DocumentCat])

    for dc in DocumentCat:
        doccat: DocumentCatElement = dc.value
        if doccat.topic == DocumentTopic.FINANCING:
            print(f"DocumentCat.{dc.name},")

    print(sorted_doc_cat)
    print(f"{len(DocumentCat) = }")

    assert sorted_doc_cat == [
        "ACCEPTANCE_OF_MORTAGE_OFFER",
        "ACCOUNTS_RECEIVABLE_PAYABLE_COMPANY",
        "ADDITIONAL_COST_ACCOUNT",
        "AFFORDABILITY_CALCULATION",
        "AGREEMENT_CHARGE_IMMOVABLE_PROPERTY",
        "ANNUAL_REPORT_COMPANY",
        "ARCHITECT_CONTRACT",
        "ASSET_INCOME",
        "ASSUMPTION_DEBT_NOTICE",
        "AUDIT_REPORT_COMPANY",
        "AUTHORIZATION_EMAIL",
        "AUTHORIZATION_FOR_INQUIRIES",
        "BALANCE_SHEET_COMPANY",
        "BANK_DOCUMENT",
        "BANK_MISC",
        "BANK_STATEMENT_OF_INTEREST_CAPITAL",
        "BASE_CONTRACT",
        "BEKB_EKD108",
        "BEKB_EKD120",
        "BEKB_EKD142",
        "BEKB_FIPLA_FORM",
        "BEKB_FIPLA_RESULT",
        "BILL_MISC",
        "BONUS_REGULATIONS",
        "BROKER_AUTHORIZATION",
        "BROKER_AUTHORIZATION_BANK_SECRECY",
        "BROKER_MANDATE",
        "BROKER_MISC",
        "BUDGET_CONTROL_COMPANY",
        "BUDGET_PLANNING",
        "BUILDING_DESCRIPTION",
        "BUILDING_RIGHTS_AGREEMENT",
        "BUILDING_RIGHTS_MISC",
        "BUILDING_RIGHT_INTEREST",
        "BUSINESS_PLAN_COMPANY",
        "CASH_FLOW_COMPANY",
        "CIVIL_STATUS_DOCUMENT",
        "CLIENTIS_DCB_ARBEITSHILFE_FIN",
        "COMPANY_MISC",
        "COMPENSATION_AGREEMENT",
        "CONDOMINIUM_MISC",
        "CONDOMINIUM_MIX",
        "CONFIRMATION_ALIMONY",
        "CONFIRMATION_OF_RESIDENCE",
        "CONSTRUCTION_ACCOUNT",
        "CONSTRUCTION_COMPANY_LIST",
        "CONSTRUCTION_CONTRACT",
        "CONSTRUCTION_COST_ESTIMATE",
        "CONSTRUCTION_COST_SUMMARY",
        "CONSTRUCTION_INSURANCE",
        "CONSTRUCTION_MISC",
        "CONSTRUCTION_PERMIT",
        "CONSTRUCTION_PLAN",
        "CONSTRUCTION_QUOTATION",
        "CONSTRUCTION_REGULATIONS",
        "CONSTRUCTION_REQUEST",
        "CONSUMER_LOAN",
        "CONTRACT_GENERAL_CONTRACTOR",
        "CONTRACT_OF_SALE",
        "CONTRACT_TOTAL_CONTRACTOR",
        "CORPORATE_BYLAWS",
        "CORRESPONDENCE_EMAIL",
        "CORRESPONDENCE_LETTER",
        "CORRESPONDENCE_NOTARY",
        "CREDITOR_CHANGE",
        "CREDITWORTHINESS_MISC",
        "CREDIT_CARD_BILL",
        "CREDIT_MISC",
        "CRIF_DATA_INFO",
        "CRIF_QUICK_CONSUMER_CHECK",
        "CRIF_TELEDATA",
        "CRIMINAL_RECORDS",
        "CV_CLIENT",
        "DAILY_ALLOWANCES",
        "DAYCARE_CONFIRMATION",
        "DEATH_CERTIFICATE",
        "DEBT_CERTIFICATE",
        "DEBT_COLLECTION_INFORMATION",
        "DEBT_COLLECTION_INFORMATION_BILL",
        "DEBT_COLLECTION_INFORMATION_ORDER_BETREIBUNGSCHALTER_PLUS",
        "DEBT_COLLECTION_INFORMATION_ORDER_CRESURA",
        "DEBT_COLLECTION_INFORMATION_ORDER_TELEDATA",
        "DEBT_COLLECTION_INFORMATION_RECEIPT",
        "DEED_OF_GIFT",
        "DETERMINATION_OF_BENEFICIARY",
        "DIVIDENDS",
        "DIVORCE_CONVENTION",
        "DIVORCE_DECREE",
        "DIVORCE_DOCUMENT",
        "DIVORCE_MISC",
        "DIVORCE_SEPARATION_AGREEMENT",
        "DONATION_CONFIRMATION",
        "DRAFT_CONTRACT_OF_SALE",
        "EASEMENT_CONTRACT",
        "EMPLOYMENT_CONFIRMATION",
        "EMPLOYMENT_CONTRACT",
        "ENERGY_CERTFICATE",
        "EXPENSE_REGULATIONS",
        "EXTRACT_AHV_ACCOUNT",
        "EXTRACT_FROM_LAND_REGISTER",
        "FILE_NOTE_FINANCING",
        "FINANCE_MISC",
        "FINANCIAL_STATEMENT_COMPANY",
        "FINANCING_CHECKLIST_DOCUMENTS",
        "FINANCING_CONFIRMATION",
        "FINANCING_FEES_LIST",
        "FINANCING_MISC",
        "FINANCING_OFFER",
        "FIN_REPORTING_COMPANY",
        "FOREIGN_NATIONAL_ID",
        "FOUNDATION_CERTIFICATE_CONDOMINIUM",
        "FS24_AUTHORIZATION",
        "FS24_FACTSHEET",
        "FS24_INFO_DOSSIER",
        "FS24_MISC",
        "FS24_ZINSANNAHME",
        "GEAK_CERTIFICATE",
        "GENERAL_INFO",
        "GIS_INFO",
        "HBL_DARLEHENSZUSICHERUNG",
        "HBL_DIREKTAUFTRAG",
        "HBL_MORTGAGE_RENEWAL",
        "HBL_PRICING",
        "HEALTH_INSURANCE",
        "HRA",
        "HYPOGUIDE_ZINSANNAHME",
        "ID",
        "IDENTITY_MISC",
        "ID_OTHER",
        "IKO_CHECK",
        "IMMUTABLE_XLS",
        "INCOME_MISC",
        "INCOME_STATEMENT_COMPANY",
        "INCORPORATION_COMPANY",
        "INHERITANCE_ADVANCE",
        "INHERITANCE_CERTIFICATE",
        "INHERITANCE_MISC",
        "INHERITANCE_TESTAMENT",
        "INVESTMENT_PLAN_COMPANY",
        "IRREVOCABLE_PROMISES_TO_PAY",
        "LAND_REGISTER_BILL",
        "LAND_REGISTER_MISC",
        "LEASING_AGREEMENT",
        "LEGITIMATION_FDFA",
        "LETTER_COMMITMENT_NOTARY",
        "LIQUIDITY_PLAN_COMPANY",
        "LIST_OF_RENOVATIONS",
        "LOAN_AGREEMENT",
        "LOAN_AGREEMENT_SHAREHOLDER_COMPANY",
        "MARRIAGE_CONTRACT",
        "MB_CREDIT_APPROVAL_REQUEST",
        "MB_EKB",
        "MB_INTERNET_BANKING_MESSAGE",
        "MB_KINFO",
        "MB_SOKO",
        "MB_TOTAL_ENGAGEMENT",
        "MB_TOTAL_ENGAGEMENT_PROTOCOL",
        "MEETING_MINUTES_CONDOMINIUM",
        "MINERGIE_CERTIFICATE",
        "MISC_CAT",
        "MORTGAGE_CONTRACT",
        "MORTGAGE_CONTRACT_CONFIRMATION",
        "MORTGAGE_DUE_NOTICE",
        "MORTGAGE_FRAMEWORK_CONTRACT",
        "MORTGAGE_MISC",
        "MORTGAGE_PRODUCT_CONFIRMATION",
        "MORTGAGE_REQUEST_FORM",
        "MORTGAGE_SUBORDINATION_AGREEMENT",
        "MORTGAGE_TERMINATION",
        "NOTARY_MISC",
        "PASSPORT_CH",
        "PASSPORT_DE",
        "PASSPORT_FR",
        "PASSPORT_IT",
        "PASSPORT_OTHER",
        "PAYSLIP",
        "PENSION3A_ACCOUNT",
        "PENSION3A_CREDIT_NOTE",
        "PENSION3A_INSURANCE_CONTRACT",
        "PENSION3A_INSURANCE_LETTER_REDEMPTION",
        "PENSION3A_INSURANCE_STATEMENT",
        "PENSION3_INSURANCE_APPLICATION",
        "PENSION3_REGULATIONS",
        "PENSION_CERTIFICATE",
        "PENSION_CERTIFICATE_AHV",
        "PENSION_CERTIFICATE_CLOSING_STATEMENT",
        "PENSION_CERTIFICATE_CREDIT_NOTE",
        "PENSION_CERTIFICATE_INFO",
        "PENSION_CERTIFICATE_LETTER",
        "PENSION_CERTIFICATE_SIM_ALL",
        "PENSION_CONTRIBUTION_CONFIRMATION",
        "PENSION_MISC",
        "PENSION_PAYMENT_AHV",
        "PENSION_PAYMENT_BVG",
        "PENSION_PLEDGE",
        "PENSION_REGULATIONS",
        "PENSION_SIMULATION1",
        "PENSION_WITHDRAWL",
        "PENSION_WITHDRAWL_PURPOSE_CONFIRMATION",
        "PERSON_MISC",
        "PILLAR_ONE_CALCULATION",
        "PILLAR_ONE_MISC",
        "PILLAR_THREE_MISC",
        "PILLAR_TWO_MISC",
        "PLAN_ANY",
        "PLAN_CADASTER",
        "PLAN_FLOOR",
        "PLAN_SITUATION",
        "PLATFORM_AGREEMENT",
        "PLEDGE_NOTICE",
        "PLR_CADASTRE",
        "POWER_OF_ATTORNEY",
        "PREPAYMENT_PENALTY",
        "PROCESSING_ERROR",
        "PROJECT_BUDGET",
        "PROOF_OF_FUNDS",
        "PROOF_OF_INCOME",
        "PROPERTY_ACCOUNTS",
        "PROPERTY_BILL",
        "PROPERTY_DOCUMENTATION",
        "PROPERTY_INFO",
        "PROPERTY_INSURANCE",
        "PROPERTY_MISC",
        "PROPERTY_PHOTOS",
        "PROPERTY_TRUSTEE_CONTRACT",
        "PROPERTY_VALUATION",
        "PROPERTY_VALUATION_GOV",
        "PURCHASE_CONTRACT_REGISTRATION",
        "PURCHASE_MISC",
        "PURCHASE_PRICE_LIST_PROPERTY",
        "REGISTRATION_LAND_REGISTER",
        "RENOVATIONS",
        "RENOVATION_FUND",
        "RENTAL_MISC",
        "RESERVATION_CONTRACT",
        "RESERVATION_PAYMENT",
        "RESIDENCE_PERMIT",
        "RETIREMENT_ANALYSIS",
        "RISK_LIFE_INSURANCE",
        "SAFETY_CERTIFICATE_ELECTRICAL",
        "SALARY_ACCOUNT",
        "SALARY_BONUS",
        "SALARY_CERTIFICATE",
        "SALARY_CONFIRMATION",
        "SALARY_CONFIRMATION_13",
        "SALARY_CONFIRMATION_FORM",
        "SALES_DOCUMENTATION",
        "SCHUFA_BONITAETSCHECK",
        "SHARE_REGISTER",
        "SHORT_TIME_WORK",
        "SPECIMEN_SIGNATURE",
        "STATEMENT_OF_ASSETS",
        "STATEMENT_PENSION",
        "STATEMENT_VALUE_RATIO_PROPERTY",
        "TAX_ASSESSMENT",
        "TAX_ATTACHMENTS",
        "TAX_AT_SOURCE_CONFIRMATION",
        "TAX_BILL",
        "TAX_BUDGET",
        "TAX_CALCULATION",
        "TAX_DEBT_INVENTORY",
        "TAX_DECLARATION",
        "TAX_LIST_FINANCIAL_ASSETS",
        "TAX_MISC",
        "TENANCY_AGREEMENT",
        "TENANT_DIRECTORY",
        "TERMS_AND_CONDITIONS",
        "TRANSFER_AGREEMENT",
        "TRANSFER_OF_SECURITY",
        "TURNOVER_COMPANY",
        "UNEMPLOYMENT_SALARY_CERTIFICATE",
        "UNKNOWN",
        "UNKNOWN_DE",
        "UNKNOWN_EN",
        "UNKNOWN_FR",
        "UNKNOWN_IT",
        "USER_REGULATIONS_CONDOMINIUM",
        "US_PERSON_FORM",
        "VESTED_BENEFITS_ACCOUNT",
        "VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT",
        "VESTED_BENEFITS_STATEMENT",
        "VOLUME_CALCULATION_SIA",
        "VZ_GREY_CASE_APPLICATION",
        "WHITE_PAGES",
        "WORLD_CHECK",
        "YELLOW_IDENTIFICATION_POST",
        "ZEK_CHECK",
        "ZEK_INFO",
        "ZKB_17557_BONITAETSDOKUMENT_ALLGEMEIN_IGL",
        "ZKB_17627_COSTNG_PRICING_IGL",
        "ZKB_17988_FINANZUNTERLAGE_IGL",
        "ZKB_18190_JAHRESRECHNUNG_KUNDE_LEASING",
        "ZKB_18401_LEASINGANTRAG_FIRMENANGABE",
        "ZKB_18589_OBJEKTUNTERLAGEN_IGL",
        "ZKB_19643_AUSKUNFT_OBERZOLLDIREKTION_OZD_FUER_LSVA",
        "ZKB_19747_UNTERNEHMENSPROFIL",
        "ZKB_19807_ERFASSUNGSGESCHAEFT_HANDELSGESCHAEFTE",
        "ZKB_19833_LEASING_RAHMENLIMITE",
        "ZKB_30021_KORRESPONDENZ_IGL_ALLGEMEIN",
        "ZKB_VBV",
    ]

    # 240916 this includes DEATH_CERTIFICATE and MORTGAGE_SUBORDINATION_AGREEMENT
    # 241023 mt: +2 (DocumentCat.INCORPORATION_COMPANY, DocumentCat.BEKB_EKD142)
    # 250129 mt: +1 for VZ, +1 for Clientis
    # 250310 mt: +2 for BEKB_FIPLA
    assert len(DocumentCat) == 300
