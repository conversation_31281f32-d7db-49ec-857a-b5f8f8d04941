from pathlib import Path

from PIL import Image
from PIL.ImageFile import ImageFile

# This is needed to load heic and avif images
# Source: https://codecalamity.com/using-avif-and-heif-images-with-python-pil/
# Must be imported before PIL
from pillow_avif import AvifImagePlugin
from pillow_heif import register_heif_opener

register_heif_opener()

ImageFile.LOAD_TRUNCATED_IMAGES = True  # Optional: helps with broken files


def convert_any_image_to_jpeg(
    p_in: Path, p_dir_out: Path, extension: str = ".jpeg"
) -> Path:
    """
    Convert various advanced image formats to jpeg for further processing.
    This works by using PIL plugins, loading the image and saving as jpeg
    Currently supported: .avif, .heic, .heif
    @param p_in:
    @param p_dir_out:
    @param extension:
    @return:
    """
    # We need the import to support Avif images.
    # Reference here is just done so this line is not removed
    # import pillow_avif
    var_not_used_but_enforces_import = AvifImagePlugin
    assert var_not_used_but_enforces_import is not None

    p_out = p_dir_out / f"{p_in.stem}{extension}"

    img = Image.open(p_in)

    # Convert mode if needed
    if img.mode in ("RGBA", "LA", "P"):  # P = palette mode may also cause issues
        img = img.convert("RGB")

    img.save(p_out, format="jpeg")

    return p_out
