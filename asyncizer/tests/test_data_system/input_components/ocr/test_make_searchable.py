import shutil
from pathlib import Path
from uuid import uuid4

import pytest

from asyncizer.dossier_processor import (
    make_searchable,
    SearchablePage,
    cached_make_searchable,
)
from asyncizer.processing_config import OriginalFileProcessingConfig
from asyncizer.rpc_pika import get_rpc_client
from asyncizer.s3 import (
    upload_file,
    download_prefix,
    object_exists,
    make_bucket_available,
)
from constants import OUTPUT_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS


@pytest.mark.asyncio
async def test_make_searchable():
    # file_path = Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM) / "input_test/test-ocr/Bild_EFH_2.jpg"
    file_path = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS)
        / "ocr/Betreibungen Manuel Angelica.pdf"
    )

    assert file_path.exists()

    dest_folder = Path(OUTPUT_DIR) / "test_make_searchable"

    # setup rpc client, so there is just one instance
    await get_rpc_client()
    bucket = "test"
    await make_bucket_available(bucket)
    dossier_uuid = uuid4()
    dossier_name = f"{dossier_uuid}"

    object_name = f"{dossier_name}/extracted_files/{file_path.name}"

    await upload_file(bucket, object_name, file_path)

    searchable_pages = [
        page
        async for page in make_searchable(
            bucket=bucket,
            dossier=dossier_name,
            extracted_file=file_path.name,
            use_ocr_cache=True,
            processing_config=OriginalFileProcessingConfig(),
        )
    ]

    s1 = SearchablePage(
        bucket="test",
        dossier=dossier_name,
        extracted_file="Betreibungen Manuel Angelica.pdf",
        page_number=0,
        pdf_path=f"{dossier_name}/searchable/Betreibungen Manuel Angelica.pdf/0/out.pdf",
        text_file=f"{dossier_name}/searchable/Betreibungen Manuel Angelica.pdf/0/txt/0.txt",
        rotation_decision=True,
    )
    s2 = SearchablePage(
        bucket="test",
        dossier=dossier_name,
        extracted_file="Betreibungen Manuel Angelica.pdf",
        page_number=1,
        pdf_path=f"{dossier_name}/searchable/Betreibungen Manuel Angelica.pdf/1/out.pdf",
        text_file=f"{dossier_name}/searchable/Betreibungen Manuel Angelica.pdf/1/txt/0.txt",
        rotation_decision=True,
    )

    assert searchable_pages == [s1, s2] or searchable_pages == [s2, s1]

    print(searchable_pages)
    shutil.rmtree(dest_folder / dossier_name, ignore_errors=True)
    (dest_folder / dossier_name).mkdir(parents=True, exist_ok=True)
    await download_prefix(bucket, f"{dossier_name}", dest_folder / dossier_name)

    p1_pdf = dest_folder / s1.pdf_path
    assert p1_pdf.exists(), f"Could not find pdf for s1 at {p1_pdf}"

    p2_pdf = dest_folder / s2.pdf_path
    assert p2_pdf.exists(), f"Could not find pdf for s2 at {p2_pdf}"


@pytest.mark.asyncio
async def test_make_image_searchable():
    file_path = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS) / "ocr/Bild_EFH_2.jpg"
    )
    assert file_path.exists()
    dest_folder = Path(f"{OUTPUT_DIR}/test_make_image_searchable")

    # setup rpc client, so there is just one instance
    await get_rpc_client()

    bucket = "test"
    await make_bucket_available(bucket)
    dossier_uuid = uuid4()
    dossier_name = f"{dossier_uuid}"

    object_name = f"{dossier_name}/extracted_files/{file_path.name}"

    await upload_file(bucket, object_name, file_path)

    searchable_pages = [
        page
        async for page in make_searchable(
            bucket=bucket,
            dossier=dossier_name,
            extracted_file=file_path.name,
            # Change config to use no retries (single try)
            processing_config=OriginalFileProcessingConfig(ocr_max_num_tries=1),
        )
    ]

    assert searchable_pages == [
        SearchablePage(
            bucket="test",
            dossier=dossier_name,
            extracted_file="Bild_EFH_2.jpg",
            page_number=0,
            pdf_path=f"{dossier_name}/searchable/Bild_EFH_2.jpg/out.pdf",
            text_file=f"{dossier_name}/searchable/Bild_EFH_2.jpg/txt/0.txt",
            rotation_decision=False,
        )
    ]

    print(searchable_pages)
    shutil.rmtree(dest_folder / dossier_name, ignore_errors=True)
    (dest_folder / dossier_name).mkdir(parents=True, exist_ok=True)
    await download_prefix(bucket, f"{dossier_name}", dest_folder / dossier_name)


@pytest.mark.asyncio
async def test_make_two_office_pages_searchable(use_ocr_cache=True):
    file_path = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS)
        / "ocr/two offfice pages.odt"
    )
    assert file_path.exists()

    dest_folder = Path(f"{OUTPUT_DIR}/test_make_two_office_pages_searchable")

    # setup rpc client, so there is just one instance
    await get_rpc_client()
    bucket = "test"
    await make_bucket_available(bucket)

    dossier_uuid = uuid4()
    dossier_name = f"{dossier_uuid}"

    object_name = f"{dossier_name}/extracted_files/{file_path.name}"

    await upload_file(bucket, object_name, file_path)

    searchable_pages = [
        page
        async for page in make_searchable(
            bucket=bucket,
            dossier=dossier_name,
            extracted_file=file_path.name,
            use_ocr_cache=use_ocr_cache,
            # Do without retry
            processing_config=OriginalFileProcessingConfig(ocr_max_num_tries=1),
        )
    ]

    assert searchable_pages == [
        SearchablePage(
            bucket="test",
            dossier=dossier_name,
            extracted_file="two offfice pages.odt",
            page_number=0,
            pdf_path=f"{dossier_name}/searchable/two offfice pages.odt/pages/0.pdf",
            text_file=f"{dossier_name}/searchable/two offfice pages.odt/full.pdf/txt/0.txt",
            rotation_decision=True,
        ),
        SearchablePage(
            bucket="test",
            dossier=dossier_name,
            extracted_file="two offfice pages.odt",
            page_number=1,
            pdf_path=f"{dossier_name}/searchable/two offfice pages.odt/pages/1.pdf",
            text_file=f"{dossier_name}/searchable/two offfice pages.odt/full.pdf/txt/1.txt",
            rotation_decision=True,
        ),
    ]

    print(searchable_pages)
    shutil.rmtree(dest_folder / dossier_name, ignore_errors=True)
    (dest_folder / dossier_name).mkdir(parents=True, exist_ok=True)
    await download_prefix(bucket, f"{dossier_name}", dest_folder / dossier_name)


@pytest.mark.asyncio
async def test_make_cached_searchable():
    file_path = (
        Path(PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS) / "ocr/Bild_EFH_2.jpg"
    )
    assert file_path.exists()

    dest_folder = Path(f"{OUTPUT_DIR}/test_make_cached_searchable")

    # setup rpc client, so there is just one instance
    await get_rpc_client()
    bucket = "test"
    await make_bucket_available(bucket)

    dossier_uuid = uuid4()
    dossier_name = f"{dossier_uuid}"

    object_name = f"{dossier_name}/extracted_files/{file_path.name}"

    await upload_file(bucket, object_name, file_path)

    searchable_pages = [
        page
        async for page in cached_make_searchable(
            bucket=bucket,
            dossier=dossier_name,
            extracted_file=file_path.name,
            processing_config=OriginalFileProcessingConfig(),
        )
    ]

    assert searchable_pages == [
        SearchablePage(
            bucket="test",
            dossier=dossier_name,
            extracted_file="Bild_EFH_2.jpg",
            page_number=0,
            pdf_path=f"{dossier_name}/searchable_pages/Bild_EFH_2.jpg/0.pdf",
            text_file=f"{dossier_name}/searchable_pages/Bild_EFH_2.jpg/0.txt",
            rotation_decision=False,
        )
    ]

    for searchable_page in searchable_pages:
        assert await object_exists(bucket, searchable_page.pdf_path)
        assert await object_exists(bucket, searchable_page.text_file)

    print(searchable_pages)
    shutil.rmtree(dest_folder / dossier_name, ignore_errors=True)
    (dest_folder / dossier_name).mkdir(parents=True, exist_ok=True)
    await download_prefix(bucket, f"{dossier_name}", dest_folder / dossier_name)
