from dataclasses import dataclass
from pathlib import Path
from typing import List, <PERSON><PERSON>

from pypdf import PdfWriter, PdfReader

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.Extractions import Extractions
from hypodossier.core.domain.PageLocation import PageLocation
from hypodossier.core.domain.SemanticField import (
    FIELD_FULLNAME,
    FIELD_CANTON_SHORT,
    FIELD_YEAR,
    FIELD_FIRSTNAME,
    FIELD_LASTNAME,
)
from hypodossier.util.file_util import safe_filename

import structlog

logger = structlog.getLogger(__name__)

FIELDS_TITLE_DEFAULT = [FIELD_FULLNAME]
FIELDS_FILENAME_DEFAULT = [FIELD_FULLNAME, FIELD_CANTON_SHORT, FIELD_YEAR]

# 210915 mt: as filenames are no longer generated in the hyextract we switch from short titles to long titles
USE_LONG_TITLES = True
USE_LONG_FILENAMES = True

# Make sure with extension we are still under 100 chars
MAX_LENGTH_FILENAME_STEM = 95

# For these categories the filename will be appended only if the input is an image
LIST_OF_DOC_CAT_TO_APPEND_ORIGINAL_FILENAME_FOR_IMAGES = [
    DocumentCat.TAX_DECLARATION,
    DocumentCat.TAX_ASSESSMENT,
    DocumentCat.TAX_LIST_FINANCIAL_ASSETS,
    DocumentCat.TAX_DEBT_INVENTORY,
    DocumentCat.TAX_CALCULATION,
]

# For these categories the filename will be appended in all cases (independent of input format)
LIST_OF_DOC_CAT_TO_APPEND_ORIGINAL_FILENAME_ALWAYS = [
    DocumentCat.PROPERTY_PHOTOS,
    DocumentCat.CONSTRUCTION_COST_SUMMARY,
    DocumentCat.BILL_MISC,
]


def optionally_add_source_file_name(
    elements: List[str],
    doc_cat: DocumentCat,
    source_filename_with_ext: str,
    add_document_category: bool,
    replace_doc_cat_title: bool = True,
):
    """
    Add original filename only for photos and for tax stuff

    add_document_cat: serves as an information flag if the 2 elements for the doc cat are present or not
    """

    # num_doc_cat_prefix_elements: Either 2 if (111 Title_of_Doc_Cat) is the prefix or 0 if these 2 elements are not present
    num_doc_cat_prefix_elements = 2 if add_document_category else 0

    p = Path(source_filename_with_ext)
    ext: str = p.suffix.lower()
    is_image = ext in [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".tif",
        ".tiff",
        ".heic",
        ".heif",
    ]
    is_correct_doc_cat_for_image = (
        doc_cat in LIST_OF_DOC_CAT_TO_APPEND_ORIGINAL_FILENAME_FOR_IMAGES
    )

    is_correct_always = doc_cat in LIST_OF_DOC_CAT_TO_APPEND_ORIGINAL_FILENAME_ALWAYS

    if (is_image and is_correct_doc_cat_for_image) or is_correct_always:
        has_suffix = len(elements) > num_doc_cat_prefix_elements

        filename = p.stem.strip()

        if replace_doc_cat_title:
            if num_doc_cat_prefix_elements >= 2:
                doc_cat_title = elements[1]
                filename = filename.replace(doc_cat_title, "_")

        if has_suffix:
            # Inject original filename after doc cat id and doc cat title because we need it for sorting
            elements = (
                elements[0:num_doc_cat_prefix_elements]
                + [filename]
                + elements[num_doc_cat_prefix_elements:]
            )
        else:
            elements.append(filename)
    return elements


@dataclass
class FileCreator:
    require_firstname: bool = True
    custom_title_elements: List[str] = None

    def get_best_effort_name(self, extractions, require_firstname: bool = True):
        firstname = extractions.get_value(FIELD_FIRSTNAME.name)
        if firstname:
            if len(firstname) > 30:
                firstname = None

        lastname = extractions.get_value(FIELD_LASTNAME.name)
        if lastname:
            if len(lastname) > 30:
                lastname = None

        fullname = extractions.get_value(FIELD_FULLNAME.name)
        if fullname:
            # There are easily names with length 30.. so better make this long
            if len(fullname) > 40:
                fullname = None

        best_effort_name = firstname
        if firstname and lastname:
            best_effort_name = firstname + " " + lastname
        elif require_firstname:
            if firstname and fullname and firstname in fullname:
                best_effort_name = fullname
        else:
            if fullname:
                best_effort_name = fullname

        return best_effort_name

    def create_file(
        self, path_output_dir: Path, filename: str, page_sources: List[PageLocation]
    ) -> Path:
        # Loop over pages, concatenate PDF and save it with new filename
        # Return path to new file

        path_output_dir.mkdir(parents=True, exist_ok=True)
        writer = PdfWriter()
        path_output_file = path_output_dir.joinpath(filename)
        logger.info("Write file", filename=filename, path_output_dir=path_output_dir)
        with path_output_file.open("wb") as out:
            for p in page_sources:
                path_page = Path(p.path)
                pdf = PdfReader(str(path_page))
                writer.add_page(pdf.pages[p.page_index])
            writer.write(out)

        return path_output_file

    def compose_document_filename(
        self,
        doc_cat,
        extractions: Extractions,
        filename: str,
        source_filename_with_ext: str,
        client_lang: str,
        pages: List,
    ) -> Tuple[str, List[str]]:
        title, elements = self.compose_document_title(
            doc_cat,
            extractions,
            filename,
            source_filename_with_ext,
            client_lang,
            pages,
            USE_LONG_FILENAMES,
        )
        # return f'{safe_filename(title)}_XXX_{page_source.filename} {page_source.page_index}.pdf'

        filename_stem = safe_filename(title)

        if len(filename_stem) > MAX_LENGTH_FILENAME_STEM:
            filename_stem = filename_stem[0:MAX_LENGTH_FILENAME_STEM]

        return f"{filename_stem}.pdf", elements

    def compose_document_title(
        self,
        doc_cat,
        extractions: Extractions,
        filename: str,
        source_filename_with_ext: str,
        client_lang: str,
        pages: List,
        long_version=USE_LONG_TITLES,
        add_document_category=True,
    ):
        elements = []
        if add_document_category:
            d = doc_cat.value.trans(client_lang)
            elements += [str(doc_cat.value.docid), d]

        if self.custom_title_elements:
            elements += self.custom_title_elements
        else:
            elements += self.get_document_title_elements(
                [], extractions, filename, client_lang, pages, long_version
            )
        elements = list(filter(None, elements))  # remove empty entries from the list

        elements = optionally_add_source_file_name(
            elements, doc_cat, source_filename_with_ext, add_document_category
        )

        s = " ".join(elements)
        s = s.replace("\n", " ")
        return s, elements

    def get_document_title_elements(
        self,
        elements: List[str],
        extractions: Extractions,
        filename: str,
        client_lang: str,
        pages: List,
        long_version: bool,
    ):
        fields = FIELDS_FILENAME_DEFAULT if long_version else FIELDS_TITLE_DEFAULT
        for f in fields:
            if f.name in extractions:
                elements.append(extractions[f.name].value)
        return elements

    def __repr__(self):
        return self.__class__.__name__
