# from abbyyplumber.converter.ValueConverter import <PERSON><PERSON><PERSON><PERSON>er, ParagraphConverter, CleanNameConverter
# from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
# from abbyyplumber.plumberstudio.SearchElement import SearchElement<PERSON><PERSON><PERSON><PERSON><PERSON>, \
#     SearchElementMultiLabeledField, SearchElementReference, SearchElementConstrainedArea, SearchElementFirstname
# from abbyyplumber.plumberstudio.SearchRelation import SearchRelationBelow
# from abbyyplumber.util.plumberstudio_util import PercentageRange
# from hypodossier.core.domain.SemanticField import FIELD_FULLNAME, FIELD_DOCUMENT_DATE, FIELD_AHV_NEW, FIELD_PERSON_ID, \
#     FIELD_PRODUCT, FIELD_FIRSTNAME, FIELD_DATE_OF_BIRTH
# from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import \
#     StandardPensionCertificatePageOnePageParser, \
#     StandardPensionCertificatePageTwoPageParser
# from mortgageparser.util.search_element_util import create_element_company, create_element_product, \
#     create_search_element_firstname
# from mortgageparser.util.string_utils import contains_all_strings
#
#
# class ZurichPensionCertificatePageOnePageParser(StandardPensionCertificatePageOnePageParser):
#
#
#     def match_page_by_text(self, page_index: int, text: str) -> bool:
#         # There is another version without the words 'Sammelstiftung Vita' which is handled with a generic class
#         return contains_all_strings(text, [
#             'Sammelstiftung Vita', 'Vorsorgeausweis', 'Vertrags-Nummer:', 'Altersleistungen', 'Alterskapital', 'Stand am'
#
#         ])
#
#     def parse_page_header(self):
#         success = self.page.set_header_by_text("Vertrags-Nummer", include_pattern=False)
#         return success
#
#     def parse_page_footer(self):
#         success = self.page.set_footer_by_percentage(2 / 30)
#         return success
#
#     def create_content_extractor(self) -> ContentExtractor:
#         return ContentExtractor([
#             SearchElementLabeledField(FIELD_DOCUMENT_DATE.name, self.page.main, label="Stand am",
#                                       converter=DateConverter()),
#
#             SearchElementConstrainedArea(FIELD_FULLNAME.name, self.page.main, text_top="Stand am",
#                                          text_left="Name/Vorname", text_bottom="Geburtsdatum",
#                                          compress_whitespace=True,
#                                          converter=CleanNameConverter()),
#             SearchElementFirstname(FIELD_FIRSTNAME.name, self.page.main, target_name=FIELD_FULLNAME.name, compress_whitespace=True,
#                                    converter=CleanNameConverter()),
#             SearchElementConstrainedArea(FIELD_DATE_OF_BIRTH.name, self.page.main, text_top="Name/Vorname",
#                                          text_left="Geburtsdatum", text_bottom="Policen-Nummer",
#                                          compress_whitespace=True,
#                                          converter=DateConverter()),
#             SearchElementMultiLabeledField(FIELD_AHV_NEW.name, self.page.main, labels={"AHV-Nummer": 0.2, "AHV-Nr.": 1},
#                                       field_vertical_line_scale=2.5,
#                                       relations=[SearchRelationBelow(FIELD_FULLNAME.name)]),
#             SearchElementReference(FIELD_PERSON_ID.name, ref=FIELD_AHV_NEW),
#             create_element_company("Zürich"),
#
#             # This can be 'Vita' or 'Vita Plus'
#             # Source:
#             # Sammelstiftung Vita Plus der
#             # Zürich Lebensversicherungs-Gesellschaft AG
#             # or
#             # Sammelstiftung Vita
#             # Vorsorgewerk
#             SearchElementConstrainedArea(FIELD_PRODUCT.name, self.page.header, text_top="ZURICH", text_left="Sammelstiftung", text_bottom="Vorsorgewerk", compress_whitespace=True,
#                                          # Keep only first line and keep only at max 2 words (1 space in between), so 'Vita' or 'Vita Plus'
#                                          converter=ParagraphConverter(max_num_lines=1, max_num_spaces_per_line=1)),
#
#         ])
#
#
