from pathlib import Path

import pytest
import structlog

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM
from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250509_email_encoding_problem"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/vontobel/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_vontobel_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_encoding_problem():

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder,
        show_page=-1,
        source_file_filter=["encoding.msg"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: 1,
                DocumentCat.BUILDING_DESCRIPTION: 1,
                DocumentCat.CORRESPONDENCE_EMAIL: 5,
                DocumentCat.EXTRACT_FROM_LAND_REGISTER: 4,
                DocumentCat.LIST_OF_RENOVATIONS: 1,
                DocumentCat.PROPERTY_INSURANCE: 1,
                DocumentCat.TAX_DECLARATION: 21,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()
