from abbyyplumber.api import Page
from abbyyplumber.converter.ValueConverter import (
    DateConverter,
    YearConverter,
    CleanNameConverter,
    NewAhvConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementRegex,
    SearchElementArea,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    ReferenceBoundaryVertical,
    ReferenceBoundaryHorizontal,
    SearchRelationRightOf,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_FULLNAME,
    P1_DATE_OF_BIRTH,
    P2_DATE_OF_BIRTH,
    P1_AHV_NEW,
    P2_AHV_NEW,
    P1_PROFESSION,
    P2_PROFESSION,
    P1_EMPLOYER,
    P2_EMPLOYER,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_YEAR
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)


class TaxDeclarationSOPersonalDataPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA
        self.canton_short = "SO"

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = []
        elements += [
            SearchElementConstrainedArea(
                FIELD_YEAR.name,
                page.main,
                text_top="Personalien, Berufs- und",
                text_left="Familienverhältnisse am 31. 12.",
                text_bottom="Steuerpflichtige",
                converter=YearConverter(),
            ),
            SearchElementConstrainedArea(
                "address_inside",
                page.main,
                text_top="direkte Bundessteuer",
                text_left="Kanton Solothurn",
                text_bottom="Personalien, Berufs",
                compress_whitespace=True,
            ),
            # SearchElementMultiStaticText("153", page.main, target_name="address_inside", labels={"151-":0, "153-":0},
            #
            #                         relations=[SearchRelationAbove(FIELD_YEAR.name)]),
            # something like 155-123-45, first number seems to be mostly in range 130-160
            SearchElementRegex(
                "person_id",
                page.main,
                target_name="address_inside",
                regex="(\d\d\d[-]\d\d\d[-]\d\d)",
            ),
            SearchElementConstrainedArea(
                P1_FULLNAME.name,
                page.main,
                target_name="address_inside",
                compress_whitespace=True,
                converter=CleanNameConverter(max_num_lines=1),
                relations=[
                    SearchRelationBelow(
                        "person_id", ref_boundary=ReferenceBoundaryVertical.CENTER
                    )
                ],
            ),
            SearchElementConstrainedArea(
                "target_personal_info",
                page.main,
                extract=False,
                text_top="Personalien, Berufs-",
                text_bottom="Hat sich die Bankverbindung",
            ),
            SearchElementConstrainedArea(
                "p_left",
                page.main,
                target_name="target_personal_info",
                extract=False,
                text_left="Steuerpflichtige(r)",
                text_left_offset_chars=-2,
                text_left_boundary=ReferenceBoundaryHorizontal.LEFT,
                text_right="Ehefrau, ein",
                text_right_offset_chars=-1,
            ),
            # Person 2 is too close to Person 1
            SearchElementArea(
                "p_right",
                page.main,
                target_name="target_personal_info",
                extract=False,
                relations=[SearchRelationRightOf("p_left")],
            ),
            SearchElementConstrainedArea(
                P1_AHV_NEW.name,
                page.main,
                target_name="p_left",
                text_top="Pers. Nr",
                text_left="Geburtsdatum",
                text_bottom="Geburtsdatum",
                converter=NewAhvConverter(),
            ),
            SearchElementConstrainedArea(
                P1_DATE_OF_BIRTH.name,
                page.main,
                target_name="p_left",
                text_top="AHV-Nr",
                text_left="Geburtsdatum",
                text_bottom="Zivilstand",
                text_right="Konf",
                converter=DateConverter(),
            ),
            SearchElementConstrainedArea(
                P1_PROFESSION.name,
                page.main,
                target_name="p_left",
                text_top="getrennt",
                text_left="Beruf",
                text_bottom="Arbeitgeber",
            ),
            SearchElementConstrainedArea(
                P1_EMPLOYER.name,
                page.main,
                target_name="p_left",
                text_top="Beruf",
                text_left="Arbeitgeber",
                text_bottom="Arbeitsort",
            ),
            SearchElementConstrainedArea(
                P2_AHV_NEW.name,
                page.main,
                target_name="p_right",
                text_top="Pers. Nr",
                text_left="Geburtsdatum",
                text_bottom="Geburtsdatum",
                converter=NewAhvConverter(),
            ),
            SearchElementConstrainedArea(
                P2_DATE_OF_BIRTH.name,
                page.main,
                target_name="p_right",
                text_top="AHV-Nr",
                text_left="Geburtsdatum",
                text_bottom="Partnerschaft",
                text_right="Konf",
                converter=DateConverter(),
            ),
            SearchElementConstrainedArea(
                P2_PROFESSION.name,
                page.main,
                target_name="p_right",
                text_top="Partnerschaft",
                text_left="Beruf",
                text_bottom="Arbeitgeber",
            ),
            SearchElementConstrainedArea(
                P2_EMPLOYER.name,
                page.main,
                target_name="p_right",
                text_top="Beruf",
                text_left="Arbeitgeber",
                text_bottom="Arbeitsort",
            ),
            # Does not work because the line below the text is converted to underscores and messes up the email address
            # SearchElementConstrainedArea(FIELD_EMAIL.name, page.main,
            #                              text_top="Arbeitgeber/in",
            #                              text_left="E-mail:",
            #                              text_bottom="Vertretung nur zugestellt",
            #
            #                              converter=EmailConverter()),
        ]
        return elements
