from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/725_financing_offer"
)
dest_folder_prefix = "input_system_local_doccat_725_financing_offer_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_epotek_fr():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "epotek_2022",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["epotek_2022.pdf"],
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency={DocumentCat.FINANCING_OFFER: 1},
            expected_page_objects={
                "0": {
                    "company": "e-Potek",
                },
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_offer_migrosbank():
    await DossierTest(
        override_existing=True,
        show_page=-1,
        source_folder=source_folder / "migrosbank",
        source_file_filter=["725 MB Finanzierungsvorschlag.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.FINANCING_OFFER: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.FINANCING_OFFER: 8,
                DocumentCat.PROPERTY_PHOTOS: 1,
            },
            expected_page_objects={
                "0": {
                    "company": "Migros Bank",
                },
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_offer_moneypark():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "moneypark",
        source_file_filter=["moneypark_fr.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.FINANCING_OFFER: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.FINANCING_OFFER: 3,
            },
            expected_page_objects={
                "0": {
                    "company": "MoneyPark",
                },
            },
        ),
    ).run()
