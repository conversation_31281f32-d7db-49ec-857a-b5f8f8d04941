from pathlib import Path

import natsort as natsort
from itertools import islice

from hypodossier.unpack import unpack_path

import structlog

logger = structlog.getLogger(__name__)


def unpack_list_of_zip_files(path_root: Path, max_num_items=None):
    """
    CAUTION: This replaces the zip file with a folder of the same name. ZIP file does not exist anymore after running this!
    :param path_root:
    :param max_num_items:
    :return:
    """
    logger.info("Extract zip files inplace", path_root=path_root)
    fs24files = islice(
        natsort.os_sorted(list(path_root.glob("*.zip"))), 0, max_num_items
    )

    for f in fs24files:
        logger.info("Now handle next file", f=f)
        ret = unpack_path(f)
        print(ret)


if __name__ == "__main__":
    unpack_list_of_zip_files(
        Path("D:/exo/hypodossier_training/data_fs24_201208_unpacked"),
        max_num_items=999999,
    )
