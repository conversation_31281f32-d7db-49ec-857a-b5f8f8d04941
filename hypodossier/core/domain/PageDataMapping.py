from hypodossier.core.documents.bank_account.BankAccountPageData import (
    BankAccountPageData,
)
from hypodossier.core.documents.correspondence.CorrespondencePageData import (
    EmailCorrespondencePageData,
)
from hypodossier.core.documents.criminalrecords.CriminalRecordsPageData import (
    CriminalRecordsPageData,
)
from hypodossier.core.documents.debtcollectioninformation.DebtCollectionPageData import (
    DebtCollectionPageData,
)
from hypodossier.core.documents.financial_statement_company.FinancialStatementCompanyPageData import (
    FinancialStatementCompanyPageData,
)
from hypodossier.core.documents.generic_letter.GenericLetterPageData import (
    GenericLetterPageData,
)
from hypodossier.core.documents.mortgage.MortgagePageData import MortgagePageData
from hypodossier.core.documents.id_ch.IdChPageData import IdChPageData
from hypodossier.core.documents.land_register.LandRegisterPageData import (
    LandRegisterPageData,
)
from hypodossier.core.documents.hra.HraPageData import HraPageData
from hypodossier.core.documents.payslip.PayslipPageData import PayslipPageData
from hypodossier.core.documents.pension3_insurance.Pension3InsurancePageData import (
    Pension3InsurancePageData,
)
from hypodossier.core.documents.pension_contribution_confirmation.PensionContributionConfirmationPageData import (
    PensionContributionPageData,
)
from hypodossier.core.documents.pension_payment_all.PensionPaymentAllPageData import (
    PensionPaymentAllPageData,
)
from hypodossier.core.documents.pensioncertificate.PensionCertificatePageData import (
    PensionCertificatePageData,
)
from hypodossier.core.documents.property_insurance.PropertyInsurancePageData import (
    PropertyInsurancePageData,
)
from hypodossier.core.documents.property_valuation.PropertyValutionPageData import (
    PropertyValuationPageData,
)
from hypodossier.core.documents.property_valuation_gov.PropertyValutionGovPageData import (
    PropertyValuationGovPageData,
)
from hypodossier.core.documents.salarycertificate.SalaryCertificatePageData import (
    SalaryCertificatePageData,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    TaxDeclarationPageData,
)
from hypodossier.core.documents.vested_benefits.VestedBenefitsAccountPageData import (
    VestedBenefitsAccountPageData,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageData import PersonPageData, CompanyProductPageData

doc_cat_pagedata_dict = {}


def create_doc_cat_pagedata_dict():
    return {
        DocumentCat.PASSPORT_CH: IdChPageData(),
        DocumentCat.PASSPORT_DE: IdChPageData(),
        DocumentCat.PASSPORT_IT: IdChPageData(),
        DocumentCat.PASSPORT_FR: IdChPageData(),
        DocumentCat.PASSPORT_OTHER: IdChPageData(),
        DocumentCat.ID: IdChPageData(),
        DocumentCat.ID_OTHER: IdChPageData(),
        DocumentCat.FOREIGN_NATIONAL_ID: IdChPageData(),
        DocumentCat.RESIDENCE_PERMIT: IdChPageData(),
        DocumentCat.DEBT_COLLECTION_INFORMATION: DebtCollectionPageData(),
        DocumentCat.CRIMINAL_RECORDS: CriminalRecordsPageData(),
        DocumentCat.ZEK_CHECK: PersonPageData(),
        DocumentCat.IKO_CHECK: PersonPageData(),
        DocumentCat.WORLD_CHECK: GenericLetterPageData(),
        DocumentCat.CRIF_TELEDATA: CompanyProductPageData(),
        DocumentCat.CRIF_DATA_INFO: CompanyProductPageData(),
        DocumentCat.TAX_DECLARATION: TaxDeclarationPageData(),
        DocumentCat.TAX_CALCULATION: TaxDeclarationPageData(),
        DocumentCat.TAX_LIST_FINANCIAL_ASSETS: TaxDeclarationPageData(),
        DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: BankAccountPageData(),
        DocumentCat.BANK_DOCUMENT: BankAccountPageData(),
        DocumentCat.SALARY_CERTIFICATE: SalaryCertificatePageData(),
        DocumentCat.SALARY_CONFIRMATION: GenericLetterPageData(),
        DocumentCat.EMPLOYMENT_CONFIRMATION: GenericLetterPageData(),
        DocumentCat.PAYSLIP: PayslipPageData(),
        DocumentCat.SALARY_CONFIRMATION_FORM: GenericLetterPageData(),
        DocumentCat.TAX_AT_SOURCE_CONFIRMATION: GenericLetterPageData(),
        DocumentCat.PENSION_PAYMENT_AHV: PensionPaymentAllPageData(),
        DocumentCat.PENSION_PAYMENT_BVG: PensionPaymentAllPageData(),
        DocumentCat.UNEMPLOYMENT_SALARY_CERTIFICATE: PensionPaymentAllPageData(),
        DocumentCat.CONSUMER_LOAN: PersonPageData(),
        DocumentCat.CREDIT_CARD_BILL: PersonPageData(),
        DocumentCat.LOAN_AGREEMENT: PersonPageData(),
        DocumentCat.PENSION_CERTIFICATE_AHV: GenericLetterPageData(),
        DocumentCat.PENSION_SIMULATION1: PensionPaymentAllPageData(),
        DocumentCat.PENSION_CERTIFICATE: PensionCertificatePageData(),
        DocumentCat.PENSION_REGULATIONS: GenericLetterPageData(),
        DocumentCat.PENSION_WITHDRAWL: PensionCertificatePageData(),
        DocumentCat.PENSION_CERTIFICATE_SIM_ALL: PensionCertificatePageData(),
        DocumentCat.PENSION_CERTIFICATE_LETTER: PensionCertificatePageData(),
        DocumentCat.PENSION_CERTIFICATE_INFO: PensionCertificatePageData(),
        DocumentCat.PENSION_CERTIFICATE_CREDIT_NOTE: PensionCertificatePageData(),
        DocumentCat.PENSION_CERTIFICATE_CLOSING_STATEMENT: PensionCertificatePageData(),
        DocumentCat.VESTED_BENEFITS_ACCOUNT: VestedBenefitsAccountPageData(),
        DocumentCat.VESTED_BENEFITS_ACCOUNT_CLOSING_STATEMENT: PensionCertificatePageData(),
        DocumentCat.PENSION3A_ACCOUNT: BankAccountPageData(),
        DocumentCat.PENSION3A_CREDIT_NOTE: BankAccountPageData(),
        DocumentCat.PENSION3A_INSURANCE_CONTRACT: Pension3InsurancePageData(),
        DocumentCat.PENSION3A_INSURANCE_LETTER_REDEMPTION: Pension3InsurancePageData(),
        DocumentCat.PENSION3A_INSURANCE_STATEMENT: Pension3InsurancePageData(),
        DocumentCat.PENSION3_INSURANCE_APPLICATION: GenericLetterPageData(),
        DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION: PensionContributionPageData(),
        DocumentCat.PENSION_WITHDRAWL_PURPOSE_CONFIRMATION: PensionContributionPageData(),
        DocumentCat.RISK_LIFE_INSURANCE: PersonPageData(),
        DocumentCat.RETIREMENT_ANALYSIS: GenericLetterPageData(),
        DocumentCat.PENSION_MISC: GenericLetterPageData(),
        DocumentCat.HRA: HraPageData(),
        DocumentCat.FINANCIAL_STATEMENT_COMPANY: FinancialStatementCompanyPageData(),
        DocumentCat.EXTRACT_FROM_LAND_REGISTER: LandRegisterPageData(),
        DocumentCat.PROPERTY_INSURANCE: PropertyInsurancePageData(),
        DocumentCat.MINERGIE_CERTIFICATE: CompanyProductPageData(),
        DocumentCat.SALES_DOCUMENTATION: PropertyValuationPageData(),
        DocumentCat.PROPERTY_VALUATION: PropertyValuationPageData(),
        DocumentCat.PROPERTY_VALUATION_GOV: PropertyValuationGovPageData(),
        DocumentCat.PROPERTY_PHOTOS: PropertyValuationPageData(),
        DocumentCat.PROPERTY_INFO: PropertyValuationPageData(),
        DocumentCat.RESERVATION_CONTRACT: PersonPageData(),
        DocumentCat.BUILDING_DESCRIPTION: PersonPageData(),
        # 700 below here
        DocumentCat.MORTGAGE_REQUEST_FORM: PersonPageData(),
        DocumentCat.AUTHORIZATION_FOR_INQUIRIES: PersonPageData(),
        DocumentCat.AUTHORIZATION_EMAIL: PersonPageData(),
        DocumentCat.US_PERSON_FORM: PersonPageData(),
        DocumentCat.IRREVOCABLE_PROMISES_TO_PAY: PersonPageData(),
        DocumentCat.MORTGAGE_CONTRACT: MortgagePageData(),
        DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION: MortgagePageData(),
        DocumentCat.MORTGAGE_FRAMEWORK_CONTRACT: MortgagePageData(),
        DocumentCat.FINANCING_CONFIRMATION: PersonPageData(),
        DocumentCat.FINANCING_OFFER: GenericLetterPageData(),
        DocumentCat.ACCEPTANCE_OF_MORTAGE_OFFER: PersonPageData(),
        DocumentCat.MORTGAGE_CONTRACT_CONFIRMATION: PersonPageData(),
        DocumentCat.AFFORDABILITY_CALCULATION: PersonPageData(),
        DocumentCat.CREDITOR_CHANGE: GenericLetterPageData(),
        DocumentCat.TRANSFER_OF_SECURITY: PersonPageData(),
        DocumentCat.TRANSFER_AGREEMENT: PersonPageData(),
        DocumentCat.BASE_CONTRACT: PersonPageData(),
        DocumentCat.FILE_NOTE_FINANCING: GenericLetterPageData(),
        DocumentCat.DEBT_CERTIFICATE: CompanyProductPageData(),
        DocumentCat.PENSION_PLEDGE: PersonPageData(),
        DocumentCat.PLEDGE_NOTICE: PersonPageData(),
        DocumentCat.FINANCING_CHECKLIST_DOCUMENTS: CompanyProductPageData(),
        DocumentCat.FINANCING_FEES_LIST: CompanyProductPageData(),
        DocumentCat.TERMS_AND_CONDITIONS: CompanyProductPageData(),
        DocumentCat.GENERAL_INFO: CompanyProductPageData(),
        DocumentCat.BROKER_MANDATE: PersonPageData(),
        DocumentCat.BROKER_AUTHORIZATION_BANK_SECRECY: PersonPageData(),
        DocumentCat.BROKER_AUTHORIZATION: PersonPageData(),
        DocumentCat.BROKER_MISC: GenericLetterPageData(),
        DocumentCat.ASSUMPTION_DEBT_NOTICE: GenericLetterPageData(),
        DocumentCat.BEKB_EKD108: PersonPageData(),
        DocumentCat.FS24_FACTSHEET: GenericLetterPageData(),
        DocumentCat.FS24_AUTHORIZATION: GenericLetterPageData(),
        DocumentCat.FS24_ZINSANNAHME: GenericLetterPageData(),
        DocumentCat.HYPOGUIDE_ZINSANNAHME: GenericLetterPageData(),
        DocumentCat.MB_TOTAL_ENGAGEMENT: PersonPageData(),
        DocumentCat.MB_TOTAL_ENGAGEMENT_PROTOCOL: PersonPageData(),
        DocumentCat.MB_KINFO: PersonPageData(),
        DocumentCat.MB_EKB: PersonPageData(),
        DocumentCat.MB_SOKO: PersonPageData(),
        DocumentCat.MB_CREDIT_APPROVAL_REQUEST: PersonPageData(),
        DocumentCat.MB_INTERNET_BANKING_MESSAGE: PersonPageData(),
        DocumentCat.HBL_PRICING: PersonPageData(),
        DocumentCat.HBL_DARLEHENSZUSICHERUNG: PersonPageData(),
        DocumentCat.HBL_DIREKTAUFTRAG: PersonPageData(),
        DocumentCat.HBL_MORTGAGE_RENEWAL: PersonPageData(),
        # DocumentCat.ZKB_GVZ_VIEWER: PropertyInsurancePageData(),
        # DocumentCat.ZKB_RENT_CALCULATOR: PersonPageData(),
        # DocumentCat.ZKB_DOSSIER_SUMMARY: PersonPageData(),
        DocumentCat.ZKB_VBV: GenericLetterPageData(),
        DocumentCat.FINANCING_MISC: GenericLetterPageData(),
        DocumentCat.CORRESPONDENCE_EMAIL: EmailCorrespondencePageData(),
        DocumentCat.CREDITWORTHINESS_MISC: GenericLetterPageData(),
    }


def get_pagedata_by_doc_cat(dc: DocumentCat):
    global doc_cat_pagedata_dict
    if not doc_cat_pagedata_dict:
        doc_cat_pagedata_dict = create_doc_cat_pagedata_dict()
    if dc in doc_cat_pagedata_dict:
        return doc_cat_pagedata_dict[dc]
    else:
        return None


# if __name__ == '__main__':
#     count = 0
#     for d in DocumentCat:
#         if d.value.pagedata:
#             print(f'{d}: {d.value.pagedata.__class__.__name__}(),')
#             count += 1
#
#     print(f'count={count}')
