import shutil
from pathlib import Path
from shutil import copy

import extract_msg
import pytest
import structlog

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from constants import OUTPUT_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.unpack import unpack_path, FileExtraction

logger = structlog.getLogger(__name__)


path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS_LOCAL}/email/email_problems"
)
dest_folder_prefix = "input_system_components_local_email_problems_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


def test_unpack_01_exception():
    path_email = path_root_folder / "01_exception" / "Scan_von_HP_MFP.msg"
    assert path_email.exists()
    assert path_email.is_file()

    path_target_folder = Path(OUTPUT_DIR) / "unpack_tests" / "email" / "01_exception"
    if path_target_folder.exists():
        shutil.rmtree(path_target_folder)
    path_target_folder.mkdir(parents=True, exist_ok=True)
    assert path_target_folder.exists()

    path_target_email = path_target_folder / path_email.name

    copy(path_email, path_target_email)
    assert path_target_email.exists()
    assert path_target_email.is_file()
    file_extraction: FileExtraction = unpack_path(path_target_email)
    logger.info(f"file_extraction={file_extraction}")
    assert file_extraction.extracted_files == [
        "Scan_von_HP_MFP.msg/_Scan_2025-04-03_13-00-13.pdf"
    ]
    assert not file_extraction.exceptions


def test_parse_01_exception():
    path_email = path_root_folder / "01_exception" / "Scan_von_HP_MFP.msg"
    # msg: Message = extract_msg.Message(path_email)
    msg = extract_msg.openMsg(path_email)

    # Print email subject, body, sender, etc.
    logger.info("Subject:", subject=msg.subject)
    logger.info("From:", sender=msg.sender)
    logger.info("Date:", date=msg.date)
    logger.info("Body:", body=msg.body)
    path_target_folder = (
        Path(OUTPUT_DIR) / "unpack_tests" / "email" / "01_exception_parse"
    )

    if path_target_folder.exists():
        shutil.rmtree(path_target_folder)
    path_target_folder.mkdir(parents=True, exist_ok=True)


@pytest.mark.asyncio
async def test_01_exception():
    target_folder = path_root_folder / "01_exception"
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # No email is printed because body of email is empty
            expected_doc_cat_frequency={DocumentCat.UNKNOWN_DE: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.INHERITANCE_CERTIFICATE: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
        ),
    ).run()
