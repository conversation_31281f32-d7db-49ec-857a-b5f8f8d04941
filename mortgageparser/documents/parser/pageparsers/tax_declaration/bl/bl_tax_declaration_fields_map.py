from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_SELF_SIDE,
    P2_INCOME_SELF_SIDE,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    P1_INCOME_EO,
    P2_INCOME_EO,
    INCOME_PORTFOLIO,
    INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_CHILDREN,
    INCOME_OTHER,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    P1_CONTRIBUTION_PILLAR_2,
    P2_CONTRIBUTION_PILLAR_2,
    DEDUCTIONS_OTHER,
    DEDUCTIONS_TOTAL,
    EXPENSE_CHILDREN_DAYCARE,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_OTHER,
    ASSETS_GROSS_TOTAL,
    DEBT_PRIVATE,
    DEBT_BUSINESS,
    ASSETS_NET_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    INCOME_GROSS_TOTAL,
    ASSETS_CASH_GOLD,
    ASSETS_REAL_ESTATE_TOTAL_GROSS,
)
from hypodossier.core.domain.SemanticField import (
    FIELD_PROPERTY_MAINTENANCE_COST,
    FIELD_PROPERTY_IMPUTED_RENTAL_VALUE,
)

tax_bl_codes_income_1 = {
    "100": P1_INCOME_EMPLOYED_MAIN,
    "105": P2_INCOME_EMPLOYED_MAIN,
    "110": P1_INCOME_EMPLOYED_SIDE,
    "115": P2_INCOME_EMPLOYED_SIDE,
    "150": P1_INCOME_SELF_MAIN,
    "155": P2_INCOME_SELF_MAIN,
    "160": P1_INCOME_SELF_SIDE,
    "165": P2_INCOME_SELF_SIDE,
    "200": P1_INCOME_PENSION_AHV,
    "205": P2_INCOME_PENSION_AHV,
    "220": P1_INCOME_PENSION,
    "225": P2_INCOME_PENSION,
    "260": P1_INCOME_EO,
    "270": P2_INCOME_EO,
    "300": INCOME_PORTFOLIO,
    # There is no separation between P1 and P2, map it to P1
    "310": INCOME_ALIMONY_PARTNER,
    # There is no separation between P1 and P2, map it to P1
    "320": INCOME_ALIMONY_CHILDREN,
    "380": INCOME_OTHER,
}

tax_bl_codes_income_2 = {
    "400": FIELD_PROPERTY_IMPUTED_RENTAL_VALUE,
    "415": FIELD_PROPERTY_MAINTENANCE_COST,
    "499": INCOME_GROSS_TOTAL,
}

tax_bl_codes_deductions = {
    "520": P1_EXPENSE_EMPLOYMENT,
    "525": P2_EXPENSE_EMPLOYMENT,
    "550": INTEREST_PAID_ON_DEBT,
    "570": EXPENSE_ALIMONY_PARTNER,
    "575": EXPENSE_ALIMONY_CHILDREN,
    "580": EXPENSE_ANNUITY_CONTRIBUTIONS,
    "600": P1_CONTRIBUTION_PILLAR_2,
    "605": P2_CONTRIBUTION_PILLAR_2,
    "610": P1_CONTRIBUTION_PILLAR_3A,
    "615": P2_CONTRIBUTION_PILLAR_3A,
    "620": INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    "640": EXPENSE_CHILDREN_DAYCARE,
    "670": DEDUCTIONS_OTHER,
    "789": DEDUCTIONS_TOTAL,
    "790": INCOME_TAXABLE_LOCAL,
}

tax_bl_codes_assets = {
    "800": ASSETS_PORTFOLIO_ACCOUNTS,
    "805": ASSETS_CASH_GOLD,
    "810": ASSETS_LIFE_INSURANCE,
    "815": ASSETS_CARS,
    "825": ASSETS_OTHER,
    # it is not main property but all properties
    "841": ASSETS_REAL_ESTATE_TOTAL_GROSS,
    "885": ASSETS_GROSS_TOTAL,
    "890": DEBT_PRIVATE,
    "892": DEBT_BUSINESS,
    "899": ASSETS_NET_TOTAL,
    "910": ASSETS_TAXABLE_LOCAL,  # only in canton (not other cantons / countries)
}
