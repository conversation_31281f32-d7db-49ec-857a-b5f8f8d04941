from abbyyplumber.converter.ValueConverter import DateConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementLabeledField,
    SearchElementReference,
    SearchElementArea,
)
from abbyyplumber.util.plumberstudio_util import Per<PERSON>ageRange
from hypodossier.core.domain.SemanticField import (
    FIELD_DOCUMENT_DATE,
    FIELD_AHV_NEW,
    FIELD_PERSON_ID,
    FIELD_FIRSTNAME,
)
from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import (
    StandardPensionCertificatePageOnePageParser,
)
from mortgageparser.util.search_element_util import (
    create_element_company,
    create_search_elements_address,
)
from mortgageparser.util.string_utils import contains_all_strings


class SulzerPensionCertificateSinglePagePageParser(
    StandardPensionCertificatePageOnePageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "SVE",
                "Sulzer Vorsorgeeinrichtung",
                "Versicherungsausweis",
                "Stand Ihrer Versicherung am",
                "Altersguthaben",
            ],
        )

    def parse_page_header(self):
        success = self.page.set_header_by_any_text(
            ["Versicherungsausweis", "Stand Ihrer Versicherung"], include_pattern=False
        )
        if not success:
            success = self.page.set_header_by_percentage(6 / 30)
        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text("Dieser Ausweis ersetzt alle")
        return success

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementLabeledField(
                    FIELD_AHV_NEW.name,
                    self.page.header,
                    label="AHV-Nr.",
                    field_vertical_line_scale=2,
                ),
                SearchElementReference(FIELD_PERSON_ID.name, ref=FIELD_AHV_NEW.name),
                SearchElementLabeledField(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.main,
                    label="Stand Ihrer Versicherung am",
                    converter=DateConverter(),
                ),
                SearchElementArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.header,
                    x_range=PercentageRange(0, 0.5),
                ),
                create_element_company("Sulzer"),
            ]
            + create_search_elements_address(self.page.header, self.page)
        )
