from dataclasses import dataclass

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    TemplateGenericLetterPageParser,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    FromStartTextCond,
    FromBottomTextCond,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplateUnknownPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.UNKNOWN


def get_parsers_unknown():
    parsers = [
        TemplatePageParser(
            desc="Generic short second page letter DE 2021",
            page_cat=PageCat.QUITE_EMPTY_PAGE,
            doc_cat=DocumentCat.UNKNOWN_DE,
            max_num_chars_alpha=200,
            required_tokens_any=[["Freundliche Grüsse"], ["Seite 2 von 2"]],
        ),
        TemplatePageParser(
            desc="demo doc obi mietgeräge",
            page_cat=PageCat.GENERIC_PAGE,
            doc_cat=DocumentCat.UNKNOWN_DE,
            min_num_chars_alpha=400,
            required_text_conditions=[
                FromStartTextCond("Preisliste der OBI Mietgeräte", num_lines=3),
                FromStartTextCond("Art.-Nr.", num_lines=6),
            ],
        ),
        TemplateGenericLetterPageParser(  # Simple page with text
            doc_cat=DocumentCat.UNKNOWN,
            company="SBB",
            desc="SBB train schedule 2025",
            required_text_conditions=[
                FromStartTextCond(["Fahrplan", "Verbindung"], num_lines=3),
                FromStartTextCond(["Seite"], num_lines=3),
                FromBottomTextCond(["www.sbb.ch"], num_lines=1),
            ],
        ),
    ]
    return parsers
