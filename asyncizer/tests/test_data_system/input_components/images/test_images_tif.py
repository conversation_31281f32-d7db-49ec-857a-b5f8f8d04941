from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/images"
path_root_folder = Path(folder)
dest_folder_prefix = "input_system_components_images_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_tif_in_dossier():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "tif/random_tif_image",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency_per_page={DocumentCat.UNKNOWN: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_tiff_one_mb_in_dossier():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "tif/tiff_one_mb",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_PHOTOS: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_tiff_five_mb_in_dossier():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "tif/tiff_five_mb",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_PHOTOS: 1},
        ),
    ).run()
