from dataclasses import dataclass

from abbyyplumber.plumberstudio.SearchElement import SearchElementConstant
from hypodossier.core.documents.generic_letter.GenericLetterPageData import (
    FIELDS_GENERIC_LETTER,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import FIELD_COMPANY
from mortgageparser.documents.parser.generic_letter.generic_letter import (
    GenericLetterSearchElements,
)
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    RankedTitle,
)
from mortgageparser.documents.parser.pageparsers.TemplatePageParser import (
    TemplatePageParser,
)


@dataclass
class TemplatePensionWithdrawlPageParser(TemplatePageParser):
    page_cat: PageCat = PageCat.GENERIC_PAGE
    doc_cat: DocumentCat = DocumentCat.PENSION_WITHDRAWL
    company: str = None

    se: GenericLetterSearchElements = None

    def update_search_elements(self):
        super().update_search_elements_generic(self.se, FIELDS_GENERIC_LETTER.keys())

        if self.company:
            self.search_elements.append(
                SearchElementConstant(FIELD_COMPANY.name, self.company)
            )


def get_parsers_pension_withdrawl():
    parsers = [
        TemplatePensionWithdrawlPageParser(
            desc="AMAG Antragsformular PK Rückzahlung 2021",
            page_cat=PageCat.GENERIC_PAGE,
            company="AMAG",
            ranked_titles_all=[RankedTitle("ANTRAG KAPITALAUSZAHLUNG", 3, 8)],
            required_tokens_any=[
                ["AMAG Group Pensionskasse"],
                ["Höhe der Kapitalauszahlung"],
                ["Unterschrift Versicherte"],
            ],
        ),
        TemplatePensionWithdrawlPageParser(
            desc="Generisch Antragsformular PK Rückzahlung (z.B. AMAG) 2021",
            page_cat=PageCat.GENERIC_PAGE,
            ranked_titles_all=[RankedTitle("MERKBLATT KAPITALAUSZAHLUNG", 1, 8)],
            required_tokens_any=[["Pensionskasse"]],
        ),
    ]
    return parsers
