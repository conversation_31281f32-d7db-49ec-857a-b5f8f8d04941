import time
from itertools import islice
from pathlib import Path
from typing import List

import xlsxwriter
from natsort import os_sorted

from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.DocumentCatElement import DocumentCatElement
from hypodossier.shared import Seman<PERSON><PERSON><PERSON><PERSON>


def add_processing_issues(
    workbook: xlsxwriter.Workbook, semantic_dossiers: List[SemanticDossier]
):
    worksheet = workbook.add_worksheet(name="issues")
    headers = [
        "Dossier",
        "Filename",
        "Type",
        "HypoDossierException",
        "en",
        "de",
        "details",
    ]
    for idx, header in enumerate(headers):
        worksheet.write(0, idx, header)
    row = 1
    for semantic_dossier in semantic_dossiers:
        for (
            extracted_file,
            file_extractions,
        ) in semantic_dossier.extracted_files.items():
            for file, exception in file_extractions.exceptions.items():
                worksheet.write(row, 0, semantic_dossier.name)
                worksheet.write(row, 1, file)
                worksheet.write(row, 2, "extraction")
                worksheet.write(row, 3, exception.type.name)
                worksheet.write(row, 4, exception.en)
                worksheet.write(row, 5, exception.de)
                worksheet.write(row, 6, exception.details)
                row = row + 1

        for (
            file,
            processing_exception,
        ) in semantic_dossier.processing_exceptions.items():
            worksheet.write(row, 0, semantic_dossier.name)
            worksheet.write(row, 1, file)
            worksheet.write(row, 2, "processing")
            worksheet.write(row, 3, processing_exception.id.name)
            worksheet.write(row, 4, processing_exception.en)
            worksheet.write(row, 5, processing_exception.de)
            worksheet.write(row, 6, processing_exception.details)
            row = row + 1


def add_semantic_documents(
    workbook: xlsxwriter.Workbook, semantic_dossiers: List[SemanticDossier]
):
    worksheet = workbook.add_worksheet(name="semantic_documents")
    headers = [
        "dossier",
        "topic",
        "category_id",
        "category_name",
        "category_name_de",
        "semantic_filename",
        "num_pages",
        "page_sources",
    ]
    for idx, header in enumerate(headers):
        worksheet.write(0, idx, header)
    row = 1
    for semantic_dossier in semantic_dossiers:
        for idx, semantic_document in enumerate(semantic_dossier.semantic_documents):
            col = 0
            row = row + 1
            document_cat_element: DocumentCatElement = DocumentCat[
                semantic_document.document_category.name
            ].value
            worksheet.write(row, col, semantic_dossier.name)
            worksheet.write(row, col + 1, document_cat_element.topic.name)
            worksheet.write(row, col + 2, semantic_document.document_category.id)
            worksheet.write(row, col + 3, semantic_document.document_category.name)
            worksheet.write(row, col + 4, document_cat_element.de)
            worksheet.write(row, col + 5, semantic_document.filename)
            worksheet.write(row, col + 6, len(semantic_document.semantic_pages))
            worksheet.write(
                row,
                col + 7,
                "\n".join(
                    [
                        f"{page.source_file_path} {page.source_page_number}"
                        for page in semantic_document.semantic_pages
                    ]
                ),
            )

            # worksheet.write_url(row, col + 5, f"{semantic_dossier.name}/{semantic_document.filename}")


def analyse_semantic_dossier_in_folder(input_folder, dest_file):
    semantic_document_paths = list(
        islice(os_sorted(list(input_folder.rglob("semantic-document.json"))), 0, None)
    )
    workbook = xlsxwriter.Workbook(dest_file)
    semantic_dossiers = []
    for semantic_document_path in semantic_document_paths:
        semantic_dossiers.append(SemanticDossier.parse_file(semantic_document_path))
    add_semantic_documents(workbook, semantic_dossiers)
    add_processing_issues(workbook, semantic_dossiers)
    workbook.close()


if __name__ == "__main__":
    dest = Path("/home/<USER>/Documents/output/hbl_210408_v4")
    input_folder = Path("/home/<USER>/Documents/output/hbl_210408_v4")
    timestr = time.strftime("%Y%m%d-%H%M%S")
    analyse_semantic_dossier_in_folder(input_folder, f"{dest}/analysis_{timestr}.xlsx")
