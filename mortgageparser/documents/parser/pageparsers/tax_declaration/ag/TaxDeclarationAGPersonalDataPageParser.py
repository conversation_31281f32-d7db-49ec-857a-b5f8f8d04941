from abbyyplumber.api import Page
from abbyyplumber.converter.ValueConverter import (
    DateConverter,
    YearConverter,
    CleanNameConverter,
    CleanPhonenumberConverter,
    EmailConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    SearchElementLabeledField,
    SearchElementMultiStaticText,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationLeftOf,
    SearchRelationRightOf,
)
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_LASTNAME,
    P1_FIRSTNAME,
    P2_LASTNAME,
    P2_FIRSTNAME,
    P1_DATE_OF_BIRTH,
    P2_DATE_OF_BIRTH,
    P1_PROFESSION,
    P2_PROFESSION,
    P1_EMPLOYER,
    P2_EMPLOYER,
    P1_MARITAL_STATUS,
    P1_EMPLOYER_LOCATION,
    P2_EMPLOYER_LOCATION,
)
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_YEAR,
    FIELD_STREET,
    FIELD_PHONE_PRIMARY,
    FIELD_PHONE_SECONDARY,
    FIELD_EMAIL,
    FIELD_ZIP_CITY,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.StandardTaxDeclarationParser import (
    StandardKnnTaxDeclarationPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.ag_tax_declaration_fields_map import (
    create_search_elements_standard_ag,
)
from mortgageparser.util.search_element_util import (
    create_fields_zip_city,
    create_fields_person_from_p1,
)


class TaxDeclarationAGPersonalDataPageParser(StandardKnnTaxDeclarationPageParser):
    def __init__(self):
        super().__init__()
        self.page_cat = PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA
        self.canton_short = "AG"
        # self.required_search_results = [P1_FIRSTNAME.name, P1_LASTNAME.name]

    def create_search_elements(self, page: Page) -> ContentExtractor:
        elements = [
            SearchElementConstrainedArea(
                FIELD_YEAR.name,
                page.fullpage,
                text_left="Steuererklärung",
                text_right="Adress-Nr.",
                text_bottom="Kanton Aargau",
                converter=YearConverter(),
            ),
            SearchElementConstrainedArea(
                "data_table",
                page.fullpage,
                text_top="Personalien, Berufs- und",
                text_bottom="Minderjährige oder in",
                extract=False,
            ),
            SearchElementMultiStaticText(
                "data_table_right_col_marker",
                page.fullpage,
                labels={"Ehefrau/PartnerIn": 2},
                extract=False,
            ),
            SearchElementConstrainedArea(
                "data_table_left",
                page.fullpage,
                text_top="Personalien, Berufs- und",
                text_bottom="Minderjährige oder in",
                text_left="Geburtsdatum",
                relations=[
                    SearchRelationLeftOf(
                        "data_table_right_col_marker", offset=page.width_chars(-2)
                    )
                ],
                extract=False,
            ),
            SearchElementConstrainedArea(
                "data_table_right",
                page.fullpage,
                text_top="Personalien, Berufs- und",
                text_bottom="Minderjährige oder in",
                relations=[SearchRelationRightOf("data_table_left")],
                extract=False,
            ),
            SearchElementLabeledField(
                P1_LASTNAME.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Name",
                converter=CleanNameConverter(),
            ),
            SearchElementLabeledField(
                P2_LASTNAME.name,
                page.main,
                target_name="data_table_right",
                target_name_label="data_table",
                label="Name",
                converter=CleanNameConverter(),
            ),
            SearchElementLabeledField(
                P1_FIRSTNAME.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Vorname",
                converter=CleanNameConverter(),
            ),
            SearchElementLabeledField(
                P2_FIRSTNAME.name,
                page.main,
                target_name="data_table_right",
                target_name_label="data_table",
                label="Vorname",
                converter=CleanNameConverter(),
            ),
            SearchElementConstrainedArea(
                FIELD_PHONE_PRIMARY.name,
                page.main,
                target_name="data_table",
                text_top="PLZ/Ort",
                text_bottom="E-Mail",
                text_left="Tel.Privat",
                text_right="Tel. Geschäft",
                converter=CleanPhonenumberConverter(),
            ),
            SearchElementConstrainedArea(
                FIELD_PHONE_SECONDARY.name,
                page.main,
                target_name="data_table",
                text_top="PLZ/Ort",
                text_bottom="E-Mail",
                text_left="Tel. Geschäft",
                converter=CleanPhonenumberConverter(),
            ),
            SearchElementConstrainedArea(
                FIELD_EMAIL.name,
                page.main,
                target_name="data_table",
                text_top="Tel.Privat",
                text_bottom="Gemeinde",
                text_left="E-Mail",
                converter=EmailConverter(),
            ),
            SearchElementLabeledField(
                FIELD_STREET.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Strasse",
            ),
            SearchElementLabeledField(
                FIELD_ZIP_CITY.sr_inside,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="PLZ/Ort",
            ),
            SearchElementLabeledField(
                P1_DATE_OF_BIRTH.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Geburtsdatum",
                converter=DateConverter(),
            ),
            SearchElementLabeledField(
                P2_DATE_OF_BIRTH.name,
                page.main,
                target_name="data_table_right",
                target_name_label="data_table",
                label="Geburtsdatum",
                converter=DateConverter(),
            ),
            SearchElementLabeledField(
                P1_PROFESSION.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Beruf/Tätigk.",
            ),
            SearchElementLabeledField(
                P2_PROFESSION.name,
                page.main,
                target_name="data_table_right",
                target_name_label="data_table",
                label="Beruf/Tätigk.",
            ),
            SearchElementLabeledField(
                P1_EMPLOYER.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Arbeitgeber",
            ),
            SearchElementLabeledField(
                P2_EMPLOYER.name,
                page.main,
                target_name="data_table_right",
                target_name_label="data_table",
                label="Arbeitgeber",
            ),
            SearchElementLabeledField(
                P1_EMPLOYER_LOCATION.name,
                page.main,
                target_name="data_table_left",
                target_name_label="data_table",
                label="Arbeitsort",
            ),
            SearchElementLabeledField(
                P2_EMPLOYER_LOCATION.name,
                page.main,
                target_name="data_table_right",
                target_name_label="data_table",
                label="Arbeitsort",
            ),
            SearchElementLabeledField(
                P1_MARITAL_STATUS.name,
                page.main,
                target_name="data_table",
                target_name_label="data_table",
                label="Tarifbest.",
            ),
        ]
        elements += create_fields_zip_city(
            page.main, page
        ) + create_fields_person_from_p1(page.main, page)

        elements += create_search_elements_standard_ag(page)

        return elements
