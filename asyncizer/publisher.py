from abc import ABC

import aio_pika
from aio_pika.pool import Pool


class DossierEventPublisher(ABC):
    async def publish(self, type: str, event: bytes):
        pass


class RabbitMQDossierEventPublisher(DossierEventPublisher):
    def __init__(self, reply_to: str, channel_pool: Pool[aio_pika.Channel]):
        self.channel_pool = channel_pool
        self.reply_to = reply_to

    async def publish(self, type: str, event: bytes):
        async with self.channel_pool.acquire() as channel:
            await channel.default_exchange.publish(
                aio_pika.Message(type=type, body=event), self.reply_to
            )
