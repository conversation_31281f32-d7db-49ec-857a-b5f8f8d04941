from abbyyplumber.converter.ValueConverter import DateConverter
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementLabeledField,
    SearchElementArea,
    SearchElementStaticText,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    SearchRelationAbove,
    ReferenceBoundaryVertical,
)
from hypodossier.core.domain.SemanticField import FIELD_DOCUMENT_DATE, FIELD_FIRSTNAME
from mortgageparser.documents.parser.pageparsers.pensioncertificate.StandardPensionCertificateParser import (
    StandardPensionCertificatePageOnePageParser,
    StandardPensionCertificatePageTwoPageParser,
)
from mortgageparser.util.search_element_util import (
    create_element_company,
    create_search_elements_address,
)
from mortgageparser.util.string_utils import contains_all_strings


class SbbPensionCertificatePageOnePageParser(
    StandardPensionCertificatePageOnePageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Pensionskasse SBB",
                "Versicherungsausweis per",
                "Altersguthaben",
                "Einkaufsmöglichkeiten",
                "<EMAIL>",
            ],
        )

    def parse_page_footer(self):
        success = self.page.set_footer_by_text("Kundendienst")
        if not success:
            success = super().parse_page_footer()
        return success

    def create_content_extractor(self) -> ContentExtractor:
        return ContentExtractor(
            [
                SearchElementStaticText(
                    "text_addressblock_above",
                    self.page.main,
                    label="CH-3000 Bern 65",
                    max_l_dist=0,
                    extract=False,
                ),
                SearchElementStaticText(
                    "text_addressblock_below",
                    self.page.main,
                    label="Bern",
                    max_l_dist=0,
                    extract=False,
                    relations=[SearchRelationBelow("text_addressblock_above")],
                ),
                SearchElementArea(
                    FIELD_FIRSTNAME.sr_inside,
                    self.page.main,
                    relations=[
                        SearchRelationBelow(
                            "text_addressblock_above",
                            ref_boundary=ReferenceBoundaryVertical.CENTER,
                        ),
                        SearchRelationAbove(
                            "text_addressblock_below",
                            ref_boundary=ReferenceBoundaryVertical.CENTER,
                        ),
                    ],
                ),
                SearchElementLabeledField(
                    FIELD_DOCUMENT_DATE.name,
                    self.page.main,
                    label="Versicherungsausweis per",
                    converter=DateConverter(),
                ),
                create_element_company("SBB"),
            ]
            + create_search_elements_address(self.page.main, self.page)
        )


class SbbPensionCertificatePageTwoPageParser(
    StandardPensionCertificatePageTwoPageParser
):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(
            text,
            [
                "Bei Invalidität oder Tod",
                "Austrittsleistung",
                "Wohneigentumsförderung",
                "Pensionskasse SBB",
                "<EMAIL>",
            ],
        )

        return success
