import asyncio
import pickle
import uuid
from time import time
from typing import Optional

import aio_pika
import structlog
from aio_pika import Message, connect_robust
from aio_pika.abc import AbstractIncomingMessage, AbstractConnection

import global_settings

""" implements a async rpc client with aio_pika """

logger = structlog.getLogger(__name__)

MAX_RETRIES = 3  # Number of retries before failing
INITIAL_BACKOFF = 10  # Initial delay in seconds
BACKOFF_MULTIPLIER = 2  # Exponential backoff factor


class RpcClient:
    def __init__(self, loop, url):
        self.url = url
        self.connection: Optional[AbstractConnection] = None
        self.channel: Optional[aio_pika.Channel] = None
        self.callback_queue = None
        self.futures = {}
        self.loop = loop

    async def connect(self) -> "RpcClient":
        if self.loop is not asyncio.get_event_loop():
            # This is needed for parallel execution as there is one singleton
            # RpcClient but parallel execution of functions uses one
            # event loop per function so the event loop changes
            self.loop = asyncio.get_event_loop()

        self.connection = await connect_robust(self.url, loop=self.loop)
        self.channel = await self.connection.channel(on_return_raises=True)
        self.callback_queue = await self.channel.declare_queue(exclusive=True)
        await self.callback_queue.consume(self.on_response)

        return self

    async def on_response(self, message: AbstractIncomingMessage):
        correlation_id = message.correlation_id
        if correlation_id not in self.futures:
            await logger.awarning("Received unknown correlation_id: %s", correlation_id)
            await message.ack()
            return

        future = self.futures.pop(correlation_id, None)
        if not future or future.done():
            await logger.awarning(
                "Future already resolved or does not exist: %s", correlation_id
            )
            await message.ack()
            return

        try:
            if message.content_type == "pickle":
                try:
                    raise pickle.loads(message.body)
                except Exception as e:
                    future.set_exception(e)
            else:
                future.set_result(message.body)
        except Exception as e:
            future.set_exception(e)
        finally:
            await message.ack()
        # logger.info(f"message acked {message.correlation_id}")

    async def call(
        self, routing_key, request, timeout: float = 300.0, max_retries=MAX_RETRIES
    ):
        """

        @param routing_key:
        @param request:
        @param timeout: 300.0 means 300 seconds. 0.5 means 500 milliseconds.
        @param max_retries:
        @return:
        """
        correlation_id = str(uuid.uuid4())
        start = time()
        await logger.ainfo(
            "rpc start",
            routing_key=routing_key,
            correlation_id=correlation_id,
            timeout=timeout,
            max_retries=max_retries,
        )

        future = self.loop.create_future()
        self.futures[correlation_id] = future

        retries = 0
        backoff = INITIAL_BACKOFF

        try:
            while retries <= max_retries:
                try:
                    await self.channel.default_exchange.publish(
                        Message(
                            request.encode(),
                            content_type="application/json",
                            correlation_id=correlation_id,
                            reply_to=self.callback_queue.name,
                        ),
                        routing_key=routing_key,
                    )
                    return await asyncio.wait_for(future, timeout=timeout)

                # except :
                #     pass  # do the same as for the timeouterror with pass through
                except (asyncio.CancelledError, asyncio.TimeoutError):
                    # this should be solved by dlx queue, but it first needs to be created for e.g.
                    # - hyextract.PageAnalysisRequest
                    # - ObjectClassifier.PageObjectClassificationRequestV1
                    # - hylayoutlm.labeling.ImagePredictionRequest
                    if correlation_id in self.futures:
                        del self.futures[correlation_id]  # Clean up stale futures
                    else:
                        logger.ainfo(
                            "Found unknown correlation_id",
                            correlation_id=correlation_id,
                        )
                    await logger.aerror(
                        "rpc call timed out",
                        routing_key=routing_key,
                        attempt=retries + 1,
                        correlation_id=correlation_id,
                    )

                    if retries >= max_retries:
                        # So in case we did a retry the exception "e" is a
                        # CancelledError. We still return a TimeoutError
                        # to simplify usage of this method
                        raise asyncio.TimeoutError(
                            f"RPC call to {routing_key} timed out with retries={retries}, max_retries={max_retries}. correlations: {correlation_id}"
                        )

                    retries += 1
                    await asyncio.sleep(backoff)  # Wait before retrying
                    backoff *= BACKOFF_MULTIPLIER  # Increase the backoff time

                except Exception as e:
                    await logger.aexception(
                        "rpc failed",
                        routing_key=routing_key,
                        exc_info=e,
                        duration=time() - start,
                        correlation_id=correlation_id,
                    )
                    raise e
        finally:
            await logger.ainfo(
                "rpc end",
                routing_key=routing_key,
                duration=time() - start,
                correlation_id=correlation_id,
            )


RPC_CLIENT = None


async def get_rpc_client() -> RpcClient:
    """singleton of an rpc client instance"""
    global RPC_CLIENT
    if RPC_CLIENT is None:
        RPC_CLIENT = await RpcClient(
            asyncio.get_running_loop(), global_settings.RABBIT_URL
        ).connect()
    return RPC_CLIENT
