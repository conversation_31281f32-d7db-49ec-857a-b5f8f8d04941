from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/email_messages_msg"
)
dest_folder_prefix = "input_system_components_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_messages_msg_1():
    target_folder = path_root_folder
    assert target_folder.exists()
    # Contains a single email with multiple attachments
    # reflects a typical hypodossier email (no weird polish stuff or shopping lists)

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["Datenlieferung Demo-Dossier 1.msg"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE 000 Hypodossier Datenextraktion.pdf",
                "240 Betreibungsauskunft Maria Mustermann Keine Betreibungen 2014-04-11.pdf",
                "322 Bankdokument Mustermann Alex Zürcher Kantonalbank 2016-07-13.pdf",
                "330 Lohnausweis Maximilian Mustermann 2016.pdf",
                "410 PK Ausweis Max Mustermann Allianz 2017-03-03.pdf",
                "615 Fotos Liegenschaft 615 Property Photos visualisierung.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_gmail.com.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 3,
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 1,
                DocumentCat.LIQUIDITY_PLAN_COMPANY: 1,
                DocumentCat.PAYSLIP: 1,
                DocumentCat.PENSION_CERTIFICATE: 3,
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
                DocumentCat.UNKNOWN_EN: 1,
                DocumentCat.WHITE_PAGES: 1,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_msg_2():
    target_folder = path_root_folder
    assert target_folder.exists()
    # Contains a single email with multiple attachments
    # reflects a typical hypodossier email (no weird polish stuff or shopping lists)

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["Lieferung Steuererklärung als Einzelseiten.msg"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "210 Pass CH Manuel Antonius Thiemann 1977-09-04.pdf",
                "220 Identitätskarte CH Manuel Antonius Thiemann 1977-09-04.pdf",
                "220 Identitätskarte CH.pdf",
                "310 Steuererklärung IMG_9743 Mustermann Max ZH 2019.pdf",
                "738 Email-Korrespondenz manuel_at_thiemann.ch 11.09.2023.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 1,
                DocumentCat.ID: 2,
                DocumentCat.PASSPORT_CH: 1,
                DocumentCat.TAX_DECLARATION: 14,
            },
        ),
    ).run()
