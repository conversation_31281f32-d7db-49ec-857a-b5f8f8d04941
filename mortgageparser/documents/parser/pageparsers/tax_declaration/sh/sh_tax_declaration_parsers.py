from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementConstrainedArea,
    create_labeled_fields,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.tax_declaration.sh.tax_declaration_sh_fields_map import (
    tax_sh_codes_deductions,
    tax_sh_codes_assets,
    tax_sh_codes_income_main,
    tax_sh_codes_income_bottom,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.template_tax_declaration_util import (
    TemplateTaxDeclarationPageParser,
    TaxDeclarationSearchElements,
)

# no classification, just extraction - therefore, not part of the return statement at the bottom
TAX_DECLARATION_SH_INCOME_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax SH Income 2021",
    canton="SH",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_INCOME,
    regions=[
        SearchElementConstrainedArea(
            name="col_code_main",
            text_top="Ausland",
            text_left="Hilfsformular",
            text_right="Ausnahmen",
        ).exclude_right(-2),
        SearchElementConstrainedArea(
            name="col_val_main", text_top="Ausland", text_left="Ausnahmen"
        ).include_left(2),
        SearchElementConstrainedArea(
            name="col_code_bottom",
            text_top="Übertrag von Ziffer 6",
            text_left="Liegenschaftsverzeichnis",
            text_right="Kantonssteuer",
        ).exclude_right(-6),
        SearchElementConstrainedArea(
            name="col_val_bottom",
            text_top="Übertrag von Ziffer 6",
            text_left="Kantonssteuer",
            text_right="Kantonssteuer",
        )
        .include_left(-4)
        .include_right(4),
    ],
    se=TaxDeclarationSearchElements(
        **{
            search_element.name: search_element
            for search_element in [
                *create_labeled_fields(
                    tax_sh_codes_income_main,
                    "col_code_main",
                    "col_val_main",
                    max_l_dist=0,
                ),
                *create_labeled_fields(
                    tax_sh_codes_income_bottom,
                    "col_code_bottom",
                    "col_val_bottom",
                    max_l_dist=0,
                ),
            ]
        }
    ),
)

# no classification, just extraction - therefore, not part of the return statement at the bottom
TAX_DECLARATION_SH_DEDUCTIONS_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax SH Deductions 2021",
    canton="SH",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_DEDUCTIONS,
    regions=[
        SearchElementConstrainedArea(
            name="col_code",
            text_top="Kantonssteuer",
            text_left="Schuldenverzeichnis",
            text_right="Kantonssteuer",
        ).exclude_right(-8),
        SearchElementConstrainedArea(
            name="col_val",
            text_top="Kantonssteuer",
            text_left="Kantonssteuer",
            text_right="Kantonssteuer",
        )
        .include_left()
        .include_right(2),
    ],
    se=TaxDeclarationSearchElements(
        **{
            search_element.name: search_element
            for search_element in [
                *create_labeled_fields(
                    tax_sh_codes_deductions, "col_code", "col_val", max_l_dist=0
                )
            ]
        }
    ),
)

# no classification, just extraction - therefore, not part of the return statement at the bottom
TAX_DECLARATION_SH_ASSETS_2021 = TemplateTaxDeclarationPageParser(
    desc="Tax SH Assets 2021",
    canton="SH",
    doc_cat=DocumentCat.TAX_DECLARATION,
    page_cat=PageCat.TAX_DECLARATION_PAGE_ASSETS,
    regions=[
        SearchElementConstrainedArea(
            name="col_code",
            text_top="Steuerwert",
            text_left="Wertschriftenverzeichnis",
            text_right="Steuerwert",
        ).exclude_right(-2),
        SearchElementConstrainedArea(
            name="col_val", text_top="Steuerwert", text_left="Steuerwert"
        ).include_left(4),
    ],
    se=TaxDeclarationSearchElements(
        **{
            search_element.name: search_element
            for search_element in [
                *create_labeled_fields(
                    tax_sh_codes_assets, "col_code", "col_val", max_l_dist=0
                )
            ]
        }
    ),
)


def get_parsers_tax_declaration_sh():
    return []
