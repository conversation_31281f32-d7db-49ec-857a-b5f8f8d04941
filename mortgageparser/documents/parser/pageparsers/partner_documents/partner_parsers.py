from mortgageparser.documents.parser.pageparsers.partner_documents.allianz.allianz_parsers import (
    get_parsers_allianz,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.axa.axa_parsers import (
    get_parsers_axa,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.bekb.bekb_parsers import (
    get_parsers_bekb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.blkb.blkb_parsers import (
    get_parsers_blkb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.bskb.bskb_parsers import (
    get_parsers_bskb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.credit_agricole.credit_agricole_parsers import (
    get_parsers_credit_agricole,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.credit_suisse.credit_suisse_parsers import (
    get_parsers_credit_suisse,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.fs24.fs24_parsers import (
    get_parsers_fs24,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.generali.generali_parsers import (
    get_parsers_generali,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.glkb.glkb_parsers import (
    get_parsers_glkb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.grkb.grkb_parsers import (
    get_parsers_grkb,
)

from mortgageparser.documents.parser.pageparsers.partner_documents.hbl.hbl_parsers import (
    get_parsers_hbl,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.hypoplus.hypoplus_parsers import (
    get_parsers_hypoplus,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.hypothekenboerse.hypothekenboerse_parsers import (
    get_parsers_hypothekenboerse,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.iazi.iazi_parsers import (
    get_parsers_iazi,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.raiffeisen.raiffeisen_parsers import (
    get_parsers_raiffeisen,
)

from mortgageparser.documents.parser.pageparsers.partner_documents.regiobank_solothurn.regiobank_solothurn_parsers import (
    get_parsers_regiobank_solothurn,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.sgkb.sgkb_parsers import (
    get_parsers_sgkb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.soba.soba_parsers import (
    get_parsers_soba,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.swisslife.swiss_life_parsers import (
    get_parsers_swiss_life,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.swisslife_immopulse.swisslife_immopulse_parsers import (
    get_parsers_swisslife_immopulse,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.tgkb.tgkb_parsers import (
    get_parsers_tgkb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.ubs.ubs_parsers import (
    get_parsers_ubs,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.valiant.valiant_parsers import (
    get_parsers_valiant,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.valuu.valuu_parsers import (
    get_parsers_valuu,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.vontobel.vontobel_parsers import (
    get_parsers_vontobel,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.vz.vz_parsers import (
    get_parsers_vz,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.wuuest_partner.wueest_partner_parsers import (
    get_parsers_wueest_partner,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.zkb.zkb_parsers import (
    get_parsers_zkb,
)
from mortgageparser.documents.parser.pageparsers.partner_documents.zurich.zurich_parsers import (
    get_parsers_zurich,
)


def get_parsers_partners():
    return (
        get_parsers_allianz()
        + get_parsers_axa()
        + get_parsers_bekb()
        + get_parsers_blkb()
        + get_parsers_bskb()
        + get_parsers_credit_agricole()
        + get_parsers_credit_suisse()
        + get_parsers_fs24()
        + get_parsers_generali()
        + get_parsers_glkb()
        + get_parsers_grkb()
        + get_parsers_hbl()
        + get_parsers_hypoplus()
        + get_parsers_hypothekenboerse()
        + get_parsers_iazi()
        + get_parsers_raiffeisen()
        + get_parsers_regiobank_solothurn()
        + get_parsers_sgkb()
        + get_parsers_soba()
        + get_parsers_swiss_life()
        + get_parsers_swisslife_immopulse()
        + get_parsers_tgkb()
        + get_parsers_ubs()
        + get_parsers_valiant()
        + get_parsers_valuu()
        + get_parsers_vontobel()
        + get_parsers_vz()
        + get_parsers_wueest_partner()
        + get_parsers_zkb()
        + get_parsers_zurich()
    )


# Disable migrosbank parsers because many are too generic
# + get_parsers_migrosbank() \
