from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, PO_EXISTS, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_EMPLOYER,
    P2_FIRSTNAME,
    P1_MARITAL_STATUS,
    INCOME_GROSS_TOTAL,
    P1_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    INCOME_NET_TOTAL,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_CARS,
    ASSETS_GROSS_TOTAL,
    ASSETS_NET_TOTAL,
    ASSETS_TAXABLE_LOCAL,
)
from hypodossier.core.domain.DocumentCat import DocumentCat

source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/310_tax_declaration/cantons_de"
)
dest_folder_prefix = "input_system_local_doccat_310_tax_declaration_de_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_ag_single_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_single_2019",
        source_file_filter=["ag_single_2019.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 14},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2019",
                    "document_date": "22.02.2020",
                    "street": PO_EXISTS,
                    "zip": "5300",
                    "city": "Turgi",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p1_date_of_birth": "16.01.1978",
                    "p1_profession": "Sachbearbeiterin Treuhand",
                    "p1_employer": "Lienhard AG Tax & Consulting",
                    "p1_employer_location": "8002 Zürich",
                    "p1_marital_status": "ledig",
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_income_employed_main": "CHF 67'941",
                    "p1_contribution_pillar_3a": "CHF 6'826",
                    "income_portfolio": "CHF 329",
                    "income_gross_total": "CHF 68'270",
                    "income_net_total": "CHF 46'305",
                    "income_taxable_local": "CHF 46'305",
                    "p1_expense_employment": "CHF 7'378",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'000",
                    "deductions_wealth_management": "CHF 12",
                    "deductions_education": "CHF 4'919",
                    "deductions_donations": "CHF 830",
                    "deductions_total": "CHF 21'965",
                    "assets_portfolio": "CHF 64'007",
                    "assets_cash_gold": "CHF 105",
                    "assets_cars": "CHF 998",
                    "assets_gross_total": "CHF 65'110",
                    "assets_net_total": "CHF 61'853",
                    "assets_taxable_global": "CHF 0",
                    "assets_taxable_local": "CHF 0",
                    "debt_total": "CHF 3'257",
                    "interest_paid_on_debt": "CHF 0",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_double_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_double_2021",
        source_file_filter=["ag_double_2021.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 16},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2021",
                    "document_date": "11.03.2022",
                    "street": PO_EXISTS,
                    "zip": "5032",
                    "city": "Aarau Rohr",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_lastname": PO_EXISTS,
                    "p1_date_of_birth": "30.06.1984",
                    "p1_marital_status": "verheiratet (ungetrennt)",
                    "p1_profession": "Verkäufer",
                    "p1_employer": "Torex Handels AG",
                    "p2_date_of_birth": "07.08.1981",
                    "p2_employer": "Migros Aare",
                    "p1_income_employed_main": "CHF 84'672",
                    "p2_income_employed_main": "CHF 27'539",
                    "income_portfolio": "CHF 515",
                    "income_gross_total": "CHF 112'726",
                    "p1_contribution_pillar_3a": "CHF 3'000",
                    "p2_contribution_pillar_3a": "CHF 3'000",
                    "p1_expense_employment": "CHF 10'823",
                    "p2_expense_employment": "CHF 2'819",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "interest_paid_on_debt": "CHF 0",
                    "deductions_wealth_management": "CHF 125",
                    "deductions_donations": "CHF 270",
                    "deductions_total": "CHF 24'637",
                    "income_net_total": "CHF 88'089",
                    "income_taxable_local": "CHF 80'989",
                    "assets_portfolio": "CHF 202'307",
                    "assets_cash_gold": "CHF 167",
                    "assets_gross_total": "CHF 202'474",
                    "assets_net_total": "CHF 202'474",
                    "debt_total": "CHF 0",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_single_2020():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_single_2020",
        source_file_filter=["310_tax_ag_single_2020.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 16},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2020",
                    "document_date": "27.06.2021",
                    "street": PO_EXISTS,
                    "zip": "5603",
                    "city": "Staufen",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p1_date_of_birth": "25.11.1985",
                    "p1_profession": "Konstrukteur",
                    "p1_employer": "CanMan AG",
                    "p1_employer_location": "5705 Hallwil",
                    "p1_marital_status": "ledig, lebe im Konkubinat",
                    "phone_primary": PO_EXISTS,
                    "p1_income_employed_main": "CHF 72'606",
                    "p1_income_self_main": "CHF -1'105",
                    "p1_contribution_pillar_3a": "CHF 6'271",
                    "income_portfolio": "CHF 13",
                    "income_gross_total": "CHF 71'514",
                    "income_net_total": "CHF 52'634",
                    "income_taxable_local": "CHF 38'857",
                    "p1_expense_employment": "CHF 9'691",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'000",
                    "expense_children_daycare": "CHF 445",
                    "deductions_wealth_management": "CHF 50",
                    "deductions_illness": "CHF 423",
                    "deductions_total": "CHF 18'880",
                    "assets_portfolio": "CHF 159'432",
                    "assets_gross_business": "CHF 79'992",
                    "assets_gross_total": "CHF 159'482",
                    "assets_net_total": "CHF 159'482",
                    "assets_taxable_global": "CHF 35'482",
                    "assets_taxable_local": "CHF 35'482",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_single_2020_2():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_single_2020_2",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["ag_single_2020_2.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 13},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2020",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "5524",
                    "city": "Niederwil",
                    "p1_fullname": PO_EXISTS,
                    "p1_contribution_pillar_3a": "CHF 450",
                    "income_portfolio": "CHF 464",
                    "income_gross_total": "CHF 13'865",
                    "income_net_total": "CHF 2'853",
                    "income_taxable_local": "CHF 0",
                    "p1_expense_employment": "CHF 6'662",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'000",
                    "deductions_wealth_management": "CHF 84",
                    "deductions_illness": "CHF 1'616",
                    "deductions_education": "CHF 200",
                    "deductions_total": "CHF 11'012",
                    "assets_cash_gold": "CHF 155",
                    "assets_gross_total": "CHF 37'270",
                    "assets_net_total": "CHF 37'270",
                    "assets_taxable_global": "CHF 0",
                    "assets_taxable_local": "CHF 0",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_single_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_single_2021",
        source_file_filter=["ag_single_2021.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 19},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2021",
                    "document_date": "05.08.2022",
                    "street": PO_EXISTS,
                    "zip": "5707",
                    "city": "Seengen",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p1_date_of_birth": "16.11.1971",
                    "p1_marital_status": "geschieden / eingetragene Partnerschaft aufgelöst",
                    "p1_profession": "Eidg.dipi.Elektroinstallateur",
                    "p1_employer": "Elektro Holliger AG",
                    "p1_employer_location": "5707 Seengen",
                    "p1_income_employed_main": "CHF 97'234",
                    "income_portfolio": "CHF 305'937",
                    "income_real_estate_gross": "CHF 11'145",
                    "income_real_estate_gross_other": "CHF 6'000",
                    "income_real_estate_net": "CHF 7'936",
                    "income_gross_total": "CHF 411'107",
                    "p1_contribution_pillar_3a": "CHF 6'702",
                    "p1_expense_employment": "CHF 2'918",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'000",
                    "interest_paid_on_debt": "CHF 2'356",
                    "deductions_wealth_management": "CHF 909",
                    "property_maintenance_cost": "CHF 0",
                    "deductions_total": "CHF 14'885",
                    "income_net_total": "CHF 396'222",
                    "income_taxable_local": "CHF 396'222",
                    "assets_portfolio": "CHF 494'202",
                    "assets_cash_gold": "CHF 35'179",
                    "assets_real_estate_total_net": "CHF 531'883",
                    "assets_gross_total": "CHF 1'061'264",
                    "assets_net_total": "CHF 244'014",
                    "assets_taxable_global": "CHF 144'014",
                    "assets_taxable_local": "CHF 144'014",
                    "debt_total": "CHF -817'250",
                    "debt_detail_lines": PO_EXISTS,
                    "property_type": "nur Boden",
                    "property_year": "2008",
                    "property_purchase_year": "2010",
                    "property_imputed_rental_value": "CHF 0",
                    "property_imputed_rental_value_canton": "CHF 5'145",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_single_2021_2():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_single_2021_2",
        source_file_filter=["ag_single_2021_2.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 14},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2021",
                    "document_date": "16.06.2022",
                    "street": PO_EXISTS,
                    "zip": "5034",
                    "city": "Suhr",
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "01.01.1963",
                    "p1_marital_status": "verwitwet",
                    "p1_profession": "Produktionsmitarbeiterin",
                    "p1_employer": "Delica AG",
                    "p1_employer_location": "5033 Buchs AG",
                    "p1_income_employed_main": "CHF 69'257",
                    "p1_income_pension": "CHF 20'844",
                    "income_portfolio": "CHF 6",
                    "income_real_estate_gross_other": "CHF 19'900",
                    "income_real_estate_net": "CHF 17'222",
                    "p1_expense_employment": "CHF 4'378",
                    "deductions_illness": "CHF 1'320",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'000",
                    "interest_paid_on_debt": "CHF 8'080",
                    "deductions_wealth_management": "CHF 73",
                    "property_maintenance_cost": "CHF 5'102",
                    "deductions_total": "CHF 18'097",
                    "income_net_total": "CHF 89'232",
                    "income_taxable_local": "CHF 90'552",
                    "assets_portfolio": "CHF 107'365",
                    "assets_real_estate_total_net": "CHF 338'000",
                    "assets_gross_total": "CHF 445'365",
                    "assets_net_total": "CHF 41'365",
                    "assets_taxable_global": "CHF 0",
                    "assets_taxable_local": "CHF 0",
                    "debt_total": "CHF 404'000",
                    "debt_detail_lines": PO_EXISTS,
                    "property_type": "Stockwerk",
                    "property_year": "2004",
                    "property_purchase_year": "2006",
                    "property_imputed_rental_value": "CHF 2'828",
                    "property_imputed_rental_value_canton": "CHF 2'424",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ag_2018_debt_issue():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ag_double_2018_debt_issue",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 16},
            expected_page_objects={
                "0": {
                    "canton_short": "AG",
                    "year": "2018",
                    "document_date": "09.07.2019",
                    "street": PO_EXISTS,
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_lastname": PO_EXISTS,
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p1_date_of_birth": "03.11.1978",
                    "p1_profession": "Sicherheitsbeauftragter Kapo",
                    "p2_date_of_birth": "01.09.1978",
                    "p2_profession": "Bezirksschullehrerin",
                    "zip": "5436",
                    "city": "Würenlos",
                    "p1_employer": "Kantonspolizei Zürich",
                    "p1_employer_location": "8058 Zürich Kloten",
                    "p1_marital_status": "verheiratet (ungetrennt)",
                    "p2_employer": "Kanton Aargau",
                    "p2_employer_location": "5620 Bremgarten AG",
                    "p1_income_employed_main": "CHF 42'867",
                    "p2_income_employed_main": "CHF 77'978",
                    "p2_contribution_pillar_3a": "CHF 3'000",
                    "income_portfolio": "CHF 39",
                    "income_gross_total": "CHF 120'884",
                    "income_net_total": "CHF 87'752",
                    "income_taxable_local": "CHF 78'370",
                    "p1_expense_employment": "CHF 8'974",
                    "p2_expense_employment": "CHF 10'197",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "deductions_wealth_management": "CHF 150",
                    "deductions_illness": "CHF 6'116",
                    "deductions_total": "CHF 33'132",
                    "assets_portfolio": "CHF 32'266",
                    "assets_cash_gold": "CHF 4",
                    "assets_cars": "CHF 10'150",
                    "assets_gross_total": "CHF 42'420",
                    "assets_net_total": "CHF 27'626",
                    "assets_taxable_global": "CHF 0",
                    "assets_taxable_local": "CHF 0",
                    "debt_total": "CHF 14'794",
                    "interest_paid_on_debt": "CHF 95",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ar_single_2020():
    """
    extraction impossible due to handwriting and bad scan in many cases
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ar_single_2020",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["ar_single_2020.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 4},
            expected_page_objects={
                "0": {
                    "canton_short": "AR",
                    # "year": "2020",
                    "address_block": PO_EXISTS,
                    "zip": "9100",
                    "city": "Herisau",
                    "p1_fullname": PO_EXISTS,
                    "p2_fullname": PO_EXISTS,
                    "income_net_total": "CHF 10'371",
                    "deductions_wealth_management": "CHF 3'135",
                    "property_maintenance_cost": "CHF 305",
                    "assets_real_estate_total_gross": "CHF 80'000",
                    "debt_private": "CHF 8'000",
                    "interest_paid_on_debt": "CHF 27",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_double_de_2014():
    """
    The page property (form 7) is missing in this document.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_double_2014",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 9,
                DocumentCat.TAX_CALCULATION: 1,
                DocumentCat.UNKNOWN_DE: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2014",
                    "section_children": PO_EXISTS,
                    "p1_fullname": PO_EXISTS,
                    "p1_marital_status": "Ledig",
                    "p2_income_employed_main": "CHF 27'991",
                    "p2_income_social_security": "CHF 2'392",
                    "income_portfolio": "CHF 110",
                    "income_gross_total": "CHF 30'493",
                    "p2_contribution_pillar_3a": "CHF 6'739",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 3'450",
                    "deductions_total": "CHF 14'589",
                    "income_net_total": "CHF 15'904",
                    "income_taxable_local": "CHF -1'796",
                    "assets_portfolio": "CHF 25'272",
                    "assets_gross_total": "CHF 25'272",
                    "municipality": "Bern",
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "p2_expense_employment": "CHF 1'050",
                    "p2_expense_employment_other": "CHF 2'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_single_de_2015():
    """
    The page property (form 7) is missing in this document.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_single_2015",
        source_file_filter=["be_single_2015.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 11},
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2015",
                    "municipality": "Leuzigen, 53080",
                    "p1_fullname": PO_EXISTS,
                    "p1_income_employed_main": "CHF 60'368",
                    "p1_expense_employment": "CHF 3'427",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 3'744",
                    "deductions_donations": "CHF 200",
                    "assets_portfolio": "CHF 29'781",
                    "assets_other": "CHF 3'150",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_single_de_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_single_2019",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["be_single_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 23,
                DocumentCat.TAX_CALCULATION: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2019",
                    "municipality": "Walkringen, 23260",
                    "p1_fullname": PO_EXISTS,
                    "p1_income_employed_main": "CHF 101'457",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'408",
                    "deductions_donations": "CHF 500",
                    "assets_other": "CHF 0",
                    "debt_total": "CHF 750'015",
                    "interest_paid_on_debt": "CHF 8'469",
                    "p1_expense_employment": "CHF 3'813",
                    "p1_expense_employment_other": "CHF 3'044",
                    "assets_cars": "CHF 0",
                    "debt_detail_lines": PO_EXISTS,
                    "property_imputed_rental_value_canton": "CHF 6'510",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_single_de_2020_1():
    """
    The page property (form 7) is missing in this document.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_single_2020_1",
        source_file_filter=["be_single_2020_1.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 10,
                DocumentCat.TAX_CALCULATION: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2020",
                    "municipality": "Köniz",
                    "p1_fullname": PO_EXISTS,
                    "p1_marital_status": "Geschieden",
                    "p2_income_social_security": "CHF 37'391",
                    "income_gross_total": "CHF 77'334",
                    "income_net_total": "CHF 73'907",
                    "income_taxable_local": "CHF 68'607",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "deductions_illness": "CHF 1'363",
                    "deductions_donations": "CHF 100",
                    "deductions_total": "CHF 3'427",
                    "assets_portfolio": "CHF 21'848",
                    "assets_gross_total": "CHF 257'828",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_single_de_2020_2():
    """
    The page income (form 2), debt (form 4), child support (form 5), deductions (form 6), property (form 7)
     are missing in this document.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_single_2020_2",
        source_file_filter=["be_single_2020_2.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 6},
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "p1_fullname": PO_EXISTS,
                    "deductions_wealth_management": "CHF 36",
                    "assets_portfolio": "CHF 55'540",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_double_de_2020():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_double_2020",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["be_double_2020.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 19},
            expected_page_objects={
                "0": {
                    "canton_short": "BE",
                    "year": "2020",
                    "municipality": "Bem, 31010",
                    "p1_fullname": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p1_income_pension_ahv": "CHF 28'440",
                    "p1_income_pension": "CHF 100'268",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 37'137",
                    "deductions_donations": "CHF 500",
                    "assets_portfolio": "CHF 9'104'997",
                    "assets_cars": "CHF 0",
                    "assets_other": "CHF 250'000",
                    "property_year": "1980",
                    "property_imputed_rental_value_canton": "CHF 1'338'000",
                    "debt_total": "CHF 1'260'000",
                    "interest_paid_on_debt": "CHF 16'762",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_be_single_de_2020_mix():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "be_single_2020_mix",
        source_file_filter=["be_single_2020_mix.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.TAX_ATTACHMENTS: 1,
            },
            expected_doc_cat_frequency_per_page={
                # These page classifications are all correct
                DocumentCat.TAX_DECLARATION: 6,
                DocumentCat.BANK_DOCUMENT: 9,
                DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: 2,
                DocumentCat.TENANT_DIRECTORY: 1,
            },
            expected_page_objects={
                0: {
                    "canton_short": "BE",
                    "p1_fullname": PO_EXISTS,
                    "interest_paid_on_debt": "CHF 15'572",
                    "deductions_wealth_management": "CHF 36",
                    "assets_portfolio": "CHF 55'540",
                    "debt_total": "CHF 1'103'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_bl_double_2016():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "bl_double_2016",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["bl_double_2016.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 15},
            expected_page_objects={
                "0": {
                    "canton_short": "BL",
                    "year": "2016",
                    "zip": "4153",
                    "city": "Reinach BL",
                    "street": PO_EXISTS,
                    "p1_firstname": PO_EXISTS,
                    "p1_lastname": PO_EXISTS,
                    "p1_date_of_birth": "04.02.1970",
                    "phone_primary": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_lastname": PO_EXISTS,
                    "p2_date_of_birth": "09.04.1975",
                    "p1_marital_status": "verheiratet",
                    "p2_profession": "Lehrerin",
                    "p1_profession": "Lehrer",
                    "p2_employer": "Sekundarschule Reinach",
                    "p1_employer": "Bildungszentrum kvBL",
                    "p1_income_employed_main": "CHF 128'377",
                    "p2_income_employed_main": "CHF 44'023",
                    "income_portfolio": "CHF 173",
                    "property_imputed_rental_value": "CHF 15'874",
                    "property_maintenance_cost": "CHF 9'672",
                    "income_gross_total": "CHF 178'775",
                    "income_taxable_local": "CHF 143'995",
                    "p1_expense_employment": "CHF 2'942",
                    "p2_expense_employment": "CHF 500",
                    "interest_paid_on_debt": "CHF 8'866",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p2_contribution_pillar_3a": "CHF 6'768",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 4'000",
                    "deductions_total": "CHF 34'780",
                    "assets_portfolio": "CHF 230'221",
                    "assets_cars": "CHF 8'452",
                    "assets_real_estate_total_gross": "CHF 115'300",
                    "assets_gross_total": "CHF 388'258",
                    "debt_private": "CHF 495'000",
                    "assets_net_total": "CHF -106'742",
                    "assets_taxable_local": "CHF -256'742",
                    # The following extractions come from page 9.
                    "property_type": "Einfamilienhaus",
                    "property_year": "1957",
                    "property_purchase_year": "2011",
                    # This extraction is skipped because it was contradictory to the ones from page income.
                    # "property_imputed_rental_value_canton": "CHF 15'874",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_bs_2016():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "bs_double_2016",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["bs_double_2016.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 11},
            expected_page_objects={
                "0": {
                    "canton_short": "BS",
                    "year": "2016",
                    "municipality": "Basel",
                    "address_block": PO_EXISTS,
                    "zip": "4052",
                    "city": "Basel",
                    "p1_fullname": PO_EXISTS,
                    "p2_fullname": PO_EXISTS,
                    "p1_date_of_birth": "31.03.1985",
                    "p1_marital_status": "verheiratet",
                    "p1_profession": "Angestellter",
                    "p1_employer": "Diverse",
                    "p2_date_of_birth": "23.09.1985",
                    "p2_profession": "Angestellte",
                    "p2_employer": "F.Hoffmann-La Roche AG",
                    "p1_income_employed_main": "CHF 215'833",
                    "p2_income_employed_main": "CHF 142'131",
                    "income_gross_total": "CHF 357'961",
                    "income_portfolio": "CHF -3",
                    "expense_children_daycare": "CHF 10'000",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p2_contribution_pillar_3a": "CHF 6'768",
                    "p1_expense_employment": "CHF 4'715",
                    "p2_expense_employment": "CHF 4'000",
                    "deductions_donations": "CHF 120",
                    "deductions_total": "CHF 37'251",
                    "income_net_total": "CHF 320'710",
                    "income_taxable_local": "CHF 277'790",
                    "assets_portfolio": "CHF 200'361",
                    "assets_gross_total": "CHF 200'361",
                    "assets_net_total": "CHF 200'361",
                    "assets_taxable_local": "CHF 35'361",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_fr_double_de_2020():
    """
    Extractions added for pages personal, income (incl. assets), deductions.
    Pages 19-22 are not tax declaration anymore (mixed document).
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "fr_double_2020",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["fr_double_2020.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 17,
                DocumentCat.TAX_CALCULATION: 1,
                DocumentCat.PENSION3A_INSURANCE_CONTRACT: 3,
                DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "FR",
                    "year": "2020",
                    "address_block": PO_EXISTS,
                    "p1_fullname": PO_EXISTS,
                    "zip": "3178",
                    "city": "Bösingen",
                    "p1_ahv_new": PO_EXISTS,
                    "p2_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "21.09.1970",
                    "p2_date_of_birth": "08.10.1974",
                    "p1_profession": "Informatiker",
                    "p2_profession": "Reallehrerin",
                    "p1_employer": "Benscom AG, Zug",
                    "p1_marital_status": "verheiratet",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 110'932",
                    "income_portfolio": "CHF 652",
                    "income_real_estate_net_primary": "CHF 19'944",
                    "income_gross_total": "CHF 123'344",
                    "p1_contribution_pillar_3a": "CHF 6'826",
                    "p1_expense_employment": "CHF 3'328",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 11'880",
                    "income_net_total": "CHF 94'185",
                    "income_taxable_local": "CHF 71'585",
                    "assets_portfolio": "CHF 243'567",
                    "assets_life_insurance": "CHF 7'666",
                    "assets_cars": "CHF 26'200",
                    "assets_real_estate_main_property": "CHF 441'000",
                    "assets_gross_total": "CHF 718'433",
                    "assets_taxable_local": "CHF 190'433",
                    "debt_private": "CHF 518'000",
                    "interest_paid_on_debt": "CHF 4'695",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_gr_double_2014():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "gr_double_2014",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["gr_double_2014.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 13},
            expected_page_objects={
                "0": {
                    "canton_short": "GR",
                    "year": "2014",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "7249",
                    "city": "Serneus",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "23.02.1977",
                    "p2_ahv_new": PO_EXISTS,
                    "p2_date_of_birth": "29.01.1985",
                    "section_children": PO_EXISTS,
                    "p2_profession": "kfm. Mitarbeiterin",
                    "p1_profession": "Arzt",
                    "p1_marital_status": "verheiratet",
                    "p2_employer": "Ehemann",
                    "p1_employer": "selbständig",
                    "p1_expense_employment": "CHF 800",
                    "p2_expense_employment": "CHF 4'493",
                    "p1_contribution_pillar_2": "CHF 9'231",
                    "p1_contribution_pillar_3a": "CHF 6'739",
                    "p2_contribution_pillar_3a": "CHF 6'739",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 11'700",
                    "expense_children_daycare": "CHF 9'583",
                    "deductions_total": "CHF 49'357",
                    "income_net_total": "CHF 236'737",
                    # "income_taxable_global": "CHF 214'337",   # removed from mapping as it is local
                    "income_taxable_local": "CHF 214'337",
                    "assets_gross_business": "CHF 377'463",
                    "assets_portfolio": "CHF 28'926",
                    "assets_cars": "CHF 40'000",
                    "assets_gross_total": "CHF 446'402",
                    "debt_business": "CHF 23'430",
                    "debt_private": "CHF 48'381",
                    # "assets_net_total": "CHF 374'591",        # bad OCR, cannot read code 460
                    "assets_taxable_local": "CHF 166'591",
                    "p1_income_employed_side": "CHF 3'681",
                    "p2_income_employed_main": "CHF 28'913",
                    "p1_income_self_main": "CHF 242'346",
                    # "p2_income_self_main": "CHF 242'346",   # wrong mapping in old setting
                    "income_portfolio": "CHF 99",
                    "income_gross_total": "CHF 286'094",
                    "deductions_wealth_management": "CHF 72",
                    "deductions_donations": "CHF 100",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_gr_partial_single_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "gr_partial_single_2019",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["gr_partial_single_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 3},
            expected_page_objects={
                "0": {
                    "canton_short": "GR",
                    "year": "2019",
                    "p1_fullname": PO_EXISTS,
                    "p1_income_employed_main": "CHF 67'963",
                    "p2_income_employed_main": "CHF 56'048",
                    "p2_income_employed_side": "CHF 15'200",
                    "income_real_estate_net": "CHF 1'588",
                    "income_portfolio": "CHF 36",
                    "income_gross_total": "CHF 140'835",
                    "assets_real_estate_total_gross": "CHF 49'200",
                    "assets_portfolio": "CHF 176'450",
                    "assets_cars": "CHF 23'000",
                    "assets_gross_total": "CHF 248'651",
                    "assets_net_total": "CHF 248'651",
                    "assets_taxable_local": "CHF 118'651",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_lu_2014():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "lu_single_2014",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["310_tax_lu_single_2014.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 14},
            expected_page_objects={
                "0": {
                    P1_MARITAL_STATUS.name: "ledig",
                    P1_EMPLOYER.name: "Hochschule Luzern",
                    P2_FIRSTNAME.name: "",
                    P1_INCOME_EMPLOYED_MAIN.name: "CHF 110'966",
                    P1_INCOME_EMPLOYED_SIDE.name: "CHF 304",
                    INCOME_GROSS_TOTAL.name: "CHF 111'270",
                    INCOME_NET_TOTAL.name: "CHF 83'318",
                    INCOME_TAXABLE_LOCAL.name: "CHF 74'268",
                    ASSETS_PORTFOLIO_ACCOUNTS.name: "CHF 50'964",
                    ASSETS_CARS.name: "CHF 36'400",
                    ASSETS_GROSS_TOTAL.name: "CHF 87'364",
                    ASSETS_NET_TOTAL.name: "CHF 87'364",
                    ASSETS_TAXABLE_LOCAL.name: "CHF 32'364",
                    "canton_short": "LU",
                    "year": "2014",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "6010",
                    "city": "Kriens",
                    "p1_fullname": PO_EXISTS,
                    "p1_date_of_birth": "23.06.1976",
                    "p1_profession": "Dozent",
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p1_contribution_pillar_2": "CHF 9'070",
                    "p1_contribution_pillar_3a": "CHF 6'739",
                    "income_portfolio": "CHF 0",
                    "p1_expense_employment": "CHF 9'643",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'500",
                    "expense_children_daycare": "CHF 4'700",
                    "deductions_total": "CHF 27'952",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_nw_single_2018():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "nw_single_2018",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["nw_single_2018.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.DONATION_CONFIRMATION: 1,
                DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION: 3,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.TAX_ATTACHMENTS: 1,
                DocumentCat.TAX_DECLARATION: 1,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 22,
                DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL: 1,
                DocumentCat.BILL_MISC: 1,
                DocumentCat.DONATION_CONFIRMATION: 4,
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 2,
                DocumentCat.PENSION3A_INSURANCE_CONTRACT: 1,
                DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION: 3,
                DocumentCat.PROPERTY_ACCOUNTS: 3,
                DocumentCat.PROPERTY_VALUATION_GOV: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.TAX_DECLARATION: 16,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 4,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "NW",
                    "year": "2018",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "6370",
                    "city": "Stans",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "26.07.1960",
                    "p1_profession": "Bauführer",
                    "p1_employer": "A. Heini GmbH",
                    "p1_employer_location": "Emmenbrücke",
                    "p1_income_employed_main": "CHF 162'177",
                    "p1_contribution_pillar_2": "CHF 80'000",
                    "p1_contribution_pillar_3a": "CHF 3'000",
                    "income_portfolio": "CHF 395'253",
                    "income_real_estate_net": "CHF 18'912",
                    "income_gross_total": "CHF 576'342",
                    "income_net_total": "CHF 484'142",
                    "income_taxable_global": "CHF 484'142",
                    "income_taxable_local": "CHF 484'142",
                    "p1_expense_employment": "CHF 7'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 1'700",
                    "deductions_donations": "CHF 500",
                    "deductions_total": "CHF 91'700",
                    "assets_portfolio": "CHF 5'003'669",
                    "assets_cash_gold": "CHF 95'457",
                    "assets_cars": "CHF 2'240",
                    "assets_gross_total": "CHF 5'657'377",
                    "assets_net_total": "CHF 5'647'377",
                    "assets_taxable_global": "CHF 5'612'377",
                    "assets_taxable_local": "CHF 5'612'377",
                    "assets_real_estate_current_value": "CHF 435'100",
                    "debt_private": "CHF 10'000",
                    "interest_paid_on_debt": "CHF 0",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_ow_2020():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "ow_double_2020",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_ow_2020_double.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 36,
                DocumentCat.TAX_CALCULATION: 1,
            },
            expected_page_objects={
                "0": {
                    "year": "2020",
                    "canton_short": "OW",
                    "address_block": PO_EXISTS,
                    "zip": "6072",
                    "city": "Sachseln",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "07.09.1947",
                    "p1_profession": "Pflegeeltern",
                    "p2_date_of_birth": "24.06.1950",
                    "p2_profession": "Pflegeeltern",
                    "p1_income_self_main": "CHF -1'608",
                    "p1_income_pension_ahv": "CHF 21'336",
                    "p1_income_pension": "CHF 16'065",
                    "p2_income_pension_ahv": "CHF 21'336",
                    "income_portfolio": "CHF 259",
                    "income_real_estate_net": "CHF 82'057",
                    "income_gross_total": "CHF 210'374",
                    "income_net_total": "CHF 179'604",
                    "income_taxable_global": "CHF 159'604",
                    "income_taxable_local": "CHF 159'604",
                    "deductions_donations": "CHF 4'820",
                    "assets_portfolio": "CHF 227'366",
                    "assets_cars": "CHF 11'700",
                    "assets_gross_total": "CHF 2'946'612",
                    "assets_net_total": "CHF 1'933'409",
                    "assets_taxable_global": "CHF 1'883'409",
                    "assets_taxable_local": "CHF 1'883'409",
                    "assets_real_estate_current_value": "CHF 1'682'000",
                    "debt_private": "CHF 1'013'203",
                    "interest_paid_on_debt": "CHF 21'000",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sg_double_2015():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "sg_double_2015",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["sg_double_2015.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 14},
            expected_page_objects={
                "0": {
                    "canton_short": "SG",
                    "year": "2015",
                    "municipality": "Eschenbach",
                    "document_date": "19.12.2016",
                    "address_block": PO_EXISTS,
                    "zip": "8732",
                    "city": "Neuhaus SG",
                    "p1_fullname": PO_EXISTS,
                    "p1_date_of_birth": "28.05.1971",
                    "p1_profession": "Polizist",
                    "p1_employer": "siehe Zusatzblätter",
                    "p1_marital_status": "Verheiratet",
                    "email": PO_EXISTS,
                    "p2_fullname": PO_EXISTS,
                    "p2_date_of_birth": "25.09.1971",
                    "p2_profession": "MPA",
                    "p2_employer": "siehe Zusatzblätter",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 120'403",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p2_income_employed_main": "CHF 19'443",
                    "p2_contribution_pillar_3a": "CHF 4'020",
                    "income_portfolio": "CHF -100",
                    "income_real_estate_net": "CHF 16'842",
                    "income_gross_total": "CHF 156'588",
                    "income_net_total": "CHF 89'505",
                    "income_taxable_local": "CHF 69'100",
                    "p1_expense_employment": "CHF 20'896",
                    "p2_expense_employment": "CHF 4'399",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 6'000",
                    "property_maintenance_cost": "CHF 4'211",
                    "deductions_total": "CHF 67'083",
                    "assets_portfolio": "CHF 207'503",
                    "assets_cars": "CHF 0",
                    "assets_gross_total": "CHF 1'074'503",
                    "assets_net_total": "CHF 374'503",
                    "assets_taxable_global": "CHF 184'000",
                    "assets_taxable_local": "CHF 184'000",
                    "assets_real_estate_total_gross": "CHF 867'000",
                    "property_imputed_rental_value": "CHF 30'076",
                    "debt_private": "CHF 700'000",
                    "debt_total": "CHF 700'000",
                    "interest_paid_on_debt": "CHF 24'500",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sg_single_2016():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "sg_single_2016",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["sg_single_2016.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 19},
            expected_page_objects={
                "0": {
                    "canton_short": "SG",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "p1_fullname": PO_EXISTS,
                    "p1_date_of_birth": "20.09.1982",
                    "p1_employer": "Herzog Küchen AG",
                    "email": PO_EXISTS,
                    "phone_primary": PO_EXISTS,
                    "assets_taxable_global": "CHF 133'000",
                    "assets_real_estate_total_gross": "CHF 365'000",
                    "debt_private": "CHF 251'500",
                    "year": "2016",
                    "municipality": "Bütschwil-Ganterschwil",
                    "zip": "9606",
                    "city": "Bütschwil",
                    "document_date": "05.03.2017",
                    "p1_marital_status": "Ledig",
                    "p1_profession": "Sachbearbeiter Verkauf",
                    "p1_income_employed_main": "CHF 75'791",
                    "income_portfolio": "CHF 87",
                    "income_real_estate_net": "CHF 11'367",
                    "income_gross_total": "CHF 87'245",
                    "p1_expense_employment": "CHF 9'255",
                    "interest_paid_on_debt": "CHF 2'255",
                    "p1_contribution_pillar_3a": "CHF 5'000",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'400",
                    "property_maintenance_cost": "CHF 2'274",
                    "deductions_total": "CHF 21'784",
                    "income_net_total": "CHF 65'461",
                    "income_taxable_local": "CHF 65'400",
                    "assets_portfolio": "CHF 95'036",
                    "assets_cars": "CHF 0",
                    "assets_gross_total": "CHF 460'036",
                    "debt_total": "CHF 251'500",
                    "assets_net_total": "CHF 208'536",
                    "assets_taxable_local": "CHF 133'000",
                    "property_imputed_rental_value": "CHF 16'239",
                    "income_real_estate_gross": "CHF 11'367",
                    "p1_deductions_education": "CHF 400",
                    "deductions_wealth_management": "CHF 200",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_so_double_2016_broken_income():
    """
    Codes for income, deductions, assets were different in 2016. Therefore, the extraction doesn't work.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "so_double_2016",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["so_double_short_2016.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 7},
            expected_page_objects={"0": {"canton_short": "SO", "year": "2016"}},
        ),
    ).run()


@pytest.mark.asyncio
async def test_230111_tax_so_name():
    await DossierTest(
        override_existing=True,
        source_folder=source_folder / "so_single_name_2021",
        source_file_filter=["310-EKD53 Steuererklärung page1.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_page_objects={
                0: {
                    "canton_short": "ZH",
                    "year": "2021",
                    "document_date": "26.10.2022",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8704",
                    "city": "Herrliberg",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "20.02.1956",
                    "p1_marital_status": "gesch lede n",
                    "p1_profession": "dipi. Ing. FH Raumplanung",
                    "p1_employer": "WWF Schweiz",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_so_single_2019_de():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        # show_page=-1,
        source_folder=source_folder / "so_single_2019_de",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["so_single_2019_de.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 16,
                DocumentCat.TAX_CALCULATION: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "SO",
                    "year": "2019",
                    "municipality": "Biberist",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "4562",
                    "city": "Biberist",
                    "p1_fullname": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "01.12.1988",
                    "p1_profession": "Versicherungsberater0 ((",
                    "p1_employer_location": "Solothurn",
                    "p1_income_employed_main": "CHF 143'363",
                    "income_real_estate_net": "CHF 0",
                    "income_gross_total": "CHF 143'363",
                    "expense_alimony_partner": "CHF 19'140",
                    "p1_expense_employment": "CHF 7'200",
                    "deductions_education": "CHF 4'590",
                    "deductions_total": "CHF 40'212",
                    "income_net_total": "CHF 103'151",
                    "income_taxable_global": "CHF 103'151",
                    "income_taxable_local": "CHF 103'151",
                    "assets_life_insurance": "CHF 14'332",
                    "assets_real_estate_current_value": "CHF 0",
                    "assets_gross_total": "CHF 198'617",
                    "assets_net_total": "CHF 198'617",
                    "assets_taxable_global": "CHF 138'617",
                    "assets_taxable_local": "CHF 138'617",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_so_double_2020_de():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "so_double_2020_de",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["so_double_2020_de.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 18,
                DocumentCat.TAX_CALCULATION: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "SO",
                    "year": "2020",
                    "address_block": PO_EXISTS,
                    "zip": "4622",
                    "city": "Egerkingen",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "01.10.1983",
                    "p1_profession": "Laborant EFZ",
                    "p1_employer": "Blaser Swisslube AG",
                    "p1_employer_location": "Hasle-Rüegsau",
                    "p2_date_of_birth": "17.01.1991",
                    "p2_profession": "Lehrerin",
                    "p2_employer": "Kanton Solothurn",
                    "p2_employer_location": "Kantonsschule Solothurn",
                    "p1_income_employed_main": "CHF 65'735",
                    "p1_contribution_pillar_3a": "CHF 6'826",
                    "p2_income_employed_main": "CHF 104'209",
                    "p2_contribution_pillar_3a": "CHF 6'826",
                    "income_portfolio": "CHF 342",
                    "income_gross_total": "CHF 170'286",
                    "income_net_total": "CHF 124'518",
                    "income_taxable_global": "CHF 124'074",
                    "income_taxable_local": "CHF 124'074",
                    "p1_expense_employment": "CHF 18'402",
                    "p2_expense_employment": "CHF 7'714",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 5'000",
                    "deductions_illness": "CHF 0",
                    "deductions_donations": "CHF 444",
                    "deductions_total": "CHF 45'768",
                    "assets_portfolio": "CHF 372'772",
                    "assets_cars": "CHF 20",
                    "assets_gross_total": "CHF 372'792",
                    "assets_net_total": "CHF 372'792",
                    "assets_taxable_global": "CHF 272'792",
                    "assets_taxable_local": "CHF 272'792",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sz_single_2015():
    """
    Page 3 is EXTRACT_FROM_LAND_REGISTER and should be "DE/310/SZ/Notizen". This was no problem with classifiers
     before 2022-05-15. --> Added to Golden (class might indeed be difficult as the notes can have varying texts).
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "sz_single_2015",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_sz_single_2015.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 30},
            expected_page_objects={
                "0": {
                    "canton_short": "SZ",
                    "year": "2015",
                    "document_date": "03.01.2017",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8807",
                    "city": "Freienbach",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "30.03.1959",
                    "p1_profession": "Theater",
                    "p1_employer_location": "Zürich",
                    # no marital status as it's a checkbox
                    # no p2_fullname as it's not on the page
                    "section_children": PO_EXISTS,
                    "income_portfolio": "CHF 780",
                    "p1_income_employed_main": "CHF 43'714",
                    "income_real_estate_gross": "CHF 379'000",
                    "income_gross_total": "CHF 383'317",
                    "p1_contribution_pillar_2": "CHF 15'000",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p1_expense_employment": "CHF 5'209",
                    "deductions_illness": "CHF 0",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 3'600",
                    "interest_paid_on_debt": "CHF 182'275",
                    "deductions_wealth_management": "CHF 1'497",
                    "property_maintenance_cost": "CHF 37'952",
                    "income_net_total": "CHF 157'993",
                    "income_taxable_global": "CHF 143'793",
                    "income_taxable_local": "CHF 143'793",
                    "assets_portfolio": "CHF 498'851",  # needed field_vertical_line_scale=5
                    "assets_cars": "CHF 300",  # needed field_vertical_line_scale=5
                    "assets_real_estate_current_value": "CHF 5'933'736",  # needed field_vertical_line_scale=5
                    "assets_gross_total": "CHF 6'432'887",  # needed field_vertical_line_scale=5
                    "assets_net_total": "CHF -848'113",
                    "assets_taxable_global": "CHF 0",
                    "assets_taxable_local": "CHF 0",
                    "debt_private": "CHF 7'281'000",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_sz_single_2016():
    """
    This was no problem with classifiers earlier than 2022-05-15. --> Added to Golden (few samples).
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "sz_single_2016",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["sz_single_2016.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 32},
            expected_page_objects={
                "0": {
                    "canton_short": "SZ",
                    "year": "2016",
                    "document_date": "30.06.2017",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8807",
                    "city": "Freienbach",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "05.04.1972",
                    "p1_employer_location": "Freienbach, Dubai",
                    "section_children": PO_EXISTS,
                    "income_portfolio": "CHF 18'882",
                    "p1_income_employed_main": "CHF 60'000",
                    "p1_income_employed_side": "CHF 110'686",
                    "income_real_estate_gross": "CHF 95'128",
                    "income_gross_total": "CHF 277'796",
                    "expense_alimony_children": "CHF 12'864",
                    "p1_expense_employment": "CHF 6'900",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 3'200",
                    "interest_paid_on_debt": "CHF 31'050",
                    "deductions_wealth_management": "CHF 5'763",
                    "property_maintenance_cost": "CHF 18'117",
                    "income_net_total": "CHF 200'584",
                    "income_taxable_global": "CHF 197'384",
                    "income_taxable_local": "CHF 197'384",
                    "assets_portfolio": "CHF 1'921'081",
                    "assets_other": "CHF 327'459",
                    "assets_real_estate_current_value": "CHF 2'906'989",
                    "assets_gross_total": "CHF 5'155'529",
                    "assets_net_total": "CHF 4'180'917",
                    "assets_taxable_global": "CHF 4'055'917",
                    "assets_taxable_local": "CHF 4'055'917",
                    "debt_private": "CHF 974'612",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_tg_double_2015():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "tg_double_2015",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tg_double_2015.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={
                DocumentCat.TAX_DECLARATION: 15,
                DocumentCat.TAX_CALCULATION: 1,
            },
            expected_page_objects={
                "0": {
                    "canton_short": "TG",
                    "year": "2015",
                    "address_block": PO_EXISTS,
                    "zip": "9562",
                    "city": "Märwil",
                    "p1_fullname": PO_EXISTS,
                    "p1_date_of_birth": "20.09.1978",
                    "p2_date_of_birth": "24.01.1979",
                    "p2_profession": "Lehrerin",
                    "p1_profession": "Berater und Lehrkraft",
                    "p1_employer": "BBZ Arenenberg",
                    "p1_marital_status": "verheiratet",
                    "p2_fullname": PO_EXISTS,
                    "p2_employer": "VSG Bischofszell",
                    "p1_income_employed_main": "CHF 83'283",
                    "p1_income_employed_side": "CHF 2'508",
                    "p1_income_self_main": "CHF 97'461",
                    "p1_contribution_pillar_2": "CHF 22'154",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p2_income_employed_main": "CHF 26'788",
                    "income_portfolio": "CHF 1",
                    "income_gross_total": "CHF 210'041",
                    "income_net_total": "CHF 161'958",
                    "income_taxable_local": "CHF 154'618",
                    "p1_expense_employment": "CHF 8'627",
                    "p2_expense_employment": "CHF 3'534",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'000",
                    "deductions_donations": "CHF 340",
                    "deductions_total": "CHF 48'083",
                    "assets_portfolio": "CHF 130'423",
                    "assets_cars": "CHF 1",
                    "assets_gross_total": "CHF 1'172'814",
                    "assets_net_total": "CHF 348'387",
                    "assets_taxable_local": "CHF 48'387",
                    "assets_real_estate_total_gross": "CHF 439'500",
                    "debt_total": "CHF 824'427",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zg_double_2016():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zg_double_2016",
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 7},
            expected_page_objects={
                "0": {
                    INCOME_GROSS_TOTAL.name: "CHF 245'327",
                    "address_block": PO_EXISTS,
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": "756.5332.7700.74",
                    "p1_marital_status": "Verheiratet",
                    "p2_ahv_new": "756.4334.4985.80",
                    "income_real_estate_net_primary": "CHF -27'491",
                    "deductions_donations": "CHF 1'470",
                    "section_children_adult": "Céline Seqqinger",
                    "canton_short": "ZG",
                    "zip": "6331",
                    "city": "Hünenberg",
                    "year": "2016",
                    "p2_date_of_birth": "30.01.1960",
                    "p1_date_of_birth": "21.10.1966",
                    "p1_income_employed_main": "CHF 63'337",
                    "p2_income_employed_main": "CHF 194'702",
                    "p2_income_employed_side": "CHF 5'000",
                    "income_portfolio": "CHF 179",
                    "income_alimony_children": "CHF 9'600",
                    "p1_expense_employment": "CHF 4'880",
                    "p2_expense_employment": "CHF 18'056",
                    "interest_paid_on_debt": "CHF 4'154",
                    "p1_contribution_pillar_3a": "CHF 6'000",
                    "p2_contribution_pillar_3a": "CHF 6'768",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'700",
                    "deductions_total": "CHF 52'058",
                    "income_net_total": "CHF 191'799",
                    "income_taxable_global": "CHF 159'599",
                    "income_taxable_local": "CHF 159'599",
                    "assets_portfolio": "CHF 574'256",
                    "assets_cash_gold": "CHF 30'000",
                    "assets_cars": "CHF 20'000",
                    "assets_real_estate_main_property": "CHF 772'000",
                    "assets_gross_total": "CHF 1'396'256",
                    "debt_total": "CHF 650'000",
                    "assets_net_total": "CHF 746'256",
                    "assets_taxable_local": "CHF 544'256",
                }
            },
        ),
    ).run()


@pytest.mark.skip(reason="needs implementation")
@pytest.mark.asyncio
async def test_zg_company_2015_todo():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zg_company_2015",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=[],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.FINANCIAL_STATEMENT_COMPANY: 1,
            },
            expected_num_docs=3,
            expected_page_objects={
                # "0": {
                #     "canton_short": "ZG",
                #
                # }
                # 3 pages not yet properly classified.... to be done later
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zg_single_2016():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zg_single_2016",
        source_file_filter=["zg_single_2016.pdf"],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 16},
            expected_page_objects={
                "0": {
                    "canton_short": "ZG",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": "756.7399.6795.81",
                    "p1_date_of_birth": "30.03.1959",
                    "p1_marital_status": "Ledig",
                    "year": "2016",
                    "zip": "6340",
                    "city": "Baar",
                    "p1_income_employed_main": "CHF 761'358",
                    "income_portfolio": "CHF 70'777",
                    "income_gross_total": "CHF 832'135",
                    "p1_expense_employment": "CHF 16'380",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 85",
                    "deductions_total": "CHF 32'233",
                    "income_taxable_global": "CHF 790'597",
                    "assets_portfolio": "CHF 5'849'839",
                    "assets_gross_total": "CHF 5'849'839",
                    "assets_net_total": "CHF 5'849'839",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zg_double_2020():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zg_double_2020",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["zg_double_2020.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 17},
            expected_page_objects={
                "0": {
                    "canton_short": "ZG",
                    "year": "2020",
                    "address_block": PO_EXISTS,
                    "zip": "6343",
                    "city": "Rotkreuz",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "25.05.1969",
                    "p1_marital_status": "Verheiratet",
                    "p2_ahv_new": PO_EXISTS,
                    "p2_date_of_birth": "18.06.1966",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 113'394",
                    "p1_income_employed_side": "CHF 2'984",
                    "p1_contribution_pillar_3a": "CHF 6'826",
                    "p2_income_employed_main": "CHF 55'865",
                    "p2_contribution_pillar_3a": "CHF 6'310",
                    "income_portfolio": "CHF 281",
                    "income_real_estate_net_primary": "CHF 13'488",
                    "income_gross_total": "CHF 186'012",
                    "income_net_total": "CHF 133'060",
                    "income_taxable_global": "CHF 82'860",
                    "p1_expense_employment": "CHF 9'408",
                    "p2_expense_employment": "CHF 5'200",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 8'800",
                    "deductions_illness": "CHF 0",
                    "deductions_donations": "CHF 380",
                    "deductions_total": "CHF 52'572",
                    "assets_portfolio": "CHF 54'316",
                    "assets_cars": "CHF 5'650",
                    "assets_gross_total": "CHF 703'716",
                    "assets_net_total": "CHF 170'245",
                    "assets_real_estate_main_property": "CHF 465'000",
                    "assets_real_estate_current_value": "CHF 18'750",
                    "debt_total": "CHF 533'471",
                    "interest_paid_on_debt": "CHF 7'664",
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zh_2019():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zh_double_2019",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["zh_double_2019.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 13},
            expected_page_objects={
                "0": {
                    "canton_short": "ZH",
                    "year": "2019",
                    "document_date": "20.04.2020",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8055",
                    "city": "Zürich",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "04.09.1967",
                    "p1_profession": "Informatiker",
                    "p1_employer": "Beispielfirma AG",
                    "p1_employer_location": "Zürich",
                    "p1_marital_status": "verheiratet",
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_date_of_birth": "21.10.1976",
                    "p2_profession": "Solution Consultant",
                    "p2_employer": "ServiceFirma",
                    "p2_phone_primary": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 22'506",
                    "p1_income_employed_side": "CHF 7'502",
                    "p1_contribution_pillar_3a": "CHF 3'350",
                    "p2_income_employed_main": "CHF 128'991",
                    "p2_income_eo": "CHF 4'388",
                    "p2_income_child_benefits": "CHF 4'800",
                    "p2_contribution_pillar_3a": "CHF 6'700",
                    "income_portfolio": "CHF 15",
                    "income_real_estate_gross": "CHF 25'700",
                    "income_real_estate_net_primary": "CHF 20'560",
                    "income_gross_total": "CHF 188'762",
                    "income_net_total": "CHF 117'756",
                    "income_taxable_global": "CHF 99'756",
                    "income_taxable_local": "CHF 99'756",
                    "p1_expense_employment": "CHF 7'901",
                    "p2_expense_employment": "CHF 8'850",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'800",
                    "expense_children_daycare": "CHF 19'248",
                    "property_maintenance_cost": "CHF 5'140",
                    "deductions_total": "CHF 71'006",
                    "assets_portfolio": "CHF 2'458'532",
                    "assets_cars": "CHF 1'040",
                    "assets_other": "CHF 180'288",
                    "assets_gross_total": "CHF 3'811'860",
                    "assets_taxable_global": "CHF 2'676'860",
                    "assets_taxable_local": "CHF 2'676'860",
                    "assets_real_estate_main_property": "CHF 1'172'000",
                    "address_real_estate_primary": PO_EXISTS,
                    "property_imputed_rental_value": "CHF 25'700",
                    "debt_total": "CHF 1'135'000",
                    "interest_paid_on_debt": "CHF 11'257",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zh_2021():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zh_double_2021",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["tax_zh_2021.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 15},
            expected_page_objects={
                "0": {
                    "canton_short": "ZH",
                    # Telefon auch falsch in v13...
                    "year": "2020",
                    "document_date": "26.08.2021",
                    "p1_profession": "Informatiker",
                    "p1_employer_location": "Zürich",
                    "p1_marital_status": "verheiratet",
                    "p2_employer_location": "Opfikon",
                    "p1_income_employed_main": "CHF 76'800",
                    "p1_income_employed_side": "CHF 22'470",
                    "p1_contribution_pillar_3a": "CHF 6'700",
                    "p2_income_employed_main": "CHF 237'315",
                    "p2_contribution_pillar_3a": "CHF 6'700",
                    "income_portfolio": "CHF 5'453",
                    "income_real_estate_gross": "CHF 25'700",
                    "income_real_estate_net_primary": "CHF 20'560",
                    "income_gross_total": "CHF 362'598",
                    "income_net_total": "CHF 289'147",
                    "income_taxable_global": "CHF 271'147",
                    "income_taxable_local": "CHF 271'147",
                    "p1_expense_employment": "CHF 9'104",
                    "p2_expense_employment": "CHF 8'400",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'800",
                    "expense_children_daycare": "CHF 13'426",
                    "deductions_wealth_management": "CHF 6'000",
                    "deductions_total": "CHF 73'451",
                    "assets_portfolio": "CHF 3'538'571",
                    "assets_cars": "CHF 1'040",
                    "assets_gross_total": "CHF 4'711'611",
                    "assets_taxable_global": "CHF 3'576'611",
                    "assets_taxable_local": "CHF 3'576'611",
                    "assets_real_estate_main_property": "CHF 1'172'000",
                    "property_imputed_rental_value": "CHF 25'700",
                    "property_maintenance_cost": "CHF 5'140",
                    "debt_total": "CHF 1'135'000",
                    "interest_paid_on_debt": "CHF 9'421",
                    "address_block": PO_EXISTS,
                    "zip": "8055",
                    "city": "Zürich",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "04.09.1977",
                    "p1_employer": "Fintastic AG",
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_date_of_birth": "21.10.1976",
                    "p2_profession": "Solution Consultant",
                    "p2_employer": "ServiceNow",
                    "p2_phone_primary": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "address_real_estate_primary": PO_EXISTS,
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zh_double_2015():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zh_double_2015",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["zh_double_2015.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 14},
            expected_page_objects={
                "0": {
                    "canton_short": "ZH",
                    "year": "2015",
                    "document_date": "29.09.2016",
                    "address_block": PO_EXISTS,
                    "zip": "8902",
                    "city": "Urdorf",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "22.09.1977",
                    "p1_profession": "Netzwerkadmin",
                    "p1_employer": "Mlini AG",
                    "p1_employer_location": "Cham",
                    "p1_marital_status": "verheiratet",
                    "p2_firstname": PO_EXISTS,
                    "p2_date_of_birth": "23.07.1975",
                    "p2_profession": "AKP",
                    "p2_employer": "Mlini AG",
                    "p2_employer_location": "Cham",
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 121'453",
                    "p1_contribution_pillar_3a": "CHF 6'768",
                    "p2_income_employed_main": "CHF 20'948",
                    "p2_contribution_pillar_3a": "CHF 4'190",
                    "income_portfolio": "CHF 25",
                    "income_real_estate_gross": "CHF 19'100",
                    "income_real_estate_net_primary": "CHF 15'280",
                    "income_gross_total": "CHF 157'706",
                    "income_net_total": "CHF 93'647",
                    "income_taxable_global": "CHF 57'247",
                    "income_taxable_local": "CHF 57'247",
                    "p1_expense_employment": "CHF 18'768",
                    "p2_expense_employment": "CHF 8'700",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 10'400",
                    "deductions_wealth_management": "CHF 3'377",
                    "deductions_donations": "CHF 400",
                    "property_maintenance_cost": "CHF 3'820",
                    "deductions_total": "CHF 64'059",
                    "assets_portfolio": "CHF 1'208'609",
                    "assets_life_insurance": "CHF 18'804",
                    "assets_cars": "CHF 544",
                    "assets_gross_total": "CHF 1'801'957",
                    "assets_taxable_global": "CHF 1'301'957",
                    "assets_taxable_local": "CHF 1'301'957",
                    "assets_real_estate_main_property": "CHF 574'000",
                    "address_real_estate_primary": PO_EXISTS,
                    "property_imputed_rental_value": "CHF 19'100",
                    "debt_total": "CHF 500'000",
                    "interest_paid_on_debt": "CHF 5'956",
                    "debt_detail_lines": PO_EXISTS,
                }
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_zh_single_2020():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder / "zh_single_2020",
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["zh_single_2020.PDF"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.TAX_DECLARATION: 10},
            expected_page_objects={
                "0": {
                    "canton_short": "ZH",
                    "year": "2020",
                    "document_date": "28.09.2021",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8004",
                    "city": "Zürich",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "08.01.1980",
                    "p1_profession": "Leiter Marketing",
                    "p1_employer": "Speedgoat",
                    "p1_employer_location": "Köniz",
                    "p1_marital_status": "ledig",
                    "income_gross_total": "CHF 125'687",
                    "income_net_total": "CHF 110'980",
                    "income_taxable_global": "CHF 110'980",
                    "income_taxable_local": "CHF 110'980",
                    "p1_expense_employment": "CHF 11'572",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 2'600",
                    "deductions_education": "CHF 535",
                    "deductions_total": "CHF 14'707",
                    "p1_income_employed_main": "CHF 112'373",
                    "income_portfolio": "CHF 0",
                    "income_undistributed_inheritances": "CHF 13'314",
                    "assets_portfolio": "CHF 10'045",
                    "assets_cars": "CHF 582",
                    "assets_gross_total": "CHF 89'287",
                    "assets_taxable_global": "CHF 89'287",
                    "assets_taxable_local": "CHF 89'287",
                }
            },
        ),
    ).run()
