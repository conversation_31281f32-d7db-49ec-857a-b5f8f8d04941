from typing import List

from abbyyplumber.api import Page
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElement,
    SearchElementConstant,
    SearchElementArea,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat, PageCatElement
from hypodossier.core.domain.SemanticField import FIELD_CANTON_SHORT, FIELD_YEAR
from hypodossier.util.canton_util import valid_canton_shortcode
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.ag.ag_tax_declaration_fields_map import (
    create_search_elements_standard_ag,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.lu.TaxDeclarationLUAssetsPageParser import (
    TaxDeclarationLUAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.lu.TaxDeclarationLUDebtPageParser import (
    TaxDeclarationLUDebtPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.lu.TaxDeclarationLUDeductionsPageParser import (
    TaxDeclarationLUDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.lu.TaxDeclarationLUIncomePageParser import (
    TaxDeclarationLUIncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.lu.TaxDeclarationLUPersonalDataPageParser import (
    TaxDeclarationLUPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGAssetsPageParser import (
    TaxDeclarationSGAssetsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGDebtPageParser import (
    TaxDeclarationSGDebtPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGDeductionsPageParser import (
    TaxDeclarationSGDeductionsPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGIncomePageParser import (
    TaxDeclarationSGIncomePageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.TaxDeclarationSGPersonalDataPageParser import (
    TaxDeclarationSGPersonalDataPageParser,
)
from mortgageparser.documents.parser.pageparsers.tax_declaration.sg.tax_declaration_sg_fields_map import (
    create_search_elements_standard_sg,
)
from mortgageparser.util.search_element_util import create_search_element_recent_year
from mortgageparser.util.string_utils import substring_after


def create_search_elements_unknown_canton(
    page: Page, page_name: str, canton_short: str
):
    elements = []
    if valid_canton_shortcode(canton_short):
        elements += [
            # Parse canton_short from classification name
            SearchElementConstant(FIELD_CANTON_SHORT.name, canton_short),
            # Parse 4 digit year from top 10% of page (first occurence)
            SearchElementArea(
                FIELD_YEAR.sr_inside, page.fullpage, y_range=PercentageRange(0, 0.1)
            ),
            create_search_element_recent_year(page),
        ]
    return elements


def get_parser_tax_declaration(page: Page, page_name: str, canton_short: str):
    """Handle mapping from KNN to tax declaration page best effort.
    Some mappings (without extraction) are the same for all cantons, others are canton specific
    """
    p: AbstractPageParser = None
    page_cat: PageCat = None
    doc_cat: DocumentCat = None

    search_elements: List[SearchElement] = []

    if not p and not page_cat:
        if canton_short == "AG":
            # Schuldenverzeichnis has its own match function because the page has no title and is hard to detect with SGD
            search_elements += create_search_elements_standard_ag(page)

        elif canton_short == "LU":
            if page_name == "Deckblatt":
                p = TaxDeclarationLUPersonalDataPageParser()
            elif page_name in ["Einkünfte"]:
                p = TaxDeclarationLUIncomePageParser()
            elif page_name == "Abzüge":
                p = TaxDeclarationLUDeductionsPageParser()
            elif page_name == "Vermögen":
                p = TaxDeclarationLUAssetsPageParser()
            elif page_name == "Schuldenverzeichnis":
                p = TaxDeclarationLUDebtPageParser()
            elif page_name == "Wertschriftenverzeichnis Front":
                page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_FRONT
            elif page_name == "Wertschriftenverzeichnis Details":
                page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS

        elif canton_short == "SG":
            if page_name == "Deckblatt":
                p = TaxDeclarationSGPersonalDataPageParser()
            elif page_name == "Einkünfte":
                p = TaxDeclarationSGIncomePageParser()
            elif page_name == "Abzüge":
                p = TaxDeclarationSGDeductionsPageParser()
            elif page_name == "Vermögen":
                p = TaxDeclarationSGAssetsPageParser()
            elif page_name == "Schuldenverzeichnis":
                p = TaxDeclarationSGDebtPageParser()
            elif page_name == "Wertschriftenverzeichnis Front":
                page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_FRONT
                # No default search elements here
            elif page_name == "Rückerstattungsantrag":
                page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS
            elif page_name == "DA-1":
                page_cat = PageCat.TAX_DECLARATION_FORM_DA_1
            else:
                search_elements += create_search_elements_standard_sg(page)

    if not p and not page_cat:
        # Canton not yet supported or just this page is not supported
        search_elements += create_search_elements_unknown_canton(
            page, page_name, canton_short
        )

    if not p:
        p, doc_cat, page_cat = handle_tax_declaration_national(page_name, page_cat)

    if not p and not page_cat:
        page_cat = PageCat.TAX_DECLARATION_MISC

    if not doc_cat:
        doc_cat = DocumentCat.TAX_DECLARATION

    return p, doc_cat, page_cat, search_elements


def get_tax_page_categories():
    page_cats = {}
    for pc in PageCat:
        pce: PageCatElement = pc.value
        if (
            pce.document_cat == DocumentCat.TAX_DECLARATION
            or pce.document_cat == DocumentCat.TAX_LIST_FINANCIAL_ASSETS
        ):
            page_cats[pce.de] = pc
    return page_cats


page_cat_dict = get_tax_page_categories()


def handle_tax_declaration_national(page_name, page_cat):
    global page_cat_dict
    p: AbstractPageParser = None

    if not page_name:
        return None, DocumentCat.TAX_DECLARATION, PageCat.TAX_DECLARATION_MISC

    sub_levels = ["Aufstellung/", "Barcodeblatt/"]
    for sl in sub_levels:
        if page_name.startswith(sl):
            page_name = substring_after(page_name, sl, p_hamming_dist=0)

    if page_name in page_cat_dict:
        page_cat = page_cat_dict[page_name]
        return p, page_cat.value.document_cat, page_cat

    if page_name.startswith("Barcodeblatt"):
        page_cat = PageCat.TAX_DECLARATION_BARCODE
    elif page_name in ["Deckblatt"]:
        page_cat = PageCat.TAX_DECLARATION_PAGE_PERSONAL_DATA
    elif page_name.startswith("Direkte Bundessteuer"):
        page_cat = PageCat.TAX_DECLARATION_BUNDESSTEUER
    elif page_name.startswith("Versicherungsbeiträge"):
        page_cat = PageCat.TAX_DECLARATION_INSURANCE_PREMIUMS
    elif page_name.startswith("Berufsauslagen"):
        page_cat = PageCat.TAX_DECLARATION_OCCUPATIONAL_EXPENSES_FORM
    elif page_name.startswith("Wertschriftenverzeichnis Front"):
        page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_FRONT
    elif page_name.startswith("Wertschriftenverzeichnis Details"):
        page_cat = PageCat.TAX_DECLARATION_ACCOUNTS_FORM_DETAILS
    elif page_name in [
        "Liegenschaften",
        "Liegenschaft/Beiblatt",
        "Details zu den Liegenschaften",
    ]:
        page_cat = PageCat.TAX_DECLARATION_DETAILS_PROPERTIES
    elif page_name in ["Krankheitskosten", "Details zu den Krankheitskosten"]:
        page_cat = PageCat.TAX_DECLARATION_DETAILS_HEALTH_COSTS
    elif page_name in ["DA-1"]:
        page_cat = PageCat.TAX_DECLARATION_FORM_DA_1

    doc_cat = page_cat.value.document_cat if page_cat else None

    return p, doc_cat, page_cat
