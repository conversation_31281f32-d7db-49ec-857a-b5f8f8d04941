from pathlib import Path

import pytest

from asyncizer.tests.util_tests import DossierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/email_processor/email_messages_nested"
)
dest_folder_prefix = "input_system_components_email_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_email_messages_nested_eml_within_eml_with_attachments():
    # mt: duplicate extraction is now fixed
    # eml_within_eml_with_attachments.eml/Elektroniczny dokument sprzedaży do zamówienia nr 10013150411.eml/dokument_sprzedazy.pdf
    # eml_within_eml_with_attachments.eml/dokument_sprzedazy.pdf

    target_folder = path_root_folder
    assert target_folder.exists()
    # Two emails, one nested within the other, with attachments in both

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["eml_within_eml_with_attachments.eml"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                # There are also 2 email filenames with a random element in it
                "001 DOK DE Karta zamówienia.pdf",
                "001 DOK DE dokument_sprzedazy.pdf",
            ],
            expected_doc_cat_frequency={
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.UNKNOWN_DE: 2,
            },
            expected_doc_cat_frequency_per_page={
                DocumentCat.UNKNOWN: 2,
                DocumentCat.CORRESPONDENCE_EMAIL: 4,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_nested_zip():
    # Lots of nested emails in a zip file
    # This test takes a long while to run

    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["Example nested msg emails.zip"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE ARISA ML Foundations Assignment PL-translated.pdf",
                "001 DOK DE Karta zamówienia.pdf",
                # "001 DOK DE message.pdf",
                "310 Steuererklärung IMG_9744.pdf",
                "738 Email-Korrespondenz.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 18,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.UNKNOWN: 7,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_nested_msg():
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=[
            "Parent email with attaced pdf and two sub attached msgs also with unqiue pdfs.msg"
        ],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            # PG: As far as I can tell de-deplication does work on this
            # Deduplication does NOT work on this.
            # 2 emails with duplicates each
            # 1 unknown single page doc with duplicate
            expected_filenames=[
                "001 DOK DE ARISA ML Foundations Assignment PL-translated.pdf",
                "001 DOK DE Karta zamówienia.pdf",
                "001 DOK DE message 001 message.pdf",
                "001 DOK DE message 002 message.pdf",
                "310 Steuererklärung IMG_9744.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.CORRESPONDENCE_EMAIL: 20,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.UNKNOWN: 8,
            },
        ),
    ).run()


@pytest.mark.asyncio
async def test_email_messages_nested_msg_v2():
    target_folder = path_root_folder
    assert target_folder.exists()

    await DossierTest(
        override_existing=True,
        source_folder=target_folder,
        source_file_filter=["Nested msg emails v2.msg"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "001 DOK DE 000 Hypodossier Datenextraktion.pdf",
                "002 DOK EN arisa_mla25_final.pdf",
                "240 Betreibungsauskunft Maria Mustermann Keine Betreibungen 2014-04-11.pdf",
                "322 Bankdokument Mustermann Alex Zürcher Kantonalbank 2016-07-13.pdf",
                "330 Lohnausweis Maximilian Mustermann 2016.pdf",
                "410 PK Ausweis Max Mustermann Allianz 2017-03-03.pdf",
                "606 Situationsplan.pdf",
                "615 Fotos Liegenschaft 615 Property Photos visualisierung.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_gmail.com 30.04.2025.pdf",
                "738 Email-Korrespondenz piotr.gryko_at_gmail.com.pdf",
            ],
            expected_doc_cat_frequency_per_page={
                DocumentCat.BANK_DOCUMENT: 3,
                DocumentCat.CORRESPONDENCE_EMAIL: 2,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 1,
                DocumentCat.LIQUIDITY_PLAN_COMPANY: 1,
                DocumentCat.PAYSLIP: 1,
                DocumentCat.PENSION_CERTIFICATE: 3,
                DocumentCat.PLAN_SITUATION: 1,
                DocumentCat.PROPERTY_PHOTOS: 1,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.UNKNOWN: 1,
                DocumentCat.UNKNOWN_DE: 1,
                DocumentCat.UNKNOWN_EN: 3,
                DocumentCat.WHITE_PAGES: 1,
            },
        ),
    ).run()
