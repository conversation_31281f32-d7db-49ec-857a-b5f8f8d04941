from dataclasses import dataclass
from typing import Dict

from hypodossier.core.domain.PageData import PersonPageData
from hypodossier.core.domain.SemanticField import (
    FIELD_FULLNAME,
    FIELD_DOCUMENT_DATE,
    fields_as_dict,
    Seman<PERSON><PERSON>ield,
    FieldTitles,
    FIELD_PRODUCT,
    FIELD_ADDRESS_BLOCK,
    FIELD_COMPANY,
    ExtractionType,
    FIELD_FIRSTNAME,
    FIELD_STREET,
    FIELD_ZIP,
    FIELD_CITY,
    FIELD_IBAN,
    FIELD_DOCUMENT_VALIDITY_START_DATE,
    FIELD_DOCUMENT_VALIDITY_END_DATE,
    FIELD_SALUTATION,
    FIELD_DOCUMENT_TYPE,
)

BANK_ACCOUNT_NO = SemanticField(
    "account_no",
    ExtractionType.STRING,
    FieldTitles("Kontonummer", "Account No."),
    desc_de="Kontonummer, sofern keine IBAN vorhanden ist",
)

BANK_ACCOUNT_NAME = SemanticField(
    "account_name",
    ExtractionType.STRING,
    FieldTitles("Kontobezeichnung", "Account Name"),
    desc_de="Name des Kontos, z.B. 'Sparkonto' oder 'Vorsorgekonto Max Mustermann'",
)
BANK_ACCOUNT_TYPE = SemanticField(
    "account_type",
    ExtractionType.STRING,
    FieldTitles("Account-Typ", "Account Type"),
    desc_de="Art des Kontos, z.B. 'CS Privilegia Vorsorge'",
)

BANK_ACCOUNT_TOTAL_AMOUNT = SemanticField(
    "total_amount",
    ExtractionType.CURRENCY,
    FieldTitles("Kontostand", "Account Value"),
    desc_de="Kontostand/Gesamtbetrag/Rückkaufswert des Säule 3a Kontos resp. der Säule 3a Versicherungslösung",
)

FIELDS_BANK_ACCOUNT = fields_as_dict(
    [
        FIELD_DOCUMENT_DATE,
        FIELD_DOCUMENT_VALIDITY_START_DATE,
        FIELD_DOCUMENT_VALIDITY_END_DATE,
        FIELD_DOCUMENT_TYPE,
        FIELD_COMPANY,
        FIELD_PRODUCT,
        BANK_ACCOUNT_TOTAL_AMOUNT,
        FIELD_IBAN,
        BANK_ACCOUNT_NO,
        BANK_ACCOUNT_NAME,
        BANK_ACCOUNT_TYPE,
        FIELD_SALUTATION,
        FIELD_FULLNAME,
        FIELD_FIRSTNAME,
        FIELD_STREET,
        FIELD_ZIP,
        FIELD_CITY,
        FIELD_ADDRESS_BLOCK,
    ]
)


@dataclass
class BankAccountPageData(PersonPageData):
    document_validity_start_date: str = None
    document_validity_end_date: str = None

    document_type: str = None

    total_amount: str = None

    iban: str = None
    account_no: str = None
    account_name: str = None
    account_type: str = None

    def get_semantic_fields(self) -> Dict[str, SemanticField]:
        # d = super().get_semantic_fields()
        # return {**d, **FIELDS_BANK_ACCOUNT}
        return FIELDS_BANK_ACCOUNT
