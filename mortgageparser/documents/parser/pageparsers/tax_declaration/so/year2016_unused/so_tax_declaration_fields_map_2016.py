from hypodossier.core.documents.taxdeclaration.TaxDeclarationPageData import (
    P1_INCOME_EMPLOYED_MAIN,
    P2_INCOME_EMPLOYED_MAIN,
    P1_INCOME_EMPLOYED_SIDE,
    P2_INCOME_EMPLOYED_SIDE,
    P1_INCOME_SELF_MAIN,
    P2_INCOME_SELF_MAIN,
    P1_INCOME_PENSION,
    P2_INCOME_PENSION,
    INCOME_PORTFOLIO,
    INCOME_ALIMONY_PARTNER,
    INCOME_ALIMONY_CHILDREN,
    INCOME_GROSS_TOTAL,
    INTEREST_PAID_ON_DEBT,
    EXPENSE_ALIMONY_PARTNER,
    EXPENSE_ALIMONY_CHILDREN,
    P1_CONTRIBUTION_PILLAR_3A,
    P2_CONTRIBUTION_PILLAR_3A,
    DEDUCTIONS_TOTAL,
    INCOME_NET_TOTAL,
    INCOME_TAXABLE_LOCAL,
    ASSETS_PORTFOLIO_ACCOUNTS,
    ASSETS_LIFE_INSURANCE,
    ASSETS_CARS,
    ASSETS_OTHER,
    ASSETS_GROSS_TOTAL,
    ASSETS_TAXABLE_LOCAL,
    P1_INCOME_PENSION_AHV,
    P2_INCOME_PENSION_AHV,
    INCOME_OTHER,
    P1_EXPENSE_EMPLOYMENT,
    P2_EXPENSE_EMPLOYMENT,
    INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    EXPENSE_CHILDREN_DAYCARE,
    INCOME_TAXABLE_GLOBAL,
    EXPENSE_ANNUITY_CONTRIBUTIONS,
    P1_CONTRIBUTION_PILLAR_2,
    P2_CONTRIBUTION_PILLAR_2,
    DEDUCTIONS_OTHER,
    P1_INCOME_BOARD_SEATS,
    P2_INCOME_BOARD_SEATS,
    P1_INCOME_SOCIAL_SECURITY,
    P2_INCOME_SOCIAL_SECURITY,
    INCOME_REAL_ESTATE_NET,
    INCOME_UNDISTRIBUTED_INHERITANCES,
    INCOME_LUMP_SUM,
    DEBT_BUSINESS,
    DEBT_PRIVATE,
    ASSETS_REAL_ESTATE_MAIN_PROPERTY,
    ASSETS_GROSS_BUSINESS,
    ASSETS_TAXABLE_GLOBAL,
    P1_INCOME_CHILD_BENEFITS,
)

tax_declaration_so_fields_map_page_02 = {
    11: P1_INCOME_EMPLOYED_MAIN,
    12: P2_INCOME_EMPLOYED_MAIN,
    31: P1_INCOME_EMPLOYED_SIDE,
    32: P2_INCOME_EMPLOYED_SIDE,
    # 13, 14 missing here Privatanteile Auto und Spesen
    33: P1_INCOME_BOARD_SEATS,
    34: P2_INCOME_BOARD_SEATS,
    21: P1_INCOME_SELF_MAIN,
    22: P2_INCOME_SELF_MAIN,
    41: P1_INCOME_PENSION_AHV,
    42: P2_INCOME_PENSION_AHV,
    43: P1_INCOME_PENSION,
    44: P2_INCOME_PENSION,
    # 45, 46 missing here
    47: P1_INCOME_SOCIAL_SECURITY,
    48: P2_INCOME_SOCIAL_SECURITY,
    # Combined with 47, 48
    # 47: P1_INCOME_EO,
    # 48: P2_INCOME_EO,
    # 55, 56 missing here
    # There is no separation between P1 and P2, map it to P1
    49: P1_INCOME_CHILD_BENEFITS,
    50: INCOME_PORTFOLIO,
    # There is no separation between P1 and P2, map it to P1
    61: INCOME_ALIMONY_PARTNER,
    # There is no separation between P1 and P2, map it to P1
    62: INCOME_ALIMONY_CHILDREN,
    63: INCOME_UNDISTRIBUTED_INHERITANCES,
    64: INCOME_LUMP_SUM,
    66: INCOME_OTHER,
}

tax_declaration_so_fields_map_page_02_total = {
    91: INCOME_REAL_ESTATE_NET,
    100: INCOME_GROSS_TOTAL,
}

# "Eigenmietwert": FIELD_PROPERTY_IMPUTED_RENTAL_VALUE,
# "abzüglich Liegenschaftsunterhalt": FIELD_PROPERTY_MAINTENANCE_COST,
# "Total der Einkünfte": INCOME_GROSS_TOTAL


tax_declaration_so_fields_map_page_03 = {
    111: P1_EXPENSE_EMPLOYMENT,
    112: P2_EXPENSE_EMPLOYMENT,
    120: INTEREST_PAID_ON_DEBT,
    131: EXPENSE_ALIMONY_PARTNER,
    132: EXPENSE_ALIMONY_CHILDREN,
    133: EXPENSE_ANNUITY_CONTRIBUTIONS,
    141: P1_CONTRIBUTION_PILLAR_3A,
    142: P2_CONTRIBUTION_PILLAR_3A,
    150: INSURANCE_PREMIUMS_AND_INTEREST_ON_SAVINGS_ACCOUNTS,
    161: P1_CONTRIBUTION_PILLAR_2,
    162: P2_CONTRIBUTION_PILLAR_2,
    164: EXPENSE_CHILDREN_DAYCARE,
    165: DEDUCTIONS_OTHER,
    190: DEDUCTIONS_TOTAL,
    220: INCOME_NET_TOTAL,
    280: INCOME_TAXABLE_GLOBAL,
    300: INCOME_TAXABLE_LOCAL,
}

tax_declaration_so_fields_map_page_04 = {
    311: ASSETS_PORTFOLIO_ACCOUNTS,
    # 404: ASSETS_CASH_GOLD,
    312: ASSETS_LIFE_INSURANCE,
    314: ASSETS_CARS,
    315: ASSETS_OTHER,
    321: ASSETS_REAL_ESTATE_MAIN_PROPERTY,  # not precise because should be all properties
    336: ASSETS_GROSS_BUSINESS,
    340: ASSETS_GROSS_TOTAL,
    350: DEBT_PRIVATE,
    360: DEBT_BUSINESS,
    380: ASSETS_TAXABLE_GLOBAL,
    400: ASSETS_TAXABLE_LOCAL,
}
