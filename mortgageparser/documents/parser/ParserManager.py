from typing import List

import global_settings
from abbyyplumber.api import Page
from abbyyplumber.documentloader.DocumentLoader import DEFAULT_LANG_FOR_PAGE
from abbyyplumber.documentloader.DocumentLoaderFactory import DocumentLoaderFactory
from abbyyplumber.finereaderengine.commandline.finereader_engine_commandline import (
    transform_with_commandline_cache,
)
from abbyyplumber.util.DurationLog import DurationLog
from abbyyplumber.util.open_cv_util import display_search_results
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.PageLocation import PageLocation
from hypodossier.core.domain.SemanticPage import SemanticPage
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.documents.parser.pageparsers.CatchAllPageParsers import (
    CatchAllUnknownPageParser,
    CatchAllUnknownFRPageParser,
    CatchAllUnknownENPageParser,
    CatchAllUnknownITPageParser,
    CatchAllUnknownDEPageParser,
    CatchEmptyPageParser,
    CatchAlmostEmptyPageParser,
    CatchQuiteEmptyPageParser,
)
from mortgageparser.documents.parser.pageparsers.MatchPageResult import MatchPageResult
from mortgageparser.documents.parser.pageparsers.PageParserException import (
    PageParserException,
    PageValidationException,
)
from mortgageparser.documents.parser.pageparsers.all_pageparsers import (
    get_all_pageparsers,
)
from mortgageparser.documents.parser.pageparsers.plan_cadastre.plan_contaminated_sites_parsers import (
    PARSER_PLAN_CONTAMINATED_SITES_VD_1,
    PARSER_PLAN_CONTAMINATED_SITES_VD_2,
    PARSER_PLAN_CONTAMINATED_SITES_VD_3,
    PARSER_PLAN_CONTAMINATED_SITES_VD_4,
    PARSER_PLAN_CONTAMINATED_SITES_VS_1,
)

from mortgageparser.documents.parser.pageparsers.semantic_page_util import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.sgd.SgdPageParser import (
    SgdPageParser,
    SpacyClassifierPageParser,
)
from mortgageparser.util.string_utils import (
    string_utils_log,
    clean_text,
    reset_string_utils_log,
    fuzzy_match_timings,
)

import structlog

logger = structlog.getLogger(__name__)


def get_parsers_for_unknown_langugage(lang: str):
    parsers = [
        PARSER_PLAN_CONTAMINATED_SITES_VD_1,  # Needed because not enough info to detect language
        PARSER_PLAN_CONTAMINATED_SITES_VD_2,
        PARSER_PLAN_CONTAMINATED_SITES_VD_3,
        PARSER_PLAN_CONTAMINATED_SITES_VD_4,
        PARSER_PLAN_CONTAMINATED_SITES_VS_1,
        CatchAllUnknownPageParser(),
    ]
    for p in parsers:
        p.update_lang(lang)

    return parsers


class ParserManager:
    def __init__(self, lang: str, page_source: PageLocation):
        self.parsers = []  # List of parsers to be run against the page
        self.lang = lang  # Language of text in the current page
        self.page_source = (
            page_source  # Information about the physical file and page index
        )
        self.parser_manager_log = []
        self.page_classifications = None

    def add_parser(self, parser: AbstractPageParser):
        self.parsers.append(parser)
        parser.update_lang(self.lang)

    def add_parsers(self, parsers: List[AbstractPageParser]):
        for parser in parsers:
            self.add_parser(parser)

    def parse_page(
        self,
        page_index: int,
        page_handle: Page,
        text: str,
        skip_engine_fallback=True,
        use_xml=True,
        display_page_on_error=False,
    ) -> SemanticPage:
        log_everything = False

        dlog = DurationLog(
            f"ParserManager.parse_page(page_index={page_index}, file={page_handle.page_source}...)"
        )
        t = clean_text(text)
        # t = format_page_numbers(t)
        reset_string_utils_log()
        dlog.add_event("after cleanup - now start parsing")

        if page_handle.lang:
            used_parsers = self.parsers
        else:
            # We just set the language to German so every page has one of the 4 supported languages
            page_handle.lang = DEFAULT_LANG_FOR_PAGE
            used_parsers = get_parsers_for_unknown_langugage(page_handle.lang)
            logger.info(
                f"Could not detect language for this page: {page_handle.page_source_original}. Only apply {len(used_parsers)} special parsers."
            )
        for parser in used_parsers:
            if not parser.is_lang_supported(page_handle.lang):
                continue
            try:
                if isinstance(parser, SpacyClassifierPageParser):
                    # Inject page classifications that have previously been received from spacy
                    # So we do not have to process them again here
                    parser.page_classifications = self.page_classifications

                mpr: MatchPageResult = parser.match_page(page_index, page_handle, t)

                dlog.add_event(
                    f"match_page ({parser.__class__.__name__}) -> {mpr.confidence}"
                )
                if mpr.matched():
                    sp: SemanticPage = parser.parse_page(page_index, page_handle, t)

                    sp.match_page_result = mpr
                    sp.text_content_stats = page_handle.text_content_stats
                    sp.page_layout_info = page_handle.page_layout_info

                    dlog.add_event(f"parse_page ({parser.__class__.__name__})")

                    if sp:
                        if not parser.is_lang_supported(page_handle.lang):
                            raise PageParserException(
                                f"Language {page_handle.lang} is not supported for {parser} but it matched!"
                            )

                        sp.page_source = self.page_source
                        logger.info(
                            f"handle page {page_index}... {type(parser).__name__}, {sp.page_cat} -> dp={sp}"
                        )

                        try:
                            pass
                            # parser.validate_page(sp)
                        except Exception as err:
                            if skip_engine_fallback:
                                msg = f"Validation failed for dp={sp}, page={page_handle}\n\nError={err}\n"
                                page_handle.log_error(msg, err)
                                if display_page_on_error:
                                    display_search_results(
                                        page_handle,
                                        log_everything=log_everything,
                                        parsername=str(parser),
                                    )
                            else:
                                sp = self.apply_engine_fallback(
                                    page_handle,
                                    page_index,
                                    parser,
                                    use_xml,
                                    display_page_on_error,
                                )

                        dlog.log_all_events()

                        logger.info(
                            "string matching performance",
                            fuzzy_match_timings=fuzzy_match_timings,
                        )
                        return sp
                    else:
                        logger.error(
                            "\nstring_utils_log=\n  " + "\n  ".join(string_utils_log)
                        )
                        # message += '\n\nparser_log=\n  ' + '\n  '.join(pm.parser_manager_log)

                        message = f"Parser matched but did not return a Page. Parser={parser}, text={t}"
                        raise ValueError(message)
                else:
                    self.parser_manager_log.append(f"result false for {parser}")

            except PageValidationException as err:
                logger.error(
                    f"Validation Error while running parser. lang={self.lang}, parser={parser}, File={self.page_source.filename}, Text={t}"
                )
                page_handle.log_error()
                if display_page_on_error:
                    display_search_results(
                        page_handle,
                        log_everything=log_everything,
                        parsername=str(parser),
                    )
                    raise err
                return create_semantic_page(
                    page_cat=PageCat.VALIDATION_ERROR,
                    doc_cat=err.doc_cat,
                    lang=self.lang,
                    parsername=str(err),
                    page_source=self.page_source,
                )

            except Exception as err:
                if global_settings.RAISE_EXCEPTIONS_IN_PARSER:
                    raise err
                logger.error(
                    f"Technical Error while running parser. lang={self.lang}, parser={parser}, File={self.page_source.filename}, Text={t}"
                )
                page_handle.log_error(err=err)
                if display_page_on_error:
                    display_search_results(
                        page_handle,
                        log_everything=log_everything,
                        parsername=str(parser),
                    )
                    if global_settings.RAISE_EXCEPTIONS_IN_PARSER:
                        raise err
                return create_semantic_page(
                    page_cat=PageCat.PARSE_ERROR,
                    lang=self.lang,
                    parsername=str(err),
                    page_source=self.page_source,
                )

        if skip_engine_fallback:
            logger.error(
                f"Did not find matching parser. lang={page_handle.lang}, page={page_index}, File={self.page_source.filename}, Text={t}"
            )
            for parser in self.parsers:
                logger.error(f"    no match: parser {parser}")
            page_handle.log_error()
            if display_page_on_error:
                display_search_results(page_handle, log_everything=log_everything)
        else:
            # Run this only once and only with skip_engine_fallback=True to avoid endless loop
            self.parse_page(
                page_index=page_index,
                page_handle=page_handle,
                text=text,
                skip_engine_fallback=True,
                use_xml=use_xml,
            )
            # self.apply_engine_fallback(page_handle, page_index, parser, use_xml)

    def apply_engine_fallback(
        self, page_handle, page_index, parser, use_xml, display_page_on_error
    ):
        log_everything = False

        logger.info(
            f"Exception in regular parsing... try with engine... parser={parser}"
        )
        # now we try again with FR Engine
        dp = None
        try:
            doc2 = transform_with_commandline_cache(
                self.page_source.path, use_xml=use_xml
            )
            document2 = DocumentLoaderFactory.load(
                path_content=doc2,
                path_for_image=self.page_source.path,
                valid_empty_pages=[],
            )
            page = document2.pages[page_index]
            dp = parser.parse_page(page_index, page, page.get_text())
            if dp:
                dp.page_source = self.page_source

            logger.info(f"Found document page with engine... dp={dp}")
            try:
                parser.validate_page(dp)
            except Exception as err2:
                logger.warning(
                    f"Fallback Validation failed for dp={dp}, page={page_handle}\n\nError={err2}\n"
                )
                page_handle.log_error()
                if display_page_on_error:
                    display_search_results(
                        page_handle, log_everything=log_everything, parsername=parser
                    )

        except Exception as err3:
            logger.warning(
                f"Fallback Parsing failed for dp={dp}, page={page_handle}\n\nError={err3}\n"
            )
            page_handle.log_error()
            if display_page_on_error:
                display_search_results(
                    page_handle, log_everything=log_everything, parsername=parser
                )

        return dp


class SgdParserManager(ParserManager):
    def __init__(self, lang: str, page_source: PageLocation):
        super().__init__(lang, page_source)
        # self.add_parsers(get_all_pageparsers())

        self.add_parser(SgdPageParser())

        self.add_parsers(
            [
                CatchAllUnknownDEPageParser(),
                CatchAllUnknownFRPageParser(),
                CatchAllUnknownENPageParser(),
                CatchAllUnknownITPageParser(),
            ]
        )

        self.add_parser(CatchAllUnknownPageParser())


class SpacyClassifierPageParserManager(ParserManager):
    def __init__(self, lang: str, page_source: PageLocation):
        super().__init__(lang, page_source)
        # self.add_parsers(get_all_pageparsers())

        self.add_parser(SpacyClassifierPageParser())

        self.add_parsers(
            [
                CatchAllUnknownDEPageParser(),
                CatchAllUnknownFRPageParser(),
                CatchAllUnknownENPageParser(),
                CatchAllUnknownITPageParser(),
            ]
        )

        self.add_parser(CatchAllUnknownPageParser())


class DefaultParserManager(ParserManager):
    def __init__(self, lang: str, page_source: PageLocation):
        super().__init__(lang, page_source)

        self.add_parsers(get_all_pageparsers())

        # Catch these empty pages before the generic classifiers
        self.add_parser(CatchEmptyPageParser())

        self.add_parser(CatchAlmostEmptyPageParser())

        self.add_parser(CatchQuiteEmptyPageParser())

        # self.add_parser(SgdPageParserIT())

        self.add_parser(SpacyClassifierPageParser())

        self.add_parsers(
            [
                CatchAllUnknownDEPageParser(),
                CatchAllUnknownFRPageParser(),
                CatchAllUnknownENPageParser(),
                CatchAllUnknownITPageParser(),
            ]
        )
        self.add_parser(CatchAllUnknownPageParser())
