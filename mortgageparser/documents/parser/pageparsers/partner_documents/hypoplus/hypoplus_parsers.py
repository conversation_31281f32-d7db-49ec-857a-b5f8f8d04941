from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.pageparsers.SmartLetterPageParser import (
    SmartLetterPageParser,
    RankedTitle,
)

parsers_de = [
    SmartLetterPageParser(
        desc="HY Ermächtigung zur Einforderung von Auskünften DE",
        doc_cat=DocumentCat.AUTHORIZATION_FOR_INQUIRIES,
        page_cat=PageCat.GENERIC_SINGLE_PAGE,
        company="HypoPlus",
        titles=["IHRE HYPOTHEK VIA HYPOPLUS AG - SICHER, BEQUEM UND GÜNSTIG"],
        required_tokens=[
            "Auskunfsermächtigung",
            "via E-Mail, Telefon und Post",
            "Institute",
        ],
    ),
    SmartLetterPageParser(
        desc="HY Offert-Email DE #1 informell",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="HypoPlus",
        ranked_titles_all=[RankedTitle("Anfrage für Hypothek", 2)],
        required_tokens=["unter der Direktwahl 043 311", "an einer Hypothekarlösung"],
    ),
    SmartLetterPageParser(
        desc="HY Offert-Email DE #1a",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="HypoPlus",
        titles=["Ich habe Ihnen auf dem 'HypoPlus Share' Unterlagen"],
        required_tokens=["Der Link ist nur bis am", "Bitte stellen Sie uns ein"],
    ),
    SmartLetterPageParser(
        desc="HY Offert-Email DE #1b",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="HypoPlus",
        titles=[
            "Bitte stellen Sie uns ein Angebot zu - bitte Kunden nicht direkt kontaktieren."
        ],
        required_tokens=["Für weitere Fragen stehe ich Ihnen unter", "hypoplus.ch"],
    ),
    SmartLetterPageParser(
        desc="HY Offert-Email DE #2",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="HypoPlus",
        titles=["Aktueller Marktwert"],
        required_tokens=[
            "PLZ des Objekts",
            "Objekttyp",
            "Beginn der gewünschten Hypothek",
            "Bruttoeinkommen total",
            "Finanzierungsvolumen",
        ],
    ),
    SmartLetterPageParser(
        desc="HY Offert-Email DE #3",
        doc_cat=DocumentCat.MORTGAGE_REQUEST_FORM,
        page_cat=PageCat.GENERIC_PAGE,
        company="HypoPlus",
        titles=["Pfandsicherheiten total"],
        required_tokens=[
            "Verpfändung Pensionskasse (2. Säule)",
            "Verpfändung Säule 3a",
            "Verpfändung Säule 3b (Lebensversicherung)",
        ],
        hamming_dist=5,
    ),
]


parsers_en = []

parsers_fr = []

parsers_it = []


def get_parsers_hypoplus():
    return parsers_de + parsers_en + parsers_fr + parsers_it
