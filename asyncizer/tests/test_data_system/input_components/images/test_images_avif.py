import shutil
from pathlib import Path

import pytest

from asyncizer.dossier_processor import async_unpack_path
from asyncizer.tests.util_tests import (
    DossierExpectations,
    DossierTest,
    configure_test_logging,
)
from constants import OUTPUT_DIR
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.unpack import FileExtraction
from hypodossier.util.image_conversion_util import convert_any_image_to_jpeg

folder = f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_COMPONENTS}/images/avif"
path_root_folder = Path(folder)
dest_folder_prefix = "input_system_components_images_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_avif_convert():
    dest_root_folder = Path(f"{OUTPUT_DIR}/image_tests/test_avif_convert")
    dest_root_folder.mkdir(exist_ok=True, parents=True)

    p = path_root_folder / "single/id-card.avif"
    p_out = convert_any_image_to_jpeg(p, dest_root_folder)
    print(p_out)
    assert p_out.exists()
    assert p_out.name == "id-card.jpeg"


@pytest.mark.asyncio
async def test_avif_convert_unpack():
    configure_test_logging()

    p = path_root_folder / "single/id-card.avif"
    assert p.exists()

    # Copy the file somewhere else because unpacking happens in-place
    dest_root_folder = Path(f"{OUTPUT_DIR}/image_tests/test_avif_convert_unpack")
    dest_root_folder.mkdir(exist_ok=True, parents=True)

    if dest_root_folder.exists():
        shutil.rmtree(str(dest_root_folder))
    dest_root_folder.mkdir(parents=True, exist_ok=True)

    p2 = dest_root_folder / p.name

    shutil.copy(p, p2)

    file_extraction: FileExtraction = await async_unpack_path(p2)

    assert not p2.is_file()  # it should have been changed to a dir with jpeg inside
    assert (p2 / "id-card.jpeg").exists()

    assert file_extraction
    assert len(file_extraction.extracted_files) == 1
    assert file_extraction.extracted_files[0] == "id-card.avif/id-card.jpeg"


@pytest.mark.asyncio
async def test_avif_in_7z_as_dossier():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "7z",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency_per_page={DocumentCat.ID: 1},
        ),
    ).run()


@pytest.mark.asyncio
async def test_avif_in_rar_as_dossier():
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=path_root_folder / "rar",
        source_file_filter=[],
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=0,
            expected_doc_cat_frequency_per_page={DocumentCat.ID: 1},
        ),
    ).run()
