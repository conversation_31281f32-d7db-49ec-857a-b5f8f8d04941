from abbyyplumber.converter.ValueConverter import (
    Date<PERSON>onverter,
    C<PERSON><PERSON>cyConverter,
    AddressConverter,
    NameFromAddressConverter,
    CleanNameConverter,
)
from abbyyplumber.plumberstudio.ContentExtractor import ContentExtractor
from abbyyplumber.plumberstudio.SearchElement import (
    SearchElementStaticText,
    SearchElementArea,
    SearchElementReference,
    SearchElementLabeledField,
)
from abbyyplumber.plumberstudio.SearchRelation import (
    SearchRelationBelow,
    SearchRelationAbove,
    SearchRelationLeftOf,
    ReferenceBoundaryHorizontal,
    SearchRelationRightOf,
)
from abbyyplumber.util.plumberstudio_util import PercentageRange
from hypodossier.core.documents.debtcollectioninformation.DebtCollectionPageData import (
    DCI_FIELD_CONFIRMATION_EMPTY,
    DCI_FIELD_ADDRESS_LINE,
    DCI_FIELD_CONFIRMATION_NOT_EMPTY,
    DCI_FIELD_ADDRESS_LINE_FULLNAME,
)
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from hypodossier.core.domain.SemanticField import (
    FIELD_DOCUMENT_DATE,
    FIELD_DATE_OF_BIRTH,
    FIELD_ADDRESS_BLOCK,
    FIELD_FULLNAME,
    FIELD_FIRSTNAME,
)
from mortgageparser.documents.parser.pageparsers.StandardPageParser import (
    StandardPageParser,
)
from mortgageparser.documents.parser.pageparsers.debtcollectioninformation.DebtCollectionInformationPageParser import (
    DebtCollectionInformationPageOnePageParser,
)
from mortgageparser.documents.parser.pageparsers.debtcollectioninformation.debt_collection_information_util import (
    AddresslineFullnameConverter,
)
from mortgageparser.util.search_element_util import create_search_element_firstname
from mortgageparser.util.string_utils import contains_all_strings


class DebtCollectionInformationPageOneEmptyValaisFRPageParser(
    DebtCollectionInformationPageOnePageParser
):
    def __init__(self):
        self.supported_languages = ["de", "fr"]

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        success = contains_all_strings(text, ["Déclaration", "Bescheinigung"])
        if success:
            if contains_all_strings(
                text,
                [
                    "L'Office des poursuites et faillites du district de",
                    "Das Betreibungs- und Konkursamt des Bezirkes ",
                    "bescheinigt",
                ],
            ) or contains_all_strings(
                text,
                [
                    "L'Office des poursuites des districts de",
                    "Das Betreibungs der Bezirke Martigny und Entremont bescheinigt,",
                ],
                hamming_dist=8,
            ):
                if contains_all_strings(
                    text,
                    [
                        "ne fait pas l'objet de poursuites et n'est pas sous le coup d'actes",
                        "weder Betreibungen noch Verlustscheine bestehen",
                        "de défaut de biens dans l'arrondissement de poursuites",
                    ],
                    hamming_dist=8,
                ):
                    return True

    def parse_page_header(self):
        success = self.page.set_header_by_any_text(
            ["Bescheinigung", "Déclaration"], max_l_dist=6, include_pattern=False
        )
        if not success:
            success = self.page.set_header_by_percentage(5 / 29)

        return success

    def parse_page_footer(self):
        success = self.page.set_footer_by_text("La présente déclaration est délivrée")
        if not success:
            success = super().parse_page_footer()
        return success

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        parsers = [
            SearchElementArea(
                FIELD_DOCUMENT_DATE.name, page.footer, converter=DateConverter()
            ),
            SearchElementStaticText(
                "text_above",
                page.main,
                label="L'Office des poursuites et faillites du district de",
                extract=False,
            ),
            SearchElementStaticText(
                DCI_FIELD_CONFIRMATION_EMPTY.name,
                page.main,
                label="ne fait pas l'objet de poursuites et n'est pas sous le coup d'actes",
            ),
            SearchElementArea(
                DCI_FIELD_ADDRESS_LINE.name,
                page.main,
                relations=[
                    SearchRelationBelow("text_above"),
                    SearchRelationAbove(DCI_FIELD_CONFIRMATION_EMPTY.name),
                    SearchRelationLeftOf(
                        DCI_FIELD_CONFIRMATION_EMPTY.name,
                        ref_boundary=ReferenceBoundaryHorizontal.RIGHT,
                    ),
                ],
            ),
            SearchElementReference(
                DCI_FIELD_ADDRESS_LINE_FULLNAME.name,
                DCI_FIELD_ADDRESS_LINE.name,
                converter=AddresslineFullnameConverter(),
            ),
            SearchElementArea(
                FIELD_FIRSTNAME.sr_inside,
                page.fullpage,
                extract=False,
                compress_whitespace=True,
                relations=[SearchRelationAbove(DCI_FIELD_CONFIRMATION_EMPTY.name)],
            ),
            create_search_element_firstname(page.main),
        ]
        parsers += [
            SearchElementArea(
                FIELD_DATE_OF_BIRTH.name,
                page.main,
                relations=[
                    SearchRelationBelow("text_above"),
                    SearchRelationAbove(DCI_FIELD_CONFIRMATION_EMPTY.name),
                    SearchRelationRightOf(DCI_FIELD_CONFIRMATION_EMPTY.name),
                ],
                converter=DateConverter(),
            ),
        ]

        return ContentExtractor(parsers)


class DebtCollectionInformationPageOneNotEmptyValaisFRPageParser(StandardPageParser):
    def __init__(self):
        self.supported_languages = ["fr"]
        self.doc_cat = DocumentCat.DEBT_COLLECTION_INFORMATION
        self.page_cat = PageCat.DEBT_COLLECTION_FIRST_PAGE

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Extrait des registres art. 8a LP" "Débiteur",
                "Poursuites",
                "Montant total des poursuites",
                "Actes de défaut de biens suite à une saisie",
            ],
        )

    def parse_page_header(self):
        success = self.page.set_header_by_any_text(
            ["Extrait des registres art. 8a LP"], max_l_dist=0.3
        )
        if not success:
            success = super().parse_page_header()
        return success

    def parse_page_footer(self):
        return self.page.set_footer_by_percentage(6 / 27)

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        return ContentExtractor(
            [
                SearchElementLabeledField(
                    DCI_FIELD_CONFIRMATION_NOT_EMPTY.name,
                    page.main,
                    label="Montant total des poursuites",
                    converter=CurrencyConverter(),
                ),
                SearchElementLabeledField(
                    FIELD_ADDRESS_BLOCK.name,
                    self.page.main,
                    label="Débiteur:",
                    field_vertical_line_scale=10,
                    converter=AddressConverter(),
                    field_width=PercentageRange(0, 0.5),
                    compress_whitespace=True,
                ),
                SearchElementReference(
                    FIELD_FULLNAME.name,
                    ref=FIELD_ADDRESS_BLOCK.name,
                    converter=NameFromAddressConverter(),
                ),
                SearchElementReference(
                    DCI_FIELD_ADDRESS_LINE.name, FIELD_ADDRESS_BLOCK.name
                ),
                SearchElementArea(
                    FIELD_DOCUMENT_DATE.name, page.footer, converter=DateConverter()
                ),
                SearchElementLabeledField(
                    FIELD_DATE_OF_BIRTH.name,
                    self.page.main,
                    label="Date de naissance",
                    converter=DateConverter(),
                ),
            ]
        )


"""

Montant total des actes de défaut de biens : Fr. 0.00
Conformément à l'art. 8a al. 4 LP, il n'est pas fait mention des poursuites clôturées depuis plus de 5 ans. Ainsi, les poursuites frappées 
d'opposition totale depuis plus de 6 ans, s'il s'agit de poursuites ordinaires ou en réalisation de gage mobilier, ou 7 ans, s'il s'agit de 
poursuites en réalisation de gage immobilier, n'apparaissent pas dans les extraits (JdT 2001 II 67, ATF 128 III 334, JdT 2002 II 76).
Les créances mentionnées sont accrues des intérêts échus à ce jour et des frais déjà inscrits. Sont réservés les intérêts non encore 
échus et les frais ultérieurs dans la procédure en cours.
Office des poursuites et faitates du district de Monthey
Frais :
Emolument 0.00
Débours 0.00
Total 0.00
"""


class DebtCollectionInformationPageTwoNotEmptyValaisFRPageParser(StandardPageParser):
    def __init__(self):
        super().__init__()
        self.supported_languages = ["fr"]
        self.doc_cat = DocumentCat.DEBT_COLLECTION_INFORMATION
        self.page_cat = PageCat.DEBT_COLLECTION_OTHER_PAGE

    def match_page_by_text(self, page_index: int, text: str) -> bool:
        return contains_all_strings(
            text,
            [
                "Débiteur",
                "Montant total des actes de défaut de biens",
                #            "Montant total des poursuites",
                "Les créances mentionnées sont accrues des intérêts échus à ce jour et des frais déjà inscrits. Sont réservés les intérêts non encore échus et les frais ultérieurs dans la procédure en cours.",
            ],
        )

    def parse_page_header(self):
        success = self.page.set_header_by_percentage(0)
        return success

    def parse_page_footer(self):
        return self.page.set_footer_by_percentage(6 / 27)

    def create_content_extractor(self) -> ContentExtractor:
        page = self.page
        return ContentExtractor(
            [
                # Must not be amount because that field exists in metadata - not clean implementation yet
                # if EMPTY = '0' then valid, at the same time NOT_EMPTY must be None
                SearchElementLabeledField(
                    "amount_string",
                    page.main,
                    label="Montant total des actes de défaut de biens:",
                    converter=CurrencyConverter(),
                ),
                SearchElementReference(
                    DCI_FIELD_CONFIRMATION_EMPTY.name,
                    ref="amount_string",
                    converter=CurrencyConverter(max_amount=0),
                ),
                SearchElementReference(
                    DCI_FIELD_CONFIRMATION_NOT_EMPTY.name,
                    ref="amount_string",
                    converter=CurrencyConverter(min_amount=1),
                ),
                SearchElementLabeledField(
                    FIELD_ADDRESS_BLOCK.name,
                    self.page.main,
                    label="Débiteur:",
                    field_vertical_line_scale=2.5,
                    converter=CleanNameConverter(),
                ),
                SearchElementReference(
                    FIELD_FULLNAME.name,
                    ref=FIELD_ADDRESS_BLOCK.name,
                    converter=NameFromAddressConverter(),
                ),
                SearchElementReference(
                    DCI_FIELD_ADDRESS_LINE.name, FIELD_ADDRESS_BLOCK.name
                ),
                SearchElementArea(
                    FIELD_DOCUMENT_DATE.name, page.footer, converter=DateConverter()
                ),
            ]
        )
