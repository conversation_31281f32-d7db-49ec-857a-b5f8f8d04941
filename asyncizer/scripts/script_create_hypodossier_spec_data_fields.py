from datetime import datetime
from pathlib import Path
from typing import Dict, List

from asyncizer.hypodossier_excel_spec_data_fields import (
    create_hypodossier_excel_spec_data_fields,
    create_hypodossier_txt_spec_data_fields,
)
from constants import BASE_DIR
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.DocumentCatElement import DocumentCatalog
from hypodossier.core.domain.PageDataMapping import create_doc_cat_pagedata_dict

OFFICIAL_DATA_FIELDS_PER_DOCCAT = {
    # Identity
    "Pass": DocumentCat.PASSPORT_CH,
    # "Pass DE": DocumentCat.PASSPORT_DE,
    # "Pass FR": DocumentCat.PASSPORT_FR,
    # "Pass IT": DocumentCat.PASSPORT_IT,
    "ID": DocumentCat.ID,
    "Aufenthaltstitel": DocumentCat.RESIDENCE_PERMIT,
    "Betreibungsauskunft": DocumentCat.DEBT_COLLECTION_INFORMATION,
    # 240913 mt deactivated: DocumentCat.CRIMINAL_RECORDS.value.de: DocumentCat.CRIMINAL_RECORDS,
    # Income
    "Steuererklärung": DocumentCat.TAX_DECLARATION,
    DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL.value.de: DocumentCat.BANK_STATEMENT_OF_INTEREST_CAPITAL,
    DocumentCat.BANK_DOCUMENT.value.de: DocumentCat.BANK_DOCUMENT,
    DocumentCat.SALARY_CERTIFICATE.value.de: DocumentCat.SALARY_CERTIFICATE,
    DocumentCat.PAYSLIP.value.de: DocumentCat.PAYSLIP,
    "Rente AHV": DocumentCat.PENSION_PAYMENT_AHV,
    "Rente BVG": DocumentCat.PENSION_PAYMENT_BVG,
    "Arbeitslosenbescheinigung": DocumentCat.UNEMPLOYMENT_SALARY_CERTIFICATE,
    # Pension
    "Rentenvorausberechnung": DocumentCat.PENSION_SIMULATION1,
    "Pensionskasse": DocumentCat.PENSION_CERTIFICATE,
    "Freizügigkeit": DocumentCat.VESTED_BENEFITS_ACCOUNT,
    "Säule 3a Konto": DocumentCat.PENSION3A_ACCOUNT,
    # 240913 mt deactivated: DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION.value.de: DocumentCat.PENSION_CONTRIBUTION_CONFIRMATION,
    "Säule 3a Police": DocumentCat.PENSION3A_INSURANCE_CONTRACT,
    "Säule 3a Rückkaufswert": DocumentCat.PENSION3A_INSURANCE_LETTER_REDEMPTION,
    # Company Stuff here
    DocumentCat.HRA.value.de: DocumentCat.HRA,
    DocumentCat.FINANCIAL_STATEMENT_COMPANY.value.de: DocumentCat.FINANCIAL_STATEMENT_COMPANY,
    # Property Stuff starts here
    "Grundbuchauszug": DocumentCat.EXTRACT_FROM_LAND_REGISTER,
    "Gebäudeversicherung": DocumentCat.PROPERTY_INSURANCE,
    "Schätzung": DocumentCat.PROPERTY_VALUATION,
    "Amtlicher Wert": DocumentCat.PROPERTY_VALUATION_GOV,
    # Financing / Morgage starts here
    DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION.value.de: DocumentCat.MORTGAGE_PRODUCT_CONFIRMATION,
    DocumentCat.MORTGAGE_CONTRACT.value.de: DocumentCat.MORTGAGE_CONTRACT,
    # Misc starts here
    # 240913 mt deactivated: DocumentCat.CORRESPONDENCE_EMAIL.value.de: DocumentCat.CORRESPONDENCE_EMAIL,
}


def pretty_print_fields(doc_title_de, fields, separator=";"):
    print("  ")
    print(f"Dokument {doc_title_de}")
    print(
        f"Feld ID{separator}Feld Typ{separator}Titel DE{separator}Titel EN{separator}Feldbeschreibung"
    )
    for name, field in fields.items():
        type = str(field.extraction_type.name).lower()

        print(
            f"{name}{separator}{type}{separator}{field.titles.de}{separator}{field.titles.en}{separator}{field.desc_de}"
        )


def create_spec_data_fields(
    data_field_mapping: Dict[str, DocumentCat],
    timestamp_version=datetime.today().strftime("%Y%m%d")[2:],
):
    """
    Export the data field specification to doc/internal/spec_data_fields

    """
    # Order this by doc_id to be consistent in the future

    p_excel = Path(
        f"{BASE_DIR}/doc/internal/spec_data_fields/{timestamp_version}_Hypodossier_Spezifikation_Datenfelder.xlsx"
    )
    create_hypodossier_excel_spec_data_fields(data_field_mapping, p_excel)
    assert p_excel.exists()
    print(f"Exported  data fields excel spec to {p_excel}")

    p_txt = Path(
        f"{BASE_DIR}/doc/internal/spec_data_fields/{timestamp_version}_Hypodossier_Spezifikation_Datenfelder.txt"
    )
    num_fields = create_hypodossier_txt_spec_data_fields(data_field_mapping, p_txt)
    assert p_txt.exists()
    print(f"Exported data fields txt spec to {p_txt}")

    print(f"In total {num_fields} fields.")


def print_data_fields_mappings(
    data_field_mapping: Dict[str, DocumentCat],
    document_catalog_filter=DocumentCatalog.DEFAULT,
):
    # Iterate through OFFICIAL_DATA_FIELDS_PER_DOCCAT to print mappings

    doccat_to_pagedata = create_doc_cat_pagedata_dict()

    pagedata_to_doccat: Dict[str, List[DocumentCat]] = {}
    for doccat, pagedata in doccat_to_pagedata.items():
        key = pagedata.__class__.__name__
        if key not in pagedata_to_doccat:
            pagedata_to_doccat[key]: List[DocumentCat] = []
        pagedata_to_doccat[key].append(doccat)

    for doc_title, doc_cat in data_field_mapping.items():
        if (
            document_catalog_filter
            and doc_cat.value.document_catalog != document_catalog_filter
        ):
            print(f"SKIPPP {doc_cat}")
            continue

        pagedata = doccat_to_pagedata[doc_cat]
        pagedata_name = pagedata.__class__.__name__
        doccats = pagedata_to_doccat[pagedata_name]
        print(f"\nModell '{pagedata_name}', Tab '{doc_title}':")
        for dc in doccats:
            if (
                document_catalog_filter
                and dc.value.document_catalog != document_catalog_filter
            ):
                print(f"SKIPPP {dc}")
                continue
            print(f"  {dc.value.docid} {dc.value.de} {dc.name}")


if __name__ == "__main__":
    # create_spec_data_fields(OFFICIAL_DATA_FIELDS_PER_DOCCAT)

    print_data_fields_mappings(OFFICIAL_DATA_FIELDS_PER_DOCCAT)
