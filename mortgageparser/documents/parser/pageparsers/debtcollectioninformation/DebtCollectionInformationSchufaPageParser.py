from hypodossier.core.domain.SemanticPage import SemanticPage
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.PageCat import PageCat
from mortgageparser.documents.parser.extraction.SemanticPageCreator import (
    create_semantic_page,
)
from mortgageparser.documents.parser.pageparsers.AbstractPageParser import (
    AbstractPageParser,
)
from mortgageparser.util.string_utils import contains_all_strings


class DebtCollectionInformationSchufaPageParser(AbstractPageParser):
    def match_page_by_text(self, page_index: int, text: str) -> bool:
        # Can be "BonitätsCheck"  or 'Bonitätsauskunft'
        return contains_all_strings(
            text,
            [
                "SCHUFA-Bonitäts",
                "ausschließlich positive Vertragsinformationen vor",
                "Diese umfassen beispielsweise Daten zu Girokonten, Kreditkarten",
                "Echtheit dieses Zertifikats",
            ],
        )

    def parse(self) -> SemanticPage:
        return create_semantic_page(
            self, DocumentCat.SCHUFA_BONITAETSCHECK, PageCat.SCHUFA_BONITAETSCHECK
        )
