from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest, PO_EXISTS
from global_settings import (
    PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER,
    ENABLE_DETECTRON2_OBJECT_DETECTION,
)
from hypodossier.core.domain.DocumentCat import DocumentCat

path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_DOSSIER}/input_samples_demo/sales_pitch_mix_dir"
)
dest_folder_prefix = "input_system_doccat_sales_pitch_mix_dir_"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        path_root_folder.exists()
    ), f"Root folder for local tax tests does not exists: path_root_folder={path_root_folder}"


@pytest.mark.asyncio
async def test_gva():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["zh gvb mt.pdf"],
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=2
        ),
    ).run()


@pytest.mark.asyncio
async def test_pk_zwei_stück():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["pk_mt_zwei_stück.pdf"],
        dossier_expectations=DossierExpectations(
            expected_num_extracted_file_exceptions=2
        ),
    ).run()


@pytest.mark.asyncio
async def test_floor_plan():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=[
            "some_floorplan_with_a_really_long_filename_inside_a_zip.zip"
        ],
        dossier_expectations=DossierExpectations(
            expected_filenames=["604 Plan étage.pdf"],
            expected_page_objects={
                0: {
                    # 'plan_floor': PO_EXISTS,  # Not supported yet as the graphical page objects are not in the aggregated page objects
                }
            },
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_num_extracted_file_exceptions=2,
            expected_num_processing_exceptions=0,
            expected_num_docs=1,
        ),
        client_lang="fr",
    ).run()


@pytest.mark.asyncio
async def test_plan_situation():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["IMG_5555.jpg"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_SITUATION: 2},
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_photo_undetected():
    await DossierTest(
        use_ocr_cache=True,
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["IMG_7716.jpg"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency_per_page={DocumentCat.PROPERTY_PHOTOS: 1},
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_floor_plan_with_text():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check identity objects")
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["floorplan_with_text.pdf"],
        dossier_expectations=DossierExpectations(
            expected_filenames=["604 Grundriss.pdf"],
            expected_page_objects={0: {}},
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.PLAN_FLOOR: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_vr():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["mt vr 10 searchable.pdf"],
        dossier_expectations=DossierExpectations(
            expected_filenames=["330 Lohnausweis Manuel Thiemann 2016.pdf"],
            expected_page_objects={
                0: {
                    "document_date": "20.01.2017",
                    "fullname": PO_EXISTS,
                    # 230226 mt: these do not exist anymore with hylayoutlm
                    # "firstname": PO_EXISTS,
                    # "street": PO_EXISTS,
                    # "zip": "8055",
                    # "city": "Zürich",
                    "address_block": PO_EXISTS,
                    "ahv_new": PO_EXISTS,
                    "year": "2016",
                    "accounting_period_from": "01.03.2016",
                    "accounting_period_to": "31.12.2016",
                    "salary_board": "CHF 10'000",
                    "salary_gross": "CHF 10'000",
                    "salary_benefits_ahv": "CHF 622",
                    "salary_net": "CHF 9'378",
                    "salary_comments": "Spesenreglement durch Kanton GR am 17.11.2010 genehmigt.",
                    "company_contact": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency={DocumentCat.SALARY_CERTIFICATE: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_identity_id():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["ID CH Manuel Thiemann.pdf"],
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "220 Identitätskarte CH Manuel Antonius Thiemann 1977-09-04.pdf"
            ],
            expected_page_objects={
                0: {
                    "document_date": "25.10.2013",
                    "document_validity_end_date": "24.10.2023",
                    "sex": "M",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "C7011658",
                    "date_of_birth": "04.09.1977",
                    "hometown": "Ringgenberg BE",
                    # "person_height": "180.0",      # seems to work as jpg but not as pdf
                    "mrz": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency={DocumentCat.ID: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.ID: 2},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
# works for MT # @pytest.mark.skip("Broken test - needs to be fixed/checked by Manuel")
async def test_identity_passport():
    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["Pass Manuel Thiemann.pdf"],
        dossier_expectations=DossierExpectations(
            expected_filenames=["210 Pass CH Manuel Antonius Thiemann 1977-09-04.pdf"],
            expected_page_objects={
                0: {
                    "document_date": "10.04.2015",
                    "firstname": PO_EXISTS,
                    "lastname": PO_EXISTS,
                    "person_id": "X0668306",
                    "date_of_birth": "04.09.1977",
                    "sex": "M",
                    "document_validity_end_date": "09.04.2025",
                    "hometown": "Ringgenberg",
                    # "person_height": "180.0",      # seems to work as jpg but not as pdf
                    "mrz": PO_EXISTS,
                }
            },
            expected_doc_cat_frequency={DocumentCat.PASSPORT_CH: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.PASSPORT_CH: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_tax_new():
    source_file_filter = ["TAX_max_maria_2019 4.pdf"]

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        # show_page=1,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=source_file_filter,
        dossier_expectations=DossierExpectations(
            expected_filenames=["310 Steuererklärung Mustermann Max ZH 2019.pdf"],
            expected_page_objects={
                0: {
                    "canton_short": "ZH",
                    "year": "2019",
                    "document_date": "20.04.2020",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8055",
                    "city": "Zürich",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "04.09.1967",
                    "p1_profession": "Informatiker",
                    "p1_employer": "Beispielfirma AG",
                    "p1_employer_location": "Zürich",
                    "p1_marital_status": "verheiratet",
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_date_of_birth": "21.10.1976",
                    "p2_profession": "Solution Consultant",
                    "p2_employer": "ServiceFirma",
                    "p2_phone_primary": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 22'506",
                    "p1_income_employed_side": "CHF 7'502",
                    "p1_contribution_pillar_3a": "CHF 3'350",
                    "p2_income_employed_main": "CHF 128'991",
                    "p2_income_eo": "CHF 4'388",
                    "p2_income_child_benefits": "CHF 4'800",
                    "p2_contribution_pillar_3a": "CHF 6'700",
                    "income_portfolio": "CHF 15",
                    "income_real_estate_gross": "CHF 25'700",
                    "income_real_estate_net_primary": "CHF 20'560",
                    "income_gross_total": "CHF 188'762",
                    "income_net_total": "CHF 117'756",
                    "income_taxable_global": "CHF 99'756",
                    "income_taxable_local": "CHF 99'756",
                    "p1_expense_employment": "CHF 7'901",
                    "p2_expense_employment": "CHF 8'850",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'800",
                    "expense_children_daycare": "CHF 19'248",
                    "property_maintenance_cost": "CHF 5'140",
                    "deductions_total": "CHF 71'006",
                    "assets_portfolio": "CHF 2'458'532",
                    "assets_cars": "CHF 1'040",
                    "assets_other": "CHF 180'288",
                    "assets_gross_total": "CHF 3'811'860",
                    "assets_taxable_global": "CHF 2'676'860",
                    "assets_taxable_local": "CHF 2'676'860",
                    "assets_real_estate_main_property": "CHF 1'172'000",
                    "address_real_estate_primary": PO_EXISTS,
                    "property_imputed_rental_value": "CHF 25'700",
                    "debt_total": "CHF 1'135'000",
                    "interest_paid_on_debt": "CHF 11'257",
                    "debt_detail_lines": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257",
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_tax_new_fr():
    source_file_filter = ["TAX_max_maria_2019 4.pdf"]

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=source_file_filter,
        dossier_expectations=DossierExpectations(
            expected_filenames=["310 Déclaration impôt Mustermann Max ZH 2019.pdf"],
            expected_page_objects={
                0: {
                    "canton_short": "ZH",
                    "year": "2019",
                    "document_date": "20.04.2020",
                    "address_block": PO_EXISTS,
                    "street": PO_EXISTS,
                    "zip": "8055",
                    "city": "Zürich",
                    "p1_fullname": PO_EXISTS,
                    "p1_ahv_new": PO_EXISTS,
                    "p1_date_of_birth": "04.09.1967",
                    "p1_profession": "Informatiker",
                    "p1_employer": "Beispielfirma AG",
                    "p1_employer_location": "Zürich",
                    "p1_marital_status": "verheiratet",
                    "phone_primary": PO_EXISTS,
                    "phone_secondary": PO_EXISTS,
                    "p2_firstname": PO_EXISTS,
                    "p2_date_of_birth": "21.10.1976",
                    "p2_profession": "Solution Consultant",
                    "p2_employer": "ServiceFirma",
                    "p2_phone_primary": PO_EXISTS,
                    "section_children": PO_EXISTS,
                    "p1_income_employed_main": "CHF 22'506",
                    "p1_income_employed_side": "CHF 7'502",
                    "p1_contribution_pillar_3a": "CHF 3'350",
                    "p2_income_employed_main": "CHF 128'991",
                    "p2_income_eo": "CHF 4'388",
                    "p2_income_child_benefits": "CHF 4'800",
                    "p2_contribution_pillar_3a": "CHF 6'700",
                    "income_portfolio": "CHF 15",
                    "income_real_estate_gross": "CHF 25'700",
                    "income_real_estate_net_primary": "CHF 20'560",
                    "income_gross_total": "CHF 188'762",
                    "income_net_total": "CHF 117'756",
                    "income_taxable_global": "CHF 99'756",
                    "income_taxable_local": "CHF 99'756",
                    "p1_expense_employment": "CHF 7'901",
                    "p2_expense_employment": "CHF 8'850",
                    "insurance_premiums_and_interest_on_savings_accounts": "CHF 7'800",
                    "expense_children_daycare": "CHF 19'248",
                    "property_maintenance_cost": "CHF 5'140",
                    "deductions_total": "CHF 71'006",
                    "assets_portfolio": "CHF 2'458'532",
                    "assets_cars": "CHF 1'040",
                    "assets_other": "CHF 180'288",
                    "assets_gross_total": "CHF 3'811'860",
                    "assets_taxable_global": "CHF 2'676'860",
                    "assets_taxable_local": "CHF 2'676'860",
                    "assets_real_estate_main_property": "CHF 1'172'000",
                    "address_real_estate_primary": PO_EXISTS,
                    "property_imputed_rental_value": "CHF 25'700",
                    "debt_total": "CHF 1'135'000",
                    "interest_paid_on_debt": "CHF 11'257",
                    "debt_detail_lines": "Credit Suisse, 8070 Zürich, Fix-Hypothek                                                                1 135 000                    11 257",
                }
            },
            expected_doc_cat_frequency={DocumentCat.TAX_DECLARATION: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
        client_lang="fr",
    ).run()


@pytest.mark.asyncio
async def test_sales_new_fr():
    source_file_filter = ["Doku_Zürich_Birmensdorferstrasse 578.pdf"]

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=source_file_filter,
        dossier_expectations=DossierExpectations(
            expected_filenames=["610 Documentation vente.pdf"],
            expected_doc_cat_frequency={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
        client_lang="fr",
    ).run()


@pytest.mark.asyncio
async def test_unknown_gemini():
    source_file_filter = ["Gemini Bestimmungen.pdf"]

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=source_file_filter,
        dossier_expectations=DossierExpectations(
            # This is from error.zip
            expected_num_extracted_file_exceptions=2
        ),
        client_lang="fr",
    ).run()


@pytest.mark.asyncio
async def test_430():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check identity objects")

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        source_file_filter=["saeule_3a_at_und_unbek_mt.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[],
            expected_page_objects={},
            expected_doc_cat_frequency={},
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
            expected_num_processing_exceptions=0,
        ),
    ).run()


@pytest.mark.asyncio
async def test_full():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check identity objects")

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        # source_file_filter=["Gemini Bestimmungen.pdf"],
        # show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_filenames=[
                "210 Pass CH Manuel Antonius Thiemann 1977-09-04.pdf",
                "220 Identitätskarte CH Manuel Antonius Thiemann 1977-09-04.pdf",
                "240 Betreibungsauskunft Thiemann Manuel Antonius Keine Betreibungen 2014-04-11.pdf",
                "240 Betreibungsauskunft Thiemann-Strutz Angelica Keine Betreibungen 2014-04-11.pdf",
                "245 Strafregisterauszug Thiemann Manuel Antonius 2020-09-01.pdf",
                "310 Steuererklärung Mustermann Max ZH 2019.pdf",
                "330 Lohnausweis Manuel Thiemann 2016.pdf",
                "410 PK Ausweis Thiemann Manuel AXA Basis Kader 2017-03-01.pdf",
                # Splitting of PK docs is not done automatically anymore as this causes too many problems
                # "410 PK Ausweis Thiemann Manuel AXA Zusatz GL 2017-03-01.pdf",
                "416 PK Information.pdf",
                "424 Freizügigkeitskonto Manuel Thiemann Zürcher Kantonalbank 2019-12-31.pdf",
                # Do not test for this 430 because the name changes between " S.pdf" at the end and ".pdf"
                # "430 Vorsorgekonto Säule 3 Angelica Thiemann PostFinance S.pdf",
                "604 Grundriss.pdf",
                "610 Verkaufsdokumentation.pdf",
                "617 Gebäudeversicherungsausweis ZH Birmensdorferstrasse 576 8055 2020-03-20.pdf",
            ],
            expected_page_objects={},
            expected_doc_cat_frequency={
                DocumentCat.CRIMINAL_RECORDS: 1,
                DocumentCat.DEBT_COLLECTION_INFORMATION: 2,
                DocumentCat.ID: 1,
                DocumentCat.PASSPORT_CH: 1,
                DocumentCat.PENSION3A_ACCOUNT: 1,
                DocumentCat.PENSION_CERTIFICATE: 2,
                DocumentCat.PENSION_CERTIFICATE_INFO: 1,
                DocumentCat.PLAN_FLOOR: 1,
                DocumentCat.PLAN_SITUATION: 1,
                DocumentCat.PROPERTY_INSURANCE: 2,
                DocumentCat.SALARY_CERTIFICATE: 1,
                DocumentCat.SALES_DOCUMENTATION: 1,
                DocumentCat.TAX_DECLARATION: 1,
                DocumentCat.VESTED_BENEFITS_ACCOUNT: 1,
                DocumentCat.PROPERTY_PHOTOS: 1,
            },
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
            expected_num_processing_exceptions=2,
        ),
    ).run()


@pytest.mark.asyncio
async def test_floorplan():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check identity objects")

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["floorplan_with_text.pdf"],
        dossier_expectations=DossierExpectations(
            expected_filenames=[],
            expected_page_objects={},
            expected_doc_cat_frequency={DocumentCat.PLAN_FLOOR: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()


@pytest.mark.asyncio
async def test_sales_documentation():
    if not ENABLE_DETECTRON2_OBJECT_DETECTION:
        raise Exception("Must activate object detection to check identity objects")

    await DossierTest(
        override_existing=True,
        source_folder=path_root_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["Doku_Zürich_Birmensdorferstrasse 578.pdf"],
        dossier_expectations=DossierExpectations(
            expected_filenames=[],
            expected_page_objects={0: {}},
            expected_doc_cat_frequency={DocumentCat.SALES_DOCUMENTATION: 1},
            expected_num_docs=1,
            expected_num_extracted_file_exceptions=2,  # This is from error.zip
        ),
    ).run()
