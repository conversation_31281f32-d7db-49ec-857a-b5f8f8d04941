import codecs
from datetime import datetime
from pathlib import Path
from typing import Dict, List

from pandas import ExcelWriter, Data<PERSON>rame

from constants import BASE_DIR
from hypodossier.core.domain.DocumentCat import DocumentCat
from hypodossier.core.domain.DocumentCatElement import (
    DocumentCatElement,
    DocumentCatalog,
)
from hypodossier.core.domain.DocumentTopic import (
    COMMON_TOPICS,
    DocumentTopic,
    ALL_TOPICS,
)
from hypodossier.core.domain.PageDataMapping import create_doc_cat_pagedata_dict
from hypodossier.core.domain.document_category_util import (
    is_doc_cat_valid_as_independent_document,
    is_doc_cat_in_public_default_set,
)


def get_columns_for_doccat(doccat: DocumentCat, add_column_datamodel) -> Dict[str, str]:
    e: DocumentCatElement = doccat.value

    d = {
        "Doc ID": str(e.docid),
        "Kategorie": e.topic.name,
        "Name": str(doccat.name),
        "Titel DE": e.de,
        "Titel FR": e.fr,
        "Titel EN": e.en,
        "Titel IT": e.it,
        "Beschreibung": e.desc_de if e.desc_de else "",
        "Misc Type": str(e.misc_type),
    }
    if add_column_datamodel:
        doc_cat_pagedata_dict = create_doc_cat_pagedata_dict()
        if doccat in doc_cat_pagedata_dict:
            pagedata_name = doc_cat_pagedata_dict[doccat].__class__.__name__
        else:
            pagedata_name = ""  # "(no data model)"
        d["Data Model"] = pagedata_name
    return d


def get_columns_for_all_doccats(
    topics: List[DocumentTopic] = None,
    all_entries: bool = False,
    document_catalogs: List[DocumentCatalog] = None,
    add_column_datamodel: bool = True,
) -> List[Dict[str, str]]:
    lines = []

    for doccat in DocumentCat:
        topic = doccat.value.topic
        topic_matches = topic in topics and (
            all_entries
            or (
                is_doc_cat_valid_as_independent_document(doccat)
                or (doccat.value.topic == DocumentTopic.UNKNOWN)
            )
        )
        catalog_matches = doccat.value.document_catalog in document_catalogs
        if topic_matches and catalog_matches:
            if is_doc_cat_in_public_default_set(doccat.name, doccat):
                lines.append(get_columns_for_doccat(doccat, add_column_datamodel))
    return lines


def create_hypodossier_excel_file_plan(
    all_data: List[Dict[str, str]],
    dest_file_path: Path,
    sheet_name="Aktenplan HypoDossier",
):
    writer = ExcelWriter(dest_file_path, engine="xlsxwriter")
    # Get the xlsxwriter workbook and worksheet objects.

    # Write the dataframe data to XlsxWriter. Turn off the default header and
    # index and skip one row to allow us to insert a user defined header.
    df = DataFrame(data=all_data)
    df.to_excel(writer, sheet_name=sheet_name, startrow=0, header=True, index=False)
    worksheet = writer.sheets[sheet_name]
    # wrap_format = workbook.add_format({"text_wrap": True})

    # Get the dimensions of the dataframe.
    (max_row, max_col) = df.shape

    # Create a list of column headers, to use in add_table().
    column_settings = [{"header": column} for column in df.columns]

    # Add the Excel table structure. Pandas will add the data.
    worksheet.add_table(0, 0, max_row, max_col - 1, {"columns": column_settings})

    # Make the columns wider for clarity.
    worksheet.set_column(0, 0, 8)  # ID
    worksheet.set_column(1, 1, 20)  # Topic
    worksheet.set_column(2, 6, 30)  # Name, DE, FR, EN, IT
    worksheet.set_column(7, 7, 40)  # Desc
    writer.close()


def create_hypodossier_txt_spec_file_plan(
    all_data: List[Dict[str, str]],
    dest_file_path: Path,
    col_sep="|",
    allow_incomplete_lines: bool = False,
):
    lines = []
    for line_dict in all_data:
        for key, v in line_dict.items():
            if v is None:
                if allow_incomplete_lines:
                    v = "[EMPTY]"
                    line_dict[key] = v
                else:
                    raise Exception(f"Value missing for {line_dict}, key={key}")
        line = col_sep.join(line_dict.values())
        lines.append(line)

    print("\n".join(lines))

    dest_file_path.parent.mkdir(exist_ok=True, parents=True)

    with codecs.open(str(dest_file_path), "w", "utf-8") as f:
        f.write("\n".join(lines))

    print(f"{len(lines)} document categories")


def create_spec_file_plan(
    topics: List[DocumentTopic] = None,
    timestamp_version=datetime.today().strftime("%Y%m%d")[2:],
    filename_suffix="",
    allow_incomplete_lines=False,
    all_entries=False,
    document_catalogs: List[DocumentCatalog] = None,
    add_column_datamodel: bool = True,
):
    """
    Export the document categories to Excel

    """

    p_excel = Path(
        f"{BASE_DIR}/doc/internal/spec_file_plan/{timestamp_version}_Hypodossier_Spezifikation_Aktenplan{filename_suffix}.xlsx"
    )
    p_excel.parent.mkdir(exist_ok=True, parents=True)

    all_data = get_columns_for_all_doccats(
        topics,
        all_entries=all_entries,
        document_catalogs=document_catalogs,
        add_column_datamodel=add_column_datamodel,
    )
    create_hypodossier_excel_file_plan(all_data, p_excel)
    assert p_excel.exists()
    print(f"Exported excel file plan to {p_excel}")

    p_txt = Path(
        f"{BASE_DIR}/doc/internal/spec_file_plan/{timestamp_version}_Hypodossier_Spezifikation_Aktenplan{filename_suffix}.txt"
    )
    create_hypodossier_txt_spec_file_plan(
        all_data, p_txt, allow_incomplete_lines=allow_incomplete_lines
    )
    assert p_txt.exists()
    print(f"Exported txt file plan to {p_txt}")


if __name__ == "__main__":
    # print(get_columns_for_all_doccats())
    print(ALL_TOPICS)
    print(COMMON_TOPICS)

    add_column_datamodel = True

    create_spec_file_plan(
        COMMON_TOPICS,
        filename_suffix="_DEFAULT",
        document_catalogs=[DocumentCatalog.DEFAULT],
        add_column_datamodel=add_column_datamodel,
    )
    create_spec_file_plan(
        ALL_TOPICS,
        filename_suffix="_ALL",
        allow_incomplete_lines=True,
        # add everything except for legacy here
        document_catalogs=[
            DocumentCatalog.DEFAULT,
            DocumentCatalog.BEKB,
            DocumentCatalog.HBL,
            DocumentCatalog.ZKB,
        ],
        add_column_datamodel=add_column_datamodel,
    )

    for catalog in [DocumentCatalog.BEKB, DocumentCatalog.HBL, DocumentCatalog.ZKB]:
        create_spec_file_plan(
            ALL_TOPICS,
            filename_suffix=f"_{catalog.value}",
            allow_incomplete_lines=True,
            # add everything except for legacy here
            document_catalogs=[DocumentCatalog.DEFAULT, catalog],
            add_column_datamodel=add_column_datamodel,
        )
