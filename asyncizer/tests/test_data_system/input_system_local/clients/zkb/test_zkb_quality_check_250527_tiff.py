from pathlib import Path
from typing import <PERSON>ple

import pytest
import tifffile
from PIL import Image

from asyncizer.tests.util_tests import DossierExpectations, DossierTest
from global_settings import (
    PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS,
)

import structlog

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "250527_tiff"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_CLIENTS}/zkb/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_local_clients_zkb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_250527_tiff_broken():
    await Do<PERSON>rTest(
        override_existing=True,
        use_ocr_cache=False,
        source_folder=path_root_folder / "broken_tiff",
        source_file_filter=[],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(),
    ).run()


def is_valid_tiff_strict(path: Path) -> Tuple[bool, Exception]:
    try:
        with tifffile.TiffFile(path) as tif:
            # Attempt to read pixel data — will fail for malformed headers/tags
            _ = tif.asarray()
        return True
    except Exception as e:
        # E.g. TiffFileError('corrupted strip cannot be reshaped from (9042300,) to (1, 3549, 2550, 1)')
        return False, e


def test_repair_broken_tiff():
    """
    This code is for tiff investigation only. So far we did not have tiff files
    that could not be fixed by pillow. In case that is necessary either
    detect broken tiffs with tifffile or use the PDF ocr exception process
    to run
    :return:
    """
    p = path_root_folder / "broken_tiff" / "Police_Gebäudeversicherung.tiff"

    # Pillow does not detect a problem
    try:
        with Image.open(p) as img:
            img.load()  # Force full load of image data
    except Exception as e:
        print(f"[BROKEN] {p}: {e}")

    # Try to find problem with tifffile

    success = is_valid_tiff_strict(p)
    assert not success

    assert p.exists()
    img = Image.open(p)
    p_target = p.parent.parent / "fixed_tiff"
    p_target.mkdir(parents=True, exist_ok=True)
    img.save(p_target / "Police_Gebäudeversicherung_fixed.tiff", compression="tiff_lzw")
