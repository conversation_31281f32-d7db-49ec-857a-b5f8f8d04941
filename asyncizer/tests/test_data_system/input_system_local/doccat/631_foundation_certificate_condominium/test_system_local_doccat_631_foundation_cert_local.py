from pathlib import Path

import pytest

from asyncizer.tests.util_tests import <PERSON>ssierExpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT
from hypodossier.core.domain.DocumentCat import DocumentCat

source_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM_LOCAL_DOCCAT}/631_foundation_certificate_condominium"
)
dest_folder_prefix = "input_system_local_doccat_631_foundation_certificate_condomiumn"


@pytest.mark.asyncio
async def test_local_paths():
    assert (
        source_folder.exists()
    ), f"Source folder does not exists: source_folder={source_folder}"


@pytest.mark.asyncio
async def test_01():
    await DossierTest(
        override_existing=True,
        webbrowser=False,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_01_e2748302-f2b4-4ca6-a226-58cf84446bb1.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 45 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   PROPERTY_VALUATION
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   UNKNOWN_DE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   BUILDING_DESCRIPTION
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   AGREEMENT_CHARGE_IMMOVABLE_PROPERTY
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   USER_REGULATIONS_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   USER_REGULATIONS_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   NOTARY_MISC
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   PROPERTY_VALUATION
            #   UNKNOWN
            # --> 28 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 2 + 2 + 1 + 1 = 6 included
            #     (USER_REGULATIONS_CONDOMINIUM, EXTRACT_FROM_LAND_REGISTER, NOTARY_MISC, BUILDING_DESCRIPTION)
            # --> 2 unknown
            # --> 2 + 6 + 1 + 1 = 9 wrong
            #     (PROPERTY_VALUATION, SALES_DOCUMENTATION, BUILDING_DESCRIPTION, AGREEMENT_CHARGE_IMMOVABLE_PROPERTY)
        ),
    ).run()


@pytest.mark.asyncio
async def test_02():
    await DossierTest(
        override_existing=True,
        webbrowser=False,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_02_7d371f66-3301-4846-a819-07d5aa194787.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 13 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   PLAN_FLOOR
            #   PLAN_FLOOR
            #   PLAN_FLOOR
            #   PLAN_FLOOR
            # --> 6 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 6 included (EXTRACT_FROM_LAND_REGISTER, PLAN_FLOOR)
            # --> 1 unknown
            # --> 0 wrong
        ),
    ).run()


@pytest.mark.asyncio
async def test_03():
    await DossierTest(
        override_existing=True,
        webbrowser=False,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_03_b17f55ee-93ed-416d-8ec8-bc6a87d78e05.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 18 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   CONTRACT_OF_SALE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   NOTARY_MISC
            #   NOTARY_MISC
            # --> 10 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 3 included (EXTRACT_FROM_LAND_REGISTER, NOTARY_MISC)
            # --> 0 unknown
            # --> 5 wrong (SALES_DOCUMENTATION, CONTRACT_OF_SALE)
        ),
    ).run()


@pytest.mark.asyncio
async def test_04():
    await DossierTest(
        override_existing=True,
        webbrowser=False,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_04_001f1fd9-9dd0-4d11-8d21-b37dd0e55766.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 20 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   SALES_DOCUMENTATION
            #   UNKNOWN_DE
            #   SALES_DOCUMENTATION
            #   NOTARY_MISC
            #   REGISTRATION_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            # --> 6 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 9 included (EXTRACT_FROM_LAND_REGISTER, NOTARY_MISC)
            # --> 1 unknown
            # --> 4 wrong (SALES_DOCUMENTATION)
        ),
    ).run()


@pytest.mark.skip(reason="not solved yet")
@pytest.mark.asyncio
async def test_05():
    """
    This document is not really a foundation certificate but a certificate about a change in regulations.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_05_7fb73128-cbfe-4cb6-b584-df5dad1d287e.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 10 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   DEED_OF_GIFT
            #   MORTGAGE_CONTRACT
            #   NOTARY_MISC
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   TRANSFER_OF_SECURITY
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   NOTARY_MISC
            # --> 2 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 2 included (NOTARY_MISC)
            # --> 3 unknown
            # --> 5 wrong (MORTGAGE_CONTRACT, DEED_OF_GIFT, TRANSFER_OF_SECURITY)
        ),
    ).run()


@pytest.mark.skip(reason="not solved yet")
@pytest.mark.asyncio
async def test_06():
    """
    This document is a proper foundation certificate and shows doc cats that could be included as valid.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_06_70dd4296-45a5-4a1e-bf16-2ed711c912d6.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 16 items for doc_cat_frequency_per_page:
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   BUILDING_RIGHTS_AGREEMENT
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   MORTGAGE_CONTRACT
            #   MORTGAGE_CONTRACT
            #   AGREEMENT_CHARGE_IMMOVABLE_PROPERTY
            #   AGREEMENT_CHARGE_IMMOVABLE_PROPERTY
            #   AGREEMENT_CHARGE_IMMOVABLE_PROPERTY
            #   UNKNOWN_DE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   BUILDING_RIGHTS_AGREEMENT
            #   NOTARY_MISC
            # --> 4 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 1 included (NOTARY_MISC)
            # --> 4 unknown
            # --> 7 wrong (MORTGAGE_CONTRACT, BUILDING_RIGHTS_AGREEMENT, AGREEMENT_CHARGE_IMMOVABLE_PROPERTY)
        ),
    ).run()


@pytest.mark.asyncio
async def test_07():
    await DossierTest(
        override_existing=True,
        webbrowser=False,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_07_81e9d6e6-054b-4769-84cb-d921f8138039.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 19 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   USER_REGULATIONS_CONDOMINIUM
            #   USER_REGULATIONS_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   BUILDING_RIGHTS_AGREEMENT
            #   NOTARY_MISC
            # --> 8 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 9 included (USER_REGULATIONS_CONDOMINIUM, EXTRACT_FROM_LAND_REGISTER, NOTARY_MISC)
            # --> 1 unknown
            # --> 1 wrong (BUILDING_RIGHTS_AGREEMENT)
        ),
    ).run()


@pytest.mark.skip(reason="not solved yet")
@pytest.mark.asyncio
async def test_08():
    """
    This document is a proper foundation certificate with a lot of plans (UNKNOWN). It might serve as an example
    to value pages at the beginning of the document more and to devalue UNKNOWN pages.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_08_96868a49-e53e-449a-8d96-daed634f8ce5.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 24 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   CONTRACT_OF_SALE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   NOTARY_MISC
            #   CONSTRUCTION_PERMIT
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   PROPERTY_VALUATION
            #   PROPERTY_VALUATION
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN
            # --> 8 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 1 included (NOTARY_MISC)
            # --> 11 unknown
            # --> 4 wrong (PROPERTY_VALUATION, CONTRACT_OF_SALE, CONSTRUCTION_PERMIT)
        ),
    ).run()


@pytest.mark.asyncio
async def test_09():
    """
    This document is a mix but gets correctly assigned to FOUNDATION_CERTIFICATE_CONDOMINIUM.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_09_b6a1869b-7268-44ef-a31f-d24a03bba78e.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1,
                DocumentCat.LAND_REGISTER_BILL: 1,
            },
            # Found 25 items for doc_cat_frequency_per_page:
            #   CONTRACT_OF_SALE
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   USER_REGULATIONS_CONDOMINIUM
            #   NOTARY_MISC
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   USER_REGULATIONS_CONDOMINIUM
            #   UNKNOWN_DE
            #   USER_REGULATIONS_CONDOMINIUM
            #   PROPERTY_VALUATION
            #   USER_REGULATIONS_CONDOMINIUM
            #   PROPERTY_INSURANCE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   CONTRACT_OF_SALE
            #   IRREVOCABLE_PROMISES_TO_PAY
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   (LAND_REGISTER_BILL: gets cut to a separate doc)
            # --> 9 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 6 included (EXTRACT_FROM_LAND_REGISTER, USER_REGULATIONS_CONDOMINIUM, NOTARY_MISC)
            # --> 4 unknown
            # --> 5 wrong (CONTRACT_OF_SALE, PROPERTY_VALUATION, PROPERTY_INSURANCE, IRREVOCABLE_PROMISES_TO_PAY)
        ),
    ).run()


@pytest.mark.skip(reason="not solved yet")
@pytest.mark.asyncio
async def test_10():
    """
    This document is a proper FOUNDATION_CERTIFICATE_CONDOMINIUM with little text and many plans.
    Page 14 is a plan that weirdly gets classified as HRA and even cut as separate doc.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_10_b2317ebf-23af-4059-b9fb-e286142291d6.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1,
                # DocumentCat.UNKNOWN_DE: 1,
                # DocumentCat.HRA: 1,
            },
            # Found 16 items for doc_cat_frequency_per_page:
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   SALES_DOCUMENTATION
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   SALES_DOCUMENTATION
            #   UNKNOWN_DE
            #   PROPERTY_VALUATION
            #   UNKNOWN
            #   SALES_DOCUMENTATION
            #   UNKNOWN_DE
            #   SALES_DOCUMENTATION
            #   HRA --> this is page 14, a floor plan
            # --> 4 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 1 included (EXTRACT_FROM_LAND_REGISTER, NOTARY_MISC)
            # --> 5 unknown
            # --> 6 wrong (SALES_DOCUMENTATION, PROPERTY_VALUATION, HRA)
        ),
    ).run()


@pytest.mark.skip(reason="not solved yet")
@pytest.mark.asyncio
async def test_11():
    """
    This document is not really a foundation certificate but a certificate about a change in regulations.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=True,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_11_d6dba00c-e571-470c-aaf7-d1dd43597d75.pdf"],
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={
                DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            },
            # Found 21 items for doc_cat_frequency_per_page:
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   EXTRACT_FROM_LAND_REGISTER
            #   EXTRACT_FROM_LAND_REGISTER
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN
            #   NOTARY_MISC
            #   NOTARY_MISC
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN
            #   MEETING_MINUTES_CONDOMINIUM
            #   UNKNOWN_DE
            #   PROPERTY_VALUATION
            #   BUILDING_RIGHTS_AGREEMENT
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   UNKNOWN_DE
            # --> 7 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 4 included (EXTRACT_FROM_LAND_REGISTER, NOTARY_MISC)
            # --> 7 unknown
            # --> 3 wrong (MEETING_MINUTES_CONDOMINIUM, PROPERTY_VALUATION, BUILDING_RIGHTS_AGREEMENT)
        ),
    ).run()


@pytest.mark.asyncio
async def test_12():
    """
    This document is a proper FOUNDATION_CERTIFICATE_CONDOMINIUM that should be recognized without problems.
    """
    await DossierTest(
        override_existing=True,
        webbrowser=False,
        show_page=-1,
        source_folder=source_folder,
        dest_folder_prefix=dest_folder_prefix,
        source_file_filter=["631_12_e2ee4a0c-351b-4708-9fad-ff9f63ca59e6.pdf"],
        dossier_expectations=DossierExpectations(
            # expected_doc_cat_frequency={
            #     DocumentCat.FOUNDATION_CERTIFICATE_CONDOMINIUM: 1
            # },
            # Found 9 items for doc_cat_frequency_per_page:
            #   CONTRACT_OF_SALE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   UNKNOWN_DE
            #   FOUNDATION_CERTIFICATE_CONDOMINIUM
            #   DEED_OF_GIFT
            #   NOTARY_MISC
            #   UNKNOWN_DE
            #   NOTARY_MISC
            # --> 3 right (FOUNDATION_CERTIFICATE_CONDOMINIUM)
            # --> 2 included (NOTARY_MISC)
            # --> 2 unknown
            # --> 2 wrong (CONTRACT_OF_SALE, DEED_OF_GIFT)
        ),
    ).run()
