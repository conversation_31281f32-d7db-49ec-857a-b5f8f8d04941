from pathlib import Path

import pytest

from asyncizer.tests.util_tests import Dossier<PERSON>xpect<PERSON>, DossierTest
from global_settings import PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM

import structlog

from hypodossier.core.domain.DocumentCat import DocumentCat

logger = structlog.getLogger(__name__)

FOLDER_TYPE = "cba"
path_root_folder = Path(
    f"{PROJECT_PATH_HYPODOSSIER_DATA_SYSTEM}/input_system_local/clients/clientis/{FOLDER_TYPE}"
)
dest_folder_prefix = f"input_system_clients_dcb_{FOLDER_TYPE}_"


@pytest.mark.asyncio
async def test_local_paths():
    p = Path(f"{path_root_folder}")
    assert (
        p.exists()
    ), f"Root folder for local tax tests does not exists: source_folder={p}"


@pytest.mark.asyncio
async def test_250516_email_problem_seon_eml():
    """
    From this we cannot extract sender, recipient, date. Code fixed now that
    the email is still rendered correctly.    @return:
    """

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250516_email_problem_seon" / "01" / "eml",
        # source_file_filter=["340 Salärabrechnung Sinan_Lohn_Juni.pdf"],
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.CORRESPONDENCE_EMAIL: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 3},
        ),
    ).run()


@pytest.mark.asyncio
async def test_250516_email_problem_seon_msg():
    """
    From this we cannot extract sender, recipient, date. Code fixed now that
    the email is still rendered correctly.
    @return:
    """

    await DossierTest(
        override_existing=True,
        use_ocr_cache=True,
        source_folder=path_root_folder / "250516_email_problem_seon" / "01" / "msg",
        show_page=-1,
        dest_folder_prefix=dest_folder_prefix,
        dossier_expectations=DossierExpectations(
            expected_doc_cat_frequency={DocumentCat.CORRESPONDENCE_EMAIL: 1},
            expected_doc_cat_frequency_per_page={DocumentCat.CORRESPONDENCE_EMAIL: 3},
        ),
    ).run()
