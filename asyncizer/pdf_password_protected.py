from pathlib import Path


class PasswordProtectedFile(Exception):
    """Exception raised if the file is password protected"""

    def __init__(self, bucket, object_name) -> None:
        self.bucket = bucket
        self.object_name = object_name
        self.message = f"The file {self.object_name} in bucket {self.bucket} is password protected."
        super().__init__(self.message)


class PasswordProtectedLocalFile(Exception):
    """Exception raised if the file is password protected"""

    def __init__(self, p: Path, context: str) -> None:
        self.p = p
        self.message = f"The local file {self.p} is password protected."
        if context:
            self.message += f", context={context}"
        super().__init__(self.message)
