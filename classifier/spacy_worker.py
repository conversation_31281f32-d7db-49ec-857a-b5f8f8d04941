import logging
import pickle
from time import sleep, time
from typing import List

import structlog
from kombu import Queue, Exchange, Connection
from kombu.mixins import ConsumerProducerMixin
from pydantic import BaseModel
from structlog.contextvars import bound_contextvars
from tblib import pickling_support

import global_settings
from asyncizer.log_configuration import configure_structlog
from asyncizer.randomword import randomword
from classifier.spacy_classification import classify_with_spacy
from hypodossier.shared import Classification

pickling_support.install()

logger = structlog.getLogger(__name__)

# 220505 mt: Rename queue from 'classifier.spacy.TextRequest' because the new queue is a quorum queue
SPACY_WORKER_ROUTING_KEY = "hyextract.pageclassifier.TextClassificationRequestV1"


class TextClassificationRequest(BaseModel):
    text: str
    top_n: int = 5


#
# class TextClassificationRequestV2(BaseModel):
#     text: str
#     top_n: int = 5
#     lang: typing.Optional[str] = 'de'


class ClassificationResponse(BaseModel):
    classifications: List[Classification]


spacy_classification_map = {
    # Can be removed after retraining spacy in Mai 2024
    "DEEN/100/partner/zkb/ZKB-17684/Debitoren-Kreditorenliste": "DEEN/541/Debitoren- und Kreditorenliste",
    "DEEN/100/partner/zkb/ZKB-18416/Liquiditätsplan": "DEEN/534/Liquiditätsplan",
    "DE/310/BL/Wertschriften Detailauflistung": "DE/310/BL/Wertschriftenverzeichnis Details//Detailauflistung",
    # The next 4 be removed after retraining later than Sept 2024
    # Also remove from classification_parser_map_de
    "DE/671/Baubewilligung": "DE/672/Baubewilligung",
    "DE/782/Anmeldung Grundbuch": "DE/694/Anmeldung Grundbuch",
    "DE/789/Grundbuch Diverses": "DE/698/Grundbuch Diverses",
    "DE/920/Rechnung Liegenschaft": "DE/690/Rechnung Liegenschaft",
    "DE/955/Rechnung Grundbuchamt": "DE/697/Rechnung Grundbuchamt",
    "DE/680/Generalunternehmervertrag": "DE/675/Generalunternehmervertrag",
    "FR/789/Grundbuch Diverses": "FR/698/Grundbuch Diverses",
    "IT/782/Anmeldung Grundbuch": "IT/694/Anmeldung Grundbuch",
    "DEENFRIT/322/Bank-DE": "DEENFRIT/322/Bank//DE",
    "DEENFRIT/322/Bank-EN": "DEENFRIT/322/Bank//EN",
    "DEENFRIT/322/Bank-FR": "DEENFRIT/322/Bank//FR",
    "DEENFRIT/322/Bank-IT": "DEENFRIT/322/Bank//IT",
    # This was due to a typo with sss
    "DE/310/AG/Personen, Vermögen, Abschlussituation (PVA)": "DE/310/AG/Personen, Vermögen, Abschlusssituation (PVA)",
    "DE/400/BEKB/EKD120/Fragebogen Finanzplanung": "DE/100/partner/bekb/FIPLA_FORM/200-EKD120/Fragebogen Finanzplanung",
    "DE/100/partner/bekb/FIPLA_FORM/400-EKD120/Fragebogen Finanzplanung": "DE/100/partner/bekb/FIPLA_FORM/200-EKD120/Fragebogen Finanzplanung",
    "FR/400/BEKB/EKD120/Fragebogen Finanzplanung": "FR/100/partner/bekb/FIPLA_FORM/200-EKD120/Fragebogen Finanzplanung",
    "FR/100/partner/bekb/FIPLA_FORM/400-EKD120/Fragebogen Finanzplanung": "FR/100/partner/bekb/FIPLA_FORM/200-EKD120/Fragebogen Finanzplanung",
}


def map_spacy_classification_name(classification: str) -> str:
    """
    Apply some mapping to the resulting classification string.
    E.g. if a classification should be renamed but the model has not yet been retrained.
    @param classification:
    @return:
    """
    if classification and classification in spacy_classification_map:
        return spacy_classification_map[classification]
    return classification


class SpacyWorker(ConsumerProducerMixin):
    task_queue = Queue(
        SPACY_WORKER_ROUTING_KEY,
        Exchange(""),
        SPACY_WORKER_ROUTING_KEY,
        queue_arguments={"x-queue-type": "quorum"},
    )

    def __init__(self, connection):
        self.connection: Connection = connection

    def get_consumers(self, Consumer, channel):
        return [
            Consumer(
                queues=[self.task_queue], callbacks=[self.on_task], prefetch_count=1
            )
        ]

    def on_consume_ready(self, connection, channel, consumers, **kwargs):
        logger.info(
            "global_settings.SPACY_MODEL_*_S3_OBJECT_PATH",
            models=dict(
                de=global_settings.SPACY_MODEL_DE_S3_OBJECT_PATH,
                en=global_settings.SPACY_MODEL_EN_S3_OBJECT_PATH,
                fr=global_settings.SPACY_MODEL_FR_S3_OBJECT_PATH,
                it=global_settings.SPACY_MODEL_IT_S3_OBJECT_PATH,
            ),
        )
        self.initialize_all_language_models()
        logger.info("consuming...")

    def initialize_all_language_models(self):
        logger.info("Now load model de")
        classify_with_spacy(
            text="Das Leben ist schön und es ist noch schöner mit Hypodossier."
        )
        logger.info("Now load model en")
        classify_with_spacy(text="Life is good and it's even better with Hypodossier.")
        logger.info("Now load model fr")
        classify_with_spacy(
            text="La vie est belle et elle est encore plus belle avec Hypodossier."
        )
        logger.info("Now load model it")
        classify_with_spacy(
            text="La vita è bella e con Hypodossier lo è ancora di più."
        )

    @staticmethod
    def process_classification_request(
        request: TextClassificationRequest,
    ) -> ClassificationResponse:
        # doc = self.nlp(request.text)

        # text, size_mb = get_mem_stats("before classify")
        # logger.info(text)

        classifications_raw, name = classify_with_spacy(
            request.text, top_n=request.top_n
        )

        # text, size_mb = get_mem_stats("after classify")
        # logger.info(text)
        classifications: List[Classification] = []

        if classifications_raw:
            classifications = [
                Classification(
                    classification=map_spacy_classification_name(classification[0]),
                    confidence=classification[1],
                    classifier=name,
                )
                for classification in classifications_raw
            ]
            logger.info(
                "Found top classification", top_classification=classifications[0].dict()
            )
        else:
            logger.info(f"No classification found ...text={request.text[0:100]}...")

        return ClassificationResponse(classifications=classifications)

    def on_task(self, body, message):
        start = time()
        correlation_id = message.properties.get("correlation_id")
        with bound_contextvars(cid=correlation_id if correlation_id else randomword(4)):
            try:

                request = TextClassificationRequest(**body)
                response = self.process_classification_request(request)

                self.producer.publish(
                    response.json(),
                    exchange="",
                    routing_key=message.properties["reply_to"],
                    correlation_id=message.properties["correlation_id"],
                    retry=True,
                )
                message.ack()
            except Exception as e:
                logger.exception("could not classify", exc_info=True)
                self.producer.publish(
                    pickle.dumps(e),
                    exchange="",
                    routing_key=message.properties["reply_to"],
                    correlation_id=message.properties["correlation_id"],
                    content_type="pickle",
                    retry=True,
                )
                message.ack()
            finally:
                logger.info("handled spacy classification", duration=time() - start)


def monitor_heartbeat(conn):
    rate = conn.heartbeat
    while True:
        try:
            # conn.connection.send_heartbeat()
            conn.heartbeat_check(rate)
            print("HEARTBEAT OK")
            sleep(rate / 2.0)
        except (KeyboardInterrupt, SystemExit):
            logging.warning("monitor heartbeat stop")
            break
        except:
            print("HEARTBEAT FAILED")
            # logging.error(traceback.format_exc())
            sleep(1)


def main():
    # So we can run via watchgod via `watchgod -w . -- python -m classifier.spacy_worker.main`
    configure_structlog()
    with Connection(global_settings.RABBIT_URL, heartbeat=20) as connection:
        # t = Thread(target=monitor_heartbeat, args=(connection,))
        # t.setDaemon(True)
        # t.start()
        SpacyWorker(connection).run()


if __name__ == "__main__":
    main()
